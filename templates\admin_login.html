{% extends 'base.html' %}

{% block title %}Вход администратора - KPI{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-4 col-sm-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <h3 class="card-title text-center">Вход администратора</h3>
                    </div>
                </div>
                <form method="post" action="/admin/login">
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Имя пользователя</label>
                            <input type="text" name="username" class="form-control" placeholder="Введите имя пользователя" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Пароль</label>
                            <input type="password" name="password" class="form-control" placeholder="Введите пароль" required>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary w-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                                    <path d="M20 12h-13l3 -3m0 6l-3 -3"></path>
                                </svg>
                                Войти
                            </button>
                        </div>
                        <div class="text-center mt-3">
                            <a href="/" class="text-muted">← Вернуться на главную</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <script>
            {% for category, message in messages %}
                {% if category == 'error' %}
                    alert('Ошибка: {{ message }}');
                {% elif category == 'success' %}
                    alert('{{ message }}');
                {% endif %}
            {% endfor %}
        </script>
    {% endif %}
{% endwith %}
{% endblock %}
