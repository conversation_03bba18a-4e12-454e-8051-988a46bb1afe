{% extends 'base.html' %}

{% block title %}Подробности опроса - Админ{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Подробности опроса #{{ survey.id }}</h2>
                <a href="/admin/dashboard" class="btn btn-outline-primary">← Назад к списку</a>
            </div>
            
            <!-- Информация об опросе -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">Информация об опросе</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Участник:</strong></td>
                                    <td>{{ user.first_name }} {{ user.last_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Тип опроса:</strong></td>
                                    <td>{{ survey_type.description }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Общий балл:</strong></td>
                                    <td><span class="badge bg-success fs-6">{{ survey.total_score }}</span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Дата прохождения:</strong></td>
                                    <td>{{ survey.completed_at.strftime('%d.%m.%Y в %H:%M') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Количество вопросов:</strong></td>
                                    <td>{{ answers|length }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Статус:</strong></td>
                                    <td><span class="badge bg-primary">{{ survey.status }}</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Ответы на вопросы -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Ответы на вопросы</h3>
                </div>
                <div class="card-body">
                    {% if answers %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th width="10%">№</th>
                                    <th width="50%">Вопрос</th>
                                    <th width="25%">Ответ</th>
                                    <th width="15%">Балл</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for answer in answers %}
                                <tr>
                                    <td>{{ answer.question_number }}</td>
                                    <td>{{ answer.question_text }}</td>
                                    <td>
                                        <span class="text-muted">{{ answer.answer_value }}</span>
                                    </td>
                                    <td>
                                        {% if answer.answer_score >= 0 %}
                                            <span class="badge bg-success">+{{ answer.answer_score }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ answer.answer_score }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <td colspan="3"><strong>Итого:</strong></td>
                                    <td>
                                        <strong>
                                            {% if survey.total_score >= 0 %}
                                                <span class="text-success">{{ survey.total_score }}</span>
                                            {% else %}
                                                <span class="text-danger">{{ survey.total_score }}</span>
                                            {% endif %}
                                        </strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <div class="text-muted">
                            <h4>Ответы не найдены</h4>
                            <p>Возможно, данные были повреждены</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
