/**
 * TinyMCE version 6.4.2 (2023-04-26)
 */
!function(){"use strict";var e=function(e){if(null===e)return"null";if(void 0===e)return"undefined";var t=typeof e;return"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t},t=function(e){return{eq:e}},n=t((function(e,t){return e===t})),o=function(e){return t((function(t,n){if(t.length!==n.length)return!1;for(var o=t.length,r=0;r<o;r++)if(!e.eq(t[r],n[r]))return!1;return!0}))},r=function(e){return t((function(r,s){var a=Object.keys(r),i=Object.keys(s);if(!function(e,n){return function(e,n){return t((function(t,o){return e.eq(n(t),n(o))}))}(o(e),(function(e){return function(e,t){return Array.prototype.slice.call(e).sort(t)}(e,n)}))}(n).eq(a,i))return!1;for(var l=a.length,d=0;d<l;d++){var c=a[d];if(!e.eq(r[c],s[c]))return!1}return!0}))},s=t((function(t,n){if(t===n)return!0;var a=e(t);return a===e(n)&&(function(e){return-1!==["undefined","boolean","number","string","function","xml","null"].indexOf(e)}(a)?t===n:"array"===a?o(s).eq(t,n):"object"===a&&r(s).eq(t,n))}));const a=Object.getPrototypeOf,i=(e,t,n)=>{var o;return!!n(e,t.prototype)||(null===(o=e.constructor)||void 0===o?void 0:o.name)===t.name},l=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&i(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":t})(t)===e,d=e=>t=>typeof t===e,c=e=>t=>e===t,u=(e,t)=>f(e)&&i(e,t,((e,t)=>a(e)===t)),m=l("string"),f=l("object"),g=e=>u(e,Object),p=l("array"),h=c(null),b=d("boolean"),v=c(void 0),y=e=>null==e,C=e=>!y(e),w=d("function"),x=d("number"),k=(e,t)=>{if(p(e)){for(let n=0,o=e.length;n<o;++n)if(!t(e[n]))return!1;return!0}return!1},E=()=>{},S=(e,t)=>(...n)=>e(t.apply(null,n)),_=(e,t)=>n=>e(t(n)),N=e=>()=>e,R=e=>e,A=(e,t)=>e===t;function O(e,...t){return(...n)=>{const o=t.concat(n);return e.apply(null,o)}}const T=e=>t=>!e(t),B=e=>()=>{throw new Error(e)},D=e=>e(),P=e=>{e()},L=N(!1),M=N(!0);class I{constructor(e,t){this.tag=e,this.value=t}static some(e){return new I(!0,e)}static none(){return I.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?I.some(e(this.value)):I.none()}bind(e){return this.tag?e(this.value):I.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:I.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return C(e)?I.some(e):I.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}I.singletonNone=new I(!1);const F=Array.prototype.slice,U=Array.prototype.indexOf,z=Array.prototype.push,j=(e,t)=>U.call(e,t),H=(e,t)=>j(e,t)>-1,$=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return!0;return!1},V=(e,t)=>{const n=e.length,o=new Array(n);for(let r=0;r<n;r++){const n=e[r];o[r]=t(n,r)}return o},q=(e,t)=>{for(let n=0,o=e.length;n<o;n++)t(e[n],n)},W=(e,t)=>{for(let n=e.length-1;n>=0;n--)t(e[n],n)},K=(e,t)=>{const n=[],o=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?n:o).push(s)}return{pass:n,fail:o}},G=(e,t)=>{const n=[];for(let o=0,r=e.length;o<r;o++){const r=e[o];t(r,o)&&n.push(r)}return n},Y=(e,t,n)=>(W(e,((e,o)=>{n=t(n,e,o)})),n),X=(e,t,n)=>(q(e,((e,o)=>{n=t(n,e,o)})),n),Q=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++){const r=e[o];if(t(r,o))return I.some(r);if(n(r,o))break}return I.none()},J=(e,t)=>Q(e,t,L),Z=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return I.some(n);return I.none()},ee=e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!p(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);z.apply(t,e[n])}return t},te=(e,t)=>ee(V(e,t)),ne=(e,t)=>{for(let n=0,o=e.length;n<o;++n)if(!0!==t(e[n],n))return!1;return!0},oe=e=>{const t=F.call(e,0);return t.reverse(),t},re=(e,t)=>G(e,(e=>!H(t,e))),se=(e,t)=>{const n={};for(let o=0,r=e.length;o<r;o++){const r=e[o];n[String(r)]=t(r,o)}return n},ae=(e,t)=>{const n=F.call(e,0);return n.sort(t),n},ie=(e,t)=>t>=0&&t<e.length?I.some(e[t]):I.none(),le=e=>ie(e,0),de=e=>ie(e,e.length-1),ce=w(Array.from)?Array.from:e=>F.call(e),ue=(e,t)=>{for(let n=0;n<e.length;n++){const o=t(e[n],n);if(o.isSome())return o}return I.none()},me=Object.keys,fe=Object.hasOwnProperty,ge=(e,t)=>{const n=me(e);for(let o=0,r=n.length;o<r;o++){const r=n[o];t(e[r],r)}},pe=(e,t)=>he(e,((e,n)=>({k:n,v:t(e,n)}))),he=(e,t)=>{const n={};return ge(e,((e,o)=>{const r=t(e,o);n[r.k]=r.v})),n},be=e=>(t,n)=>{e[n]=t},ve=(e,t,n,o)=>{ge(e,((e,r)=>{(t(e,r)?n:o)(e,r)}))},ye=(e,t)=>{const n={};return ve(e,t,be(n),E),n},Ce=(e,t)=>{const n=[];return ge(e,((e,o)=>{n.push(t(e,o))})),n},we=e=>Ce(e,R),xe=(e,t)=>ke(e,t)?I.from(e[t]):I.none(),ke=(e,t)=>fe.call(e,t),Ee=(e,t)=>ke(e,t)&&void 0!==e[t]&&null!==e[t],Se=e=>{const t={};return q(e,(e=>{t[e]={}})),me(t)},_e=e=>void 0!==e.length,Ne=Array.isArray,Re=(e,t,n)=>{if(!e)return!1;if(n=n||e,_e(e)){for(let o=0,r=e.length;o<r;o++)if(!1===t.call(n,e[o],o,e))return!1}else for(const o in e)if(ke(e,o)&&!1===t.call(n,e[o],o,e))return!1;return!0},Ae=(e,t)=>{const n=[];return Re(e,((o,r)=>{n.push(t(o,r,e))})),n},Oe=(e,t)=>{const n=[];return Re(e,((o,r)=>{t&&!t(o,r,e)||n.push(o)})),n},Te=(e,t,n,o)=>{let r=v(n)?e[0]:n;for(let n=0;n<e.length;n++)r=t.call(o,r,e[n],n);return r},Be=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++)if(t.call(n,e[o],o,e))return o;return-1},De=e=>e[e.length-1],Pe=e=>{let t,n=!1;return(...o)=>(n||(n=!0,t=e.apply(null,o)),t)},Le=()=>Me(0,0),Me=(e,t)=>({major:e,minor:t}),Ie={nu:Me,detect:(e,t)=>{const n=String(t).toLowerCase();return 0===e.length?Le():((e,t)=>{const n=((e,t)=>{for(let n=0;n<e.length;n++){const o=e[n];if(o.test(t))return o}})(e,t);if(!n)return{major:0,minor:0};const o=e=>Number(t.replace(n,"$"+e));return Me(o(1),o(2))})(e,n)},unknown:Le},Fe=(e,t)=>{const n=String(t).toLowerCase();return J(e,(e=>e.search(n)))},Ue=(e,t,n)=>""===t||e.length>=t.length&&e.substr(n,n+t.length)===t,ze=(e,t)=>He(e,t)?((e,t)=>e.substring(t))(e,t.length):e,je=(e,t,n=0,o)=>{const r=e.indexOf(t,n);return-1!==r&&(!!v(o)||r+t.length<=o)},He=(e,t)=>Ue(e,t,0),$e=(e,t)=>Ue(e,t,e.length-t.length),Ve=e=>t=>t.replace(e,""),qe=Ve(/^\s+|\s+$/g),We=Ve(/^\s+/g),Ke=Ve(/\s+$/g),Ge=e=>e.length>0,Ye=e=>!Ge(e),Xe=(e,t=10)=>{const n=parseInt(e,t);return isNaN(n)?I.none():I.some(n)},Qe=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Je=e=>t=>je(t,e),Ze=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>je(e,"edge/")&&je(e,"chrome")&&je(e,"safari")&&je(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Qe],search:e=>je(e,"chrome")&&!je(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>je(e,"msie")||je(e,"trident")},{name:"Opera",versionRegexes:[Qe,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Je("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Je("firefox")},{name:"Safari",versionRegexes:[Qe,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(je(e,"safari")||je(e,"mobile/"))&&je(e,"applewebkit")}],et=[{name:"Windows",search:Je("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>je(e,"iphone")||je(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Je("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Je("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Je("linux"),versionRegexes:[]},{name:"Solaris",search:Je("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Je("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Je("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],tt={browsers:N(Ze),oses:N(et)},nt="Edge",ot="Chromium",rt="Opera",st="Firefox",at="Safari",it=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isEdge:o(nt),isChromium:o(ot),isIE:o("IE"),isOpera:o(rt),isFirefox:o(st),isSafari:o(at)}},lt=()=>it({current:void 0,version:Ie.unknown()}),dt=it,ct=(N(nt),N(ot),N("IE"),N(rt),N(st),N(at),"Windows"),ut="Android",mt="Linux",ft="macOS",gt="Solaris",pt="FreeBSD",ht="ChromeOS",bt=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isWindows:o(ct),isiOS:o("iOS"),isAndroid:o(ut),isMacOS:o(ft),isLinux:o(mt),isSolaris:o(gt),isFreeBSD:o(pt),isChromeOS:o(ht)}},vt=()=>bt({current:void 0,version:Ie.unknown()}),yt=bt,Ct=(N(ct),N("iOS"),N(ut),N(mt),N(ft),N(gt),N(pt),N(ht),e=>window.matchMedia(e).matches);let wt=Pe((()=>((e,t,n)=>{const o=tt.browsers(),r=tt.oses(),s=t.bind((e=>((e,t)=>ue(t.brands,(t=>{const n=t.brand.toLowerCase();return J(e,(e=>{var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Ie.nu(parseInt(t.version,10),0)})))})))(o,e))).orThunk((()=>((e,t)=>Fe(e,t).map((e=>{const n=Ie.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(o,e))).fold(lt,dt),a=((e,t)=>Fe(e,t).map((e=>{const n=Ie.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(r,e).fold(vt,yt),i=((e,t,n,o)=>{const r=e.isiOS()&&!0===/ipad/i.test(n),s=e.isiOS()&&!r,a=e.isiOS()||e.isAndroid(),i=a||o("(pointer:coarse)"),l=r||!s&&a&&o("(min-device-width:768px)"),d=s||a&&!l,c=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),u=!d&&!l&&!c;return{isiPad:N(r),isiPhone:N(s),isTablet:N(l),isPhone:N(d),isTouch:N(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:N(c),isDesktop:N(u)}})(a,s,e,n);return{browser:s,os:a,deviceType:i}})(navigator.userAgent,I.from(navigator.userAgentData),Ct)));const xt=()=>wt(),kt=navigator.userAgent,Et=xt(),St=Et.browser,_t=Et.os,Nt=Et.deviceType,Rt=-1!==kt.indexOf("Windows Phone"),At={transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",documentMode:St.isIE()?document.documentMode||7:10,cacheSuffix:null,container:null,canHaveCSP:!St.isIE(),windowsPhone:Rt,browser:{current:St.current,version:St.version,isChromium:St.isChromium,isEdge:St.isEdge,isFirefox:St.isFirefox,isIE:St.isIE,isOpera:St.isOpera,isSafari:St.isSafari},os:{current:_t.current,version:_t.version,isAndroid:_t.isAndroid,isChromeOS:_t.isChromeOS,isFreeBSD:_t.isFreeBSD,isiOS:_t.isiOS,isLinux:_t.isLinux,isMacOS:_t.isMacOS,isSolaris:_t.isSolaris,isWindows:_t.isWindows},deviceType:{isDesktop:Nt.isDesktop,isiPad:Nt.isiPad,isiPhone:Nt.isiPhone,isPhone:Nt.isPhone,isTablet:Nt.isTablet,isTouch:Nt.isTouch,isWebView:Nt.isWebView}},Ot=/^\s*|\s*$/g,Tt=e=>y(e)?"":(""+e).replace(Ot,""),Bt=function(e,t,n,o){o=o||this,e&&(n&&(e=e[n]),Re(e,((e,r)=>!1!==t.call(o,e,r,n)&&(Bt(e,t,n,o),!0))))},Dt={trim:Tt,isArray:Ne,is:(e,t)=>t?!("array"!==t||!Ne(e))||typeof e===t:void 0!==e,toArray:e=>{if(Ne(e))return e;{const t=[];for(let n=0,o=e.length;n<o;n++)t[n]=e[n];return t}},makeMap:(e,t,n={})=>{const o=m(e)?e.split(t||","):e||[];let r=o.length;for(;r--;)n[o[r]]={};return n},each:Re,map:Ae,grep:Oe,inArray:(e,t)=>{if(e)for(let n=0,o=e.length;n<o;n++)if(e[n]===t)return n;return-1},hasOwn:ke,extend:(e,...t)=>{for(let n=0;n<t.length;n++){const o=t[n];for(const t in o)if(ke(o,t)){const n=o[t];void 0!==n&&(e[t]=n)}}return e},walk:Bt,resolve:(e,t=window)=>{const n=e.split(".");for(let e=0,o=n.length;e<o&&(t=t[n[e]]);e++);return t},explode:(e,t)=>p(e)?e:""===e?[]:Ae(e.split(t||","),Tt),_addCacheSuffix:e=>{const t=At.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Pt=(e,t,n=A)=>e.exists((e=>n(e,t))),Lt=(e,t,n)=>e.isSome()&&t.isSome()?I.some(n(e.getOrDie(),t.getOrDie())):I.none(),Mt=(e,t)=>e?I.some(t):I.none(),It="undefined"!=typeof window?window:Function("return this;")(),Ft=(e,t)=>((e,t)=>{let n=null!=t?t:It;for(let t=0;t<e.length&&null!=n;++t)n=n[e[t]];return n})(e.split("."),t),Ut=Object.getPrototypeOf,zt=e=>{const t=Ft("ownerDocument.defaultView",e);return f(e)&&((e=>((e,t)=>{const n=((e,t)=>Ft(e,t))(e,t);if(null==n)throw new Error(e+" not available on this browser");return n})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Ut(e).constructor.name))},jt=e=>e.dom.nodeName.toLowerCase(),Ht=e=>e.dom.nodeType,$t=e=>t=>Ht(t)===e,Vt=$t(1),qt=$t(3),Wt=$t(9),Kt=$t(11),Gt=e=>t=>Vt(t)&&jt(t)===e,Yt=(e,t,n)=>{if(!(m(n)||b(n)||x(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},Xt=(e,t,n)=>{Yt(e.dom,t,n)},Qt=(e,t)=>{const n=e.dom;ge(t,((e,t)=>{Yt(n,t,e)}))},Jt=(e,t)=>{const n=e.dom.getAttribute(t);return null===n?void 0:n},Zt=(e,t)=>I.from(Jt(e,t)),en=(e,t)=>{const n=e.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},tn=(e,t)=>{e.dom.removeAttribute(t)},nn=e=>X(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),on=(e,t)=>{const n=Jt(e,t);return void 0===n||""===n?[]:n.split(" ")},rn=e=>void 0!==e.dom.classList,sn=e=>on(e,"class"),an=(e,t)=>((e,t,n)=>{const o=on(e,t).concat([n]);return Xt(e,t,o.join(" ")),!0})(e,"class",t),ln=(e,t)=>((e,t,n)=>{const o=G(on(e,t),(e=>e!==n));return o.length>0?Xt(e,t,o.join(" ")):tn(e,t),!1})(e,"class",t),dn=(e,t)=>{rn(e)?e.dom.classList.add(t):an(e,t)},cn=e=>{0===(rn(e)?e.dom.classList:sn(e)).length&&tn(e,"class")},un=(e,t)=>{rn(e)?e.dom.classList.remove(t):ln(e,t),cn(e)},mn=(e,t)=>rn(e)&&e.dom.classList.contains(t),fn=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},gn=(e,t)=>{const n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return fn(n.childNodes[0])},pn=(e,t)=>{const n=(t||document).createElement(e);return fn(n)},hn=(e,t)=>{const n=(t||document).createTextNode(e);return fn(n)},bn=fn,vn=(e,t,n)=>I.from(e.dom.elementFromPoint(t,n)).map(fn),yn=(e,t)=>{const n=[],o=e=>(n.push(e),t(e));let r=t(e);do{r=r.bind(o)}while(r.isSome());return n},Cn=(e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},wn=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,xn=(e,t)=>e.dom===t.dom,kn=(e,t)=>{const n=e.dom,o=t.dom;return n!==o&&n.contains(o)},En=e=>bn(e.dom.ownerDocument),Sn=e=>Wt(e)?e:En(e),_n=e=>bn(Sn(e).dom.defaultView),Nn=e=>I.from(e.dom.parentNode).map(bn),Rn=e=>I.from(e.dom.parentElement).map(bn),An=(e,t)=>{const n=w(t)?t:L;let o=e.dom;const r=[];for(;null!==o.parentNode&&void 0!==o.parentNode;){const e=o.parentNode,t=bn(e);if(r.push(t),!0===n(t))break;o=e}return r},On=e=>I.from(e.dom.previousSibling).map(bn),Tn=e=>I.from(e.dom.nextSibling).map(bn),Bn=e=>oe(yn(e,On)),Dn=e=>yn(e,Tn),Pn=e=>V(e.dom.childNodes,bn),Ln=(e,t)=>{const n=e.dom.childNodes;return I.from(n[t]).map(bn)},Mn=e=>Ln(e,0),In=e=>Ln(e,e.dom.childNodes.length-1),Fn=e=>e.dom.childNodes.length,Un=e=>Kt(e)&&C(e.dom.host),zn=w(Element.prototype.attachShadow)&&w(Node.prototype.getRootNode),jn=N(zn),Hn=zn?e=>bn(e.dom.getRootNode()):Sn,$n=e=>Un(e)?e:(e=>{const t=e.dom.head;if(null==t)throw new Error("Head is not available yet");return bn(t)})(Sn(e)),Vn=e=>bn(e.dom.host),qn=e=>{if(jn()&&C(e.target)){const t=bn(e.target);if(Vt(t)&&Wn(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return le(t)}}return I.from(e.target)},Wn=e=>C(e.dom.shadowRoot),Kn=e=>{const t=qt(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const n=t.ownerDocument;return(e=>{const t=Hn(e);return Un(t)?I.some(t):I.none()})(bn(t)).fold((()=>n.body.contains(t)),_(Kn,Vn))};var Gn=(e,t,n,o,r)=>e(n,o)?I.some(n):w(r)&&r(n)?I.none():t(n,o,r);const Yn=(e,t,n)=>{let o=e.dom;const r=w(n)?n:L;for(;o.parentNode;){o=o.parentNode;const e=bn(o);if(t(e))return I.some(e);if(r(e))break}return I.none()},Xn=(e,t,n)=>Gn(((e,t)=>t(e)),Yn,e,t,n),Qn=(e,t,n)=>Yn(e,(e=>Cn(e,t)),n),Jn=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return wn(n)?I.none():I.from(n.querySelector(e)).map(bn)})(t,e),Zn=(e,t,n)=>Gn(((e,t)=>Cn(e,t)),Qn,e,t,n),eo=(e,t=!1)=>{return Kn(e)?e.dom.isContentEditable:(n=e,Zn(n,"[contenteditable]")).fold(N(t),(e=>"true"===to(e)));var n},to=e=>e.dom.contentEditable,no=e=>void 0!==e.style&&w(e.style.getPropertyValue),oo=(e,t,n)=>{if(!m(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);no(e)&&e.style.setProperty(t,n)},ro=(e,t,n)=>{const o=e.dom;oo(o,t,n)},so=(e,t)=>{const n=e.dom;ge(t,((e,t)=>{oo(n,t,e)}))},ao=(e,t)=>{const n=e.dom,o=window.getComputedStyle(n).getPropertyValue(t);return""!==o||Kn(e)?o:io(n,t)},io=(e,t)=>no(e)?e.style.getPropertyValue(t):"",lo=(e,t)=>{const n=e.dom,o=io(n,t);return I.from(o).filter((e=>e.length>0))},co=e=>{const t={},n=e.dom;if(no(n))for(let e=0;e<n.style.length;e++){const o=n.style.item(e);t[o]=n.style[o]}return t},uo=(e,t)=>{((e,t)=>{no(e)&&e.style.removeProperty(t)})(e.dom,t),Pt(Zt(e,"style").map(qe),"")&&tn(e,"style")},mo=(e,t)=>{Nn(e).each((n=>{n.dom.insertBefore(t.dom,e.dom)}))},fo=(e,t)=>{Tn(e).fold((()=>{Nn(e).each((e=>{po(e,t)}))}),(e=>{mo(e,t)}))},go=(e,t)=>{Mn(e).fold((()=>{po(e,t)}),(n=>{e.dom.insertBefore(t.dom,n.dom)}))},po=(e,t)=>{e.dom.appendChild(t.dom)},ho=(e,t)=>{mo(e,t),po(t,e)},bo=(e,t)=>{q(t,(t=>{po(e,t)}))},vo=e=>{e.dom.textContent="",q(Pn(e),(e=>{yo(e)}))},yo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Co=e=>{const t=Pn(e);var n,o;t.length>0&&(n=e,q(o=t,((e,t)=>{const r=0===t?n:o[t-1];fo(r,e)}))),yo(e)},wo=e=>V(e,bn),xo=e=>e.dom.innerHTML,ko=(e,t)=>{const n=En(e).dom,o=bn(n.createDocumentFragment()),r=((e,t)=>{const n=(t||document).createElement("div");return n.innerHTML=e,Pn(bn(n))})(t,n);bo(o,r),vo(e),po(e,o)},Eo=(e,t,n,o)=>((e,t,n,o,r)=>{const s=((e,t)=>n=>{e(n)&&t((e=>{const t=bn(qn(e).getOr(e.target)),n=()=>e.stopPropagation(),o=()=>e.preventDefault(),r=S(o,n);return((e,t,n,o,r,s,a)=>({target:e,x:t,y:n,stop:o,prevent:r,kill:s,raw:a}))(t,e.clientX,e.clientY,n,o,r,e)})(n))})(n,o);return e.dom.addEventListener(t,s,false),{unbind:O(So,e,t,s,false)}})(e,t,n,o),So=(e,t,n,o)=>{e.dom.removeEventListener(t,n,o)},_o=(e,t)=>({left:e,top:t,translate:(n,o)=>_o(e+n,t+o)}),No=_o,Ro=(e,t)=>void 0!==e?e:void 0!==t?t:0,Ao=e=>{const t=e.dom,n=t.ownerDocument.body;return n===t?No(n.offsetLeft,n.offsetTop):Kn(e)?(e=>{const t=e.getBoundingClientRect();return No(t.left,t.top)})(t):No(0,0)},Oo=e=>{const t=void 0!==e?e.dom:document,n=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return No(n,o)},To=(e,t)=>{xt().browser.isSafari()&&w(e.dom.scrollIntoViewIfNeeded)?e.dom.scrollIntoViewIfNeeded(!1):e.dom.scrollIntoView(t)},Bo=(e,t,n,o)=>({x:e,y:t,width:n,height:o,right:e+n,bottom:t+o}),Do=e=>{const t=void 0===e?window:e,n=t.document,o=Oo(bn(n));return(e=>{const t=void 0===e?window:e;return xt().browser.isFirefox()?I.none():I.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,n=e.clientWidth,r=e.clientHeight;return Bo(o.left,o.top,n,r)}),(e=>Bo(Math.max(e.pageLeft,o.left),Math.max(e.pageTop,o.top),e.width,e.height)))},Po=(e,t)=>{let n=[];return q(Pn(e),(e=>{t(e)&&(n=n.concat([e])),n=n.concat(Po(e,t))})),n},Lo=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return wn(n)?[]:V(n.querySelectorAll(e),bn)})(t,e),Mo=(e,t,n)=>Qn(e,t,n).isSome();class Io{constructor(e,t){this.node=e,this.rootNode=t,this.current=this.current.bind(this),this.next=this.next.bind(this),this.prev=this.prev.bind(this),this.prev2=this.prev2.bind(this)}current(){return this.node}next(e){return this.node=this.findSibling(this.node,"firstChild","nextSibling",e),this.node}prev(e){return this.node=this.findSibling(this.node,"lastChild","previousSibling",e),this.node}prev2(e){return this.node=this.findPreviousNode(this.node,e),this.node}findSibling(e,t,n,o){if(e){if(!o&&e[t])return e[t];if(e!==this.rootNode){let t=e[n];if(t)return t;for(let o=e.parentNode;o&&o!==this.rootNode;o=o.parentNode)if(t=o[n],t)return t}}}findPreviousNode(e,t){if(e){const n=e.previousSibling;if(this.rootNode&&n===this.rootNode)return;if(n){if(!t)for(let e=n.lastChild;e;e=e.lastChild)if(!e.lastChild)return e;return n}const o=e.parentNode;if(o&&o!==this.rootNode)return o}}}const Fo=e=>t=>!!t&&t.nodeType===e,Uo=e=>!!e&&!Object.getPrototypeOf(e),zo=Fo(1),jo=e=>{const t=e.toLowerCase();return e=>C(e)&&e.nodeName.toLowerCase()===t},Ho=e=>{const t=e.map((e=>e.toLowerCase()));return e=>{if(e&&e.nodeName){const n=e.nodeName.toLowerCase();return H(t,n)}return!1}},$o=(e,t)=>{const n=t.toLowerCase().split(" ");return t=>{if(zo(t)){const o=t.ownerDocument.defaultView;if(o)for(let r=0;r<n.length;r++){const s=o.getComputedStyle(t,null);if((s?s.getPropertyValue(e):null)===n[r])return!0}}return!1}},Vo=e=>t=>zo(t)&&t.hasAttribute(e),qo=e=>zo(e)&&e.hasAttribute("data-mce-bogus"),Wo=e=>zo(e)&&"TABLE"===e.tagName,Ko=e=>t=>{if(zo(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},Go=Ho(["textarea","input"]),Yo=Fo(3),Xo=Fo(4),Qo=Fo(7),Jo=Fo(8),Zo=Fo(9),er=Fo(11),tr=jo("br"),nr=jo("img"),or=Ko("true"),rr=Ko("false"),sr=Ho(["td","th"]),ar=Ho(["td","th","caption"]),ir=Ho(["video","audio","object","embed"]),lr=jo("li"),dr="\ufeff",cr="\xa0",ur=e=>e===dr,mr=((e,t)=>{const n=t=>e(t)?I.from(t.dom.nodeValue):I.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return n(t).getOr("")},getOption:n,set:(t,n)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=n}}})(qt),fr=e=>mr.get(e),gr=e=>mr.getOption(e),pr=["pre"].concat(["h1","h2","h3","h4","h5","h6"]),hr=e=>{let t;return n=>(t=t||se(e,M),ke(t,jt(n)))},br=hr(["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"]),vr=e=>Vt(e)&&!br(e),yr=e=>Vt(e)&&"br"===jt(e),Cr=hr(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),wr=hr(["ul","ol","dl"]),xr=hr(["li","dd","dt"]),kr=hr(["thead","tbody","tfoot"]),Er=hr(["td","th"]),Sr=hr(["pre","script","textarea","style"]),_r=hr(pr),Nr=e=>_r(e)||vr(e),Rr=()=>{const e=pn("br");return Xt(e,"data-mce-bogus","1"),e},Ar=e=>{vo(e),po(e,Rr())},Or=e=>{In(e).each((t=>{On(t).each((n=>{br(e)&&yr(t)&&br(n)&&yo(t)}))}))},Tr=dr,Br=ur,Dr=e=>e.replace(/\uFEFF/g,""),Pr=zo,Lr=Yo,Mr=e=>(Lr(e)&&(e=e.parentNode),Pr(e)&&e.hasAttribute("data-mce-caret")),Ir=e=>Lr(e)&&Br(e.data),Fr=e=>Mr(e)||Ir(e),Ur=e=>e.firstChild!==e.lastChild||!tr(e.firstChild),zr=e=>{const t=e.container();return!!Yo(t)&&(t.data.charAt(e.offset())===Tr||e.isAtStart()&&Ir(t.previousSibling))},jr=e=>{const t=e.container();return!!Yo(t)&&(t.data.charAt(e.offset()-1)===Tr||e.isAtEnd()&&Ir(t.nextSibling))},Hr=e=>Lr(e)&&e.data[0]===Tr,$r=e=>Lr(e)&&e.data[e.data.length-1]===Tr,Vr=e=>e&&e.hasAttribute("data-mce-caret")?((e=>{var t;const n=e.getElementsByTagName("br"),o=n[n.length-1];qo(o)&&(null===(t=o.parentNode)||void 0===t||t.removeChild(o))})(e),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("data-mce-style"),e.removeAttribute("_moz_abspos"),e):null,qr=e=>Mr(e.startContainer),Wr=or,Kr=rr,Gr=tr,Yr=Yo,Xr=Ho(["script","style","textarea"]),Qr=Ho(["img","input","textarea","hr","iframe","video","audio","object","embed"]),Jr=Ho(["table"]),Zr=Fr,es=e=>!Zr(e)&&(Yr(e)?!Xr(e.parentNode):Qr(e)||Gr(e)||Jr(e)||ts(e)),ts=e=>!(e=>zo(e)&&"true"===e.getAttribute("unselectable"))(e)&&Kr(e),ns=(e,t)=>es(e)&&((e,t)=>{for(let n=e.parentNode;n&&n!==t;n=n.parentNode){if(ts(n))return!1;if(Wr(n))return!0}return!0})(e,t),os=/^[ \t\r\n]*$/,rs=e=>os.test(e),ss=e=>"\n"===e||"\r"===e,as=(e,t=4,n=!0,o=!0)=>{const r=((e,t)=>t<=0?"":new Array(t+1).join(" "))(0,t),s=e.replace(/\t/g,r),a=X(s,((e,t)=>(e=>-1!==" \f\t\v".indexOf(e))(t)||t===cr?e.pcIsSpace||""===e.str&&n||e.str.length===s.length-1&&o||((e,t)=>t<e.length&&t>=0&&ss(e[t]))(s,e.str.length+1)?{pcIsSpace:!1,str:e.str+cr}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:ss(t),str:e.str+t}),{pcIsSpace:!1,str:""});return a.str},is=(e,t)=>es(e)&&!((e,t)=>Yo(e)&&rs(e.data)&&!((e,t)=>{const n=bn(t),o=bn(e);return Mo(o,"pre,code",O(xn,n))})(e,t))(e,t)||(e=>zo(e)&&"A"===e.nodeName&&!e.hasAttribute("href")&&(e.hasAttribute("name")||e.hasAttribute("id")))(e)||ls(e),ls=Vo("data-mce-bookmark"),ds=Vo("data-mce-bogus"),cs=("data-mce-bogus","all",e=>zo(e)&&"all"===e.getAttribute("data-mce-bogus"));const us=(e,t=!0)=>((e,t)=>{let n=0;if(is(e,e))return!1;{let o=e.firstChild;if(!o)return!0;const r=new Io(o,e);do{if(t){if(cs(o)){o=r.next(!0);continue}if(ds(o)){o=r.next();continue}}if(tr(o))n++,o=r.next();else{if(is(o,e))return!1;o=r.next()}}while(o);return n<=1}})(e.dom,t),ms="data-mce-block",fs=e=>(e=>G(me(e),(e=>!/[A-Z]/.test(e))))(e).join(","),gs=(e,t)=>C(t.querySelector(e))?(t.setAttribute(ms,"true"),"inline-boundary"===t.getAttribute("data-mce-selected")&&t.removeAttribute("data-mce-selected"),!0):(t.removeAttribute(ms),!1),ps=(e,t)=>{const n=fs(e.getTransparentElements()),o=fs(e.getBlockElements());return G(t.querySelectorAll(n),(e=>gs(o,e)))},hs=(e,t)=>{var n;const o=t?"lastChild":"firstChild";for(let t=e[o];t;t=t[o])if(us(bn(t)))return void(null===(n=t.parentNode)||void 0===n||n.removeChild(t))},bs=(e,t,n)=>{const o=e.getBlockElements(),r=bn(t),s=e=>jt(e)in o,a=e=>xn(e,r);q(wo(n),(t=>{Yn(t,s,a).each((n=>{const o=((t,o)=>G(Pn(t),(t=>s(t)&&!e.isValidChild(jt(n),jt(t)))))(t);if(o.length>0){const t=Rn(n);q(o,(e=>{Yn(e,s,a).each((t=>{((e,t)=>{const n=document.createRange(),o=e.parentNode;if(o){n.setStartBefore(e),n.setEndBefore(t);const r=n.extractContents();hs(r,!0),n.setStartAfter(t),n.setEndAfter(e);const s=n.extractContents();hs(s,!1),us(bn(r))||o.insertBefore(r,e),us(bn(t))||o.insertBefore(t,e),us(bn(s))||o.insertBefore(s,e),o.removeChild(e)}})(t.dom,e.dom)}))})),t.each((t=>ps(e,t.dom)))}}))}))},vs=(e,t)=>{const n=ps(e,t);bs(e,t,n),((e,t,n)=>{q([...n,...ks(e,t)?[t]:[]],(t=>q(Lo(bn(t),t.nodeName.toLowerCase()),(t=>{Es(e,t.dom)&&Co(t)}))))})(e,t,n)},ys=(e,t)=>{if(xs(e,t)){const n=fs(e.getBlockElements());gs(n,t)}},Cs=e=>e.hasAttribute(ms),ws=(e,t)=>ke(e.getTransparentElements(),t),xs=(e,t)=>zo(t)&&ws(e,t.nodeName),ks=(e,t)=>xs(e,t)&&Cs(t),Es=(e,t)=>xs(e,t)&&!Cs(t),Ss=(e,t)=>1===t.type&&ws(e,t.name)&&v(t.attr(ms)),_s=xt().browser,Ns=e=>J(e,Vt),Rs=(e,t)=>e.children&&H(e.children,t),As=(e,t={})=>{let n=0;const o={},r=bn(e),s=Sn(r),a=e=>new Promise(((a,i)=>{let l;const d=Dt._addCacheSuffix(e),c=(e=>xe(o,e).getOrThunk((()=>({id:"mce-u"+n++,passed:[],failed:[],count:0}))))(d);o[d]=c,c.count++;const u=(e,t)=>{q(e,P),c.status=t,c.passed=[],c.failed=[],l&&(l.onload=null,l.onerror=null,l=null)},m=()=>u(c.passed,2),f=()=>u(c.failed,3);if(a&&c.passed.push(a),i&&c.failed.push(i),1===c.status)return;if(2===c.status)return void m();if(3===c.status)return void f();c.status=1;const g=pn("link",s.dom);var p;Qt(g,{rel:"stylesheet",type:"text/css",id:c.id}),t.contentCssCors&&Xt(g,"crossOrigin","anonymous"),t.referrerPolicy&&Xt(g,"referrerpolicy",t.referrerPolicy),l=g.dom,l.onload=m,l.onerror=f,p=g,po($n(r),p),Xt(g,"href",d)})),i=e=>{const t=Dt._addCacheSuffix(e);xe(o,t).each((e=>{0==--e.count&&(delete o[t],(e=>{const t=$n(r);Jn(t,"#"+e).each(yo)})(e.id))}))};return{load:a,loadAll:e=>Promise.allSettled(V(e,(e=>a(e).then(N(e))))).then((e=>{const t=K(e,(e=>"fulfilled"===e.status));return t.fail.length>0?Promise.reject(V(t.fail,(e=>e.reason))):V(t.pass,(e=>e.value))})),unload:i,unloadAll:e=>{q(e,(e=>{i(e)}))},_setReferrerPolicy:e=>{t.referrerPolicy=e},_setContentCssCors:e=>{t.contentCssCors=e}}},Os=(()=>{const e=new WeakMap;return{forElement:(t,n)=>{const o=Hn(t).dom;return I.from(e.get(o)).getOrThunk((()=>{const t=As(o,n);return e.set(o,t),t}))}}})(),Ts=(e,t)=>C(e)&&(is(e,t)||vr(bn(e))),Bs=e=>(e=>"span"===e.nodeName.toLowerCase())(e)&&"bookmark"===e.getAttribute("data-mce-type"),Ds=(e,t,n)=>{var o;const r=n||t;if(zo(t)&&Bs(t))return t;const s=t.childNodes;for(let t=s.length-1;t>=0;t--)Ds(e,s[t],r);if(zo(t)){const e=t.childNodes;1===e.length&&Bs(e[0])&&(null===(o=t.parentNode)||void 0===o||o.insertBefore(e[0],t))}return(e=>er(e)||Zo(e))(t)||is(t,r)||(e=>!!zo(e)&&e.childNodes.length>0)(t)||((e,t)=>Yo(e)&&e.data.length>0&&((e,t)=>{const n=new Io(e,t).prev(!1),o=new Io(e,t).next(!1),r=v(n)||Ts(n,t),s=v(o)||Ts(o,t);return r&&s})(e,t))(t,r)||e.remove(t),t},Ps=Dt.makeMap,Ls=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Ms=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Is=/[<>&\"\']/g,Fs=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,Us={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"},zs={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},js={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"},Hs=(e,t)=>{const n={};if(e){const o=e.split(",");t=t||10;for(let e=0;e<o.length;e+=2){const r=String.fromCharCode(parseInt(o[e],t));if(!zs[r]){const t="&"+o[e+1]+";";n[r]=t,n[t]=r}}return n}},$s=Hs("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32),Vs=(e,t)=>e.replace(t?Ls:Ms,(e=>zs[e]||e)),qs=(e,t)=>e.replace(t?Ls:Ms,(e=>e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":zs[e]||"&#"+e.charCodeAt(0)+";")),Ws=(e,t,n)=>{const o=n||$s;return e.replace(t?Ls:Ms,(e=>zs[e]||o[e]||e))},Ks={encodeRaw:Vs,encodeAllRaw:e=>(""+e).replace(Is,(e=>zs[e]||e)),encodeNumeric:qs,encodeNamed:Ws,getEncodeFunc:(e,t)=>{const n=Hs(t)||$s,o=Ps(e.replace(/\+/g,","));return o.named&&o.numeric?(e,t)=>e.replace(t?Ls:Ms,(e=>void 0!==zs[e]?zs[e]:void 0!==n[e]?n[e]:e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";")):o.named?t?(e,t)=>Ws(e,t,n):Ws:o.numeric?qs:Vs},decode:e=>e.replace(Fs,((e,t)=>t?(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))>65535?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):Us[t]||String.fromCharCode(t):js[e]||$s[e]||(e=>{const t=pn("div").dom;return t.innerHTML=e,t.textContent||t.innerText||e})(e)))},Gs={},Ys={},Xs={},Qs=Dt.makeMap,Js=Dt.each,Zs=Dt.extend,ea=Dt.explode,ta=Dt.inArray,na=(e,t)=>(e=Dt.trim(e))?e.split(t||" "):[],oa=(e,t={})=>{const n=Qs(e," ",Qs(e.toUpperCase()," "));return Zs(n,t)},ra=e=>oa("td th li dt dd figcaption caption details summary",e.getTextBlockElements()),sa=(e,t)=>{if(e){const n={};return m(e)&&(e={"*":e}),Js(e,((e,o)=>{n[o]=n[o.toUpperCase()]="map"===t?Qs(e,/[, ]/):ea(e,/[, ]/)})),n}},aa=(e={})=>{var t;const n={},o={};let r=[];const s={},a={},i=(t,n,o)=>{const r=e[t];if(r)return Qs(r,/[, ]/,Qs(r.toUpperCase(),/[, ]/));{let e=Ys[t];return e||(e=oa(n,o),Ys[t]=e),e}},l=null!==(t=e.schema)&&void 0!==t?t:"html5",d=(e=>{const t={};let n,o,r,s;const a=(e,o="",r="")=>{const s=na(r),a=na(e);let i=a.length;for(;i--;){const e=na([n,o].join(" "));t[a[i]]={attributes:se(e,(()=>({}))),attributesOrder:e,children:se(s,N(Xs))}}},i=(e,n)=>{const o=na(e),r=na(n);let s=o.length;for(;s--;){const e=t[o[s]];for(let t=0,n=r.length;t<n;t++)e.attributes[r[t]]={},e.attributesOrder.push(r[t])}};if(Gs[e])return Gs[e];if(n="id accesskey class dir lang style tabindex title role",o="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",r="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(n+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",o+=" article aside details dialog figure main header footer hgroup section nav a ins del canvas map",r+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==e){n+=" xml:lang";const e="acronym applet basefont big font strike tt";r=[r,e].join(" "),Js(na(e),(e=>{a(e,"",r)}));const t="center dir isindex noframes";o=[o,t].join(" "),s=[o,r].join(" "),Js(na(t),(e=>{a(e,"",s)}))}return s=s||[o,r].join(" "),a("html","manifest","head body"),a("head","","base command link meta noscript script style title"),a("title hr noscript br"),a("base","href target"),a("link","href rel media hreflang type sizes hreflang"),a("meta","name http-equiv content charset"),a("style","media type scoped"),a("script","src async defer type charset"),a("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",s),a("address dt dd div caption","",s),a("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",r),a("blockquote","cite",s),a("ol","reversed start type","li"),a("ul","","li"),a("li","value",s),a("dl","","dt dd"),a("a","href target rel media hreflang type",s),a("q","cite",r),a("ins del","cite datetime",s),a("img","src sizes srcset alt usemap ismap width height"),a("iframe","src name width height",s),a("embed","src type width height"),a("object","data type typemustmatch name usemap form width height",[s,"param"].join(" ")),a("param","name value"),a("map","name",[s,"area"].join(" ")),a("area","alt coords shape href target rel media hreflang type"),a("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),a("colgroup","span","col"),a("col","span"),a("tbody thead tfoot","","tr"),a("tr","","td th"),a("td","colspan rowspan headers",s),a("th","colspan rowspan headers scope abbr",s),a("form","accept-charset action autocomplete enctype method name novalidate target",s),a("fieldset","disabled form name",[s,"legend"].join(" ")),a("label","form for",r),a("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),a("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?s:r),a("select","disabled form multiple name required size","option optgroup"),a("optgroup","disabled label","option"),a("option","disabled label selected value"),a("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),a("menu","type label",[s,"li"].join(" ")),a("noscript","",s),"html4"!==e&&(a("wbr"),a("ruby","",[r,"rt rp"].join(" ")),a("figcaption","",s),a("mark rt rp summary bdi","",r),a("canvas","width height",s),a("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[s,"track source"].join(" ")),a("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[s,"track source"].join(" ")),a("picture","","img source"),a("source","src srcset type media sizes"),a("track","kind src srclang label default"),a("datalist","",[r,"option"].join(" ")),a("article section nav aside main header footer","",s),a("hgroup","","h1 h2 h3 h4 h5 h6"),a("figure","",[s,"figcaption"].join(" ")),a("time","datetime",r),a("dialog","open",s),a("command","type label icon disabled checked radiogroup command"),a("output","for form name",r),a("progress","value max",r),a("meter","value min max low high optimum",r),a("details","open",[s,"summary"].join(" ")),a("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==e&&(i("script","language xml:space"),i("style","xml:space"),i("object","declare classid code codebase codetype archive standby align border hspace vspace"),i("embed","align name hspace vspace"),i("param","valuetype type"),i("a","charset name rev shape coords"),i("br","clear"),i("applet","codebase archive code object alt name width height align hspace vspace"),i("img","name longdesc align border hspace vspace"),i("iframe","longdesc frameborder marginwidth marginheight scrolling align"),i("font basefont","size color face"),i("input","usemap align"),i("select"),i("textarea"),i("h1 h2 h3 h4 h5 h6 div p legend caption","align"),i("ul","type compact"),i("li","type"),i("ol dl menu dir","compact"),i("pre","width xml:space"),i("hr","align noshade size width"),i("isindex","prompt"),i("table","summary width frame rules cellspacing cellpadding align bgcolor"),i("col","width align char charoff valign"),i("colgroup","width align char charoff valign"),i("thead","align char charoff valign"),i("tr","align char charoff valign bgcolor"),i("th","axis align char charoff valign nowrap bgcolor width height"),i("form","accept"),i("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),i("tfoot","align char charoff valign"),i("tbody","align char charoff valign"),i("area","nohref"),i("body","background bgcolor text link vlink alink")),"html4"!==e&&(i("input button select textarea","autofocus"),i("input textarea","placeholder"),i("a","download"),i("link script img","crossorigin"),i("img","loading"),i("iframe","sandbox seamless allow allowfullscreen loading")),"html4"!==e&&q([t.video,t.audio],(e=>{delete e.children.audio,delete e.children.video})),Js(na("a form meter progress dfn"),(e=>{t[e]&&delete t[e].children[e]})),delete t.caption.children.table,delete t.script,Gs[e]=t,t})(l);!1===e.verify_html&&(e.valid_elements="*[*]");const c=sa(e.valid_styles),u=sa(e.invalid_styles,"map"),m=sa(e.valid_classes,"map"),f=i("whitespace_elements","pre script noscript style textarea video audio iframe object code"),g=i("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),p=i("void_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),h=i("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls allowfullscreen"),b="td th iframe video audio object script code",v=i("non_empty_elements",b+" pre",p),y=i("move_caret_before_on_enter_elements",b+" table",p),C=i("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),w=i("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary",C),x=i("text_inline_elements","span strong b em i font s strike u var cite dfn code mark q sup sub samp"),k=i("transparent_elements","a ins del canvas map");Js("script noscript iframe noframes noembed title style textarea xmp plaintext".split(" "),(e=>{a[e]=new RegExp("</"+e+"[^>]*>","gi")}));const E=e=>new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$"),S=e=>{const t=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)])?$/,o=/^([!\-])?(\w+[\\:]:\w+|[^=~<]+)?(?:([=~<])(.*))?$/,s=/[*?+]/;if(e){const a=na(e,",");let i,l;n["@"]&&(i=n["@"].attributes,l=n["@"].attributesOrder);for(let e=0,d=a.length;e<d;e++){let d=t.exec(a[e]);if(d){const e=d[1],t=d[2],a=d[3],c=d[5],u={},m=[],f={attributes:u,attributesOrder:m};if("#"===e&&(f.paddEmpty=!0),"-"===e&&(f.removeEmpty=!0),"!"===d[4]&&(f.removeEmptyAttrs=!0),i&&(ge(i,((e,t)=>{u[t]=e})),l&&m.push(...l)),c){const e=na(c,"|");for(let t=0,n=e.length;t<n;t++)if(d=o.exec(e[t]),d){const e={},t=d[1],n=d[2].replace(/[\\:]:/g,":"),o=d[3],r=d[4];if("!"===t&&(f.attributesRequired=f.attributesRequired||[],f.attributesRequired.push(n),e.required=!0),"-"===t){delete u[n],m.splice(ta(m,n),1);continue}if(o&&("="===o&&(f.attributesDefault=f.attributesDefault||[],f.attributesDefault.push({name:n,value:r}),e.defaultValue=r),"~"===o&&(f.attributesForced=f.attributesForced||[],f.attributesForced.push({name:n,value:r}),e.forcedValue=r),"<"===o&&(e.validValues=Qs(r,"?"))),s.test(n)){const t=e;f.attributePatterns=f.attributePatterns||[],t.pattern=E(n),f.attributePatterns.push(t)}else u[n]||m.push(n),u[n]=e}}if(i||"@"!==t||(i=u,l=m),a&&(f.outputName=t,n[a]=f),s.test(t)){const e=f;e.pattern=E(t),r.push(e)}else n[t]=f}}}},_=e=>{r=[],q(me(n),(e=>{delete n[e]})),S(e),Js(d,((e,t)=>{o[t]=e.children}))},R=e=>{const t=/^(~)?(.+)$/;e&&(delete Ys.text_block_elements,delete Ys.block_elements,Js(na(e,","),(e=>{const r=t.exec(e);if(r){const e="~"===r[1],t=e?"span":"div",a=r[2];if(o[a]=o[t],s[a]=t,v[a.toUpperCase()]={},v[a]={},e||(w[a.toUpperCase()]={},w[a]={}),!n[a]){let e=n[t];e=Zs({},e),delete e.removeEmptyAttrs,delete e.removeEmpty,n[a]=e}Js(o,((e,n)=>{e[t]&&(o[n]=e=Zs({},o[n]),e[a]=e[t])}))}})))},A=e=>{const t=/^([+\-]?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)\[([^\]]+)]$/;delete Gs[l],e&&Js(na(e,","),(e=>{const n=t.exec(e);if(n){const e=n[1];let t;t=e?o[n[2]]:o[n[2]]={"#comment":{}},t=o[n[2]],Js(na(n[3],"|"),(n=>{"-"===e?delete t[n]:t[n]={}}))}}))},O=e=>{const t=n[e];if(t)return t;let o=r.length;for(;o--;){const t=r[o];if(t.pattern.test(e))return t}};e.valid_elements?_(e.valid_elements):(Js(d,((e,t)=>{n[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},o[t]=e.children})),Js(na("strong/b em/i"),(e=>{const t=na(e,"/");n[t[1]].outputName=t[0]})),Js(x,((t,o)=>{n[o]&&(e.padd_empty_block_inline_children&&(n[o].paddInEmptyBlock=!0),n[o].removeEmpty=!0)})),Js(na("ol ul blockquote a table tbody"),(e=>{n[e]&&(n[e].removeEmpty=!0)})),Js(na("p h1 h2 h3 h4 h5 h6 th td pre div address caption li"),(e=>{n[e].paddEmpty=!0})),Js(na("span"),(e=>{n[e].removeEmptyAttrs=!0}))),R(e.custom_elements),A(e.valid_children),S(e.extended_valid_elements),A("+ol[ul|ol],+ul[ul|ol]"),Js({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},((e,t)=>{n[t]&&(n[t].parentsRequired=na(e))})),e.invalid_elements&&Js(ea(e.invalid_elements),(e=>{n[e]&&delete n[e]})),O("span")||S("span[!data-mce-type|*]");const T=N(c),B=N(u),D=N(m),P=N(h),L=N(w),M=N(C),I=N(x),F=N(Object.seal(p)),U=N(g),z=N(v),j=N(y),H=N(f),$=N(k),V=N(Object.seal(a)),W=N(s);return{type:l,children:o,elements:n,getValidStyles:T,getValidClasses:D,getBlockElements:L,getInvalidStyles:B,getVoidElements:F,getTextBlockElements:M,getTextInlineElements:I,getBoolAttrs:P,getElementRule:O,getSelfClosingElements:U,getNonEmptyElements:z,getMoveCaretBeforeOnEnterElements:j,getWhitespaceElements:H,getTransparentElements:$,getSpecialElements:V,isValidChild:(e,t)=>{const n=o[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:(e,t)=>{const n=O(e);if(n){if(!t)return!0;{if(n.attributes[t])return!0;const e=n.attributePatterns;if(e){let n=e.length;for(;n--;)if(e[n].pattern.test(t))return!0}}}return!1},getCustomElements:W,addValidElements:S,setValidElements:_,addCustomElements:R,addValidChildren:A}},ia=(e={},t)=>{const n=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,o=/\s*([^:]+):\s*([^;]+);?/g,r=/\s+$/,s={};let a,i;t&&(a=t.getValidStyles(),i=t.getInvalidStyles());const l="\\\" \\' \\; \\: ; : \ufeff".split(" ");for(let e=0;e<l.length;e++)s[l[e]]="\ufeff"+e,s["\ufeff"+e]=l[e];const d={parse:t=>{const a={};let i=!1;const l=e.url_converter,c=e.url_converter_scope||d,u=(e,t,n)=>{const o=a[e+"-top"+t];if(!o)return;const r=a[e+"-right"+t];if(!r)return;const s=a[e+"-bottom"+t];if(!s)return;const i=a[e+"-left"+t];if(!i)return;const l=[o,r,s,i];let d=l.length-1;for(;d--&&l[d]===l[d+1];);d>-1&&n||(a[e+t]=-1===d?l[0]:l.join(" "),delete a[e+"-top"+t],delete a[e+"-right"+t],delete a[e+"-bottom"+t],delete a[e+"-left"+t])},m=e=>{const t=a[e];if(!t)return;const n=t.split(" ");let o=n.length;for(;o--;)if(n[o]!==n[0])return!1;return a[e]=n[0],!0},f=e=>(i=!0,s[e]),g=(e,t)=>(i&&(e=e.replace(/\uFEFF[0-9]/g,(e=>s[e]))),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e),p=e=>String.fromCharCode(parseInt(e.slice(1),16)),h=e=>e.replace(/\\[0-9a-f]+/gi,p),b=(t,n,o,r,s,a)=>{if(s=s||a)return"'"+(s=g(s)).replace(/\'/g,"\\'")+"'";if(n=g(n||o||r||""),!e.allow_script_urls){const t=n.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(t))return"";if(!e.allow_svg_data_urls&&/^data:image\/svg/i.test(t))return""}return l&&(n=l.call(c,n,"style")),"url('"+n.replace(/\'/g,"\\'")+"')"};if(t){let s;for(t=(t=t.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,f).replace(/\"[^\"]+\"|\'[^\']+\'/g,(e=>e.replace(/[;:]/g,f)));s=o.exec(t);){o.lastIndex=s.index+s[0].length;let t=s[1].replace(r,"").toLowerCase(),l=s[2].replace(r,"");if(t&&l){if(t=h(t),l=h(l),-1!==t.indexOf("\ufeff")||-1!==t.indexOf('"'))continue;if(!e.allow_script_urls&&("behavior"===t||/expression\s*\(|\/\*|\*\//.test(l)))continue;"font-weight"===t&&"700"===l?l="bold":"color"!==t&&"background-color"!==t||(l=l.toLowerCase()),l=l.replace(n,b),a[t]=i?g(l,!0):l}}u("border","",!0),u("border","-width"),u("border","-color"),u("border","-style"),u("padding",""),u("margin",""),"border",y="border-style",C="border-color",m(v="border-width")&&m(y)&&m(C)&&(a.border=a[v]+" "+a[y]+" "+a[C],delete a[v],delete a[y],delete a[C]),"medium none"===a.border&&delete a.border,"none"===a["border-image"]&&delete a["border-image"]}var v,y,C;return a},serialize:(e,t)=>{let n="";const o=(t,o)=>{const r=o[t];if(r)for(let t=0,o=r.length;t<o;t++){const o=r[t],s=e[o];s&&(n+=(n.length>0?" ":"")+o+": "+s+";")}};return t&&a?(o("*",a),o(t,a)):ge(e,((e,o)=>{e&&((e,t)=>{if(!i||!t)return!0;let n=i["*"];return!(n&&n[e]||(n=i[t],n&&n[e]))})(o,t)&&(n+=(n.length>0?" ":"")+o+": "+e+";")})),n}};return d},la={keyLocation:!0,layerX:!0,layerY:!0,returnValue:!0,webkitMovementX:!0,webkitMovementY:!0,keyIdentifier:!0,mozPressure:!0},da=(e,t)=>{const n=null!=t?t:{};for(const t in e)ke(la,t)||(n[t]=e[t]);return C(e.composedPath)&&(n.composedPath=()=>e.composedPath()),n},ca=(e,t,n,o)=>{var r;const s=da(t,o);return s.type=e,y(s.target)&&(s.target=null!==(r=s.srcElement)&&void 0!==r?r:n),(e=>y(e.preventDefault)||(e=>e instanceof Event||w(e.initEvent))(e))(t)&&(s.preventDefault=()=>{s.defaultPrevented=!0,s.isDefaultPrevented=M,w(t.preventDefault)&&t.preventDefault()},s.stopPropagation=()=>{s.cancelBubble=!0,s.isPropagationStopped=M,w(t.stopPropagation)&&t.stopPropagation()},s.stopImmediatePropagation=()=>{s.isImmediatePropagationStopped=M,s.stopPropagation()},(e=>e.isDefaultPrevented===M||e.isDefaultPrevented===L)(s)||(s.isDefaultPrevented=!0===s.defaultPrevented?M:L,s.isPropagationStopped=!0===s.cancelBubble?M:L,s.isImmediatePropagationStopped=L)),s},ua=/^(?:mouse|contextmenu)|click/,ma=(e,t,n,o)=>{e.addEventListener(t,n,o||!1)},fa=(e,t,n,o)=>{e.removeEventListener(t,n,o||!1)},ga=(e,t)=>{const n=ca(e.type,e,document,t);if((e=>C(e)&&ua.test(e.type))(e)&&v(e.pageX)&&!v(e.clientX)){const t=n.target.ownerDocument||document,o=t.documentElement,r=t.body,s=n;s.pageX=e.clientX+(o&&o.scrollLeft||r&&r.scrollLeft||0)-(o&&o.clientLeft||r&&r.clientLeft||0),s.pageY=e.clientY+(o&&o.scrollTop||r&&r.scrollTop||0)-(o&&o.clientTop||r&&r.clientTop||0)}return n},pa=(e,t,n)=>{const o=e.document,r={type:"ready"};if(n.domLoaded)return void t(r);const s=()=>{fa(e,"DOMContentLoaded",s),fa(e,"load",s),n.domLoaded||(n.domLoaded=!0,t(r)),e=null};"complete"===o.readyState||"interactive"===o.readyState&&o.body?s():ma(e,"DOMContentLoaded",s),n.domLoaded||ma(e,"load",s)};class ha{constructor(){this.domLoaded=!1,this.events={},this.count=1,this.expando="mce-data-"+(+new Date).toString(32),this.hasFocusIn="onfocusin"in document.documentElement,this.count=1}bind(e,t,n,o){const r=this;let s;const a=window,i=e=>{r.executeHandlers(ga(e||a.event),l)};if(!e||Yo(e)||Jo(e))return n;let l;e[r.expando]?l=e[r.expando]:(l=r.count++,e[r.expando]=l,r.events[l]={}),o=o||e;const d=t.split(" ");let c=d.length;for(;c--;){let t=d[c],u=i,m=!1,f=!1;"DOMContentLoaded"===t&&(t="ready"),r.domLoaded&&"ready"===t&&"complete"===e.readyState?n.call(o,ga({type:t})):(r.hasFocusIn||"focusin"!==t&&"focusout"!==t||(m=!0,f="focusin"===t?"focus":"blur",u=e=>{const t=ga(e||a.event);t.type="focus"===t.type?"focusin":"focusout",r.executeHandlers(t,l)}),s=r.events[l][t],s?"ready"===t&&r.domLoaded?n(ga({type:t})):s.push({func:n,scope:o}):(r.events[l][t]=s=[{func:n,scope:o}],s.fakeName=f,s.capture=m,s.nativeHandler=u,"ready"===t?pa(e,u,r):ma(e,f||t,u,m)))}return e=s=null,n}unbind(e,t,n){if(!e||Yo(e)||Jo(e))return this;const o=e[this.expando];if(o){let r=this.events[o];if(t){const o=t.split(" ");let s=o.length;for(;s--;){const t=o[s],a=r[t];if(a){if(n){let e=a.length;for(;e--;)if(a[e].func===n){const n=a.nativeHandler,o=a.fakeName,s=a.capture,i=a.slice(0,e).concat(a.slice(e+1));i.nativeHandler=n,i.fakeName=o,i.capture=s,r[t]=i}}n&&0!==a.length||(delete r[t],fa(e,a.fakeName||t,a.nativeHandler,a.capture))}}}else ge(r,((t,n)=>{fa(e,t.fakeName||n,t.nativeHandler,t.capture)})),r={};for(const e in r)if(ke(r,e))return this;delete this.events[o];try{delete e[this.expando]}catch(t){e[this.expando]=null}}return this}fire(e,t,n){return this.dispatch(e,t,n)}dispatch(e,t,n){if(!e||Yo(e)||Jo(e))return this;const o=ga({type:t,target:e},n);do{const t=e[this.expando];t&&this.executeHandlers(o,t),e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow}while(e&&!o.isPropagationStopped());return this}clean(e){if(!e||Yo(e)||Jo(e))return this;if(e[this.expando]&&this.unbind(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName){this.unbind(e);const t=e.getElementsByTagName("*");let n=t.length;for(;n--;)(e=t[n])[this.expando]&&this.unbind(e)}return this}destroy(){this.events={}}cancel(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}executeHandlers(e,t){const n=this.events[t],o=n&&n[e.type];if(o)for(let t=0,n=o.length;t<n;t++){const n=o[t];if(n&&!1===n.func.call(n.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return}}}ha.Event=new ha;const ba=Dt.each,va=Dt.grep,ya="data-mce-style",Ca=Dt.makeMap("fill-opacity font-weight line-height opacity orphans widows z-index zoom"," "),wa=(e,t,n)=>{y(n)||""===n?tn(e,t):Xt(e,t,n)},xa=e=>e.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase())),ka=(e,t)=>{let n=0;if(e)for(let o=e.nodeType,r=e.previousSibling;r;r=r.previousSibling){const e=r.nodeType;(!t||!Yo(r)||e!==o&&r.data.length)&&(n++,o=e)}return n},Ea=(e,t)=>{const n=Jt(t,"style"),o=e.serialize(e.parse(n),jt(t));wa(t,ya,o)},Sa=(e,t,n)=>{const o=xa(t);y(n)||""===n?uo(e,o):ro(e,o,((e,t)=>x(e)?ke(Ca,t)?e+"":e+"px":e)(n,o))},_a=(e,t={})=>{const n={},o=window,r={};let s=0;const a=Os.forElement(bn(e),{contentCssCors:t.contentCssCors,referrerPolicy:t.referrerPolicy}),i=[],l=t.schema?t.schema:aa({}),d=ia({url_converter:t.url_converter,url_converter_scope:t.url_converter_scope},t.schema),c=t.ownEvents?new ha:ha.Event,u=l.getBlockElements(),f=t=>t&&e&&m(t)?e.getElementById(t):t,g=e=>{const t=f(e);return C(t)?bn(t):null},h=(e,t,n="")=>{let o;const r=g(e);if(C(r)&&Vt(r)){const e=Y[t];o=e&&e.get?e.get(r.dom,t):Jt(r,t)}return C(o)?o:n},b=e=>{const t=f(e);return y(t)?[]:t.attributes},v=(e,n,o)=>{T(e,(e=>{if(zo(e)){const r=bn(e),s=""===o?null:o,a=Jt(r,n),i=Y[n];i&&i.set?i.set(r.dom,s,n):wa(r,n,s),a!==s&&t.onSetAttrib&&t.onSetAttrib({attrElm:r.dom,attrName:n,attrValue:s})}}))},x=()=>t.root_element||e.body,k=(t,n)=>((e,t,n)=>{let o=0,r=0;const s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===ao(bn(e),"position")){const n=t.getBoundingClientRect();return o=n.left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,r=n.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop,{x:o,y:r}}let a=t;for(;a&&a!==n&&a.nodeType&&!Rs(a,n);){const e=a;o+=e.offsetLeft||0,r+=e.offsetTop||0,a=e.offsetParent}for(a=t.parentNode;a&&a!==n&&a.nodeType&&!Rs(a,n);)o-=a.scrollLeft||0,r-=a.scrollTop||0,a=a.parentNode;r+=(e=>_s.isFirefox()&&"table"===jt(e)?Ns(Pn(e)).filter((e=>"caption"===jt(e))).bind((e=>Ns(Dn(e)).map((t=>{const n=t.dom.offsetTop,o=e.dom.offsetTop,r=e.dom.offsetHeight;return n<=o?-r:0})))).getOr(0):0)(bn(t))}return{x:o,y:r}})(e.body,f(t),n),S=(e,t,n)=>{const o=f(e);if(!y(o)&&zo(o))return n?ao(bn(o),xa(t)):("float"===(t=t.replace(/-(\D)/g,((e,t)=>t.toUpperCase())))&&(t="cssFloat"),o.style?o.style[t]:void 0)},_=e=>{const t=f(e);if(!t)return{w:0,h:0};let n=S(t,"width"),o=S(t,"height");return n&&-1!==n.indexOf("px")||(n="0"),o&&-1!==o.indexOf("px")||(o="0"),{w:parseInt(n,10)||t.offsetWidth||t.clientWidth,h:parseInt(o,10)||t.offsetHeight||t.clientHeight}},R=(e,t)=>{if(!e)return!1;const n=p(e)?e:[e];return $(n,(e=>Cn(bn(e),t)))},A=(e,t,n,o)=>{const r=[];let s=f(e);o=void 0===o;const a=n||("BODY"!==x().nodeName?x().parentNode:null);if(m(t))if("*"===t)t=zo;else{const e=t;t=t=>R(t,e)}for(;s&&!(s===a||y(s.nodeType)||Zo(s)||er(s));){if(!t||t(s)){if(!o)return[s];r.push(s)}s=s.parentNode}return o?r:null},O=(e,t,n)=>{let o=t;if(e){m(t)&&(o=e=>R(e,t));for(let t=e[n];t;t=t[n])if(w(o)&&o(t))return t}return null},T=function(e,t,n){const o=null!=n?n:this;if(p(e)){const n=[];return ba(e,((e,r)=>{const s=f(e);s&&n.push(t.call(o,s,r))})),n}{const n=f(e);return!!n&&t.call(o,n)}},B=(e,t)=>{T(e,(e=>{ge(t,((t,n)=>{v(e,n,t)}))}))},D=(e,t)=>{T(e,(e=>{const n=bn(e);ko(n,t)}))},P=(t,n,o,r,s)=>T(t,(t=>{const a=m(n)?e.createElement(n):n;return C(o)&&B(a,o),r&&(!m(r)&&r.nodeType?a.appendChild(r):m(r)&&D(a,r)),s?a:t.appendChild(a)})),L=(t,n,o)=>P(e.createElement(t),t,n,o,!0),M=Ks.encodeAllRaw,I=(e,t)=>T(e,(e=>{const n=bn(e);return t&&q(Pn(n),(e=>{qt(e)&&0===e.dom.length?yo(e):mo(n,e)})),yo(n),n.dom})),F=(e,t,n)=>{T(e,(e=>{if(zo(e)){const o=bn(e),r=t.split(" ");q(r,(e=>{C(n)?(n?dn:un)(o,e):((e,t)=>{const n=rn(e)?e.dom.classList.toggle(t):((e,t)=>H(sn(e),t)?ln(e,t):an(e,t))(e,t);cn(e)})(o,e)}))}}))},U=(e,t,n)=>T(t,(o=>{var r;const s=p(t)?e.cloneNode(!0):e;return n&&ba(va(o.childNodes),(e=>{s.appendChild(e)})),null===(r=o.parentNode)||void 0===r||r.replaceChild(s,o),o})),z=e=>{if(zo(e)){const t="a"===e.nodeName.toLowerCase()&&!h(e,"href")&&h(e,"id");if(h(e,"name")||h(e,"data-mce-bookmark")||t)return!0}return!1},j=()=>e.createRange(),V=(n,r,s,a)=>{if(p(n)){let e=n.length;const t=[];for(;e--;)t[e]=V(n[e],r,s,a);return t}return!t.collect||n!==e&&n!==o||i.push([n,r,s,a]),c.bind(n,r,s,a||G)},W=(t,n,r)=>{if(p(t)){let e=t.length;const o=[];for(;e--;)o[e]=W(t[e],n,r);return o}if(i.length>0&&(t===e||t===o)){let e=i.length;for(;e--;){const[o,s,a]=i[e];t!==o||n&&n!==s||r&&r!==a||c.unbind(o,s,a)}}return c.unbind(t,n,r)},K=e=>{if(e&&zo(e)){const t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},G={doc:e,settings:t,win:o,files:r,stdMode:!0,boxModel:!0,styleSheetLoader:a,boundEvents:i,styles:d,schema:l,events:c,isBlock:e=>m(e)?ke(u,e):zo(e)&&(ke(u,e.nodeName)||ks(l,e)),root:null,clone:(e,t)=>e.cloneNode(t),getRoot:x,getViewPort:e=>{const t=Do(e);return{x:t.x,y:t.y,w:t.width,h:t.height}},getRect:e=>{const t=f(e),n=k(t),o=_(t);return{x:n.x,y:n.y,w:o.w,h:o.h}},getSize:_,getParent:(e,t,n)=>{const o=A(e,t,n,!1);return o&&o.length>0?o[0]:null},getParents:A,get:f,getNext:(e,t)=>O(e,t,"nextSibling"),getPrev:(e,t)=>O(e,t,"previousSibling"),select:(n,o)=>{var r,s;const a=null!==(s=null!==(r=f(o))&&void 0!==r?r:t.root_element)&&void 0!==s?s:e;return w(a.querySelectorAll)?ce(a.querySelectorAll(n)):[]},is:R,add:P,create:L,createHTML:(e,t,n="")=>{let o="<"+e;for(const e in t)Ee(t,e)&&(o+=" "+e+'="'+M(t[e])+'"');return Ye(n)&&ke(l.getVoidElements(),e)?o+" />":o+">"+n+"</"+e+">"},createFragment:t=>{const n=e.createElement("div"),o=e.createDocumentFragment();let r;for(o.appendChild(n),t&&(n.innerHTML=t);r=n.firstChild;)o.appendChild(r);return o.removeChild(n),o},remove:I,setStyle:(e,n,o)=>{T(e,(e=>{const r=bn(e);Sa(r,n,o),t.update_styles&&Ea(d,r)}))},getStyle:S,setStyles:(e,n)=>{T(e,(e=>{const o=bn(e);ge(n,((e,t)=>{Sa(o,t,e)})),t.update_styles&&Ea(d,o)}))},removeAllAttribs:e=>T(e,(e=>{const t=e.attributes;for(let n=t.length-1;n>=0;n--)e.removeAttributeNode(t.item(n))})),setAttrib:v,setAttribs:B,getAttrib:h,getPos:k,parseStyle:e=>d.parse(e),serializeStyle:(e,t)=>d.serialize(e,t),addStyle:t=>{if(G!==_a.DOM&&e===document){if(n[t])return;n[t]=!0}let o=e.getElementById("mceDefaultStyles");if(!o){o=e.createElement("style"),o.id="mceDefaultStyles",o.type="text/css";const t=e.head;t.firstChild?t.insertBefore(o,t.firstChild):t.appendChild(o)}o.styleSheet?o.styleSheet.cssText+=t:o.appendChild(e.createTextNode(t))},loadCSS:e=>{e||(e=""),q(e.split(","),(e=>{r[e]=!0,a.load(e).catch(E)}))},addClass:(e,t)=>{F(e,t,!0)},removeClass:(e,t)=>{F(e,t,!1)},hasClass:(e,t)=>{const n=g(e),o=t.split(" ");return C(n)&&ne(o,(e=>mn(n,e)))},toggleClass:F,show:e=>{T(e,(e=>uo(bn(e),"display")))},hide:e=>{T(e,(e=>ro(bn(e),"display","none")))},isHidden:e=>{const t=g(e);return C(t)&&Pt(lo(t,"display"),"none")},uniqueId:e=>(e||"mce_")+s++,setHTML:D,getOuterHTML:e=>{const t=g(e);return C(t)?zo(t.dom)?t.dom.outerHTML:(e=>{const t=pn("div"),n=bn(e.dom.cloneNode(!0));return po(t,n),xo(t)})(t):""},setOuterHTML:(e,t)=>{T(e,(e=>{zo(e)&&(e.outerHTML=t)}))},decode:Ks.decode,encode:M,insertAfter:(e,t)=>{const n=f(t);return T(e,(e=>{const t=null==n?void 0:n.parentNode,o=null==n?void 0:n.nextSibling;return t&&(o?t.insertBefore(e,o):t.appendChild(e)),e}))},replace:U,rename:(e,t)=>{if(e.nodeName!==t.toUpperCase()){const n=L(t);return ba(b(e),(t=>{v(n,t.nodeName,h(e,t.nodeName))})),U(n,e,!0),n}return e},findCommonAncestor:(e,t)=>{let n=e;for(;n;){let e=t;for(;e&&n!==e;)e=e.parentNode;if(n===e)break;n=n.parentNode}return!n&&e.ownerDocument?e.ownerDocument.documentElement:n},run:T,getAttribs:b,isEmpty:(e,t)=>{let n=0;if(z(e))return!1;const o=e.firstChild;if(o){const r=new Io(o,e),s=l?l.getWhitespaceElements():{},a=t||(l?l.getNonEmptyElements():null);let i=o;do{if(zo(i)){const e=i.getAttribute("data-mce-bogus");if(e){i=r.next("all"===e);continue}const t=i.nodeName.toLowerCase();if(a&&a[t]){if("br"===t){n++,i=r.next();continue}return!1}if(z(i))return!1}if(Jo(i))return!1;if(Yo(i)&&!rs(i.data))return!1;if(Yo(i)&&i.parentNode&&s[i.parentNode.nodeName]&&rs(i.data))return!1;i=r.next()}while(i)}return n<=1},createRng:j,nodeIndex:ka,split:(e,t,n)=>{let o,r,s=j();if(e&&t&&e.parentNode&&t.parentNode){const a=e.parentNode;return s.setStart(a,ka(e)),s.setEnd(t.parentNode,ka(t)),o=s.extractContents(),s=j(),s.setStart(t.parentNode,ka(t)+1),s.setEnd(a,ka(e)+1),r=s.extractContents(),a.insertBefore(Ds(G,o),e),n?a.insertBefore(n,e):a.insertBefore(t,e),a.insertBefore(Ds(G,r),e),I(e),n||t}},bind:V,unbind:W,fire:(e,t,n)=>c.dispatch(e,t,n),dispatch:(e,t,n)=>c.dispatch(e,t,n),getContentEditable:K,getContentEditableParent:e=>{const t=x();let n=null;for(let o=e;o&&o!==t&&(n=K(o),null===n);o=o.parentNode);return n},isEditable:e=>{if(C(e)){const t=zo(e)?e:e.parentElement,n="true"===K(x());return C(t)&&eo(bn(t),n)}return!1},destroy:()=>{if(i.length>0){let e=i.length;for(;e--;){const[t,n,o]=i[e];c.unbind(t,n,o)}}ge(r,((e,t)=>{a.unload(t),delete r[t]}))},isChildOf:(e,t)=>e===t||t.contains(e),dumpRng:e=>"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset},Y=((e,t,n)=>{const o=t.keep_values,r={set:(e,o,r)=>{const s=bn(e);w(t.url_converter)&&C(o)&&(o=t.url_converter.call(t.url_converter_scope||n(),String(o),r,e)),wa(s,"data-mce-"+r,o),wa(s,r,o)},get:(e,t)=>{const n=bn(e);return Jt(n,"data-mce-"+t)||Jt(n,t)}},s={style:{set:(t,n)=>{const r=bn(t);o&&wa(r,ya,n),tn(r,"style"),m(n)&&so(r,e.parse(n))},get:t=>{const n=bn(t),o=Jt(n,ya)||Jt(n,"style");return e.serialize(e.parse(o),jt(n))}}};return o&&(s.href=s.src=r),s})(d,t,N(G));return G};_a.DOM=_a(document),_a.nodeIndex=ka;const Na=_a.DOM;class Ra{constructor(e={}){this.states={},this.queue=[],this.scriptLoadedCallbacks={},this.queueLoadedCallbacks=[],this.loading=!1,this.settings=e}_setReferrerPolicy(e){this.settings.referrerPolicy=e}loadScript(e){return new Promise(((t,n)=>{const o=Na;let r;const s=()=>{o.remove(a),r&&(r.onerror=r.onload=r=null)},a=o.uniqueId();r=document.createElement("script"),r.id=a,r.type="text/javascript",r.src=Dt._addCacheSuffix(e),this.settings.referrerPolicy&&o.setAttrib(r,"referrerpolicy",this.settings.referrerPolicy),r.onload=()=>{s(),t()},r.onerror=()=>{s(),n("Failed to load script: "+e)},(document.getElementsByTagName("head")[0]||document.body).appendChild(r)}))}isDone(e){return 2===this.states[e]}markDone(e){this.states[e]=2}add(e){const t=this;return t.queue.push(e),void 0===t.states[e]&&(t.states[e]=0),new Promise(((n,o)=>{t.scriptLoadedCallbacks[e]||(t.scriptLoadedCallbacks[e]=[]),t.scriptLoadedCallbacks[e].push({resolve:n,reject:o})}))}load(e){return this.add(e)}remove(e){delete this.states[e],delete this.scriptLoadedCallbacks[e]}loadQueue(){const e=this.queue;return this.queue=[],this.loadScripts(e)}loadScripts(e){const t=this,n=(e,n)=>{xe(t.scriptLoadedCallbacks,n).each((t=>{q(t,(t=>t[e](n)))})),delete t.scriptLoadedCallbacks[n]},o=e=>{const t=G(e,(e=>"rejected"===e.status));return t.length>0?Promise.reject(te(t,(({reason:e})=>p(e)?e:[e]))):Promise.resolve()},r=e=>Promise.allSettled(V(e,(e=>2===t.states[e]?(n("resolve",e),Promise.resolve()):3===t.states[e]?(n("reject",e),Promise.reject(e)):(t.states[e]=1,t.loadScript(e).then((()=>{t.states[e]=2,n("resolve",e);const s=t.queue;return s.length>0?(t.queue=[],r(s).then(o)):Promise.resolve()}),(()=>(t.states[e]=3,n("reject",e),Promise.reject(e)))))))),s=e=>(t.loading=!0,r(e).then((e=>{t.loading=!1;const n=t.queueLoadedCallbacks.shift();return I.from(n).each(P),o(e)}))),a=Se(e);return t.loading?new Promise(((e,n)=>{t.queueLoadedCallbacks.push((()=>s(a).then(e,n)))})):s(a)}}Ra.ScriptLoader=new Ra;const Aa=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Oa={},Ta=Aa("en"),Ba=()=>xe(Oa,Ta.get()),Da={getData:()=>pe(Oa,(e=>({...e}))),setCode:e=>{e&&Ta.set(e)},getCode:()=>Ta.get(),add:(e,t)=>{let n=Oa[e];n||(Oa[e]=n={}),ge(t,((e,t)=>{n[t.toLowerCase()]=e}))},translate:e=>{const t=Ba().getOr({}),n=e=>w(e)?Object.prototype.toString.call(e):o(e)?"":""+e,o=e=>""===e||null==e,r=e=>{const o=n(e);return xe(t,o.toLowerCase()).map(n).getOr(o)},s=e=>e.replace(/{context:\w+}$/,"");if(o(e))return"";if(f(a=e)&&ke(a,"raw"))return n(e.raw);var a;if((e=>p(e)&&e.length>1)(e)){const t=e.slice(1);return s(r(e[0]).replace(/\{([0-9]+)\}/g,((e,o)=>ke(t,o)?n(t[o]):e)))}return s(r(e))},isRtl:()=>Ba().bind((e=>xe(e,"_dir"))).exists((e=>"rtl"===e)),hasCode:e=>ke(Oa,e)},Pa=()=>{const e=[],t={},n={},o=[],r=(e,t)=>{const n=G(o,(n=>n.name===e&&n.state===t));q(n,(e=>e.resolve()))},s=e=>ke(t,e),a=(e,n)=>{const o=Da.getCode();!o||n&&-1===(","+(n||"")+",").indexOf(","+o+",")||Ra.ScriptLoader.add(t[e]+"/langs/"+o+".js")},i=(e,t="added")=>"added"===t&&(e=>ke(n,e))(e)||"loaded"===t&&s(e)?Promise.resolve():new Promise((n=>{o.push({name:e,state:t,resolve:n})}));return{items:e,urls:t,lookup:n,get:e=>{if(n[e])return n[e].instance},requireLangPack:(e,t)=>{!1!==Pa.languageLoad&&(s(e)?a(e,t):i(e,"loaded").then((()=>a(e,t))))},add:(t,o)=>(e.push(o),n[t]={instance:o},r(t,"added"),o),remove:e=>{delete t[e],delete n[e]},createUrl:(e,t)=>m(t)?m(e)?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}:t,load:(e,o)=>{if(t[e])return Promise.resolve();let s=m(o)?o:o.prefix+o.resource+o.suffix;0!==s.indexOf("/")&&-1===s.indexOf("://")&&(s=Pa.baseURL+"/"+s),t[e]=s.substring(0,s.lastIndexOf("/"));const a=()=>(r(e,"loaded"),Promise.resolve());return n[e]?a():Ra.ScriptLoader.add(s).then(a)},waitFor:i}};Pa.languageLoad=!0,Pa.baseURL="",Pa.PluginManager=Pa(),Pa.ThemeManager=Pa(),Pa.ModelManager=Pa();const La=e=>{const t=Aa(I.none()),n=()=>t.get().each((e=>clearInterval(e)));return{clear:()=>{n(),t.set(I.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:o=>{n(),t.set(I.some(setInterval(o,e)))}}},Ma=()=>{const e=(e=>{const t=Aa(I.none()),n=()=>t.get().each(e);return{clear:()=>{n(),t.set(I.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{n(),t.set(I.some(e))}}})(E);return{...e,on:t=>e.get().each(t)}},Ia=(e,t)=>{let n=null;return{cancel:()=>{h(n)||(clearTimeout(n),n=null)},throttle:(...o)=>{h(n)&&(n=setTimeout((()=>{n=null,e.apply(null,o)}),t))}}},Fa=(e,t)=>{let n=null;const o=()=>{h(n)||(clearTimeout(n),n=null)};return{cancel:o,throttle:(...r)=>{o(),n=setTimeout((()=>{n=null,e.apply(null,r)}),t)}}},Ua=N("mce-annotation"),za=N("data-mce-annotation"),ja=N("data-mce-annotation-uid"),Ha=N("data-mce-annotation-active"),$a=N("data-mce-annotation-classes"),Va=N("data-mce-annotation-attrs"),qa=e=>t=>xn(t,e),Wa=(e,t)=>{const n=e.selection.getRng(),o=bn(n.startContainer),r=bn(e.getBody()),s=t.fold((()=>"."+Ua()),(e=>`[${za()}="${e}"]`)),a=Ln(o,n.startOffset).getOr(o);return Zn(a,s,qa(r)).bind((t=>Zt(t,`${ja()}`).bind((n=>Zt(t,`${za()}`).map((t=>{const o=Ga(e,n);return{uid:n,name:t,elements:o}}))))))},Ka=(e,t)=>en(e,"data-mce-bogus")||Mo(e,'[data-mce-bogus="all"]',qa(t)),Ga=(e,t)=>{const n=bn(e.getBody()),o=Lo(n,`[${ja()}="${t}"]`);return G(o,(e=>!Ka(e,n)))},Ya=(e,t)=>{const n=bn(e.getBody()),o=Lo(n,`[${za()}="${t}"]`),r={};return q(o,(e=>{if(!Ka(e,n)){const t=Jt(e,ja()),n=xe(r,t).getOr([]);r[t]=n.concat([e])}})),r};let Xa=0;const Qa=e=>{const t=(new Date).getTime(),n=Math.floor(1e9*Math.random());return Xa++,e+"_"+n+Xa+String(t)},Ja=(e,t)=>bn(e.dom.cloneNode(t)),Za=e=>Ja(e,!1),ei=e=>Ja(e,!0),ti=(e,t,n=L)=>{const o=new Io(e,t),r=e=>{let t;do{t=o[e]()}while(t&&!Yo(t)&&!n(t));return I.from(t).filter(Yo)};return{current:()=>I.from(o.current()).filter(Yo),next:()=>r("next"),prev:()=>r("prev"),prev2:()=>r("prev2")}},ni=(e,t)=>{const n=t||(t=>e.isBlock(t)||tr(t)||rr(t)),o=(e,t,n,r)=>{if(Yo(e)){const n=r(e,t,e.data);if(-1!==n)return I.some({container:e,offset:n})}return n().bind((e=>o(e.container,e.offset,n,r)))};return{backwards:(t,r,s,a)=>{const i=ti(t,null!=a?a:e.getRoot(),n);return o(t,r,(()=>i.prev().map((e=>({container:e,offset:e.length})))),s).getOrNull()},forwards:(t,r,s,a)=>{const i=ti(t,null!=a?a:e.getRoot(),n);return o(t,r,(()=>i.next().map((e=>({container:e,offset:0})))),s).getOrNull()}}},oi=Math.round,ri=e=>e?{left:oi(e.left),top:oi(e.top),bottom:oi(e.bottom),right:oi(e.right),width:oi(e.width),height:oi(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0},si=(e,t)=>(e=ri(e),t||(e.left=e.left+e.width),e.right=e.left,e.width=0,e),ai=(e,t,n)=>e>=0&&e<=Math.min(t.height,n.height)/2,ii=(e,t)=>{const n=Math.min(t.height/2,e.height/2);return e.bottom-n<t.top||!(e.top>t.bottom)&&ai(t.top-e.bottom,e,t)},li=(e,t)=>e.top>t.bottom||!(e.bottom<t.top)&&ai(t.bottom-e.top,e,t),di=(e,t,n)=>{const o=Math.max(Math.min(t,e.left+e.width),e.left),r=Math.max(Math.min(n,e.top+e.height),e.top);return Math.sqrt((t-o)*(t-o)+(n-r)*(n-r))},ci=e=>{const t=e.startContainer,n=e.startOffset;return t===e.endContainer&&t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},ui=(e,t)=>{if(zo(e)&&e.hasChildNodes()){const n=e.childNodes,o=((e,t,n)=>Math.min(Math.max(e,0),n))(t,0,n.length-1);return n[o]}return e},mi=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),fi=e=>m(e)&&e.charCodeAt(0)>=768&&mi.test(e),gi=zo,pi=es,hi=$o("display","block table"),bi=$o("float","left right"),vi=((...e)=>t=>{for(let n=0;n<e.length;n++)if(!e[n](t))return!1;return!0})(gi,pi,T(bi)),yi=T($o("white-space","pre pre-line pre-wrap")),Ci=Yo,wi=tr,xi=_a.nodeIndex,ki=(e,t)=>t<0&&zo(e)&&e.hasChildNodes()?void 0:ui(e,t),Ei=e=>e?e.createRange():_a.DOM.createRng(),Si=e=>m(e)&&/[\r\n\t ]/.test(e),_i=e=>!!e.setStart&&!!e.setEnd,Ni=e=>{const t=e.startContainer,n=e.startOffset;if(Si(e.toString())&&yi(t.parentNode)&&Yo(t)){const e=t.data;if(Si(e[n-1])||Si(e[n+1]))return!0}return!1},Ri=e=>0===e.left&&0===e.right&&0===e.top&&0===e.bottom,Ai=e=>{var t;let n;const o=e.getClientRects();return n=o.length>0?ri(o[0]):ri(e.getBoundingClientRect()),!_i(e)&&wi(e)&&Ri(n)?(e=>{const t=e.ownerDocument,n=Ei(t),o=t.createTextNode(cr),r=e.parentNode;r.insertBefore(o,e),n.setStart(o,0),n.setEnd(o,1);const s=ri(n.getBoundingClientRect());return r.removeChild(o),s})(e):Ri(n)&&_i(e)&&null!==(t=(e=>{const t=e.startContainer,n=e.endContainer,o=e.startOffset,r=e.endOffset;if(t===n&&Yo(n)&&0===o&&1===r){const t=e.cloneRange();return t.setEndAfter(n),Ai(t)}return null})(e))&&void 0!==t?t:n},Oi=(e,t)=>{const n=si(e,t);return n.width=1,n.right=n.left+1,n},Ti=(e,t,n)=>{const o=()=>(n||(n=(e=>{const t=[],n=e=>{var n,o;0!==e.height&&(t.length>0&&(n=e,o=t[t.length-1],n.left===o.left&&n.top===o.top&&n.bottom===o.bottom&&n.right===o.right)||t.push(e))},o=(e,t)=>{const o=Ei(e.ownerDocument);if(t<e.data.length){if(fi(e.data[t]))return;if(fi(e.data[t-1])&&(o.setStart(e,t),o.setEnd(e,t+1),!Ni(o)))return void n(Oi(Ai(o),!1))}t>0&&(o.setStart(e,t-1),o.setEnd(e,t),Ni(o)||n(Oi(Ai(o),!1))),t<e.data.length&&(o.setStart(e,t),o.setEnd(e,t+1),Ni(o)||n(Oi(Ai(o),!0)))},r=e.container(),s=e.offset();if(Ci(r))return o(r,s),t;if(gi(r))if(e.isAtEnd()){const e=ki(r,s);Ci(e)&&o(e,e.data.length),vi(e)&&!wi(e)&&n(Oi(Ai(e),!1))}else{const a=ki(r,s);if(Ci(a)&&o(a,0),vi(a)&&e.isAtEnd())return n(Oi(Ai(a),!1)),t;const i=ki(e.container(),e.offset()-1);vi(i)&&!wi(i)&&(hi(i)||hi(a)||!vi(a))&&n(Oi(Ai(i),!1)),vi(a)&&n(Oi(Ai(a),!0))}return t})(Ti(e,t))),n);return{container:N(e),offset:N(t),toRange:()=>{const n=Ei(e.ownerDocument);return n.setStart(e,t),n.setEnd(e,t),n},getClientRects:o,isVisible:()=>o().length>0,isAtStart:()=>(Ci(e),0===t),isAtEnd:()=>Ci(e)?t>=e.data.length:t>=e.childNodes.length,isEqual:n=>n&&e===n.container()&&t===n.offset(),getNode:n=>ki(e,n?t-1:t)}};Ti.fromRangeStart=e=>Ti(e.startContainer,e.startOffset),Ti.fromRangeEnd=e=>Ti(e.endContainer,e.endOffset),Ti.after=e=>Ti(e.parentNode,xi(e)+1),Ti.before=e=>Ti(e.parentNode,xi(e)),Ti.isAbove=(e,t)=>Lt(le(t.getClientRects()),de(e.getClientRects()),ii).getOr(!1),Ti.isBelow=(e,t)=>Lt(de(t.getClientRects()),le(e.getClientRects()),li).getOr(!1),Ti.isAtStart=e=>!!e&&e.isAtStart(),Ti.isAtEnd=e=>!!e&&e.isAtEnd(),Ti.isTextPosition=e=>!!e&&Yo(e.container()),Ti.isElementPosition=e=>!Ti.isTextPosition(e);const Bi=(e,t)=>{Yo(t)&&0===t.data.length&&e.remove(t)},Di=(e,t,n)=>{er(n)?((e,t,n)=>{const o=I.from(n.firstChild),r=I.from(n.lastChild);t.insertNode(n),o.each((t=>Bi(e,t.previousSibling))),r.each((t=>Bi(e,t.nextSibling)))})(e,t,n):((e,t,n)=>{t.insertNode(n),Bi(e,n.previousSibling),Bi(e,n.nextSibling)})(e,t,n)},Pi=Yo,Li=qo,Mi=_a.nodeIndex,Ii=e=>{const t=e.parentNode;return Li(t)?Ii(t):t},Fi=e=>e?Te(e.childNodes,((e,t)=>(Li(t)&&"BR"!==t.nodeName?e=e.concat(Fi(t)):e.push(t),e)),[]):[],Ui=e=>t=>e===t,zi=e=>(Pi(e)?"text()":e.nodeName.toLowerCase())+"["+(e=>{let t,n;t=Fi(Ii(e)),n=Be(t,Ui(e),e),t=t.slice(0,n+1);const o=Te(t,((e,n,o)=>(Pi(n)&&Pi(t[o-1])&&e++,e)),0);return t=Oe(t,Ho([e.nodeName])),n=Be(t,Ui(e),e),n-o})(e)+"]",ji=(e,t)=>{let n,o=[],r=t.container(),s=t.offset();if(Pi(r))n=((e,t)=>{let n=e;for(;(n=n.previousSibling)&&Pi(n);)t+=n.data.length;return t})(r,s);else{const e=r.childNodes;s>=e.length?(n="after",s=e.length-1):n="before",r=e[s]}o.push(zi(r));let a=((e,t,n)=>{const o=[];for(let n=t.parentNode;n&&n!==e;n=n.parentNode)o.push(n);return o})(e,r);return a=Oe(a,T(qo)),o=o.concat(Ae(a,(e=>zi(e)))),o.reverse().join("/")+","+n},Hi=(e,t)=>{if(!t)return null;const n=t.split(","),o=n[0].split("/"),r=n.length>1?n[1]:"before",s=Te(o,((e,t)=>{const n=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t);return n?("text()"===n[1]&&(n[1]="#text"),((e,t,n)=>{let o=Fi(e);return o=Oe(o,((e,t)=>!Pi(e)||!Pi(o[t-1]))),o=Oe(o,Ho([t])),o[n]})(e,n[1],parseInt(n[2],10))):null}),e);if(!s)return null;if(!Pi(s)&&s.parentNode){let e;return e="after"===r?Mi(s)+1:Mi(s),Ti(s.parentNode,e)}return((e,t)=>{let n=e,o=0;for(;Pi(n);){const r=n.data.length;if(t>=o&&t<=o+r){e=n,t-=o;break}if(!Pi(n.nextSibling)){e=n,t=r;break}o+=r,n=n.nextSibling}return Pi(e)&&t>e.data.length&&(t=e.data.length),Ti(e,t)})(s,parseInt(r,10))},$i=rr,Vi=(e,t,n,o,r)=>{const s=r?o.startContainer:o.endContainer;let a=r?o.startOffset:o.endOffset;const i=[],l=e.getRoot();if(Yo(s))i.push(n?((e,t,n)=>{let o=e(t.data.slice(0,n)).length;for(let n=t.previousSibling;n&&Yo(n);n=n.previousSibling)o+=e(n.data).length;return o})(t,s,a):a);else{let t=0;const o=s.childNodes;a>=o.length&&o.length&&(t=1,a=Math.max(0,o.length-1)),i.push(e.nodeIndex(o[a],n)+t)}for(let t=s;t&&t!==l;t=t.parentNode)i.push(e.nodeIndex(t,n));return i},qi=(e,t,n)=>{let o=0;return Dt.each(e.select(t),(e=>"all"===e.getAttribute("data-mce-bogus")?void 0:e!==n&&void o++)),o},Wi=(e,t)=>{let n=t?e.startContainer:e.endContainer,o=t?e.startOffset:e.endOffset;if(zo(n)&&"TR"===n.nodeName){const r=n.childNodes;n=r[Math.min(t?o:o-1,r.length-1)],n&&(o=t?0:n.childNodes.length,t?e.setStart(n,o):e.setEnd(n,o))}},Ki=e=>(Wi(e,!0),Wi(e,!1),e),Gi=(e,t)=>{if(zo(e)&&(e=ui(e,t),$i(e)))return e;if(Fr(e)){Yo(e)&&Mr(e)&&(e=e.parentNode);let t=e.previousSibling;if($i(t))return t;if(t=e.nextSibling,$i(t))return t}},Yi=(e,t,n)=>{const o=n.getNode(),r=n.getRng();if("IMG"===o.nodeName||$i(o)){const e=o.nodeName;return{name:e,index:qi(n.dom,e,o)}}const s=(e=>Gi(e.startContainer,e.startOffset)||Gi(e.endContainer,e.endOffset))(r);if(s){const e=s.tagName;return{name:e,index:qi(n.dom,e,s)}}return((e,t,n,o)=>{const r=t.dom,s=Vi(r,e,n,o,!0),a=t.isForward(),i=qr(o)?{isFakeCaret:!0}:{};return t.isCollapsed()?{start:s,forward:a,...i}:{start:s,end:Vi(r,e,n,o,!1),forward:a,...i}})(e,n,t,r)},Xi=(e,t,n)=>{const o={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",o,"&#xFEFF;"):e.create("span",o)},Qi=(e,t)=>{const n=e.dom;let o=e.getRng();const r=n.uniqueId(),s=e.isCollapsed(),a=e.getNode(),i=a.nodeName,l=e.isForward();if("IMG"===i)return{name:i,index:qi(n,i,a)};const d=Ki(o.cloneRange());if(!s){d.collapse(!1);const e=Xi(n,r+"_end",t);Di(n,d,e)}o=Ki(o),o.collapse(!0);const c=Xi(n,r+"_start",t);return Di(n,o,c),e.moveToBookmark({id:r,keep:!0,forward:l}),{id:r,forward:l}},Ji=O(Yi,R,!0),Zi=e=>{const t=t=>t(e),n=N(e),o=()=>r,r={tag:!0,inner:e,fold:(t,n)=>n(e),isValue:M,isError:L,map:t=>tl.value(t(e)),mapError:o,bind:t,exists:t,forall:t,getOr:n,or:o,getOrThunk:n,orThunk:o,getOrDie:n,each:t=>{t(e)},toOptional:()=>I.some(e)};return r},el=e=>{const t=()=>n,n={tag:!1,inner:e,fold:(t,n)=>t(e),isValue:L,isError:M,map:t,mapError:t=>tl.error(t(e)),bind:t,exists:L,forall:M,getOr:R,or:R,getOrThunk:D,orThunk:D,getOrDie:B(String(e)),each:E,toOptional:I.none};return n},tl={value:Zi,error:el,fromOption:(e,t)=>e.fold((()=>el(t)),Zi)},nl=e=>{if(!p(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],n={};return q(e,((o,r)=>{const s=me(o);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],i=o[a];if(void 0!==n[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!p(i))throw new Error("case arguments must be an array");t.push(a),n[a]=(...n)=>{const o=n.length;if(o!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+o);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,n)},match:e=>{const o=me(e);if(t.length!==o.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+o.join(","));if(!ne(t,(e=>H(o,e))))throw new Error("Not all branches were specified when using match. Specified: "+o.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,n)},log:e=>{console.log(e,{constructors:t,constructor:a,params:n})}}}})),n};nl([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const ol=e=>"inline-command"===e.type||"inline-format"===e.type,rl=e=>"block-command"===e.type||"block-format"===e.type,sl=e=>{const t=t=>tl.error({message:t,pattern:e}),n=(n,o,r)=>{if(void 0!==e.format){let r;if(p(e.format)){if(!ne(e.format,m))return t(n+" pattern has non-string items in the `format` array");r=e.format}else{if(!m(e.format))return t(n+" pattern has non-string `format` parameter");r=[e.format]}return tl.value(o(r))}return void 0!==e.cmd?m(e.cmd)?tl.value(r(e.cmd,e.value)):t(n+" pattern has non-string `cmd` parameter"):t(n+" pattern is missing both `format` and `cmd` parameters")};if(!f(e))return t("Raw pattern is not an object");if(!m(e.start))return t("Raw pattern is missing `start` parameter");if(void 0!==e.end){if(!m(e.end))return t("Inline pattern has non-string `end` parameter");if(0===e.start.length&&0===e.end.length)return t("Inline pattern has empty `start` and `end` parameters");let o=e.start,r=e.end;return 0===r.length&&(r=o,o=""),n("Inline",(e=>({type:"inline-format",start:o,end:r,format:e})),((e,t)=>({type:"inline-command",start:o,end:r,cmd:e,value:t})))}return void 0!==e.replacement?m(e.replacement)?0===e.start.length?t("Replacement pattern has empty `start` parameter"):tl.value({type:"inline-command",start:"",end:e.start,cmd:"mceInsertContent",value:e.replacement}):t("Replacement pattern has non-string `replacement` parameter"):0===e.start.length?t("Block pattern has empty `start` parameter"):n("Block",(t=>({type:"block-format",start:e.start,format:t[0]})),((t,n)=>({type:"block-command",start:e.start,cmd:t,value:n})))},al=e=>G(e,rl),il=e=>G(e,ol),ll=e=>{const t=(e=>{const t=[],n=[];return q(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{n.push(e)}))})),{errors:t,values:n}})(V(e,sl));return q(t.errors,(e=>console.error(e.message,e.pattern))),t.values},dl=xt().deviceType,cl=dl.isTouch(),ul=_a.DOM,ml=e=>u(e,RegExp),fl=e=>t=>t.options.get(e),gl=e=>m(e)||f(e),pl=(e,t="")=>n=>{const o=m(n);if(o){if(-1!==n.indexOf("=")){const r=(e=>{const t=e.indexOf("=")>0?e.split(/[;,](?![^=;,]*(?:[;,]|$))/):e.split(",");return X(t,((e,t)=>{const n=t.split("="),o=n[0],r=n.length>1?n[1]:o;return e[qe(o)]=qe(r),e}),{})})(n);return{value:xe(r,e.id).getOr(t),valid:o}}return{value:n,valid:o}}return{valid:!1,message:"Must be a string."}},hl=fl("iframe_attrs"),bl=fl("doctype"),vl=fl("document_base_url"),yl=fl("body_id"),Cl=fl("body_class"),wl=fl("content_security_policy"),xl=fl("br_in_pre"),kl=fl("forced_root_block"),El=fl("forced_root_block_attrs"),Sl=fl("newline_behavior"),_l=fl("br_newline_selector"),Nl=fl("no_newline_selector"),Rl=fl("keep_styles"),Al=fl("end_container_on_empty_block"),Ol=fl("automatic_uploads"),Tl=fl("images_reuse_filename"),Bl=fl("images_replace_blob_uris"),Dl=fl("icons"),Pl=fl("icons_url"),Ll=fl("images_upload_url"),Ml=fl("images_upload_base_path"),Il=fl("images_upload_credentials"),Fl=fl("images_upload_handler"),Ul=fl("content_css_cors"),zl=fl("referrer_policy"),jl=fl("language"),Hl=fl("language_url"),$l=fl("indent_use_margin"),Vl=fl("indentation"),ql=fl("content_css"),Wl=fl("content_style"),Kl=fl("font_css"),Gl=fl("directionality"),Yl=fl("inline_boundaries_selector"),Xl=fl("object_resizing"),Ql=fl("resize_img_proportional"),Jl=fl("placeholder"),Zl=fl("event_root"),ed=fl("service_message"),td=fl("theme"),nd=fl("theme_url"),od=fl("model"),rd=fl("model_url"),sd=fl("inline_boundaries"),ad=fl("formats"),id=fl("preview_styles"),ld=fl("format_empty_lines"),dd=fl("format_noneditable_selector"),cd=fl("custom_ui_selector"),ud=fl("inline"),md=fl("hidden_input"),fd=fl("submit_patch"),gd=fl("add_form_submit_trigger"),pd=fl("add_unload_trigger"),hd=fl("custom_undo_redo_levels"),bd=fl("disable_nodechange"),vd=fl("readonly"),yd=fl("content_css_cors"),Cd=fl("plugins"),wd=fl("external_plugins"),xd=fl("block_unsupported_drop"),kd=fl("visual"),Ed=fl("visual_table_class"),Sd=fl("visual_anchor_class"),_d=fl("iframe_aria_text"),Nd=fl("setup"),Rd=fl("init_instance_callback"),Ad=fl("urlconverter_callback"),Od=fl("auto_focus"),Td=fl("browser_spellcheck"),Bd=fl("protect"),Dd=fl("paste_block_drop"),Pd=fl("paste_data_images"),Ld=fl("paste_preprocess"),Md=fl("paste_postprocess"),Id=fl("paste_webkit_styles"),Fd=fl("paste_remove_styles_if_webkit"),Ud=fl("paste_merge_formats"),zd=fl("smart_paste"),jd=fl("paste_as_text"),Hd=fl("paste_tab_spaces"),$d=fl("allow_html_data_urls"),Vd=fl("text_patterns"),qd=fl("text_patterns_lookup"),Wd=fl("noneditable_class"),Kd=fl("editable_class"),Gd=fl("noneditable_regexp"),Yd=fl("preserve_cdata"),Xd=fl("highlight_on_focus"),Qd=fl("xss_sanitization"),Jd=e=>Dt.explode(e.options.get("images_file_types")),Zd=fl("table_tab_navigation"),ec=zo,tc=Yo,nc=e=>{const t=e.parentNode;t&&t.removeChild(e)},oc=e=>{const t=Dr(e);return{count:e.length-t.length,text:t}},rc=e=>{let t;for(;-1!==(t=e.data.lastIndexOf(Tr));)e.deleteData(t,1)},sc=(e,t)=>(ic(e),t),ac=(e,t)=>Ti.isTextPosition(t)?((e,t)=>tc(e)&&t.container()===e?((e,t)=>{const n=oc(e.data.substr(0,t.offset())),o=oc(e.data.substr(t.offset()));return(n.text+o.text).length>0?(rc(e),Ti(e,t.offset()-n.count)):t})(e,t):sc(e,t))(e,t):((e,t)=>t.container()===e.parentNode?((e,t)=>{const n=t.container(),o=((e,t)=>{const n=j(e,t);return-1===n?I.none():I.some(n)})(ce(n.childNodes),e).map((e=>e<t.offset()?Ti(n,t.offset()-1):t)).getOr(t);return ic(e),o})(e,t):sc(e,t))(e,t),ic=e=>{ec(e)&&Fr(e)&&(Ur(e)?e.removeAttribute("data-mce-caret"):nc(e)),tc(e)&&(rc(e),0===e.data.length&&nc(e))},lc=rr,dc=ir,cc=sr,uc=(e,t,n)=>{const o=si(t.getBoundingClientRect(),n);let r,s;if("BODY"===e.tagName){const t=e.ownerDocument.documentElement;r=e.scrollLeft||t.scrollLeft,s=e.scrollTop||t.scrollTop}else{const t=e.getBoundingClientRect();r=e.scrollLeft-t.left,s=e.scrollTop-t.top}o.left+=r,o.right+=r,o.top+=s,o.bottom+=s,o.width=1;let a=t.offsetWidth-t.clientWidth;return a>0&&(n&&(a*=-1),o.left+=a,o.right+=a),o},mc=(e,t,n,o)=>{const r=Ma();let s,a;const i=kl(e),l=e.dom,d=()=>{(e=>{var t,n;const o=Lo(bn(e),"*[contentEditable=false],video,audio,embed,object");for(let e=0;e<o.length;e++){const r=o[e].dom;let s=r.previousSibling;if($r(s)){const e=s.data;1===e.length?null===(t=s.parentNode)||void 0===t||t.removeChild(s):s.deleteData(e.length-1,1)}s=r.nextSibling,Hr(s)&&(1===s.data.length?null===(n=s.parentNode)||void 0===n||n.removeChild(s):s.deleteData(0,1))}})(t),a&&(ic(a),a=null),r.on((e=>{l.remove(e.caret),r.clear()})),s&&(clearInterval(s),s=void 0)};return{show:(e,c)=>{let u;if(d(),cc(c))return null;if(!n(c))return a=((e,t)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createTextNode(Tr),r=e.parentNode;if(t){const t=e.previousSibling;if(Lr(t)){if(Fr(t))return t;if($r(t))return t.splitText(t.data.length-1)}null==r||r.insertBefore(o,e)}else{const t=e.nextSibling;if(Lr(t)){if(Fr(t))return t;if(Hr(t))return t.splitText(1),t}e.nextSibling?null==r||r.insertBefore(o,e.nextSibling):null==r||r.appendChild(o)}return o})(c,e),u=c.ownerDocument.createRange(),gc(a.nextSibling)?(u.setStart(a,0),u.setEnd(a,0)):(u.setStart(a,1),u.setEnd(a,1)),u;{const n=((e,t,n)=>{var o;const r=(null!==(o=t.ownerDocument)&&void 0!==o?o:document).createElement(e);r.setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(Rr().dom);const s=t.parentNode;return n?null==s||s.insertBefore(r,t):t.nextSibling?null==s||s.insertBefore(r,t.nextSibling):null==s||s.appendChild(r),r})(i,c,e),d=uc(t,c,e);l.setStyle(n,"top",d.top),a=n;const m=l.create("div",{class:"mce-visual-caret","data-mce-bogus":"all"});l.setStyles(m,{...d}),l.add(t,m),r.set({caret:m,element:c,before:e}),e&&l.addClass(m,"mce-visual-caret-before"),s=setInterval((()=>{r.on((e=>{o()?l.toggleClass(e.caret,"mce-visual-caret-hidden"):l.addClass(e.caret,"mce-visual-caret-hidden")}))}),500),u=c.ownerDocument.createRange(),u.setStart(n,0),u.setEnd(n,0)}return u},hide:d,getCss:()=>".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}",reposition:()=>{r.on((e=>{const n=uc(t,e.element,e.before);l.setStyles(e.caret,{...n})}))},destroy:()=>clearInterval(s)}},fc=()=>At.browser.isFirefox(),gc=e=>lc(e)||dc(e),pc=e=>(gc(e)||Wo(e)&&fc())&&Rn(bn(e)).exists(eo),hc=or,bc=rr,vc=ir,yc=$o("display","block table table-cell table-caption list-item"),Cc=Fr,wc=Mr,xc=zo,kc=Yo,Ec=es,Sc=e=>e>0,_c=e=>e<0,Nc=(e,t)=>{let n;for(;n=e(t);)if(!wc(n))return n;return null},Rc=(e,t,n,o,r)=>{const s=new Io(e,o),a=bc(e)||wc(e);let i;if(_c(t)){if(a&&(i=Nc(s.prev.bind(s),!0),n(i)))return i;for(;i=Nc(s.prev.bind(s),r);)if(n(i))return i}if(Sc(t)){if(a&&(i=Nc(s.next.bind(s),!0),n(i)))return i;for(;i=Nc(s.next.bind(s),r);)if(n(i))return i}return null},Ac=(e,t)=>{for(;e&&e!==t;){if(yc(e))return e;e=e.parentNode}return null},Oc=(e,t,n)=>Ac(e.container(),n)===Ac(t.container(),n),Tc=(e,t)=>{if(!t)return I.none();const n=t.container(),o=t.offset();return xc(n)?I.from(n.childNodes[o+e]):I.none()},Bc=(e,t)=>{var n;const o=(null!==(n=t.ownerDocument)&&void 0!==n?n:document).createRange();return e?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),o},Dc=(e,t,n)=>Ac(t,e)===Ac(n,e),Pc=(e,t,n)=>{const o=e?"previousSibling":"nextSibling";let r=n;for(;r&&r!==t;){let e=r[o];if(e&&Cc(e)&&(e=e[o]),bc(e)||vc(e)){if(Dc(t,e,r))return e;break}if(Ec(e))break;r=r.parentNode}return null},Lc=O(Bc,!0),Mc=O(Bc,!1),Ic=(e,t,n)=>{let o;const r=O(Pc,!0,t),s=O(Pc,!1,t),a=n.startContainer,i=n.startOffset;if(Mr(a)){const e=kc(a)?a.parentNode:a,t=e.getAttribute("data-mce-caret");if("before"===t&&(o=e.nextSibling,pc(o)))return Lc(o);if("after"===t&&(o=e.previousSibling,pc(o)))return Mc(o)}if(!n.collapsed)return n;if(Yo(a)){if(Cc(a)){if(1===e){if(o=s(a),o)return Lc(o);if(o=r(a),o)return Mc(o)}if(-1===e){if(o=r(a),o)return Mc(o);if(o=s(a),o)return Lc(o)}return n}if($r(a)&&i>=a.data.length-1)return 1===e&&(o=s(a),o)?Lc(o):n;if(Hr(a)&&i<=1)return-1===e&&(o=r(a),o)?Mc(o):n;if(i===a.data.length)return o=s(a),o?Lc(o):n;if(0===i)return o=r(a),o?Mc(o):n}return n},Fc=(e,t)=>Tc(e?0:-1,t).filter(bc),Uc=(e,t,n)=>{const o=Ic(e,t,n);return-1===e?Ti.fromRangeStart(o):Ti.fromRangeEnd(o)},zc=e=>I.from(e.getNode()).map(bn),jc=(e,t)=>{let n=t;for(;n=e(n);)if(n.isVisible())return n;return n},Hc=(e,t)=>{const n=Oc(e,t);return!(n||!tr(e.getNode()))||n};var $c;!function(e){e[e.Backwards=-1]="Backwards",e[e.Forwards=1]="Forwards"}($c||($c={}));const Vc=rr,qc=Yo,Wc=zo,Kc=tr,Gc=es,Yc=e=>Qr(e)||(e=>!!ts(e)&&!X(ce(e.getElementsByTagName("*")),((e,t)=>e||Wr(t)),!1))(e),Xc=ns,Qc=(e,t)=>e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null,Jc=(e,t)=>{if(Sc(e)){if(Gc(t.previousSibling)&&!qc(t.previousSibling))return Ti.before(t);if(qc(t))return Ti(t,0)}if(_c(e)){if(Gc(t.nextSibling)&&!qc(t.nextSibling))return Ti.after(t);if(qc(t))return Ti(t,t.data.length)}return _c(e)?Kc(t)?Ti.before(t):Ti.after(t):Ti.before(t)},Zc=(e,t,n)=>{let o,r,s,a;if(!Wc(n)||!t)return null;if(t.isEqual(Ti.after(n))&&n.lastChild){if(a=Ti.after(n.lastChild),_c(e)&&Gc(n.lastChild)&&Wc(n.lastChild))return Kc(n.lastChild)?Ti.before(n.lastChild):a}else a=t;const i=a.container();let l=a.offset();if(qc(i)){if(_c(e)&&l>0)return Ti(i,--l);if(Sc(e)&&l<i.length)return Ti(i,++l);o=i}else{if(_c(e)&&l>0&&(r=Qc(i,l-1),Gc(r)))return!Yc(r)&&(s=Rc(r,e,Xc,r),s)?qc(s)?Ti(s,s.data.length):Ti.after(s):qc(r)?Ti(r,r.data.length):Ti.before(r);if(Sc(e)&&l<i.childNodes.length&&(r=Qc(i,l),Gc(r)))return Kc(r)?((e,t)=>{const n=t.nextSibling;return n&&Gc(n)?qc(n)?Ti(n,0):Ti.before(n):Zc($c.Forwards,Ti.after(t),e)})(n,r):!Yc(r)&&(s=Rc(r,e,Xc,r),s)?qc(s)?Ti(s,0):Ti.before(s):qc(r)?Ti(r,0):Ti.after(r);o=r||a.getNode()}if(o&&(Sc(e)&&a.isAtEnd()||_c(e)&&a.isAtStart())&&(o=Rc(o,e,M,n,!0),Xc(o,n)))return Jc(e,o);r=o?Rc(o,e,Xc,n):o;const d=De(G(((e,t)=>{const n=[];let o=e;for(;o&&o!==t;)n.push(o),o=o.parentNode;return n})(i,n),Vc));return!d||r&&d.contains(r)?r?Jc(e,r):null:(a=Sc(e)?Ti.after(d):Ti.before(d),a)},eu=e=>({next:t=>Zc($c.Forwards,t,e),prev:t=>Zc($c.Backwards,t,e)}),tu=e=>Ti.isTextPosition(e)?0===e.offset():es(e.getNode()),nu=e=>{if(Ti.isTextPosition(e)){const t=e.container();return e.offset()===t.data.length}return es(e.getNode(!0))},ou=(e,t)=>!Ti.isTextPosition(e)&&!Ti.isTextPosition(t)&&e.getNode()===t.getNode(!0),ru=(e,t,n)=>{const o=eu(t);return I.from(e?o.next(n):o.prev(n))},su=(e,t,n)=>ru(e,t,n).bind((o=>Oc(n,o,t)&&((e,t,n)=>{return e?!ou(t,n)&&(o=t,!(!Ti.isTextPosition(o)&&tr(o.getNode())))&&nu(t)&&tu(n):!ou(n,t)&&tu(t)&&nu(n);var o})(e,n,o)?ru(e,t,o):I.some(o))),au=(e,t,n,o)=>su(e,t,n).bind((n=>o(n)?au(e,t,n,o):I.some(n))),iu=(e,t)=>{const n=e?t.firstChild:t.lastChild;return Yo(n)?I.some(Ti(n,e?0:n.data.length)):n?es(n)?I.some(e?Ti.before(n):tr(o=n)?Ti.before(o):Ti.after(o)):((e,t,n)=>{const o=e?Ti.before(n):Ti.after(n);return ru(e,t,o)})(e,t,n):I.none();var o},lu=O(ru,!0),du=O(ru,!1),cu=O(iu,!0),uu=O(iu,!1),mu="_mce_caret",fu=e=>zo(e)&&e.id===mu,gu=(e,t)=>{let n=t;for(;n&&n!==e;){if(fu(n))return n;n=n.parentNode}return null},pu=e=>ke(e,"name"),hu=e=>Dt.isArray(e.start),bu=e=>!(!pu(e)&&b(e.forward))||e.forward,vu=(e,t)=>(zo(t)&&e.isBlock(t)&&!t.innerHTML&&(t.innerHTML='<br data-mce-bogus="1" />'),t),yu=(e,t)=>uu(e).fold(L,(e=>(t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0))),Cu=(e,t,n)=>!(!(e=>!e.hasChildNodes())(t)||!gu(e,t)||(((e,t)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createTextNode(Tr);e.appendChild(o),t.setStart(o,0),t.setEnd(o,0)})(t,n),0)),wu=(e,t,n,o)=>{const r=n[t?"start":"end"],s=e.getRoot();if(r){let e=s,n=r[0];for(let t=r.length-1;e&&t>=1;t--){const n=e.childNodes;if(Cu(s,e,o))return!0;if(r[t]>n.length-1)return!!Cu(s,e,o)||yu(e,o);e=n[r[t]]}Yo(e)&&(n=Math.min(r[0],e.data.length)),zo(e)&&(n=Math.min(r[0],e.childNodes.length)),t?o.setStart(e,n):o.setEnd(e,n)}return!0},xu=e=>Yo(e)&&e.data.length>0,ku=(e,t,n)=>{const o=e.get(n.id+"_"+t),r=null==o?void 0:o.parentNode,s=n.keep;if(o&&r){let a,i;if("start"===t?s?o.hasChildNodes()?(a=o.firstChild,i=1):xu(o.nextSibling)?(a=o.nextSibling,i=0):xu(o.previousSibling)?(a=o.previousSibling,i=o.previousSibling.data.length):(a=r,i=e.nodeIndex(o)+1):(a=r,i=e.nodeIndex(o)):s?o.hasChildNodes()?(a=o.firstChild,i=1):xu(o.previousSibling)?(a=o.previousSibling,i=o.previousSibling.data.length):(a=r,i=e.nodeIndex(o)):(a=r,i=e.nodeIndex(o)),!s){const r=o.previousSibling,s=o.nextSibling;let l;for(Dt.each(Dt.grep(o.childNodes),(e=>{Yo(e)&&(e.data=e.data.replace(/\uFEFF/g,""))}));l=e.get(n.id+"_"+t);)e.remove(l,!0);if(Yo(s)&&Yo(r)&&!At.browser.isOpera()){const t=r.data.length;r.appendData(s.data),e.remove(s),a=r,i=t}}return I.some(Ti(a,i))}return I.none()},Eu=(e,t,n)=>((e,t,n=!1)=>2===t?Yi(Dr,n,e):3===t?(e=>{const t=e.getRng();return{start:ji(e.dom.getRoot(),Ti.fromRangeStart(t)),end:ji(e.dom.getRoot(),Ti.fromRangeEnd(t)),forward:e.isForward()}})(e):t?(e=>({rng:e.getRng(),forward:e.isForward()}))(e):Qi(e,!1))(e,t,n),Su=(e,t)=>{((e,t)=>{const n=e.dom;if(t){if(hu(t))return((e,t)=>{const n=e.createRng();return wu(e,!0,t,n)&&wu(e,!1,t,n)?I.some({range:n,forward:bu(t)}):I.none()})(n,t);if((e=>m(e.start))(t))return((e,t)=>{const n=I.from(Hi(e.getRoot(),t.start)),o=I.from(Hi(e.getRoot(),t.end));return Lt(n,o,((n,o)=>{const r=e.createRng();return r.setStart(n.container(),n.offset()),r.setEnd(o.container(),o.offset()),{range:r,forward:bu(t)}}))})(n,t);if((e=>ke(e,"id"))(t))return((e,t)=>{const n=ku(e,"start",t),o=ku(e,"end",t);return Lt(n,o.or(n),((n,o)=>{const r=e.createRng();return r.setStart(vu(e,n.container()),n.offset()),r.setEnd(vu(e,o.container()),o.offset()),{range:r,forward:bu(t)}}))})(n,t);if(pu(t))return((e,t)=>I.from(e.select(t.name)[t.index]).map((t=>{const n=e.createRng();return n.selectNode(t),{range:n,forward:!0}})))(n,t);if((e=>ke(e,"rng"))(t))return I.some({range:t.rng,forward:bu(t)})}return I.none()})(e,t).each((({range:t,forward:n})=>{e.setRng(t,n)}))},_u=e=>zo(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type"),Nu=(cr,e=>"\xa0"===e);const Ru=e=>""!==e&&-1!==" \f\n\r\t\v".indexOf(e),Au=e=>!Ru(e)&&!Nu(e)&&!ur(e),Ou=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Tu=e=>(e=>{return{value:(t=e,ze(t,"#").toUpperCase())};var t})(Ou(e.red)+Ou(e.green)+Ou(e.blue)),Bu=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,Du=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,Pu=(e,t,n,o)=>({red:e,green:t,blue:n,alpha:o}),Lu=(e,t,n,o)=>{const r=parseInt(e,10),s=parseInt(t,10),a=parseInt(n,10),i=parseFloat(o);return Pu(r,s,a,i)},Mu=e=>(e=>{if("transparent"===e)return I.some(Pu(0,0,0,0));const t=Bu.exec(e);if(null!==t)return I.some(Lu(t[1],t[2],t[3],"1"));const n=Du.exec(e);return null!==n?I.some(Lu(n[1],n[2],n[3],n[4])):I.none()})(e).map(Tu).map((e=>"#"+e.value)).getOr(e),Iu=e=>{const t=[];if(e)for(let n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},Fu=(e,t)=>{const n=Lo(t,"td[data-mce-selected],th[data-mce-selected]");return n.length>0?n:(e=>G((e=>te(e,(e=>{const t=ci(e);return t?[bn(t)]:[]})))(e),Er))(e)},Uu=e=>Fu(Iu(e.selection.getSel()),bn(e.getBody())),zu=(e,t)=>Qn(e,"table",t),ju=e=>Mn(e).fold(N([e]),(t=>[e].concat(ju(t)))),Hu=e=>In(e).fold(N([e]),(t=>"br"===jt(t)?On(t).map((t=>[e].concat(Hu(t)))).getOr([]):[e].concat(Hu(t)))),$u=(e,t)=>Lt((e=>{const t=e.startContainer,n=e.startOffset;return Yo(t)?0===n?I.some(bn(t)):I.none():I.from(t.childNodes[n]).map(bn)})(t),(e=>{const t=e.endContainer,n=e.endOffset;return Yo(t)?n===t.data.length?I.some(bn(t)):I.none():I.from(t.childNodes[n-1]).map(bn)})(t),((t,n)=>{const o=J(ju(e),O(xn,t)),r=J(Hu(e),O(xn,n));return o.isSome()&&r.isSome()})).getOr(!1),Vu=(e,t,n,o)=>{const r=n,s=new Io(n,r),a=ye(e.schema.getMoveCaretBeforeOnEnterElements(),((e,t)=>!H(["td","th","table"],t.toLowerCase())));let i=n;do{if(Yo(i)&&0!==Dt.trim(i.data).length)return void(o?t.setStart(i,0):t.setEnd(i,i.data.length));if(a[i.nodeName])return void(o?t.setStartBefore(i):"BR"===i.nodeName?t.setEndBefore(i):t.setEndAfter(i))}while(i=o?s.next():s.prev());"BODY"===r.nodeName&&(o?t.setStart(r,0):t.setEnd(r,r.childNodes.length))},qu=e=>{const t=e.selection.getSel();return C(t)&&t.rangeCount>0},Wu=(e,t)=>{const n=Uu(e);n.length>0?q(n,(n=>{const o=n.dom,r=e.dom.createRng();r.setStartBefore(o),r.setEndAfter(o),t(r,!0)})):t(e.selection.getRng(),!1)},Ku=(e,t,n)=>{const o=Qi(e,t);n(o),e.moveToBookmark(o)},Gu=e=>x(null==e?void 0:e.nodeType),Yu=e=>zo(e)&&!_u(e)&&!fu(e)&&!qo(e),Xu=e=>!0===e.isContentEditable,Qu=(e,t,n)=>{const{selection:o,dom:r}=e,s=o.getNode(),a=rr(s);Ku(o,!0,(()=>{t()})),a&&rr(s)&&r.isChildOf(s,e.getBody())?e.selection.select(s):n(o.getStart())&&Ju(r,o)},Ju=(e,t)=>{var n,o;const r=t.getRng(),{startContainer:s,startOffset:a}=r;if(!((e,t)=>{if(Yu(t)&&!/^(TD|TH)$/.test(t.nodeName)){const n=e.getAttrib(t,"data-mce-selected"),o=parseInt(n,10);return!isNaN(o)&&o>0}return!1})(e,t.getNode())&&zo(s)){const i=s.childNodes,l=e.getRoot();let d;if(a<i.length){const t=i[a];d=new Io(t,null!==(n=e.getParent(t,e.isBlock))&&void 0!==n?n:l)}else{const t=i[i.length-1];d=new Io(t,null!==(o=e.getParent(t,e.isBlock))&&void 0!==o?o:l),d.next(!0)}for(let n=d.current();n;n=d.next()){if("false"===e.getContentEditable(n))return;if(Yo(n)&&!nm(n))return r.setStart(n,0),void t.setRng(r)}}},Zu=(e,t,n)=>{if(e){const o=t?"nextSibling":"previousSibling";for(e=n?e:e[o];e;e=e[o])if(zo(e)||!nm(e))return e}},em=(e,t)=>!!e.getTextBlockElements()[t.nodeName.toLowerCase()]||ks(e,t),tm=(e,t,n)=>e.schema.isValidChild(t,n),nm=(e,t=!1)=>{if(C(e)&&Yo(e)){const n=t?e.data.replace(/ /g,"\xa0"):e.data;return rs(n)}return!1},om=(e,t)=>{const n=e.dom;return Yu(t)&&"false"===n.getContentEditable(t)&&((e,t)=>{const n="[data-mce-cef-wrappable]",o=dd(e),r=Ye(o)?n:`${n},${o}`;return Cn(bn(t),r)})(e,t)&&0===n.select('[contenteditable="true"]',t).length},rm=(e,t)=>w(e)?e(t):(C(t)&&(e=e.replace(/%(\w+)/g,((e,n)=>t[n]||e))),e),sm=(e,t)=>(t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()),am=(e,t)=>{if(y(e))return null;{let n=String(e);return"color"!==t&&"backgroundColor"!==t||(n=Mu(n)),"fontWeight"===t&&700===e&&(n="bold"),"fontFamily"===t&&(n=n.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),n}},im=(e,t,n)=>{const o=e.getStyle(t,n);return am(o,n)},lm=(e,t)=>{let n;return e.getParent(t,(t=>!!zo(t)&&(n=e.getStyle(t,"text-decoration"),!!n&&"none"!==n))),n},dm=(e,t,n)=>e.getParents(t,n,e.getRoot()),cm=(e,t,n)=>{const o=e.formatter.get(t);return C(o)&&$(o,n)},um=e=>Ee(e,"block"),mm=e=>Ee(e,"selector"),fm=e=>Ee(e,"inline"),gm=e=>mm(e)&&!1!==e.expand&&!fm(e),pm=_u,hm=dm,bm=nm,vm=em,ym=(e,t)=>{let n=t;for(;n;){if(zo(n)&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},Cm=(e,t,n,o)=>{const r=t.data;if(e){for(let e=n;e>0;e--)if(o(r.charAt(e-1)))return e}else for(let e=n;e<r.length;e++)if(o(r.charAt(e)))return e;return-1},wm=(e,t,n)=>Cm(e,t,n,(e=>Nu(e)||Ru(e))),xm=(e,t,n)=>Cm(e,t,n,Au),km=(e,t,n,o,r,s)=>{let a;const i=e.getParent(n,e.isBlock)||t,l=(t,n,o)=>{const s=ni(e),l=r?s.backwards:s.forwards;return I.from(l(t,n,((e,t)=>pm(e.parentNode)?-1:(a=e,o(r,e,t))),i))};return l(n,o,wm).bind((e=>s?l(e.container,e.offset+(r?-1:0),xm):I.some(e))).orThunk((()=>a?I.some({container:a,offset:r?0:a.length}):I.none()))},Em=(e,t,n,o,r)=>{const s=o[r];Yo(o)&&Ye(o.data)&&s&&(o=s);const a=hm(e,o);for(let o=0;o<a.length;o++)for(let r=0;r<t.length;r++){const s=t[r];if((!C(s.collapsed)||s.collapsed===n.collapsed)&&mm(s)&&e.is(a[o],s.selector))return a[o]}return o},Sm=(e,t,n,o)=>{var r;let s=n;const a=e.getRoot(),i=t[0];if(um(i)&&(s=i.wrapper?null:e.getParent(n,i.block,a)),!s){const t=null!==(r=e.getParent(n,"LI,TD,TH"))&&void 0!==r?r:a;s=e.getParent(Yo(n)?n.parentNode:n,(t=>t!==a&&vm(e.schema,t)),t)}if(s&&um(i)&&i.wrapper&&(s=hm(e,s,"ul,ol").reverse()[0]||s),!s)for(s=n;s&&s[o]&&!e.isBlock(s[o])&&(s=s[o],!sm(s,"br")););return s||n},_m=(e,t,n,o)=>{const r=n.parentNode;return!C(n[o])&&(!(r!==t&&!y(r)&&!e.isBlock(r))||_m(e,t,r,o))},Nm=(e,t,n,o,r)=>{let s=n;const a=r?"previousSibling":"nextSibling",i=e.getRoot();if(Yo(n)&&!bm(n)&&(r?o>0:o<n.data.length))return n;for(;s;){if(!t[0].block_expand&&e.isBlock(s))return s;for(let t=s[a];t;t=t[a]){const n=Yo(t)&&!_m(e,i,t,a);if(!pm(t)&&(!tr(l=t)||!l.getAttribute("data-mce-bogus")||l.nextSibling)&&!bm(t,n))return s}if(s===i||s.parentNode===i){n=s;break}s=s.parentNode}var l;return n},Rm=e=>pm(e.parentNode)||pm(e),Am=(e,t,n,o=!1)=>{let{startContainer:r,startOffset:s,endContainer:a,endOffset:i}=t;const l=n[0];return zo(r)&&r.hasChildNodes()&&(r=ui(r,s),Yo(r)&&(s=0)),zo(a)&&a.hasChildNodes()&&(a=ui(a,t.collapsed?i:i-1),Yo(a)&&(i=a.data.length)),r=ym(e,r),a=ym(e,a),Rm(r)&&(r=pm(r)?r:r.parentNode,r=t.collapsed?r.previousSibling||r:r.nextSibling||r,Yo(r)&&(s=t.collapsed?r.length:0)),Rm(a)&&(a=pm(a)?a:a.parentNode,a=t.collapsed?a.nextSibling||a:a.previousSibling||a,Yo(a)&&(i=t.collapsed?0:a.length)),t.collapsed&&(km(e,e.getRoot(),r,s,!0,o).each((({container:e,offset:t})=>{r=e,s=t})),km(e,e.getRoot(),a,i,!1,o).each((({container:e,offset:t})=>{a=e,i=t}))),(fm(l)||l.block_expand)&&(fm(l)&&Yo(r)&&0!==s||(r=Nm(e,n,r,s,!0)),fm(l)&&Yo(a)&&i!==a.data.length||(a=Nm(e,n,a,i,!1))),gm(l)&&(r=Em(e,n,t,r,"previousSibling"),a=Em(e,n,t,a,"nextSibling")),(um(l)||mm(l))&&(r=Sm(e,n,r,"previousSibling"),a=Sm(e,n,a,"nextSibling"),um(l)&&(e.isBlock(r)||(r=Nm(e,n,r,s,!0)),e.isBlock(a)||(a=Nm(e,n,a,i,!1)))),zo(r)&&r.parentNode&&(s=e.nodeIndex(r),r=r.parentNode),zo(a)&&a.parentNode&&(i=e.nodeIndex(a)+1,a=a.parentNode),{startContainer:r,startOffset:s,endContainer:a,endOffset:i}},Om=(e,t,n)=>{var o;const r=t.startOffset,s=ui(t.startContainer,r),a=t.endOffset,i=ui(t.endContainer,a-1),l=e=>{const t=e[0];Yo(t)&&t===s&&r>=t.data.length&&e.splice(0,1);const n=e[e.length-1];return 0===a&&e.length>0&&n===i&&Yo(n)&&e.splice(e.length-1,1),e},d=(e,t,n)=>{const o=[];for(;e&&e!==n;e=e[t])o.push(e);return o},c=(t,n)=>e.getParent(t,(e=>e.parentNode===n),n),u=(e,t,o)=>{const r=o?"nextSibling":"previousSibling";for(let s=e,a=s.parentNode;s&&s!==t;s=a){a=s.parentNode;const t=d(s===e?s:s[r],r);t.length&&(o||t.reverse(),n(l(t)))}};if(s===i)return n(l([s]));const m=null!==(o=e.findCommonAncestor(s,i))&&void 0!==o?o:e.getRoot();if(e.isChildOf(s,i))return u(s,m,!0);if(e.isChildOf(i,s))return u(i,m);const f=c(s,m)||s,g=c(i,m)||i;u(s,f,!0);const p=d(f===s?f:f.nextSibling,"nextSibling",g===i?g.nextSibling:g);p.length&&n(l(p)),u(i,g)},Tm=['pre[class*=language-][contenteditable="false"]',"figure.image","div[data-ephox-embed-iri]","div.tiny-pageembed","div.mce-toc","div[data-mce-toc]"],Bm=(e,t,n,o,r,s)=>{const{uid:a=t,...i}=n;dn(e,Ua()),Xt(e,`${ja()}`,a),Xt(e,`${za()}`,o);const{attributes:l={},classes:d=[]}=r(a,i);if(Qt(e,l),((e,t)=>{q(t,(t=>{dn(e,t)}))})(e,d),s){d.length>0&&Xt(e,`${$a()}`,d.join(","));const t=me(l);t.length>0&&Xt(e,`${Va()}`,t.join(","))}},Dm=(e,t,n,o,r)=>{const s=pn("span",e);return Bm(s,t,n,o,r,!1),s},Pm=(e,t,n,o,r,s)=>{const a=[],i=Dm(e.getDoc(),n,s,o,r),l=Ma(),d=()=>{l.clear()},c=e=>{q(e,u)},u=t=>{switch(((e,t,n,o)=>Nn(t).fold((()=>"skipping"),(r=>"br"===o||(e=>qt(e)&&fr(e)===Tr)(t)?"valid":(e=>Vt(e)&&mn(e,Ua()))(t)?"existing":fu(t.dom)?"caret":$(Tm,(e=>Cn(t,e)))?"valid-block":tm(e,n,o)&&tm(e,jt(r),n)?"valid":"invalid-child")))(e,t,"span",jt(t))){case"invalid-child":{d();const e=Pn(t);c(e),d();break}case"valid-block":d(),Bm(t,n,s,o,r,!0);break;case"valid":{const e=l.get().getOrThunk((()=>{const e=Za(i);return a.push(e),l.set(e),e}));ho(t,e);break}}};return Om(e.dom,t,(e=>{d(),(e=>{const t=V(e,bn);c(t)})(e)})),a},Lm=e=>{const t=(()=>{const e={};return{register:(t,n)=>{e[t]={name:t,settings:n}},lookup:t=>xe(e,t).map((e=>e.settings)),getNames:()=>me(e)}})();((e,t)=>{const n=za(),o=e=>I.from(e.attr(n)).bind(t.lookup),r=e=>{var t,n;e.attr(ja(),null),e.attr(za(),null),e.attr(Ha(),null);const o=I.from(e.attr(Va())).map((e=>e.split(","))).getOr([]),r=I.from(e.attr($a())).map((e=>e.split(","))).getOr([]);q(o,(t=>e.attr(t,null)));const s=null!==(n=null===(t=e.attr("class"))||void 0===t?void 0:t.split(" "))&&void 0!==n?n:[],a=re(s,[Ua()].concat(r));e.attr("class",a.length>0?a.join(" "):null),e.attr($a(),null),e.attr(Va(),null)};e.serializer.addTempAttr(Ha()),e.serializer.addAttributeFilter(n,(e=>{for(const t of e)o(t).each((e=>{!1===e.persistent&&("span"===t.name?t.unwrap():r(t))}))}))})(e,t);const n=((e,t)=>{const n=Aa({}),o=()=>({listeners:[],previous:Ma()}),r=(e,t)=>{s(e,(e=>(t(e),e)))},s=(e,t)=>{const r=n.get(),s=t(xe(r,e).getOrThunk(o));r[e]=s,n.set(r)},a=(t,n)=>{q(Ga(e,t),(e=>{n?Xt(e,Ha(),"true"):tn(e,Ha())}))},i=Fa((()=>{const n=ae(t.getNames());q(n,(t=>{s(t,(n=>{const o=n.previous.get();return Wa(e,I.some(t)).fold((()=>{o.each((e=>{(e=>{r(e,(t=>{q(t.listeners,(t=>t(!1,e)))}))})(t),n.previous.clear(),a(e,!1)}))}),(({uid:e,name:t,elements:s})=>{Pt(o,e)||(o.each((e=>a(e,!1))),((e,t,n)=>{r(e,(o=>{q(o.listeners,(o=>o(!0,e,{uid:t,nodes:V(n,(e=>e.dom))})))}))})(t,e,s),n.previous.set(e),a(e,!0))})),{previous:n.previous,listeners:n.listeners}}))}))}),30);return e.on("remove",(()=>{i.cancel()})),e.on("NodeChange",(()=>{i.throttle()})),{addListener:(e,t)=>{s(e,(e=>({previous:e.previous,listeners:e.listeners.concat([t])})))}}})(e,t),o=Gt("span"),r=e=>{q(e,(e=>{o(e)?Co(e):(e=>{un(e,Ua()),tn(e,`${ja()}`),tn(e,`${za()}`),tn(e,`${Ha()}`);const t=Zt(e,`${Va()}`).map((e=>e.split(","))).getOr([]),n=Zt(e,`${$a()}`).map((e=>e.split(","))).getOr([]);var o;q(t,(t=>tn(e,t))),o=e,q(n,(e=>{un(o,e)})),tn(e,`${$a()}`),tn(e,`${Va()}`)})(e)}))};return{register:(e,n)=>{t.register(e,n)},annotate:(n,o)=>{t.lookup(n).each((t=>{((e,t,n,o)=>{e.undoManager.transact((()=>{const r=e.selection,s=r.getRng(),a=Uu(e).length>0,i=Qa("mce-annotation");if(s.collapsed&&!a&&((e,t)=>{const n=Am(e.dom,t,[{inline:"span"}]);t.setStart(n.startContainer,n.startOffset),t.setEnd(n.endContainer,n.endOffset),e.selection.setRng(t)})(e,s),r.getRng().collapsed&&!a){const s=Dm(e.getDoc(),i,o,t,n.decorate);ko(s,cr),r.getRng().insertNode(s.dom),r.select(s.dom)}else Ku(r,!1,(()=>{Wu(e,(r=>{Pm(e,r,i,t,n.decorate,o)}))}))}))})(e,n,t,o)}))},annotationChanged:(e,t)=>{n.addListener(e,t)},remove:t=>{Wa(e,I.some(t)).each((({elements:t})=>{const n=e.selection.getBookmark();r(t),e.selection.moveToBookmark(n)}))},removeAll:t=>{const n=e.selection.getBookmark();ge(Ya(e,t),((e,t)=>{r(e)})),e.selection.moveToBookmark(n)},getAll:t=>{const n=Ya(e,t);return pe(n,(e=>V(e,(e=>e.dom))))}}},Mm=e=>({getBookmark:O(Eu,e),moveToBookmark:O(Su,e)});Mm.isBookmarkNode=_u;const Im=(e,t,n)=>!n.collapsed&&$(n.getClientRects(),(n=>((e,t,n)=>t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom)(n,e,t))),Fm=(e,t,n)=>{e.dispatch(t,n)},Um=(e,t,n,o)=>{e.dispatch("FormatApply",{format:t,node:n,vars:o})},zm=(e,t,n,o)=>{e.dispatch("FormatRemove",{format:t,node:n,vars:o})},jm=(e,t)=>e.dispatch("SetContent",t),Hm=(e,t)=>e.dispatch("GetContent",t),$m=(e,t)=>e.dispatch("PastePlainTextToggle",{state:t}),Vm={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,ESC:27,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,modifierPressed:e=>e.shiftKey||e.ctrlKey||e.altKey||Vm.metaKeyPressed(e),metaKeyPressed:e=>At.os.isMacOS()||At.os.isiOS()?e.metaKey:e.ctrlKey&&!e.altKey},qm="data-mce-selected",Wm=Math.abs,Km=Math.round,Gm={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]},Ym=(e,t)=>{const n=t.dom,o=t.getDoc(),r=document,s=t.getBody();let a,i,l,d,c,u,m,f,g,p,h,b,v,y,w;const x=e=>C(e)&&(nr(e)||n.is(e,"figure.image")),k=e=>ir(e)||n.hasClass(e,"mce-preview-object"),E=e=>{const n=e.target;((e,t)=>{if((e=>"longpress"===e.type||0===e.type.indexOf("touch"))(e)){const n=e.touches[0];return x(e.target)&&!Im(n.clientX,n.clientY,t)}return x(e.target)&&!Im(e.clientX,e.clientY,t)})(e,t.selection.getRng())&&!e.isDefaultPrevented()&&t.selection.select(n)},S=e=>n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?[e,e.firstElementChild]:n.is(e,"figure.image")?[e.querySelector("img")]:[e],_=e=>{const o=Xl(t);return!!o&&"false"!==e.getAttribute("data-mce-resize")&&e!==t.getBody()&&(n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?Cn(bn(e.firstElementChild),o):Cn(bn(e),o))},N=(e,o,r)=>{if(C(r)){const s=S(e);q(s,(e=>{e.style[o]||!t.schema.isValid(e.nodeName.toLowerCase(),o)?n.setStyle(e,o,r):n.setAttrib(e,o,""+r)}))}},R=(e,t,n)=>{N(e,"width",t),N(e,"height",n)},A=e=>{let o,r,c,C,E;o=e.screenX-u,r=e.screenY-m,b=o*d[2]+f,v=r*d[3]+g,b=b<5?5:b,v=v<5?5:v,c=(x(a)||k(a))&&!1!==Ql(t)?!Vm.modifierPressed(e):Vm.modifierPressed(e),c&&(Wm(o)>Wm(r)?(v=Km(b*p),b=Km(v/p)):(b=Km(v/p),v=Km(b*p))),R(i,b,v),C=d.startPos.x+o,E=d.startPos.y+r,C=C>0?C:0,E=E>0?E:0,n.setStyles(l,{left:C,top:E,display:"block"}),l.innerHTML=b+" &times; "+v,d[2]<0&&i.clientWidth<=b&&n.setStyle(i,"left",void 0+(f-b)),d[3]<0&&i.clientHeight<=v&&n.setStyle(i,"top",void 0+(g-v)),o=s.scrollWidth-y,r=s.scrollHeight-w,o+r!==0&&n.setStyles(l,{left:C-o,top:E-r}),h||(((e,t,n,o,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:n,height:o,origin:r})})(t,a,f,g,"corner-"+d.name),h=!0)},O=()=>{const e=h;h=!1,e&&(N(a,"width",b),N(a,"height",v)),n.unbind(o,"mousemove",A),n.unbind(o,"mouseup",O),r!==o&&(n.unbind(r,"mousemove",A),n.unbind(r,"mouseup",O)),n.remove(i),n.remove(l),n.remove(c),T(a),e&&(((e,t,n,o,r)=>{e.dispatch("ObjectResized",{target:t,width:n,height:o,origin:r})})(t,a,b,v,"corner-"+d.name),n.setAttrib(a,"style",n.getAttrib(a,"style"))),t.nodeChanged()},T=e=>{M();const h=n.getPos(e,s),C=h.x,x=h.y,E=e.getBoundingClientRect(),N=E.width||E.right-E.left,T=E.height||E.bottom-E.top;a!==e&&(D(),a=e,b=v=0);const B=t.dispatch("ObjectSelected",{target:e});_(e)&&!B.isDefaultPrevented()?ge(Gm,((e,t)=>{let h=n.get("mceResizeHandle"+t);h&&n.remove(h),h=n.add(s,"div",{id:"mceResizeHandle"+t,"data-mce-bogus":"all",class:"mce-resizehandle",unselectable:!0,style:"cursor:"+t+"-resize; margin:0; padding:0"}),n.bind(h,"mousedown",(h=>{h.stopImmediatePropagation(),h.preventDefault(),(h=>{const b=S(a)[0];var v;u=h.screenX,m=h.screenY,f=b.clientWidth,g=b.clientHeight,p=g/f,d=e,d.name=t,d.startPos={x:N*e[0]+C,y:T*e[1]+x},y=s.scrollWidth,w=s.scrollHeight,c=n.add(s,"div",{class:"mce-resize-backdrop","data-mce-bogus":"all"}),n.setStyles(c,{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"}),i=k(v=a)?n.create("img",{src:At.transparentSrc}):v.cloneNode(!0),n.addClass(i,"mce-clonedresizable"),n.setAttrib(i,"data-mce-bogus","all"),i.contentEditable="false",n.setStyles(i,{left:C,top:x,margin:0}),R(i,N,T),i.removeAttribute(qm),s.appendChild(i),n.bind(o,"mousemove",A),n.bind(o,"mouseup",O),r!==o&&(n.bind(r,"mousemove",A),n.bind(r,"mouseup",O)),l=n.add(s,"div",{class:"mce-resize-helper","data-mce-bogus":"all"},f+" &times; "+g)})(h)})),e.elm=h,n.setStyles(h,{left:N*e[0]+C-h.offsetWidth/2,top:T*e[1]+x-h.offsetHeight/2})})):D(!1)},B=Ia(T,0),D=(e=!0)=>{B.cancel(),M(),a&&e&&a.removeAttribute(qm),ge(Gm,((e,t)=>{const o=n.get("mceResizeHandle"+t);o&&(n.unbind(o),n.remove(o))}))},P=(e,t)=>n.isChildOf(e,t),L=o=>{if(h||t.removed||t.composing)return;const r="mousedown"===o.type?o.target:e.getNode(),a=Zn(bn(r),"table,img,figure.image,hr,video,span.mce-preview-object").map((e=>e.dom)).filter((e=>n.isEditable(e.parentElement))).getOrUndefined(),i=C(a)?n.getAttrib(a,qm,"1"):"1";if(q(n.select("img[data-mce-selected],hr[data-mce-selected]"),(e=>{e.removeAttribute(qm)})),C(a)&&P(a,s)){I();const t=e.getStart(!0);if(P(t,a)&&P(e.getEnd(!0),a))return n.setAttrib(a,qm,i),void B.throttle(a)}D()},M=()=>{ge(Gm,(e=>{e.elm&&(n.unbind(e.elm),delete e.elm)}))},I=()=>{try{t.getDoc().execCommand("enableObjectResizing",!1,"false")}catch(e){}};return t.on("init",(()=>{I(),t.on("NodeChange ResizeEditor ResizeWindow ResizeContent drop",L),t.on("keyup compositionend",(e=>{a&&"TABLE"===a.nodeName&&L(e)})),t.on("hide blur",D),t.on("contextmenu longpress",E,!0)})),t.on("remove",M),{isResizable:_,showResizeRect:T,hideResizeRect:D,updateResizeRect:L,destroy:()=>{B.cancel(),a=i=c=null}}},Xm=(e,t,n)=>{const o=e.document.createRange();var r;return r=o,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,n)=>{e.setEnd(t.dom,n)}),(t=>{e.setEndAfter(t.dom)}))})(o,n),o},Qm=(e,t,n,o,r)=>{const s=e.document.createRange();return s.setStart(t.dom,n),s.setEnd(o.dom,r),s},Jm=nl([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Zm=(e,t,n)=>t(bn(n.startContainer),n.startOffset,bn(n.endContainer),n.endOffset);Jm.ltr,Jm.rtl;const ef=(e,t,n,o)=>({start:e,soffset:t,finish:n,foffset:o}),tf=document.caretPositionFromPoint?(e,t,n)=>{var o,r;return I.from(null===(r=(o=e.dom).caretPositionFromPoint)||void 0===r?void 0:r.call(o,t,n)).bind((t=>{if(null===t.offsetNode)return I.none();const n=e.dom.createRange();return n.setStart(t.offsetNode,t.offset),n.collapse(),I.some(n)}))}:document.caretRangeFromPoint?(e,t,n)=>{var o,r;return I.from(null===(r=(o=e.dom).caretRangeFromPoint)||void 0===r?void 0:r.call(o,t,n))}:I.none,nf=nl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),of={before:nf.before,on:nf.on,after:nf.after,cata:(e,t,n,o)=>e.fold(t,n,o),getStart:e=>e.fold(R,R,R)},rf=nl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),sf={domRange:rf.domRange,relative:rf.relative,exact:rf.exact,exactFromRange:e=>rf.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>bn(e.startContainer),relative:(e,t)=>of.getStart(e),exact:(e,t,n,o)=>e}))(e);return _n(t)},range:ef},af=(e,t)=>{const n=jt(e);return"input"===n?of.after(e):H(["br","img"],n)?0===t?of.before(e):of.after(e):of.on(e,t)},lf=(e,t)=>{const n=e.fold(of.before,af,of.after),o=t.fold(of.before,af,of.after);return sf.relative(n,o)},df=(e,t,n,o)=>{const r=af(e,t),s=af(n,o);return sf.relative(r,s)},cf=(e,t)=>{const n=(t||document).createDocumentFragment();return q(e,(e=>{n.appendChild(e.dom)})),bn(n)},uf=e=>{const t=sf.getWin(e).dom,n=(e,n,o,r)=>Qm(t,e,n,o,r),o=(e=>e.match({domRange:e=>{const t=bn(e.startContainer),n=bn(e.endContainer);return df(t,e.startOffset,n,e.endOffset)},relative:lf,exact:df}))(e);return((e,t)=>{const n=((e,t)=>t.match({domRange:e=>({ltr:N(e),rtl:I.none}),relative:(t,n)=>({ltr:Pe((()=>Xm(e,t,n))),rtl:Pe((()=>I.some(Xm(e,n,t))))}),exact:(t,n,o,r)=>({ltr:Pe((()=>Qm(e,t,n,o,r))),rtl:Pe((()=>I.some(Qm(e,o,r,t,n))))})}))(e,t);return((e,t)=>{const n=t.ltr();return n.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Jm.rtl(bn(e.endContainer),e.endOffset,bn(e.startContainer),e.startOffset))).getOrThunk((()=>Zm(0,Jm.ltr,n))):Zm(0,Jm.ltr,n)})(0,n)})(t,o).match({ltr:n,rtl:n})},mf=(e,t,n)=>((e,t,n)=>((e,t,n)=>{const o=bn(e.document);return tf(o,t,n).map((e=>ef(bn(e.startContainer),e.startOffset,bn(e.endContainer),e.endOffset)))})(e,t,n))(_n(bn(n)).dom,e,t).map((e=>{const t=n.createRange();return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),t})).getOrUndefined(),ff=(e,t)=>C(e)&&C(t)&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset,gf=(e,t,n)=>null!==((e,t,n)=>{let o=e;for(;o&&o!==t;){if(n(o))return o;o=o.parentNode}return null})(e,t,n),pf=(e,t,n)=>gf(e,t,(e=>e.nodeName===n)),hf=(e,t)=>Fr(e)&&!gf(e,t,fu),bf=(e,t,n)=>{const o=t.parentNode;if(o){const r=new Io(t,e.getParent(o,e.isBlock)||e.getRoot());let s;for(;s=r[n?"prev":"next"]();)if(tr(s))return!0}return!1},vf=(e,t,n,o,r)=>{const s=e.getRoot(),a=e.schema.getNonEmptyElements(),i=r.parentNode;let l,d;if(!i)return I.none();const c=e.getParent(i,e.isBlock)||s;if(o&&tr(r)&&t&&e.isEmpty(c))return I.some(Ti(i,e.nodeIndex(r)));const u=new Io(r,c);for(;d=u[o?"prev":"next"]();){if("false"===e.getContentEditableParent(d)||hf(d,s))return I.none();if(Yo(d)&&d.data.length>0)return pf(d,s,"A")?I.none():I.some(Ti(d,o?d.data.length:0));if(e.isBlock(d)||a[d.nodeName.toLowerCase()])return I.none();l=d}return Jo(l)?I.none():n&&l?I.some(Ti(l,0)):I.none()},yf=(e,t,n,o)=>{const r=e.getRoot();let s,a=!1,i=n?o.startContainer:o.endContainer,l=n?o.startOffset:o.endOffset;const d=zo(i)&&l===i.childNodes.length,c=e.schema.getNonEmptyElements();let u=n;if(Fr(i))return I.none();if(zo(i)&&l>i.childNodes.length-1&&(u=!1),Zo(i)&&(i=r,l=0),i===r){if(u&&(s=i.childNodes[l>0?l-1:0],s)){if(Fr(s))return I.none();if(c[s.nodeName]||Wo(s))return I.none()}if(i.hasChildNodes()){if(l=Math.min(!u&&l>0?l-1:l,i.childNodes.length-1),i=i.childNodes[l],l=Yo(i)&&d?i.data.length:0,!t&&i===r.lastChild&&Wo(i))return I.none();if(((e,t)=>{let n=t;for(;n&&n!==e;){if(rr(n))return!0;n=n.parentNode}return!1})(r,i)||Fr(i))return I.none();if(i.hasChildNodes()&&!Wo(i)){s=i;const t=new Io(i,r);do{if(rr(s)||Fr(s)){a=!1;break}if(Yo(s)&&s.data.length>0){l=u?0:s.data.length,i=s,a=!0;break}if(c[s.nodeName.toLowerCase()]&&!ar(s)){l=e.nodeIndex(s),i=s.parentNode,u||l++,a=!0;break}}while(s=u?t.next():t.prev())}}}return t&&(Yo(i)&&0===l&&vf(e,d,t,!0,i).each((e=>{i=e.container(),l=e.offset(),a=!0})),zo(i)&&(s=i.childNodes[l],s||(s=i.childNodes[l-1]),!s||!tr(s)||((e,t)=>{var n;return"A"===(null===(n=e.previousSibling)||void 0===n?void 0:n.nodeName)})(s)||bf(e,s,!1)||bf(e,s,!0)||vf(e,d,t,!0,s).each((e=>{i=e.container(),l=e.offset(),a=!0})))),u&&!t&&Yo(i)&&l===i.data.length&&vf(e,d,t,!1,i).each((e=>{i=e.container(),l=e.offset(),a=!0})),a&&i?I.some(Ti(i,l)):I.none()},Cf=(e,t)=>{const n=t.collapsed,o=t.cloneRange(),r=Ti.fromRangeStart(t);return yf(e,n,!0,o).each((e=>{n&&Ti.isAbove(r,e)||o.setStart(e.container(),e.offset())})),n||yf(e,n,!1,o).each((e=>{o.setEnd(e.container(),e.offset())})),n&&o.collapse(!0),ff(t,o)?I.none():I.some(o)},wf=(e,t)=>e.splitText(t),xf=e=>{let t=e.startContainer,n=e.startOffset,o=e.endContainer,r=e.endOffset;if(t===o&&Yo(t)){if(n>0&&n<t.data.length)if(o=wf(t,n),t=o.previousSibling,r>n){r-=n;const e=wf(o,r).previousSibling;t=o=e,r=e.data.length,n=0}else r=0}else if(Yo(t)&&n>0&&n<t.data.length&&(t=wf(t,n),n=0),Yo(o)&&r>0&&r<o.data.length){const e=wf(o,r).previousSibling;o=e,r=e.data.length}return{startContainer:t,startOffset:n,endContainer:o,endOffset:r}},kf=e=>({walk:(t,n)=>Om(e,t,n),split:xf,expand:(t,n={type:"word"})=>{if("word"===n.type){const n=Am(e,t,[{inline:"span"}]),o=e.createRng();return o.setStart(n.startContainer,n.startOffset),o.setEnd(n.endContainer,n.endOffset),o}return t},normalize:t=>Cf(e,t).fold(L,(e=>(t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0)))});kf.compareRanges=ff,kf.getCaretRangeFromPoint=mf,kf.getSelectedNode=ci,kf.getNode=ui;const Ef=((e,t)=>{const n=t=>{const n=(e=>{const t=e.dom;return Kn(e)?t.getBoundingClientRect().height:t.offsetHeight})(t);if(n<=0||null===n){const n=ao(t,e);return parseFloat(n)||0}return n},o=(e,t)=>X(t,((t,n)=>{const o=ao(e,n),r=void 0===o?0:parseInt(o,10);return isNaN(r)?t:t+r}),0);return{set:(t,n)=>{if(!x(n)&&!n.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+n);const o=t.dom;no(o)&&(o.style[e]=n+"px")},get:n,getOuter:n,aggregate:o,max:(e,t,n)=>{const r=o(e,n);return t>r?t-r:0}}})("height"),Sf=()=>bn(document),_f=(e,t)=>e.view(t).fold(N([]),(t=>{const n=e.owner(t),o=_f(e,n);return[t].concat(o)}));var Nf=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?I.none():I.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(bn)},owner:e=>Sn(e)});const Rf=e=>"textarea"===jt(e),Af=(e,t)=>{const n=(e=>{const t=e.dom.ownerDocument,n=t.body,o=t.defaultView,r=t.documentElement;if(n===e.dom)return No(n.offsetLeft,n.offsetTop);const s=Ro(null==o?void 0:o.pageYOffset,r.scrollTop),a=Ro(null==o?void 0:o.pageXOffset,r.scrollLeft),i=Ro(r.clientTop,n.clientTop),l=Ro(r.clientLeft,n.clientLeft);return Ao(e).translate(a-l,s-i)})(e),o=(e=>Ef.get(e))(e);return{element:e,bottom:n.top+o,height:o,pos:n,cleanup:t}},Of=(e,t,n,o)=>{Pf(e,((r,s)=>Bf(e,t,n,o)),n)},Tf=(e,t,n,o,r)=>{const s={elm:o.element.dom,alignToTop:r};((e,t)=>e.dispatch("ScrollIntoView",t).isDefaultPrevented())(e,s)||(n(t,Oo(t).top,o,r),((e,t)=>{e.dispatch("AfterScrollIntoView",t)})(e,s))},Bf=(e,t,n,o)=>{const r=bn(e.getBody()),s=bn(e.getDoc());r.dom.offsetWidth;const a=((e,t)=>{const n=((e,t)=>{const n=Pn(e);if(0===n.length||Rf(e))return{element:e,offset:t};if(t<n.length&&!Rf(n[t]))return{element:n[t],offset:0};{const o=n[n.length-1];return Rf(o)?{element:e,offset:t}:"img"===jt(o)?{element:o,offset:1}:qt(o)?{element:o,offset:fr(o).length}:{element:o,offset:Pn(o).length}}})(e,t),o=gn('<span data-mce-bogus="all" style="display: inline-block;">\ufeff</span>');return mo(n.element,o),Af(o,(()=>yo(o)))})(bn(n.startContainer),n.startOffset);Tf(e,s,t,a,o),a.cleanup()},Df=(e,t,n,o)=>{const r=bn(e.getDoc());Tf(e,r,n,(e=>Af(bn(e),E))(t),o)},Pf=(e,t,n)=>{const o=n.startContainer,r=n.startOffset,s=n.endContainer,a=n.endOffset;t(bn(o),bn(s));const i=e.dom.createRng();i.setStart(o,r),i.setEnd(s,a),e.selection.setRng(n)},Lf=(e,t)=>e.element.dom.scrollIntoView({block:t?"start":"end"}),Mf=(e,t,n,o)=>{const r=t+e,s=n.pos.top,a=n.bottom,i=a-s>=t;s<e?Lf(n,!1!==o):s>r?Lf(n,i?!1!==o:!0===o):a>r&&!i&&Lf(n,!0===o)},If=(e,t,n,o)=>{const r=_n(e).dom.innerHeight;Mf(t,r,n,o)},Ff=(e,t,n,o)=>{const r=_n(e).dom.innerHeight;Mf(t,r,n,o);const s=(e=>{const t=Sf(),n=Oo(t),o=((e,t)=>{const n=t.owner(e);return _f(t,n)})(e,Nf),r=Ao(e),s=Y(o,((e,t)=>{const n=Ao(t);return{left:e.left+n.left,top:e.top+n.top}}),{left:0,top:0});return No(s.left+r.left+n.left,s.top+r.top+n.top)})(n.element),a=Do(window);s.top<a.y?To(n.element,!1!==o):s.top>a.bottom&&To(n.element,!0===o)},Uf=(e,t,n)=>Of(e,If,t,n),zf=(e,t,n)=>Df(e,t,If,n),jf=(e,t,n)=>Of(e,Ff,t,n),Hf=(e,t,n)=>Df(e,t,Ff,n),$f=(e,t,n)=>{(e.inline?Uf:jf)(e,t,n)},Vf=e=>e.dom.focus(),qf=e=>{const t=Hn(e).dom;return e.dom===t.activeElement},Wf=(e=Sf())=>I.from(e.dom.activeElement).map(bn),Kf=(e,t)=>{const n=qt(t)?fr(t).length:Pn(t).length+1;return e>n?n:e<0?0:e},Gf=e=>sf.range(e.start,Kf(e.soffset,e.start),e.finish,Kf(e.foffset,e.finish)),Yf=(e,t)=>!Uo(t.dom)&&(kn(e,t)||xn(e,t)),Xf=e=>t=>Yf(e,t.start)&&Yf(e,t.finish),Qf=e=>sf.range(bn(e.startContainer),e.startOffset,bn(e.endContainer),e.endOffset),Jf=e=>{const t=document.createRange();try{return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),I.some(t)}catch(e){return I.none()}},Zf=e=>{const t=(e=>e.inline||At.browser.isFirefox())(e)?(n=bn(e.getBody()),(e=>{const t=e.getSelection();return(t&&0!==t.rangeCount?I.from(t.getRangeAt(0)):I.none()).map(Qf)})(_n(n).dom).filter(Xf(n))):I.none();var n;e.bookmark=t.isSome()?t:e.bookmark},eg=e=>(e.bookmark?e.bookmark:I.none()).bind((t=>{return n=bn(e.getBody()),o=t,I.from(o).filter(Xf(n)).map(Gf);var n,o})).bind(Jf),tg={isEditorUIElement:e=>{const t=e.className.toString();return-1!==t.indexOf("tox-")||-1!==t.indexOf("mce-")}},ng={setEditorTimeout:(e,t,n)=>((e,t)=>(x(t)||(t=0),setTimeout(e,t)))((()=>{e.removed||t()}),n),setEditorInterval:(e,t,n)=>{const o=((e,t)=>(x(t)||(t=0),setInterval(e,t)))((()=>{e.removed?clearInterval(o):t()}),n);return o}};let og;const rg=_a.DOM,sg=e=>{const t=e.classList;return void 0!==t&&(t.contains("tox-edit-area")||t.contains("tox-edit-area__iframe")||t.contains("mce-content-body"))},ag=(e,t)=>{const n=cd(e),o=rg.getParent(t,(t=>(e=>zo(e)&&tg.isEditorUIElement(e))(t)||!!n&&e.dom.is(t,n)));return null!==o},ig=e=>{try{const t=Hn(bn(e.getElement()));return Wf(t).fold((()=>document.body),(e=>e.dom))}catch(e){return document.body}},lg=(e,t)=>{const n=t.editor;(e=>{const t=Ia((()=>{Zf(e)}),0);e.on("init",(()=>{e.inline&&((e,t)=>{const n=()=>{t.throttle()};_a.DOM.bind(document,"mouseup",n),e.on("remove",(()=>{_a.DOM.unbind(document,"mouseup",n)}))})(e,t),((e,t)=>{((e,t)=>{e.on("mouseup touchend",(e=>{t.throttle()}))})(e,t),e.on("keyup NodeChange AfterSetSelectionRange",(t=>{(e=>"nodechange"===e.type&&e.selectionChange)(t)||Zf(e)}))})(e,t)})),e.on("remove",(()=>{t.cancel()}))})(n);const o=(e,t)=>{Xd(e)&&!0!==e.inline&&t(bn(e.getContainer()),"tox-edit-focus")};n.on("focusin",(()=>{const t=e.focusedEditor;sg(ig(n))&&o(n,dn),t!==n&&(t&&t.dispatch("blur",{focusedEditor:n}),e.setActive(n),e.focusedEditor=n,n.dispatch("focus",{blurredEditor:t}),n.focus(!0))})),n.on("focusout",(()=>{ng.setEditorTimeout(n,(()=>{const t=e.focusedEditor;sg(ig(n))&&t===n||o(n,un),ag(n,ig(n))||t!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null)}))})),og||(og=t=>{const n=e.activeEditor;n&&qn(t).each((t=>{const o=t;o.ownerDocument===document&&(o===document.body||ag(n,o)||e.focusedEditor!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null))}))},rg.bind(document,"focusin",og))},dg=(e,t)=>{e.focusedEditor===t.editor&&(e.focusedEditor=null),!e.activeEditor&&og&&(rg.unbind(document,"focusin",og),og=null)},cg=(e,t)=>{((e,t)=>(e=>e.collapsed?I.from(ui(e.startContainer,e.startOffset)).map(bn):I.none())(t).bind((t=>kr(t)?I.some(t):kn(e,t)?I.none():I.some(e))))(bn(e.getBody()),t).bind((e=>cu(e.dom))).fold((()=>{e.selection.normalize()}),(t=>e.selection.setRng(t.toRange())))},ug=e=>{if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},mg=e=>e.inline?(e=>{const t=e.getBody();return t&&(n=bn(t),qf(n)||(o=n,Wf(Hn(o)).filter((e=>o.dom.contains(e.dom)))).isSome());var n,o})(e):(e=>C(e.iframeElement)&&qf(bn(e.iframeElement)))(e),fg=e=>e.editorManager.setActive(e),gg=(e,t,n,o,r)=>{const s=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return I.from(s).map(bn).map((e=>o&&t.collapsed?e:Ln(e,r(e,a)).getOr(e))).bind((e=>Vt(e)?I.some(e):Nn(e).filter(Vt))).map((e=>e.dom)).getOr(e)},pg=(e,t,n=!1)=>gg(e,t,!0,n,((e,t)=>Math.min(Fn(e),t))),hg=(e,t,n=!1)=>gg(e,t,!1,n,((e,t)=>t>0?t-1:t)),bg=(e,t)=>{const n=e;for(;e&&Yo(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},vg=(e,t)=>V(t,(t=>{const n=e.dispatch("GetSelectionRange",{range:t});return n.range!==t?n.range:t})),yg=["img","br"],Cg=e=>{const t=gr(e).filter((e=>0!==e.trim().length||e.indexOf(cr)>-1)).isSome();return t||H(yg,jt(e))||(e=>{return Vt(t=e)&&zt(t.dom)&&"false"===Jt(e,"contenteditable");var t})(e)},wg="[data-mce-autocompleter]",xg=(e,t)=>{if(kg(bn(e.getBody())).isNone()){const o=gn('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',e.getDoc());po(o,bn(t.extractContents())),t.insertNode(o.dom),Nn(o).each((e=>e.dom.normalize())),(n=o,((e,t)=>{const n=e=>{const o=Pn(e);for(let e=o.length-1;e>=0;e--){const r=o[e];if(t(r))return I.some(r);const s=n(r);if(s.isSome())return s}return I.none()};return n(e)})(n,Cg)).map((t=>{e.selection.setCursorLocation(t.dom,(e=>"img"===jt(e)?1:gr(e).fold((()=>Pn(e).length),(e=>e.length)))(t))}))}var n},kg=e=>Jn(e,wg),Eg={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Sg=(e,t,n)=>{const o=n?"lastChild":"firstChild",r=n?"prev":"next";if(e[o])return e[o];if(e!==t){let n=e[r];if(n)return n;for(let o=e.parent;o&&o!==t;o=o.parent)if(n=o[r],n)return n}},_g=e=>{var t;const n=null!==(t=e.value)&&void 0!==t?t:"";if(!rs(n))return!1;const o=e.parent;return!o||"span"===o.name&&!o.attr("style")||!/^[ ]+$/.test(n)},Ng=e=>{const t="a"===e.name&&!e.attr("href")&&e.attr("id");return e.attr("name")||e.attr("id")&&!e.firstChild||e.attr("data-mce-bookmark")||t};class Rg{constructor(e,t){this.name=e,this.type=t,1===t&&(this.attributes=[],this.attributes.map={})}static create(e,t){const n=new Rg(e,Eg[e]||1);return t&&ge(t,((e,t)=>{n.attr(t,e)})),n}replace(e){const t=this;return e.parent&&e.remove(),t.insert(e,t),t.remove(),t}attr(e,t){const n=this;if(!m(e))return C(e)&&ge(e,((e,t)=>{n.attr(t,e)})),n;const o=n.attributes;if(o){if(void 0!==t){if(null===t){if(e in o.map){delete o.map[e];let t=o.length;for(;t--;)if(o[t].name===e)return o.splice(t,1),n}return n}if(e in o.map){let n=o.length;for(;n--;)if(o[n].name===e){o[n].value=t;break}}else o.push({name:e,value:t});return o.map[e]=t,n}return o.map[e]}}clone(){const e=this,t=new Rg(e.name,e.type),n=e.attributes;if(n){const e=[];e.map={};for(let t=0,o=n.length;t<o;t++){const o=n[t];"id"!==o.name&&(e[e.length]={name:o.name,value:o.value},e.map[o.name]=o.value)}t.attributes=e}return t.value=e.value,t}wrap(e){const t=this;return t.parent&&(t.parent.insert(e,t),e.append(t)),t}unwrap(){const e=this;for(let t=e.firstChild;t;){const n=t.next;e.insert(t,e,!0),t=n}e.remove()}remove(){const e=this,t=e.parent,n=e.next,o=e.prev;return t&&(t.firstChild===e?(t.firstChild=n,n&&(n.prev=null)):o&&(o.next=n),t.lastChild===e?(t.lastChild=o,o&&(o.next=null)):n&&(n.prev=o),e.parent=e.next=e.prev=null),e}append(e){const t=this;e.parent&&e.remove();const n=t.lastChild;return n?(n.next=e,e.prev=n,t.lastChild=e):t.lastChild=t.firstChild=e,e.parent=t,e}insert(e,t,n){e.parent&&e.remove();const o=t.parent||this;return n?(t===o.firstChild?o.firstChild=e:t.prev&&(t.prev.next=e),e.prev=t.prev,e.next=t,t.prev=e):(t===o.lastChild?o.lastChild=e:t.next&&(t.next.prev=e),e.next=t.next,e.prev=t,t.next=e),e.parent=o,e}getAll(e){const t=this,n=[];for(let o=t.firstChild;o;o=Sg(o,t))o.name===e&&n.push(o);return n}children(){const e=[];for(let t=this.firstChild;t;t=t.next)e.push(t);return e}empty(){const e=this;if(e.firstChild){const t=[];for(let n=e.firstChild;n;n=Sg(n,e))t.push(n);let n=t.length;for(;n--;){const e=t[n];e.parent=e.firstChild=e.lastChild=e.next=e.prev=null}}return e.firstChild=e.lastChild=null,e}isEmpty(e,t={},n){var o;const r=this;let s=r.firstChild;if(Ng(r))return!1;if(s)do{if(1===s.type){if(s.attr("data-mce-bogus"))continue;if(e[s.name])return!1;if(Ng(s))return!1}if(8===s.type)return!1;if(3===s.type&&!_g(s))return!1;if(3===s.type&&s.parent&&t[s.parent.name]&&rs(null!==(o=s.value)&&void 0!==o?o:""))return!1;if(n&&n(s))return!1}while(s=Sg(s,r));return!0}walk(e){return Sg(this,null,e)}}const Ag=(e,t,n=0)=>{const o=e.toLowerCase();if(-1!==o.indexOf("[if ",n)&&((e,t)=>/^\s*\[if [\w\W]+\]>.*<!\[endif\](--!?)?>/.test(e.substr(t)))(o,n)){const e=o.indexOf("[endif]",n);return o.indexOf(">",e)}if(t){const e=o.indexOf(">",n);return-1!==e?e:o.length}{const t=/--!?>/g;t.lastIndex=n;const r=t.exec(e);return r?r.index+r[0].length:o.length}},Og=(e,t,n)=>{const o=/<([!?\/])?([A-Za-z0-9\-_:.]+)/g,r=/(?:\s(?:[^'">]+(?:"[^"]*"|'[^']*'))*[^"'>]*(?:"[^">]*|'[^'>]*)?|\s*|\/)>/g,s=e.getVoidElements();let a=1,i=n;for(;0!==a;)for(o.lastIndex=i;;){const e=o.exec(t);if(null===e)return i;if("!"===e[1]){i=He(e[2],"--")?Ag(t,!1,e.index+"!--".length):Ag(t,!0,e.index+1);break}{r.lastIndex=o.lastIndex;const n=r.exec(t);if(h(n)||n.index!==o.lastIndex)continue;"/"===e[1]?a-=1:ke(s,e[2])||(a+=1),i=o.lastIndex+n[0].length;break}}return i},Tg=(e,t)=>{const n=/<(\w+) [^>]*data-mce-bogus="all"[^>]*>/g,o=e.schema;let r=((e,t)=>{const n=new RegExp(["\\s?("+e.join("|")+')="[^"]+"'].join("|"),"gi");return t.replace(n,"")})(e.getTempAttrs(),t);const s=o.getVoidElements();let a;for(;a=n.exec(r);){const e=n.lastIndex,t=a[0].length;let i;i=s[a[1]]?e:Og(o,r,e),r=r.substring(0,e-t)+r.substring(i),n.lastIndex=e-t}return Dr(r)},Bg=Tg,Dg=e=>{const t=Lo(e,"[data-mce-bogus]");q(t,(e=>{"all"===Jt(e,"data-mce-bogus")?yo(e):yr(e)?(mo(e,hn(dr)),yo(e)):Co(e)}))},Pg=e=>{const t=Lo(e,"input");q(t,(e=>{tn(e,"name")}))},Lg=(e,t,n)=>{let o;return o="raw"===t.format?Dt.trim(Bg(e.serializer,n.innerHTML)):"text"===t.format?((e,t)=>{const n=e.getDoc(),o=Hn(bn(e.getBody())),r=pn("div",n);Xt(r,"data-mce-bogus","all"),so(r,{position:"fixed",left:"-9999999px",top:"0"}),ko(r,t.innerHTML),Dg(r),Pg(r);const s=(e=>Un(e)?e:bn(Sn(e).dom.body))(o);po(s,r);const a=Dr(r.dom.innerText);return yo(r),a})(e,n):"tree"===t.format?e.serializer.serialize(n,t):((e,t)=>{const n=kl(e),o=new RegExp(`^(<${n}[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/${n}>[\r\n]*|<br \\/>[\r\n]*)$`);return t.replace(o,"")})(e,e.serializer.serialize(n,t)),"text"!==t.format&&!Sr(bn(n))&&m(o)?Dt.trim(o):o},Mg=Dt.makeMap,Ig=e=>{const t=[],n=(e=e||{}).indent,o=Mg(e.indent_before||""),r=Mg(e.indent_after||""),s=Ks.getEncodeFunc(e.entity_encoding||"raw",e.entities),a="xhtml"!==e.element_format;return{start:(e,i,l)=>{if(n&&o[e]&&t.length>0){const e=t[t.length-1];e.length>0&&"\n"!==e&&t.push("\n")}if(t.push("<",e),i)for(let e=0,n=i.length;e<n;e++){const n=i[e];t.push(" ",n.name,'="',s(n.value,!0),'"')}if(t[t.length]=!l||a?">":" />",l&&n&&r[e]&&t.length>0){const e=t[t.length-1];e.length>0&&"\n"!==e&&t.push("\n")}},end:e=>{let o;t.push("</",e,">"),n&&r[e]&&t.length>0&&(o=t[t.length-1],o.length>0&&"\n"!==o&&t.push("\n"))},text:(e,n)=>{e.length>0&&(t[t.length]=n?e:s(e))},cdata:e=>{t.push("<![CDATA[",e,"]]>")},comment:e=>{t.push("\x3c!--",e,"--\x3e")},pi:(e,o)=>{o?t.push("<?",e," ",s(o),"?>"):t.push("<?",e,"?>"),n&&t.push("\n")},doctype:e=>{t.push("<!DOCTYPE",e,">",n?"\n":"")},reset:()=>{t.length=0},getContent:()=>t.join("").replace(/\n$/,"")}},Fg=(e={},t=aa())=>{const n=Ig(e);return e.validate=!("validate"in e)||e.validate,{serialize:o=>{const r=e.validate,s={3:e=>{var t;n.text(null!==(t=e.value)&&void 0!==t?t:"",e.raw)},8:e=>{var t;n.comment(null!==(t=e.value)&&void 0!==t?t:"")},7:e=>{n.pi(e.name,e.value)},10:e=>{var t;n.doctype(null!==(t=e.value)&&void 0!==t?t:"")},4:e=>{var t;n.cdata(null!==(t=e.value)&&void 0!==t?t:"")},11:e=>{let t=e;if(t=t.firstChild)do{a(t)}while(t=t.next)}};n.reset();const a=e=>{var o;const i=s[e.type];if(i)i(e);else{const s=e.name,i=s in t.getVoidElements();let l=e.attributes;if(r&&l&&l.length>1){const n=[];n.map={};const o=t.getElementRule(e.name);if(o){for(let e=0,t=o.attributesOrder.length;e<t;e++){const t=o.attributesOrder[e];if(t in l.map){const e=l.map[t];n.map[t]=e,n.push({name:t,value:e})}}for(let e=0,t=l.length;e<t;e++){const t=l[e].name;if(!(t in n.map)){const e=l.map[t];n.map[t]=e,n.push({name:t,value:e})}}l=n}}if(n.start(s,l,i),!i){let t=e.firstChild;if(t){"pre"!==s&&"textarea"!==s||3!==t.type||"\n"!==(null===(o=t.value)||void 0===o?void 0:o[0])||n.text("\n",!0);do{a(t)}while(t=t.next)}n.end(s)}}};return 1!==o.type||e.inner?3===o.type?s[3](o):s[11](o):a(o),n.getContent()}}},Ug=new Set;q(["margin","margin-left","margin-right","margin-top","margin-bottom","padding","padding-left","padding-right","padding-top","padding-bottom","border","border-width","border-style","border-color","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","float","position","left","right","top","bottom","z-index","display","transform","width","max-width","min-width","height","max-height","min-height","overflow","overflow-x","overflow-y","text-overflow","vertical-align","transition","transition-delay","transition-duration","transition-property","transition-timing-function"],(e=>{Ug.add(e)}));const zg=["font","text-decoration","text-emphasis"],jg=(e,t)=>me(e.parseStyle(e.getAttrib(t,"style"))),Hg=(e,t,n)=>{const o=jg(e,t),r=jg(e,n),s=o=>{var r,s;const a=null!==(r=e.getStyle(t,o))&&void 0!==r?r:"",i=null!==(s=e.getStyle(n,o))&&void 0!==s?s:"";return Ge(a)&&Ge(i)&&a!==i};return $(o,(e=>{const t=t=>$(t,(t=>t===e));if(!t(r)&&t(zg)){const e=G(r,(e=>$(zg,(t=>He(e,t)))));return $(e,s)}return s(e)}))},$g=(e,t,n)=>I.from(n.container()).filter(Yo).exists((o=>{const r=e?0:-1;return t(o.data.charAt(n.offset()+r))})),Vg=O($g,!0,Ru),qg=O($g,!1,Ru),Wg=e=>{const t=e.container();return Yo(t)&&(0===t.data.length||Br(t.data)&&Mm.isBookmarkNode(t.parentNode))},Kg=(e,t)=>n=>Tc(e?0:-1,n).filter(t).isSome(),Gg=e=>nr(e)&&"block"===ao(bn(e),"display"),Yg=e=>rr(e)&&!(e=>zo(e)&&"all"===e.getAttribute("data-mce-bogus"))(e),Xg=Kg(!0,Gg),Qg=Kg(!1,Gg),Jg=Kg(!0,ir),Zg=Kg(!1,ir),ep=Kg(!0,Wo),tp=Kg(!1,Wo),np=Kg(!0,Yg),op=Kg(!1,Yg),rp=(e,t)=>((e,t,n)=>kn(t,e)?An(e,(e=>n(e)||xn(e,t))).slice(0,-1):[])(e,t,L),sp=(e,t)=>[e].concat(rp(e,t)),ap=(e,t,n)=>au(e,t,n,Wg),ip=(e,t)=>J(sp(bn(t.container()),e),br),lp=(e,t,n)=>ap(e,t.dom,n).forall((e=>ip(t,n).fold((()=>!Oc(e,n,t.dom)),(o=>!Oc(e,n,t.dom)&&kn(o,bn(e.container())))))),dp=(e,t,n)=>ip(t,n).fold((()=>ap(e,t.dom,n).forall((e=>!Oc(e,n,t.dom)))),(t=>ap(e,t.dom,n).isNone())),cp=O(dp,!1),up=O(dp,!0),mp=O(lp,!1),fp=O(lp,!0),gp=e=>zc(e).exists(yr),pp=(e,t,n)=>{const o=G(sp(bn(n.container()),t),br),r=le(o).getOr(t);return ru(e,r.dom,n).filter(gp)},hp=(e,t)=>zc(t).exists(yr)||pp(!0,e,t).isSome(),bp=(e,t)=>(e=>I.from(e.getNode(!0)).map(bn))(t).exists(yr)||pp(!1,e,t).isSome(),vp=O(pp,!1),yp=O(pp,!0),Cp=e=>Ti.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd(),wp=(e,t)=>{const n=G(sp(bn(t.container()),e),br);return le(n).getOr(e)},xp=(e,t)=>Cp(t)?qg(t):qg(t)||du(wp(e,t).dom,t).exists(qg),kp=(e,t)=>Cp(t)?Vg(t):Vg(t)||lu(wp(e,t).dom,t).exists(Vg),Ep=e=>zc(e).bind((e=>Xn(e,Vt))).exists((e=>(e=>H(["pre","pre-wrap"],e))(ao(e,"white-space")))),Sp=(e,t)=>n=>{return o=new Io(n,e)[t](),C(o)&&rr(o)&&yc(o);var o},_p=(e,t)=>!Ep(t)&&(cp(e,t)||mp(e,t)||bp(e,t)||xp(e,t)||((e,t)=>{const n=du(e.dom,t).getOr(t),o=Sp(e.dom,"prev");return t.isAtStart()&&(o(t.container())||o(n.container()))})(e,t)),Np=(e,t)=>!Ep(t)&&(up(e,t)||fp(e,t)||hp(e,t)||kp(e,t)||((e,t)=>{const n=lu(e.dom,t).getOr(t),o=Sp(e.dom,"next");return t.isAtEnd()&&(o(t.container())||o(n.container()))})(e,t)),Rp=(e,t)=>_p(e,t)||Np(e,(e=>{const t=e.container(),n=e.offset();return Yo(t)&&n<t.data.length?Ti(t,n+1):e})(t)),Ap=(e,t)=>Nu(e.charAt(t)),Op=(e,t)=>Ru(e.charAt(t)),Tp=(e,t,n)=>{const o=t.data,r=Ti(t,0);return n||!Ap(o,0)||Rp(e,r)?!!(n&&Op(o,0)&&_p(e,r))&&(t.data=cr+o.slice(1),!0):(t.data=" "+o.slice(1),!0)},Bp=(e,t,n)=>{const o=t.data,r=Ti(t,o.length-1);return n||!Ap(o,o.length-1)||Rp(e,r)?!!(n&&Op(o,o.length-1)&&Np(e,r))&&(t.data=o.slice(0,-1)+cr,!0):(t.data=o.slice(0,-1)+" ",!0)},Dp=(e,t)=>{const n=t.container();if(!Yo(n))return I.none();if((e=>{const t=e.container();return Yo(t)&&je(t.data,cr)})(t)){const o=Tp(e,n,!1)||(e=>{const t=e.data,n=(e=>{const t=e.split("");return V(t,((e,n)=>Nu(e)&&n>0&&n<t.length-1&&Au(t[n-1])&&Au(t[n+1])?" ":e)).join("")})(t);return n!==t&&(e.data=n,!0)})(n)||Bp(e,n,!1);return Mt(o,t)}if(Rp(e,t)){const o=Tp(e,n,!0)||Bp(e,n,!0);return Mt(o,t)}return I.none()},Pp=(e,t,n)=>{if(0===n)return;const o=bn(e),r=Yn(o,br).getOr(o),s=e.data.slice(t,t+n),a=t+n>=e.data.length&&Np(r,Ti(e,e.data.length)),i=0===t&&_p(r,Ti(e,0));e.replaceData(t,n,as(s,4,i,a))},Lp=(e,t)=>{const n=e.data.slice(t),o=n.length-We(n).length;Pp(e,t,o)},Mp=(e,t)=>{const n=e.data.slice(0,t),o=n.length-Ke(n).length;Pp(e,t-o,o)},Ip=(e,t,n,o=!0)=>{const r=Ke(e.data).length,s=o?e:t,a=o?t:e;return o?s.appendData(a.data):s.insertData(0,a.data),yo(bn(a)),n&&Lp(s,r),s},Fp=(e,t)=>((e,t)=>{const n=e.container(),o=e.offset();return!Ti.isTextPosition(e)&&n===t.parentNode&&o>Ti.before(t).offset()})(t,e)?Ti(t.container(),t.offset()-1):t,Up=e=>{return es(e.previousSibling)?I.some((t=e.previousSibling,Yo(t)?Ti(t,t.data.length):Ti.after(t))):e.previousSibling?uu(e.previousSibling):I.none();var t},zp=e=>{return es(e.nextSibling)?I.some((t=e.nextSibling,Yo(t)?Ti(t,0):Ti.before(t))):e.nextSibling?cu(e.nextSibling):I.none();var t},jp=(e,t,n)=>((e,t,n)=>e?((e,t)=>zp(t).orThunk((()=>Up(t))).orThunk((()=>((e,t)=>lu(e,Ti.after(t)).orThunk((()=>du(e,Ti.before(t)))))(e,t))))(t,n):((e,t)=>Up(t).orThunk((()=>zp(t))).orThunk((()=>((e,t)=>I.from(t.previousSibling?t.previousSibling:t.parentNode).bind((t=>du(e,Ti.before(t)))).orThunk((()=>lu(e,Ti.after(t)))))(e,t))))(t,n))(e,t,n).map(O(Fp,n)),Hp=(e,t,n)=>{n.fold((()=>{e.focus()}),(n=>{e.selection.setRng(n.toRange(),t)}))},$p=(e,t)=>t&&ke(e.schema.getBlockElements(),jt(t)),Vp=e=>{if(us(e)){const t=gn('<br data-mce-bogus="1">');return vo(e),po(e,t),I.some(Ti.before(t.dom))}return I.none()},qp=(e,t,n,o=!0)=>{const r=jp(t,e.getBody(),n.dom),s=Yn(n,O($p,e),(a=e.getBody(),e=>e.dom===a));var a;const i=((e,t,n)=>{const o=On(e).filter(qt),r=Tn(e).filter(qt);return yo(e),(s=o,a=r,i=t,l=(e,t,o)=>{const r=e.dom,s=t.dom,a=r.data.length;return Ip(r,s,n),o.container()===s?Ti(r,a):o},s.isSome()&&a.isSome()&&i.isSome()?I.some(l(s.getOrDie(),a.getOrDie(),i.getOrDie())):I.none()).orThunk((()=>(n&&(o.each((e=>Mp(e.dom,e.dom.length))),r.each((e=>Lp(e.dom,0)))),t)));var s,a,i,l})(n,r,((e,t)=>ke(e.schema.getTextInlineElements(),jt(t)))(e,n));e.dom.isEmpty(e.getBody())?(e.setContent(""),e.selection.setCursorLocation()):s.bind(Vp).fold((()=>{o&&Hp(e,t,i)}),(n=>{o&&Hp(e,t,I.some(n))}))},Wp=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,Kp=(e,t)=>Cn(bn(t),Yl(e))&&!ks(e.schema,t)&&e.dom.isEditable(t),Gp=(e,t,n)=>{const o=((e,t,n)=>G(_a.DOM.getParents(n.container(),"*",t),e))(e,t,n);return I.from(o[o.length-1])},Yp=(e,t)=>{const n=t.container(),o=t.offset();return e?Ir(n)?Yo(n.nextSibling)?Ti(n.nextSibling,0):Ti.after(n):zr(t)?Ti(n,o+1):t:Ir(n)?Yo(n.previousSibling)?Ti(n.previousSibling,n.previousSibling.data.length):Ti.before(n):jr(t)?Ti(n,o-1):t},Xp=O(Yp,!0),Qp=O(Yp,!1),Jp=(e,t)=>{const n=e=>e.stopImmediatePropagation();e.on("beforeinput input",n,!0),e.getDoc().execCommand(t),e.off("beforeinput input",n)},Zp=e=>Jp(e,"Delete"),eh=e=>Cr(e)||xr(e),th=(e,t)=>kn(e,t)?Xn(t,eh,(e=>t=>Pt(Nn(t),e,xn))(e)):I.none(),nh=(e,t=!0)=>{e.dom.isEmpty(e.getBody())&&e.setContent("",{no_selection:!t})},oh=e=>{var t;return(8===Ht(t=e)||"#comment"===jt(t)?On(e):In(e)).bind(oh).orThunk((()=>I.some(e)))},rh=(e,t,n,o=!0)=>{var r;t.deleteContents();const s=oh(n).getOr(n),a=bn(null!==(r=e.dom.getParent(s.dom,e.dom.isBlock))&&void 0!==r?r:n.dom);if(a.dom===e.getBody()?nh(e,o):us(a)&&(Ar(a),o&&e.selection.setCursorLocation(a.dom,0)),!xn(n,a)){const e=Pt(Nn(a),n)?[]:Nn(i=a).map(Pn).map((e=>G(e,(e=>!xn(i,e))))).getOr([]);q(e.concat(Pn(n)),(e=>{xn(e,a)||kn(e,a)||!us(e)||yo(e)}))}var i},sh=e=>Lo(e,"td,th"),ah=(e,t)=>({start:e,end:t}),ih=nl([{singleCellTable:["rng","cell"]},{fullTable:["table"]},{partialTable:["cells","outsideDetails"]},{multiTable:["startTableCells","endTableCells","betweenRng"]}]),lh=(e,t)=>Zn(bn(e),"td,th",t),dh=e=>!xn(e.start,e.end),ch=(e,t)=>zu(e.start,t).bind((n=>zu(e.end,t).bind((e=>Mt(xn(n,e),n))))),uh=e=>t=>ch(t,e).map((e=>((e,t,n)=>({rng:e,table:t,cells:n}))(t,e,sh(e)))),mh=(e,t,n,o)=>{if(n.collapsed||!e.forall(dh))return I.none();if(t.isSameTable){const t=e.bind(uh(o));return I.some({start:t,end:t})}{const e=lh(n.startContainer,o),t=lh(n.endContainer,o),r=e.bind((e=>t=>zu(t,e).bind((e=>de(sh(e)).map((e=>ah(t,e))))))(o)).bind(uh(o)),s=t.bind((e=>t=>zu(t,e).bind((e=>le(sh(e)).map((e=>ah(e,t))))))(o)).bind(uh(o));return I.some({start:r,end:s})}},fh=(e,t)=>Z(e,(e=>xn(e,t))),gh=e=>Lt(fh(e.cells,e.rng.start),fh(e.cells,e.rng.end),((t,n)=>e.cells.slice(t,n+1))),ph=(e,t)=>{const{startTable:n,endTable:o}=t,r=e.cloneRange();return n.each((e=>r.setStartAfter(e.dom))),o.each((e=>r.setEndBefore(e.dom))),r},hh=(e,t)=>{const n=(e=>t=>xn(e,t))(e),o=((e,t)=>{const n=lh(e.startContainer,t),o=lh(e.endContainer,t);return Lt(n,o,ah)})(t,n),r=((e,t)=>{const n=e=>zu(bn(e),t),o=n(e.startContainer),r=n(e.endContainer),s=o.isSome(),a=r.isSome(),i=Lt(o,r,xn).getOr(!1);return{startTable:o,endTable:r,isStartInTable:s,isEndInTable:a,isSameTable:i,isMultiTable:!i&&s&&a}})(t,n);return((e,t,n)=>e.exists((e=>((e,t)=>!dh(e)&&ch(e,t).exists((e=>{const t=e.dom.rows;return 1===t.length&&1===t[0].cells.length})))(e,n)&&$u(e.start,t))))(o,t,n)?o.map((e=>ih.singleCellTable(t,e.start))):r.isMultiTable?((e,t,n,o)=>mh(e,t,n,o).bind((({start:e,end:o})=>{const r=e.bind(gh).getOr([]),s=o.bind(gh).getOr([]);if(r.length>0&&s.length>0){const e=ph(n,t);return I.some(ih.multiTable(r,s,e))}return I.none()})))(o,r,t,n):((e,t,n,o)=>mh(e,t,n,o).bind((({start:e,end:t})=>e.or(t))).bind((e=>{const{isSameTable:o}=t,r=gh(e).getOr([]);if(o&&e.cells.length===r.length)return I.some(ih.fullTable(e.table));if(r.length>0){if(o)return I.some(ih.partialTable(r,I.none()));{const e=ph(n,t);return I.some(ih.partialTable(r,I.some({...t,rng:e})))}}return I.none()})))(o,r,t,n)},bh=e=>q(e,(e=>{tn(e,"contenteditable"),Ar(e)})),vh=(e,t,n,o)=>{const r=n.cloneRange();o?(r.setStart(n.startContainer,n.startOffset),r.setEndAfter(t.dom.lastChild)):(r.setStartBefore(t.dom.firstChild),r.setEnd(n.endContainer,n.endOffset)),xh(e,r,t,!1).each((e=>e()))},yh=e=>{const t=Uu(e),n=bn(e.selection.getNode());sr(n.dom)&&us(n)?e.selection.setCursorLocation(n.dom,0):e.selection.collapse(!0),t.length>1&&$(t,(e=>xn(e,n)))&&Xt(n,"data-mce-selected","1")},Ch=(e,t,n)=>I.some((()=>{const o=e.selection.getRng(),r=n.bind((({rng:n,isStartInTable:r})=>{const s=((e,t)=>I.from(e.dom.getParent(t,e.dom.isBlock)).map(bn))(e,r?n.endContainer:n.startContainer);n.deleteContents(),((e,t,n)=>{n.each((n=>{t?yo(n):(Ar(n),e.selection.setCursorLocation(n.dom,0))}))})(e,r,s.filter(us));const a=r?t[0]:t[t.length-1];return vh(e,a,o,r),us(a)?I.none():I.some(r?t.slice(1):t.slice(0,-1))})).getOr(t);bh(r),yh(e)})),wh=(e,t,n,o)=>I.some((()=>{const r=e.selection.getRng(),s=t[0],a=n[n.length-1];vh(e,s,r,!0),vh(e,a,r,!1);const i=us(s)?t:t.slice(1),l=us(a)?n:n.slice(0,-1);bh(i.concat(l)),o.deleteContents(),yh(e)})),xh=(e,t,n,o=!0)=>I.some((()=>{rh(e,t,n,o)})),kh=(e,t)=>I.some((()=>qp(e,!1,t))),Eh=(e,t)=>J(sp(t,e),Er),Sh=(e,t)=>J(sp(t,e),Gt("caption")),_h=(e,t)=>I.some((()=>{Ar(t),e.selection.setCursorLocation(t.dom,0)})),Nh=(e,t)=>e?ep(t):tp(t),Rh=(e,t,n)=>{const o=bn(e.getBody());return Sh(o,n).fold((()=>((e,t,n,o)=>{const r=Ti.fromRangeStart(e.selection.getRng());return Eh(n,o).bind((o=>us(o)?_h(e,o):((e,t,n,o,r)=>su(n,e.getBody(),r).bind((e=>Eh(t,bn(e.getNode())).bind((e=>xn(e,o)?I.none():I.some(E))))))(e,n,t,o,r)))})(e,t,o,n).orThunk((()=>Mt(((e,t)=>{const n=Ti.fromRangeStart(e.selection.getRng());return Nh(t,n)||ru(t,e.getBody(),n).exists((e=>Nh(t,e)))})(e,t),E)))),(n=>((e,t,n,o)=>{const r=Ti.fromRangeStart(e.selection.getRng());return us(o)?_h(e,o):((e,t,n,o,r)=>su(n,e.getBody(),r).fold((()=>I.some(E)),(s=>((e,t,n,o)=>cu(e.dom).bind((r=>uu(e.dom).map((e=>t?n.isEqual(r)&&o.isEqual(e):n.isEqual(e)&&o.isEqual(r))))).getOr(!0))(o,n,r,s)?((e,t)=>_h(e,t))(e,o):((e,t,n)=>Sh(e,bn(n.getNode())).fold((()=>I.some(E)),(e=>Mt(!xn(e,t),E))))(t,o,s))))(e,n,t,o,r)})(e,t,o,n)))},Ah=(e,t)=>{const n=bn(e.selection.getStart(!0)),o=Uu(e);return e.selection.isCollapsed()&&0===o.length?Rh(e,t,n):((e,t,n)=>{const o=bn(e.getBody()),r=e.selection.getRng();return 0!==n.length?Ch(e,n,I.none()):((e,t,n,o)=>Sh(t,o).fold((()=>((e,t,n)=>hh(t,n).bind((t=>t.fold(O(xh,e),O(kh,e),O(Ch,e),O(wh,e)))))(e,t,n)),(t=>((e,t)=>_h(e,t))(e,t))))(e,o,r,t)})(e,n,o)},Oh=(e,t)=>{let n=t;for(;n&&n!==e;){if(or(n)||rr(n))return n;n=n.parentNode}return null},Th=["data-ephox-","data-mce-","data-alloy-","data-snooker-","_"],Bh=Dt.each,Dh=e=>{const t=e.dom,n=new Set(e.serializer.getTempAttrs()),o=e=>$(Th,(t=>He(e,t)))||n.has(e);return{compare:(e,n)=>{if(e.nodeName!==n.nodeName||e.nodeType!==n.nodeType)return!1;const r=e=>{const n={};return Bh(t.getAttribs(e),(r=>{const s=r.nodeName.toLowerCase();"style"===s||o(s)||(n[s]=t.getAttrib(e,s))})),n},s=(e,t)=>{for(const n in e)if(ke(e,n)){const o=t[n];if(v(o))return!1;if(e[n]!==o)return!1;delete t[n]}for(const e in t)if(ke(t,e))return!1;return!0};if(zo(e)&&zo(n)){if(!s(r(e),r(n)))return!1;if(!s(t.parseStyle(t.getAttrib(e,"style")),t.parseStyle(t.getAttrib(n,"style"))))return!1}return!_u(e)&&!_u(n)},isAttributeInternal:o}},Ph=(e,t,n,o)=>{const r=n.name;for(let t=0,s=e.length;t<s;t++){const s=e[t];if(s.name===r){const e=o.nodes[r];e?e.nodes.push(n):o.nodes[r]={filter:s,nodes:[n]}}}if(n.attributes)for(let e=0,r=t.length;e<r;e++){const r=t[e],s=r.name;if(s in n.attributes.map){const e=o.attributes[s];e?e.nodes.push(n):o.attributes[s]={filter:r,nodes:[n]}}}},Lh=(e,t)=>{const n=(e,n)=>{ge(e,(e=>{const o=ce(e.nodes);q(e.filter.callbacks,(r=>{for(let t=o.length-1;t>=0;t--){const r=o[t];(n?void 0!==r.attr(e.filter.name):r.name===e.filter.name)&&!y(r.parent)||o.splice(t,1)}o.length>0&&r(o,e.filter.name,t)}))}))};n(e.nodes,!1),n(e.attributes,!0)},Mh=(e,t,n,o={})=>{const r=((e,t,n)=>{const o={nodes:{},attributes:{}};return n.firstChild&&((n,r)=>{let s=n;for(;s=s.walk();)Ph(e,t,s,o)})(n),o})(e,t,n);Lh(r,o)},Ih=(e,t,n)=>{if(e.insert&&t(n)){const e=new Rg("br",1);e.attr("data-mce-bogus","1"),n.empty().append(e)}else n.empty().append(new Rg("#text",3)).value=cr},Fh=(e,t)=>{const n=null==e?void 0:e.firstChild;return C(n)&&n===e.lastChild&&n.name===t},Uh=(e,t,n,o)=>o.isEmpty(t,n,(t=>((e,t)=>{const n=e.getElementRule(t.name);return!0===(null==n?void 0:n.paddEmpty)})(e,t))),zh=e=>{let t;for(let n=e;n;n=n.parent){const e=n.attr("contenteditable");if("false"===e)break;"true"===e&&(t=n)}return I.from(t)},jh=(e,t,n=e.parent)=>{if(t.getSpecialElements()[e.name])e.empty().remove();else{const o=e.children();for(const e of o)n&&!t.isValidChild(n.name,e.name)&&jh(e,t,n);e.unwrap()}},Hh=(e,t,n,o=E)=>{const r=t.getTextBlockElements(),s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=Dt.makeMap("tr,td,th,tbody,thead,tfoot,table"),l=new Set,d=e=>e!==n&&!i[e.name];for(let n=0;n<e.length;n++){const i=e[n];let c,u,m;if(!i.parent||l.has(i))continue;if(r[i.name]&&"li"===i.parent.name){let e=i.next;for(;e&&r[e.name];)e.name="li",l.add(e),i.parent.insert(e,i.parent),e=e.next;i.unwrap();continue}const f=[i];for(c=i.parent;c&&!t.isValidChild(c.name,i.name)&&d(c);c=c.parent)f.push(c);if(c&&f.length>1)if(t.isValidChild(c.name,i.name)){f.reverse(),u=f[0].clone(),o(u);let e=u;for(let n=0;n<f.length-1;n++){t.isValidChild(e.name,f[n].name)&&n>0?(m=f[n].clone(),o(m),e.append(m)):m=e;for(let e=f[n].firstChild;e&&e!==f[n+1];){const t=e.next;m.append(e),e=t}e=m}Uh(t,s,a,u)?c.insert(i,f[0],!0):(c.insert(u,f[0],!0),c.insert(i,u)),c=f[0],(Uh(t,s,a,c)||Fh(c,"br"))&&c.empty().remove()}else jh(i,t);else if(i.parent){if("li"===i.name){let e=i.prev;if(e&&("ul"===e.name||"ol"===e.name)){e.append(i);continue}if(e=i.next,e&&("ul"===e.name||"ol"===e.name)&&e.firstChild){e.insert(i,e.firstChild,!0);continue}const t=new Rg("ul",1);o(t),i.wrap(t);continue}if(t.isValidChild(i.parent.name,"div")&&t.isValidChild("div",i.name)){const e=new Rg("div",1);o(e),i.wrap(e)}else jh(i,t)}}},$h=(e,t,n=t.parent)=>!(!n||!e.children[t.name]||e.isValidChild(n.name,t.name))||!(!n||"a"!==t.name||!((e,t)=>{let n=e;for(;n;){if("a"===n.name)return!0;n=n.parent}return!1})(n)),Vh=e=>e.collapsed?e:(e=>{const t=Ti.fromRangeStart(e),n=Ti.fromRangeEnd(e),o=e.commonAncestorContainer;return ru(!1,o,n).map((r=>!Oc(t,n,o)&&Oc(t,r,o)?((e,t,n,o)=>{const r=document.createRange();return r.setStart(e,t),r.setEnd(n,o),r})(t.container(),t.offset(),r.container(),r.offset()):e)).getOr(e)})(e),qh=(e,t)=>{let n=t.firstChild,o=t.lastChild;return n&&"meta"===n.name&&(n=n.next),o&&"mce_marker"===o.attr("id")&&(o=o.prev),((e,t)=>{const n=e.getNonEmptyElements();return C(t)&&(t.isEmpty(n)||((e,t)=>e.getBlockElements()[t.name]&&(e=>C(e.firstChild)&&e.firstChild===e.lastChild)(t)&&(e=>"br"===e.name||e.value===cr)(t.firstChild))(e,t))})(e,o)&&(o=null==o?void 0:o.prev),!(!n||n!==o||"ul"!==n.name&&"ol"!==n.name)},Wh=e=>{return e.length>0&&(!(n=e[e.length-1]).firstChild||C(null==(t=n)?void 0:t.firstChild)&&t.firstChild===t.lastChild&&(e=>e.data===cr||tr(e))(t.firstChild))?e.slice(0,-1):e;var t,n},Kh=(e,t)=>{const n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},Gh=(e,t)=>{const n=Ti.after(e),o=eu(t).prev(n);return o?o.toRange():null},Yh=(e,t,n,o)=>{const r=((e,t,n)=>{const o=t.serialize(n);return(e=>{var t,n;const o=e.firstChild,r=e.lastChild;return o&&"META"===o.nodeName&&(null===(t=o.parentNode)||void 0===t||t.removeChild(o)),r&&"mce_marker"===r.id&&(null===(n=r.parentNode)||void 0===n||n.removeChild(r)),e})(e.createFragment(o))})(t,e,o),s=Kh(t,n.startContainer),a=Wh((i=r.firstChild,G(null!==(l=null==i?void 0:i.childNodes)&&void 0!==l?l:[],(e=>"LI"===e.nodeName))));var i,l;const d=t.getRoot(),c=e=>{const o=Ti.fromRangeStart(n),r=eu(t.getRoot()),a=1===e?r.prev(o):r.next(o),i=null==a?void 0:a.getNode();return!i||Kh(t,i)!==s};return s?c(1)?((e,t,n)=>{const o=e.parentNode;return o&&Dt.each(t,(t=>{o.insertBefore(t,e)})),((e,t)=>{const n=Ti.before(e),o=eu(t).next(n);return o?o.toRange():null})(e,n)})(s,a,d):c(2)?((e,t,n,o)=>(o.insertAfter(t.reverse(),e),Gh(t[0],n)))(s,a,d,t):((e,t,n,o)=>{const r=((e,t)=>{const n=t.cloneRange(),o=t.cloneRange();return n.setStartBefore(e),o.setEndAfter(e),[n.cloneContents(),o.cloneContents()]})(e,o),s=e.parentNode;return s&&(s.insertBefore(r[0],e),Dt.each(t,(t=>{s.insertBefore(t,e)})),s.insertBefore(r[1],e),s.removeChild(e)),Gh(t[t.length-1],n)})(s,a,d,n):null},Xh=["pre"],Qh=sr,Jh=(e,t,n)=>{var o,r;const s=e.selection,a=e.dom,i=e.parser,l=n.merge,d=Fg({validate:!0},e.schema),c='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;</span>';-1===t.indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,c);let u=s.getRng();const m=u.startContainer,f=e.getBody();m===f&&s.isCollapsed()&&a.isBlock(f.firstChild)&&((e,t)=>C(t)&&!e.schema.getVoidElements()[t.nodeName])(e,f.firstChild)&&a.isEmpty(f.firstChild)&&(u=a.createRng(),u.setStart(f.firstChild,0),u.setEnd(f.firstChild,0),s.setRng(u)),s.isCollapsed()||(e=>{const t=e.dom,n=Vh(e.selection.getRng());e.selection.setRng(n);const o=t.getParent(n.startContainer,Qh);((e,t,n)=>!!C(n)&&n===e.getParent(t.endContainer,Qh)&&$u(bn(n),t))(t,n,o)?xh(e,n,bn(o)):n.startContainer===n.endContainer&&n.endOffset-n.startOffset==1&&Yo(n.startContainer.childNodes[n.startOffset])?n.deleteContents():e.getDoc().execCommand("Delete",!1)})(e);const g=s.getNode(),p={context:g.nodeName.toLowerCase(),data:n.data,insert:!0},h=i.parse(t,p);if(!0===n.paste&&qh(e.schema,h)&&((e,t)=>!!Kh(e,t))(a,g))return u=Yh(d,a,s.getRng(),h),u&&s.setRng(u),t;!0===n.paste&&((e,t,n,o)=>{var r;const s=t.firstChild,a=t.lastChild,i=s===("bookmark"===a.attr("data-mce-type")?a.prev:a),l=H(Xh,s.name);if(i&&l){const t="false"!==s.attr("contenteditable"),a=(null===(r=e.getParent(n,e.isBlock))||void 0===r?void 0:r.nodeName.toLowerCase())===s.name,i=I.from(Oh(o,n)).forall(or);return t&&a&&i}return!1})(a,h,g,e.getBody())&&(null===(o=h.firstChild)||void 0===o||o.unwrap()),(e=>{let t=e;for(;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")})(h);let b=h.lastChild;if(b&&"mce_marker"===b.attr("id")){const t=b;for(b=b.prev;b;b=b.walk(!0))if(3===b.type||!a.isBlock(b.name)){b.parent&&e.schema.isValidChild(b.parent.name,"span")&&b.parent.insert(t,b,"br"===b.name);break}}if(e._selectionOverrides.showBlockCaretContainer(g),p.invalid){e.selection.setContent(c);let n,o=s.getNode();const l=e.getBody();for(Zo(o)?o=n=l:n=o;n&&n!==l;)o=n,n=n.parentNode;t=o===l?l.innerHTML:a.getOuterHTML(o);const u=i.parse(t),m=(e=>{for(let t=e;t;t=t.walk())if("mce_marker"===t.attr("id"))return I.some(t);return I.none()})(u),f=m.bind(zh).getOr(u);m.each((e=>e.replace(h)));const g=h.children(),p=null!==(r=h.parent)&&void 0!==r?r:u;h.unwrap();const b=G(g,(t=>$h(e.schema,t,p)));Hh(b,e.schema,f),Mh(i.getNodeFilters(),i.getAttributeFilters(),u),t=d.serialize(u),o===l?a.setHTML(l,t):a.setOuterHTML(o,t)}else t=d.serialize(h),((e,t,n)=>{var o;if("all"===n.getAttribute("data-mce-bogus"))null===(o=n.parentNode)||void 0===o||o.insertBefore(e.dom.createFragment(t),n);else{const o=n.firstChild,r=n.lastChild;!o||o===r&&"BR"===o.nodeName?e.dom.setHTML(n,t):e.selection.setContent(t,{no_events:!0})}})(e,t,g);var v;return((e,t)=>{const n=e.schema.getTextInlineElements(),o=e.dom;if(t){const t=e.getBody(),r=Dh(e);Dt.each(o.select("*[data-mce-fragment]"),(e=>{if(C(n[e.nodeName.toLowerCase()])&&((e,t)=>ne(jg(e,t),(e=>!(e=>Ug.has(e))(e))))(o,e))for(let n=e.parentElement;C(n)&&n!==t&&!Hg(o,e,n);n=n.parentElement)if(r.compare(n,e)){o.remove(e,!0);break}}))}})(e,l),((e,t)=>{var n,o,r;let s;const a=e.dom,i=e.selection;if(!t)return;i.scrollIntoView(t);const l=Oh(e.getBody(),t);if(l&&"false"===a.getContentEditable(l))return a.remove(t),void i.select(l);let d=a.createRng();const c=t.previousSibling;if(Yo(c)){d.setStart(c,null!==(o=null===(n=c.nodeValue)||void 0===n?void 0:n.length)&&void 0!==o?o:0);const e=t.nextSibling;Yo(e)&&(c.appendData(e.data),null===(r=e.parentNode)||void 0===r||r.removeChild(e))}else d.setStartBefore(t),d.setEndBefore(t);const u=a.getParent(t,a.isBlock);a.remove(t),u&&a.isEmpty(u)&&(vo(bn(u)),d.setStart(u,0),d.setEnd(u,0),Qh(u)||(e=>!!e.getAttribute("data-mce-fragment"))(u)||!(s=(t=>{let n=Ti.fromRangeStart(t);return n=eu(e.getBody()).next(n),null==n?void 0:n.toRange()})(d))?a.add(u,a.create("br",{"data-mce-bogus":"1"})):(d=s,a.remove(u))),i.setRng(d)})(e,a.get("mce_marker")),v=e.getBody(),Dt.each(v.getElementsByTagName("*"),(e=>{e.removeAttribute("data-mce-fragment")})),((e,t)=>{I.from(e.getParent(t,"td,th")).map(bn).each(Or)})(a,s.getStart()),((e,t,n)=>{const o=An(bn(n),(e=>xn(e,bn(t))));ie(o,o.length-2).filter(Vt).fold((()=>vs(e,t)),(t=>vs(e,t.dom)))})(e.schema,e.getBody(),s.getStart()),t},Zh=e=>e instanceof Rg,eb=(e,t,n)=>{e.dom.setHTML(e.getBody(),t),!0!==n&&(e=>{mg(e)&&cu(e.getBody()).each((t=>{const n=t.getNode(),o=Wo(n)?cu(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e)},tb=(e,t)=>((e,t)=>{const n=e.dom;return n.parentNode?((e,t)=>J(e.dom.childNodes,(e=>t(bn(e)))).map(bn))(bn(n.parentNode),(n=>!xn(e,n)&&t(n))):I.none()})(e,t).isSome(),nb=e=>w(e)?e:L,ob=(e,t,n)=>{const o=t(e),r=nb(n);return o.orThunk((()=>r(e)?I.none():((e,t,n)=>{let o=e.dom;const r=nb(n);for(;o.parentNode;){o=o.parentNode;const e=bn(o),n=t(e);if(n.isSome())return n;if(r(e))break}return I.none()})(e,t,r)))},rb=sm,sb=(e,t,n)=>{const o=e.formatter.get(n);if(o)for(let n=0;n<o.length;n++){const r=o[n];if(mm(r)&&!1===r.inherit&&e.dom.is(t,r.selector))return!0}return!1},ab=(e,t,n,o,r)=>{const s=e.dom.getRoot();if(t===s)return!1;const a=e.dom.getParent(t,(t=>!!sb(e,t,n)||t.parentNode===s||!!db(e,t,n,o,!0)));return!!db(e,a,n,o,r)},ib=(e,t,n)=>!(!fm(n)||!rb(t,n.inline))||!(!um(n)||!rb(t,n.block))||!!mm(n)&&zo(t)&&e.is(t,n.selector),lb=(e,t,n,o,r,s)=>{const a=n[o],i="attributes"===o;if(w(n.onmatch))return n.onmatch(t,n,o);if(a)if(_e(a)){for(let n=0;n<a.length;n++)if(i?e.getAttrib(t,a[n]):im(e,t,a[n]))return!0}else for(const o in a)if(ke(a,o)){const l=i?e.getAttrib(t,o):im(e,t,o),d=rm(a[o],s),c=y(l)||Ye(l);if(c&&y(d))continue;if(r&&c&&!n.exact)return!1;if((!r||n.exact)&&!rb(l,am(d,o)))return!1}return!0},db=(e,t,n,o,r)=>{const s=e.formatter.get(n),a=e.dom;if(s&&zo(t))for(let n=0;n<s.length;n++){const i=s[n];if(ib(e.dom,t,i)&&lb(a,t,i,"attributes",r,o)&&lb(a,t,i,"styles",r,o)){const n=i.classes;if(n)for(let r=0;r<n.length;r++)if(!e.dom.hasClass(t,rm(n[r],o)))return;return i}}},cb=(e,t,n,o,r)=>{if(o)return ab(e,o,t,n,r);if(o=e.selection.getNode(),ab(e,o,t,n,r))return!0;const s=e.selection.getStart();return!(s===o||!ab(e,s,t,n,r))},ub=Tr,mb=e=>(e=>{const t=[];let n=e;for(;n;){if(Yo(n)&&n.data!==ub||n.childNodes.length>1)return[];zo(n)&&t.push(n),n=n.firstChild}return t})(e).length>0,fb=e=>{if(e){const t=new Io(e,e);for(let e=t.current();e;e=t.next())if(Yo(e))return e}return null},gb=e=>{const t=pn("span");return Qt(t,{id:mu,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&po(t,hn(ub)),t},pb=(e,t,n=!0)=>{const o=e.dom,r=e.selection;if(mb(t))qp(e,!1,bn(t),n);else{const e=r.getRng(),n=o.getParent(t,o.isBlock),s=e.startContainer,a=e.startOffset,i=e.endContainer,l=e.endOffset,d=(e=>{const t=fb(e);return t&&t.data.charAt(0)===ub&&t.deleteData(0,1),t})(t);o.remove(t,!0),s===d&&a>0&&e.setStart(d,a-1),i===d&&l>0&&e.setEnd(d,l-1),n&&o.isEmpty(n)&&Ar(bn(n)),r.setRng(e)}},hb=(e,t,n=!0)=>{const o=e.dom,r=e.selection;if(t)pb(e,t,n);else if(!(t=gu(e.getBody(),r.getStart())))for(;t=o.get(mu);)pb(e,t,!1)},bb=(e,t)=>(e.appendChild(t),t),vb=(e,t)=>{var n;const o=Y(e,((e,t)=>bb(e,t.cloneNode(!1))),t),r=null!==(n=o.ownerDocument)&&void 0!==n?n:document;return bb(o,r.createTextNode(ub))},yb=(e,t,n,o)=>{const a=e.dom,i=e.selection;let l=!1;const d=e.formatter.get(t);if(!d)return;const c=i.getRng(),u=c.startContainer,m=c.startOffset;let f=u;Yo(u)&&(m!==u.data.length&&(l=!0),f=f.parentNode);const g=[];let h;for(;f;){if(db(e,f,t,n,o)){h=f;break}f.nextSibling&&(l=!0),g.push(f),f=f.parentNode}if(h)if(l){const r=i.getBookmark();c.collapse(!0);let s=Am(a,c,d,!0);s=xf(s),e.formatter.remove(t,n,s,o),i.moveToBookmark(r)}else{const l=gu(e.getBody(),h),d=gb(!1).dom;((e,t,n)=>{var o,r;const s=e.dom,a=s.getParent(n,O(em,e.schema));a&&s.isEmpty(a)?null===(o=n.parentNode)||void 0===o||o.replaceChild(t,n):((e=>{const t=Lo(e,"br"),n=G((e=>{const t=[];let n=e.dom;for(;n;)t.push(bn(n)),n=n.lastChild;return t})(e).slice(-1),yr);t.length===n.length&&q(n,yo)})(bn(n)),s.isEmpty(n)?null===(r=n.parentNode)||void 0===r||r.replaceChild(t,n):s.insertAfter(t,n))})(e,d,null!=l?l:h);const c=((e,t,n,o,a,i)=>{const l=e.formatter,d=e.dom,c=G(me(l.get()),(e=>e!==o&&!je(e,"removeformat"))),u=((e,t,n)=>X(n,((n,o)=>{const r=((e,t)=>cm(e,t,(e=>{const t=e=>w(e)||e.length>1&&"%"===e.charAt(0);return $(["styles","attributes"],(n=>xe(e,n).exists((e=>{const n=p(e)?e:we(e);return $(n,t)}))))})))(e,o);return e.formatter.matchNode(t,o,{},r)?n.concat([o]):n}),[]))(e,n,c);if(G(u,(t=>!((e,t,n)=>{const o=["inline","block","selector","attributes","styles","classes"],a=e=>ye(e,((e,t)=>$(o,(e=>e===t))));return cm(e,t,(t=>{const o=a(t);return cm(e,n,(e=>{const t=a(e);return((e,t,n=s)=>r(n).eq(e,t))(o,t)}))}))})(e,t,o))).length>0){const e=n.cloneNode(!1);return d.add(t,e),l.remove(o,a,e,i),d.remove(e),I.some(e)}return I.none()})(e,d,h,t,n,o),u=vb(g.concat(c.toArray()),d);l&&pb(e,l,!1),i.setCursorLocation(u,1),a.isEmpty(h)&&a.remove(h)}},Cb=e=>{const t=gb(!1),n=vb(e,t.dom);return{caretContainer:t,caretPosition:Ti(n,0)}},wb=(e,t)=>{const{caretContainer:n,caretPosition:o}=Cb(t);return mo(bn(e),n),yo(bn(e)),o},xb=(e,t)=>{const n=e.schema.getTextInlineElements();return ke(n,jt(t))&&!fu(t.dom)&&!qo(t.dom)},kb=e=>fu(e.dom)&&mb(e.dom),Eb={},Sb=Ho(["pre"]);Eb.pre||(Eb.pre=[]),Eb.pre.push((e=>{if(!e.selection.getRng().collapsed){const t=e.selection.getSelectedBlocks(),n=G(G(t,Sb),(e=>t=>{const n=t.previousSibling;return Sb(n)&&H(e,n)})(t));q(n,(e=>{((e,t)=>{const n=bn(t),o=Sn(n).dom;yo(n),bo(bn(e),[pn("br",o),pn("br",o),...Pn(n)])})(e.previousSibling,e)}))}}));const _b=["fontWeight","fontStyle","color","fontSize","fontFamily"],Nb=(e,t)=>{const n=e.get(t);return p(n)?J(n,(e=>fm(e)&&"span"===e.inline&&(e=>f(e.styles)&&$(me(e.styles),(e=>H(_b,e))))(e))):I.none()},Rb=(e,t)=>du(t,Ti.fromRangeStart(e)).isNone(),Ab=(e,t)=>!1===lu(t,Ti.fromRangeEnd(e)).exists((e=>!tr(e.getNode())||lu(t,e).isSome())),Ob=e=>t=>lr(t)&&e.isEditable(t),Tb=e=>G(e.getSelectedBlocks(),Ob(e.dom)),Bb=Dt.each,Db=e=>zo(e)&&!_u(e)&&!fu(e)&&!qo(e),Pb=(e,t)=>{for(let n=e;n;n=n[t]){if(Yo(n)&&Ge(n.data))return e;if(zo(n)&&!_u(n))return n}return e},Lb=(e,t,n)=>{const o=Dh(e),r=zo(t)&&Xu(t),s=zo(n)&&Xu(n);if(r&&s){const r=Pb(t,"previousSibling"),s=Pb(n,"nextSibling");if(o.compare(r,s)){for(let e=r.nextSibling;e&&e!==s;){const t=e;e=e.nextSibling,r.appendChild(t)}return e.dom.remove(s),Dt.each(Dt.grep(s.childNodes),(e=>{r.appendChild(e)})),r}}return n},Mb=(e,t,n,o)=>{var r;if(o&&!1!==t.merge_siblings){const t=null!==(r=Lb(e,Zu(o),o))&&void 0!==r?r:o;Lb(e,t,Zu(t,!0))}},Ib=(e,t,n)=>{Bb(e.childNodes,(e=>{Db(e)&&(t(e)&&n(e),e.hasChildNodes()&&Ib(e,t,n))}))},Fb=(e,t)=>n=>!(!n||!im(e,n,t)),Ub=(e,t,n)=>o=>{e.setStyle(o,t,n),""===o.getAttribute("style")&&o.removeAttribute("style"),((e,t)=>{"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)})(e,o)},zb=nl([{keep:[]},{rename:["name"]},{removed:[]}]),jb=/^(src|href|style)$/,Hb=Dt.each,$b=sm,Vb=(e,t,n)=>e.isChildOf(t,n)&&t!==n&&!e.isBlock(n),qb=(e,t,n)=>{let o=t[n?"startContainer":"endContainer"],r=t[n?"startOffset":"endOffset"];if(zo(o)){const e=o.childNodes.length-1;!n&&r&&r--,o=o.childNodes[r>e?e:r]}return Yo(o)&&n&&r>=o.data.length&&(o=new Io(o,e.getBody()).next()||o),Yo(o)&&!n&&0===r&&(o=new Io(o,e.getBody()).prev()||o),o},Wb=(e,t)=>{const n=t?"firstChild":"lastChild",o=e[n];return(e=>/^(TR|TH|TD)$/.test(e.nodeName))(e)&&o?"TR"===e.nodeName&&o[n]||o:e},Kb=(e,t,n,o)=>{var r;const s=e.create(n,o);return null===(r=t.parentNode)||void 0===r||r.insertBefore(s,t),s.appendChild(t),s},Gb=(e,t,n,o,r)=>{const s=bn(t),a=bn(e.create(o,r)),i=n?Dn(s):Bn(s);return bo(a,i),n?(mo(s,a),go(a,s)):(fo(s,a),po(a,s)),a.dom},Yb=(e,t,n)=>{const o=t.parentNode;let r;const s=e.dom,a=kl(e);um(n)&&o===s.getRoot()&&(n.list_block&&$b(t,n.list_block)||q(ce(t.childNodes),(t=>{tm(e,a,t.nodeName.toLowerCase())?r?r.appendChild(t):(r=Kb(s,t,a),s.setAttribs(r,El(e))):r=null}))),(e=>mm(e)&&fm(e)&&Pt(xe(e,"mixed"),!0))(n)&&!$b(n.inline,t)||s.remove(t,!0)},Xb=(e,t,n)=>x(e)?{name:t,value:null}:{name:e,value:rm(t,n)},Qb=(e,t)=>{""===e.getAttrib(t,"style")&&(t.removeAttribute("style"),t.removeAttribute("data-mce-style"))},Jb=(e,t,n,o,r)=>{let s=!1;Hb(n.styles,((a,i)=>{const{name:l,value:d}=Xb(i,a,o),c=am(d,l);(n.remove_similar||h(d)||!zo(r)||$b(im(e,r,l),c))&&e.setStyle(t,l,""),s=!0})),s&&Qb(e,t)},Zb=(e,t,n,o,r)=>{const s=e.dom,a=Dh(e),i=e.schema;if(fm(t)&&ws(i,t.inline)&&ks(i,o)&&o.parentElement===e.getBody())return Yb(e,o,t),zb.removed();if(!t.ceFalseOverride&&o&&"false"===s.getContentEditableParent(o))return zb.keep();if(o&&!ib(s,o,t)&&!((e,t)=>t.links&&"A"===e.nodeName)(o,t))return zb.keep();const l=o,d=t.preserve_attributes;if(fm(t)&&"all"===t.remove&&p(d)){const e=G(s.getAttribs(l),(e=>H(d,e.name.toLowerCase())));if(s.removeAllAttribs(l),q(e,(e=>s.setAttrib(l,e.name,e.value))),e.length>0)return zb.rename("span")}if("all"!==t.remove){Jb(s,l,t,n,r),Hb(t.attributes,((e,o)=>{const{name:a,value:i}=Xb(o,e,n);if(t.remove_similar||h(i)||!zo(r)||$b(s.getAttrib(r,a),i)){if("class"===a){const e=s.getAttrib(l,a);if(e){let t="";if(q(e.split(/\s+/),(e=>{/mce\-\w+/.test(e)&&(t+=(t?" ":"")+e)})),t)return void s.setAttrib(l,a,t)}}if(jb.test(a)&&l.removeAttribute("data-mce-"+a),"style"===a&&Ho(["li"])(l)&&"none"===s.getStyle(l,"list-style-type"))return l.removeAttribute(a),void s.setStyle(l,"list-style-type","none");"class"===a&&l.removeAttribute("className"),l.removeAttribute(a)}})),Hb(t.classes,(e=>{e=rm(e,n),zo(r)&&!s.hasClass(r,e)||s.removeClass(l,e)}));const e=s.getAttribs(l);for(let t=0;t<e.length;t++){const n=e[t].nodeName;if(!a.isAttributeInternal(n))return zb.keep()}}return"none"!==t.remove?(Yb(e,l,t),zb.removed()):zb.keep()},ev=(e,t,n,o,r)=>Zb(e,t,n,o,r).fold(L,(t=>(e.dom.rename(o,t),!0)),M),tv=(e,t,n,o)=>Zb(e,t,n,o,o).fold(N(o),(t=>(e.dom.createFragment().appendChild(o),e.dom.rename(o,t))),N(null)),nv=(e,t,n,o,r)=>{const s=e.formatter.get(t),a=s[0],i=e.dom,l=e.selection,d=o=>{const i=((e,t,n,o,r)=>{let s;return t.parentNode&&q(dm(e.dom,t.parentNode).reverse(),(t=>{if(!s&&zo(t)&&"_start"!==t.id&&"_end"!==t.id){const a=db(e,t,n,o,r);a&&!1!==a.split&&(s=t)}})),s})(e,o,t,n,r);return((e,t,n,o,r,s,a,i)=>{var l,d;let c,u;const m=e.dom;if(n){const s=n.parentNode;for(let n=o.parentNode;n&&n!==s;n=n.parentNode){let o=m.clone(n,!1);for(let n=0;n<t.length&&(o=tv(e,t[n],i,o),null!==o);n++);o&&(c&&o.appendChild(c),u||(u=o),c=o)}a.mixed&&m.isBlock(n)||(o=null!==(l=m.split(n,o))&&void 0!==l?l:o),c&&u&&(null===(d=r.parentNode)||void 0===d||d.insertBefore(c,r),u.appendChild(r),fm(a)&&Mb(e,a,0,c))}return o})(e,s,i,o,o,0,a,n)},c=t=>$(s,(o=>ev(e,o,n,t,t))),u=t=>{const n=ce(t.childNodes),o=c(t)||$(s,(e=>ib(i,t,e))),r=t.parentNode;if(!o&&C(r)&&gm(a)&&c(r),a.deep&&n.length)for(let e=0;e<n.length;e++)u(n[e]);q(["underline","line-through","overline"],(n=>{zo(t)&&e.dom.getStyle(t,"text-decoration")===n&&t.parentNode&&lm(i,t.parentNode)===n&&ev(e,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:n}},void 0,t)}))},m=e=>{const t=i.get(e?"_start":"_end");if(t){let n=t[e?"firstChild":"lastChild"];return(e=>_u(e)&&zo(e)&&("_start"===e.id||"_end"===e.id))(n)&&(n=n[e?"firstChild":"lastChild"]),Yo(n)&&0===n.data.length&&(n=e?t.previousSibling||t.nextSibling:t.nextSibling||t.previousSibling),i.remove(t,!0),n}return null},f=t=>{let n,o,r=Am(i,t,s,t.collapsed);if(a.split){if(r=xf(r),n=qb(e,r,!0),o=qb(e,r),n!==o){if(n=Wb(n,!0),o=Wb(o,!1),Vb(i,n,o)){const e=I.from(n.firstChild).getOr(n);return d(Gb(i,e,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void m(!0)}if(Vb(i,o,n)){const e=I.from(o.lastChild).getOr(o);return d(Gb(i,e,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void m(!1)}n=Kb(i,n,"span",{id:"_start","data-mce-type":"bookmark"}),o=Kb(i,o,"span",{id:"_end","data-mce-type":"bookmark"});const e=i.createRng();e.setStartAfter(n),e.setEndBefore(o),Om(i,e,(e=>{q(e,(e=>{_u(e)||_u(e.parentNode)||d(e)}))})),d(n),d(o),n=m(!0),o=m()}else n=o=d(n);r.startContainer=n.parentNode?n.parentNode:n,r.startOffset=i.nodeIndex(n),r.endContainer=o.parentNode?o.parentNode:o,r.endOffset=i.nodeIndex(o)+1}Om(i,r,(e=>{q(e,u)}))};if(o){if(Gu(o)){const e=i.createRng();e.setStartBefore(o),e.setEndAfter(o),f(e)}else f(o);zm(e,t,o,n)}else l.isCollapsed()&&fm(a)&&!Uu(e).length?yb(e,t,n,r):(Qu(e,(()=>Wu(e,f)),(o=>fm(a)&&cb(e,t,n,o))),e.nodeChanged()),((e,t,n)=>{"removeformat"===t?q(Tb(e.selection),(t=>{q(_b,(n=>e.dom.setStyle(t,n,""))),Qb(e.dom,t)})):Nb(e.formatter,t).each((t=>{q(Tb(e.selection),(o=>Jb(e.dom,o,t,n,null)))}))})(e,t,n),zm(e,t,o,n)},ov=Dt.each,rv=Dt.each,sv=(e,t,n,o)=>{if(rv(n.styles,((n,r)=>{e.setStyle(t,r,rm(n,o))})),n.styles){const n=e.getAttrib(t,"style");n&&e.setAttrib(t,"data-mce-style",n)}},av=(e,t,n,o)=>{const r=e.formatter.get(t),s=r[0],a=!o&&e.selection.isCollapsed(),i=e.dom,l=e.selection,d=(e,t=s)=>{w(t.onformat)&&t.onformat(e,t,n,o),sv(i,e,t,n),rv(t.attributes,((t,o)=>{i.setAttrib(e,o,rm(t,n))})),rv(t.classes,(t=>{const o=rm(t,n);i.hasClass(e,o)||i.addClass(e,o)}))},c=(e,t)=>{let n=!1;return rv(e,(e=>!(!mm(e)||("false"!==i.getContentEditable(t)||e.ceFalseOverride)&&(!C(e.collapsed)||e.collapsed===a)&&i.is(t,e.selector)&&!fu(t)&&(d(t,e),n=!0,1)))),n},u=e=>{if(m(e)){const t=i.create(e);return d(t),t}return null},f=(o,a,i)=>{const l=[];let m=!0;const f=s.inline||s.block,g=u(f);Om(o,a,(a=>{let u;const p=a=>{let h=!1,b=m,v=!1;const y=a.parentNode,w=y.nodeName.toLowerCase(),x=o.getContentEditable(a);C(x)&&(b=m,m="true"===x,h=!0,v=om(e,a));const k=m&&!h;if(tr(a)&&!((e,t,n,o)=>{if(ld(e)&&fm(t)&&n.parentNode){const t=ra(e.schema),r=tb(bn(n),(e=>fu(e.dom)));return Ee(t,o)&&us(bn(n.parentNode),!1)&&!r}return!1})(e,s,a,w))return u=null,void(um(s)&&o.remove(a));if((o=>(e=>um(e)&&!0===e.wrapper)(s)&&db(e,o,t,n))(a))u=null;else{if(((t,n,o)=>{const r=(e=>um(e)&&!0!==e.wrapper)(s)&&em(e.schema,t)&&tm(e,n,f);return o&&r})(a,w,k)){const e=o.rename(a,f);return d(e),l.push(e),void(u=null)}if(mm(s)){let e=c(r,a);if(!e&&C(y)&&gm(s)&&(e=c(r,y)),!fm(s)||e)return void(u=null)}C(g)&&((t,n,r,a)=>{const l=t.nodeName.toLowerCase(),d=tm(e,f,l)&&tm(e,n,f),c=!i&&Yo(t)&&Br(t.data),u=fu(t),m=!fm(s)||!o.isBlock(t);return(r||a)&&d&&!c&&!u&&m})(a,w,k,v)?(u||(u=o.clone(g,!1),y.insertBefore(u,a),l.push(u)),v&&h&&(m=b),u.appendChild(a)):(u=null,q(ce(a.childNodes),p),h&&(m=b),u=null)}};q(a,p)})),!0===s.links&&q(l,(e=>{const t=e=>{"A"===e.nodeName&&d(e,s),q(ce(e.childNodes),t)};t(e)})),q(l,(a=>{const i=(e=>{let t=0;return q(e.childNodes,(e=>{(e=>C(e)&&Yo(e)&&0===e.length)(e)||_u(e)||t++})),t})(a);!(l.length>1)&&o.isBlock(a)||0!==i?(fm(s)||um(s)&&s.wrapper)&&(s.exact||1!==i||(a=(e=>{const t=J(e.childNodes,Yu).filter((e=>"false"!==o.getContentEditable(e)&&ib(o,e,s)));return t.map((t=>{const n=o.clone(t,!1);return d(n),o.replace(n,e,!0),o.remove(t,!0),n})).getOr(e)})(a)),((e,t,n,o)=>{ov(t,(t=>{fm(t)&&ov(e.dom.select(t.inline,o),(o=>{Db(o)&&ev(e,t,n,o,t.exact?o:null)})),((e,t,n)=>{if(t.clear_child_styles){const o=t.links?"*:not(a)":"*";Bb(e.select(o,n),(n=>{Db(n)&&Xu(n)&&Bb(t.styles,((t,o)=>{e.setStyle(n,o,"")}))}))}})(e.dom,t,o)}))})(e,r,n,a),((e,t,n,o,r)=>{const s=r.parentNode;db(e,s,n,o)&&ev(e,t,o,r)||t.merge_with_parents&&s&&e.dom.getParent(s,(s=>!!db(e,s,n,o)&&(ev(e,t,o,r),!0)))})(e,s,t,n,a),((e,t,n,o)=>{if(t.styles&&t.styles.backgroundColor){const r=Fb(e,"fontSize");Ib(o,(e=>r(e)&&Xu(e)),Ub(e,"backgroundColor",rm(t.styles.backgroundColor,n)))}})(o,s,n,a),((e,t,n,o)=>{const r=t=>{if(zo(t)&&zo(t.parentNode)&&Xu(t)){const n=lm(e,t.parentNode);e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null)}};t.styles&&(t.styles.color||t.styles.textDecoration)&&(Dt.walk(o,r,"childNodes"),r(o))})(o,s,0,a),((e,t,n,o)=>{if(fm(t)&&("sub"===t.inline||"sup"===t.inline)){const n=Fb(e,"fontSize");Ib(o,(e=>n(e)&&Xu(e)),Ub(e,"fontSize",""));const r=G(e.select("sup"===t.inline?"sub":"sup",o),Xu);e.remove(r,!0)}})(o,s,0,a),Mb(e,s,0,a)):o.remove(a,!0)}))},g=Gu(o)?o:l.getNode();if("false"===i.getContentEditable(g)&&!om(e,g))return c(r,o=g),void Um(e,t,o,n);if(s){if(o)if(Gu(o)){if(!c(r,o)){const e=i.createRng();e.setStartBefore(o),e.setEndAfter(o),f(i,Am(i,e,r),!0)}}else f(i,o,!0);else a&&fm(s)&&!Uu(e).length?((e,t,n)=>{let o;const r=e.selection,s=e.formatter.get(t);if(!s)return;const a=r.getRng();let i=a.startOffset;const l=a.startContainer.nodeValue;o=gu(e.getBody(),r.getStart());const d=/[^\s\u00a0\u00ad\u200b\ufeff]/;if(l&&i>0&&i<l.length&&d.test(l.charAt(i))&&d.test(l.charAt(i-1))){const o=r.getBookmark();a.collapse(!0);let i=Am(e.dom,a,s);i=xf(i),e.formatter.apply(t,n,i),r.moveToBookmark(o)}else{let s=o?fb(o):null;o&&(null==s?void 0:s.data)===ub||(c=e.getDoc(),u=gb(!0).dom,o=c.importNode(u,!0),s=o.firstChild,a.insertNode(o),i=1),e.formatter.apply(t,n,o),r.setCursorLocation(s,i)}var c,u})(e,t,n):(l.setRng(Vh(l.getRng())),Qu(e,(()=>{Wu(e,((e,t)=>{const n=t?e:Am(i,e,r);f(i,n,!1)}))}),M),e.nodeChanged()),Nb(e.formatter,t).each((t=>{q((e=>G((e=>{const t=e.getSelectedBlocks(),n=e.getRng();if(e.isCollapsed())return[];if(1===t.length)return Rb(n,t[0])&&Ab(n,t[0])?t:[];{const e=le(t).filter((e=>Rb(n,e))).toArray(),o=de(t).filter((e=>Ab(n,e))).toArray(),r=t.slice(1,-1);return e.concat(r).concat(o)}})(e),Ob(e.dom)))(e.selection),(e=>sv(i,e,t,n)))}));((e,t)=>{ke(Eb,e)&&q(Eb[e],(e=>{e(t)}))})(t,e)}Um(e,t,o,n)},iv=e=>ke(e,"vars"),lv=e=>e.selection.getStart(),dv=(e,t,n,o,r)=>Q(t,(t=>{const s=e.formatter.matchNode(t,n,null!=r?r:{},o);return!v(s)}),(t=>!!sb(e,t,n)||!o&&C(e.formatter.matchNode(t,n,r,!0)))),cv=(e,t)=>{const n=null!=t?t:lv(e);return G(dm(e.dom,n),(e=>zo(e)&&!qo(e)))},uv=(e,t,n)=>{const o=cv(e,t);ge(n,((n,r)=>{const s=n=>{const s=dv(e,o,r,n.similar,iv(n)?n.vars:void 0),a=s.isSome();if(n.state.get()!==a){n.state.set(a);const e=s.getOr(t);iv(n)?n.callback(a,{node:e,format:r,parents:o}):q(n.callbacks,(t=>t(a,{node:e,format:r,parents:o})))}};q([n.withSimilar,n.withoutSimilar],s),q(n.withVars,s)}))},mv=Dt.explode,fv=()=>{const e={};return{addFilter:(t,n)=>{q(mv(t),(t=>{ke(e,t)||(e[t]={name:t,callbacks:[]}),e[t].callbacks.push(n)}))},getFilters:()=>we(e),removeFilter:(t,n)=>{q(mv(t),(t=>{if(ke(e,t))if(C(n)){const o=e[t],r=G(o.callbacks,(e=>e!==n));r.length>0?o.callbacks=r:delete e[t]}else delete e[t]}))}}},gv=(e,t,n)=>{var o;const r=ia();t.convert_fonts_to_spans&&((e,t,n)=>{e.addNodeFilter("font",(e=>{q(e,(e=>{const o=t.parse(e.attr("style")),r=e.attr("color"),s=e.attr("face"),a=e.attr("size");r&&(o.color=r),s&&(o["font-family"]=s),a&&Xe(a).each((e=>{o["font-size"]=n[e-1]})),e.name="span",e.attr("style",t.serialize(o)),((e,t)=>{q(["color","face","size"],(t=>{e.attr(t,null)}))})(e)}))}))})(e,r,Dt.explode(null!==(o=t.font_size_legacy_values)&&void 0!==o?o:"")),((e,t,n)=>{e.addNodeFilter("strike",(e=>{const o="html4"!==t.type;q(e,(e=>{if(o)e.name="s";else{const t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))}}))}))})(e,n,r)},pv=e=>{const[t,...n]=e.split(","),o=n.join(","),r=/data:([^/]+\/[^;]+)(;.+)?/.exec(t);if(r){const e=";base64"===r[2],t=e?(e=>{const t=/([a-z0-9+\/=\s]+)/i.exec(e);return t?t[1]:""})(o):decodeURIComponent(o);return I.some({type:r[1],data:t,base64Encoded:e})}return I.none()},hv=(e,t,n=!0)=>{let o=t;if(n)try{o=atob(t)}catch(e){return I.none()}const r=new Uint8Array(o.length);for(let e=0;e<r.length;e++)r[e]=o.charCodeAt(e);return I.some(new Blob([r],{type:e}))},bv=e=>new Promise(((t,n)=>{const o=new FileReader;o.onloadend=()=>{t(o.result)},o.onerror=()=>{var e;n(null===(e=o.error)||void 0===e?void 0:e.message)},o.readAsDataURL(e)}));let vv=0;const yv=(e,t,n)=>pv(e).bind((({data:e,type:o,base64Encoded:r})=>{if(t&&!r)return I.none();{const t=r?e:btoa(e);return n(t,o)}})),Cv=(e,t,n)=>{const o=e.create("blobid"+vv++,t,n);return e.add(o),o},wv=(e,t,n=!1)=>yv(t,n,((t,n)=>I.from(e.getByData(t,n)).orThunk((()=>hv(n,t).map((n=>Cv(e,n,t)))))));function xv(e){return xv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xv(e)}function kv(e,t){return kv=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},kv(e,t)}function Ev(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function Sv(e,t,n){return Sv=Ev()?Reflect.construct:function(e,t,n){var o=[null];o.push.apply(o,t);var r=new(Function.bind.apply(e,o));return n&&kv(r,n.prototype),r},Sv.apply(null,arguments)}function _v(e){return function(e){if(Array.isArray(e))return Nv(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Nv(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Nv(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nv(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Rv=Object.hasOwnProperty,Av=Object.setPrototypeOf,Ov=Object.isFrozen,Tv=Object.getPrototypeOf,Bv=Object.getOwnPropertyDescriptor,Dv=Object.freeze,Pv=Object.seal,Lv=Object.create,Mv="undefined"!=typeof Reflect&&Reflect,Iv=Mv.apply,Fv=Mv.construct;Iv||(Iv=function(e,t,n){return e.apply(t,n)}),Dv||(Dv=function(e){return e}),Pv||(Pv=function(e){return e}),Fv||(Fv=function(e,t){return Sv(e,_v(t))});var Uv,zv=Xv(Array.prototype.forEach),jv=Xv(Array.prototype.pop),Hv=Xv(Array.prototype.push),$v=Xv(String.prototype.toLowerCase),Vv=Xv(String.prototype.match),qv=Xv(String.prototype.replace),Wv=Xv(String.prototype.indexOf),Kv=Xv(String.prototype.trim),Gv=Xv(RegExp.prototype.test),Yv=(Uv=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Fv(Uv,t)});function Xv(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return Iv(e,t,o)}}function Qv(e,t){Av&&Av(e,null);for(var n=t.length;n--;){var o=t[n];if("string"==typeof o){var r=$v(o);r!==o&&(Ov(t)||(t[n]=r),o=r)}e[o]=!0}return e}function Jv(e){var t,n=Lv(null);for(t in e)Iv(Rv,e,[t])&&(n[t]=e[t]);return n}function Zv(e,t){for(;null!==e;){var n=Bv(e,t);if(n){if(n.get)return Xv(n.get);if("function"==typeof n.value)return Xv(n.value)}e=Tv(e)}return function(e){return console.warn("fallback value for",e),null}}var ey=Dv(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),ty=Dv(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),ny=Dv(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),oy=Dv(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),ry=Dv(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),sy=Dv(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),ay=Dv(["#text"]),iy=Dv(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),ly=Dv(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),dy=Dv(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),cy=Dv(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),uy=Pv(/\{\{[\w\W]*|[\w\W]*\}\}/gm),my=Pv(/<%[\w\W]*|[\w\W]*%>/gm),fy=Pv(/^data-[\-\w.\u00B7-\uFFFF]/),gy=Pv(/^aria-[\-\w]+$/),py=Pv(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),hy=Pv(/^(?:\w+script|data):/i),by=Pv(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),vy=Pv(/^html$/i),yy=function(){return"undefined"==typeof window?null:window},Cy=function(e,t){if("object"!==xv(e)||"function"!=typeof e.createPolicy)return null;var n=null,o="data-tt-policy-suffix";t.currentScript&&t.currentScript.hasAttribute(o)&&(n=t.currentScript.getAttribute(o));var r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}},wy=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:yy(),n=function(t){return e(t)};if(n.version="2.3.8",n.removed=[],!t||!t.document||9!==t.document.nodeType)return n.isSupported=!1,n;var o=t.document,r=t.document,s=t.DocumentFragment,a=t.HTMLTemplateElement,i=t.Node,l=t.Element,d=t.NodeFilter,c=t.NamedNodeMap,u=void 0===c?t.NamedNodeMap||t.MozNamedAttrMap:c,m=t.HTMLFormElement,f=t.DOMParser,g=t.trustedTypes,p=l.prototype,h=Zv(p,"cloneNode"),b=Zv(p,"nextSibling"),v=Zv(p,"childNodes"),y=Zv(p,"parentNode");if("function"==typeof a){var C=r.createElement("template");C.content&&C.content.ownerDocument&&(r=C.content.ownerDocument)}var w=Cy(g,o),x=w?w.createHTML(""):"",k=r,E=k.implementation,S=k.createNodeIterator,_=k.createDocumentFragment,N=k.getElementsByTagName,R=o.importNode,A={};try{A=Jv(r).documentMode?r.documentMode:{}}catch(e){}var O={};n.isSupported="function"==typeof y&&E&&void 0!==E.createHTMLDocument&&9!==A;var T,B,D=uy,P=my,L=fy,M=gy,I=hy,F=by,U=py,z=null,j=Qv({},[].concat(_v(ey),_v(ty),_v(ny),_v(ry),_v(ay))),H=null,$=Qv({},[].concat(_v(iy),_v(ly),_v(dy),_v(cy))),V=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),q=null,W=null,K=!0,G=!0,Y=!1,X=!1,Q=!1,J=!1,Z=!1,ee=!1,te=!1,ne=!1,oe=!0,re=!0,se=!1,ae={},ie=null,le=Qv({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),de=null,ce=Qv({},["audio","video","img","source","image","track"]),ue=null,me=Qv({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),fe="http://www.w3.org/1998/Math/MathML",ge="http://www.w3.org/2000/svg",pe="http://www.w3.org/1999/xhtml",he=pe,be=!1,ve=["application/xhtml+xml","text/html"],ye="text/html",Ce=null,we=r.createElement("form"),xe=function(e){return e instanceof RegExp||e instanceof Function},ke=function(e){Ce&&Ce===e||(e&&"object"===xv(e)||(e={}),e=Jv(e),z="ALLOWED_TAGS"in e?Qv({},e.ALLOWED_TAGS):j,H="ALLOWED_ATTR"in e?Qv({},e.ALLOWED_ATTR):$,ue="ADD_URI_SAFE_ATTR"in e?Qv(Jv(me),e.ADD_URI_SAFE_ATTR):me,de="ADD_DATA_URI_TAGS"in e?Qv(Jv(ce),e.ADD_DATA_URI_TAGS):ce,ie="FORBID_CONTENTS"in e?Qv({},e.FORBID_CONTENTS):le,q="FORBID_TAGS"in e?Qv({},e.FORBID_TAGS):{},W="FORBID_ATTR"in e?Qv({},e.FORBID_ATTR):{},ae="USE_PROFILES"in e&&e.USE_PROFILES,K=!1!==e.ALLOW_ARIA_ATTR,G=!1!==e.ALLOW_DATA_ATTR,Y=e.ALLOW_UNKNOWN_PROTOCOLS||!1,X=e.SAFE_FOR_TEMPLATES||!1,Q=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,ne=e.RETURN_TRUSTED_TYPE||!1,Z=e.FORCE_BODY||!1,oe=!1!==e.SANITIZE_DOM,re=!1!==e.KEEP_CONTENT,se=e.IN_PLACE||!1,U=e.ALLOWED_URI_REGEXP||U,he=e.NAMESPACE||pe,e.CUSTOM_ELEMENT_HANDLING&&xe(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(V.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&xe(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(V.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(V.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),T=T=-1===ve.indexOf(e.PARSER_MEDIA_TYPE)?ye:e.PARSER_MEDIA_TYPE,B="application/xhtml+xml"===T?function(e){return e}:$v,X&&(G=!1),te&&(ee=!0),ae&&(z=Qv({},_v(ay)),H=[],!0===ae.html&&(Qv(z,ey),Qv(H,iy)),!0===ae.svg&&(Qv(z,ty),Qv(H,ly),Qv(H,cy)),!0===ae.svgFilters&&(Qv(z,ny),Qv(H,ly),Qv(H,cy)),!0===ae.mathMl&&(Qv(z,ry),Qv(H,dy),Qv(H,cy))),e.ADD_TAGS&&(z===j&&(z=Jv(z)),Qv(z,e.ADD_TAGS)),e.ADD_ATTR&&(H===$&&(H=Jv(H)),Qv(H,e.ADD_ATTR)),e.ADD_URI_SAFE_ATTR&&Qv(ue,e.ADD_URI_SAFE_ATTR),e.FORBID_CONTENTS&&(ie===le&&(ie=Jv(ie)),Qv(ie,e.FORBID_CONTENTS)),re&&(z["#text"]=!0),Q&&Qv(z,["html","head","body"]),z.table&&(Qv(z,["tbody"]),delete q.tbody),Dv&&Dv(e),Ce=e)},Ee=Qv({},["mi","mo","mn","ms","mtext"]),Se=Qv({},["foreignobject","desc","title","annotation-xml"]),_e=Qv({},["title","style","font","a","script"]),Ne=Qv({},ty);Qv(Ne,ny),Qv(Ne,oy);var Re=Qv({},ry);Qv(Re,sy);var Ae=function(e){var t=y(e);t&&t.tagName||(t={namespaceURI:pe,tagName:"template"});var n=$v(e.tagName),o=$v(t.tagName);return e.namespaceURI===ge?t.namespaceURI===pe?"svg"===n:t.namespaceURI===fe?"svg"===n&&("annotation-xml"===o||Ee[o]):Boolean(Ne[n]):e.namespaceURI===fe?t.namespaceURI===pe?"math"===n:t.namespaceURI===ge?"math"===n&&Se[o]:Boolean(Re[n]):e.namespaceURI===pe&&!(t.namespaceURI===ge&&!Se[o])&&!(t.namespaceURI===fe&&!Ee[o])&&!Re[n]&&(_e[n]||!Ne[n])},Oe=function(e){Hv(n.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=x}catch(t){e.remove()}}},Te=function(e,t){try{Hv(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){Hv(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!H[e])if(ee||te)try{Oe(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Be=function(e){var t,n;if(Z)e="<remove></remove>"+e;else{var o=Vv(e,/^[\r\n\t ]+/);n=o&&o[0]}"application/xhtml+xml"===T&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var s=w?w.createHTML(e):e;if(he===pe)try{t=(new f).parseFromString(s,T)}catch(e){}if(!t||!t.documentElement){t=E.createDocument(he,"template",null);try{t.documentElement.innerHTML=be?"":s}catch(e){}}var a=t.body||t.documentElement;return e&&n&&a.insertBefore(r.createTextNode(n),a.childNodes[0]||null),he===pe?N.call(t,Q?"html":"body")[0]:Q?t.documentElement:a},De=function(e){return S.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},Pe=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore)},Le=function(e){return"object"===xv(i)?e instanceof i:e&&"object"===xv(e)&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Me=function(e,t,o){O[e]&&zv(O[e],(function(e){e.call(n,t,o,Ce)}))},Ie=function(e){var t;if(Me("beforeSanitizeElements",e,null),Pe(e))return Oe(e),!0;if(Gv(/[\u0080-\uFFFF]/,e.nodeName))return Oe(e),!0;var o=B(e.nodeName);if(Me("uponSanitizeElement",e,{tagName:o,allowedTags:z}),e.hasChildNodes()&&!Le(e.firstElementChild)&&(!Le(e.content)||!Le(e.content.firstElementChild))&&Gv(/<[/\w]/g,e.innerHTML)&&Gv(/<[/\w]/g,e.textContent))return Oe(e),!0;if("select"===o&&Gv(/<template/i,e.innerHTML))return Oe(e),!0;if(!z[o]||q[o]){if(!q[o]&&Ue(o)){if(V.tagNameCheck instanceof RegExp&&Gv(V.tagNameCheck,o))return!1;if(V.tagNameCheck instanceof Function&&V.tagNameCheck(o))return!1}if(re&&!ie[o]){var r=y(e)||e.parentNode,s=v(e)||e.childNodes;if(s&&r)for(var a=s.length-1;a>=0;--a)r.insertBefore(h(s[a],!0),b(e))}return Oe(e),!0}return e instanceof l&&!Ae(e)?(Oe(e),!0):"noscript"!==o&&"noembed"!==o||!Gv(/<\/no(script|embed)/i,e.innerHTML)?(X&&3===e.nodeType&&(t=e.textContent,t=qv(t,D," "),t=qv(t,P," "),e.textContent!==t&&(Hv(n.removed,{element:e.cloneNode()}),e.textContent=t)),Me("afterSanitizeElements",e,null),!1):(Oe(e),!0)},Fe=function(e,t,n){if(oe&&("id"===t||"name"===t)&&(n in r||n in we))return!1;if(G&&!W[t]&&Gv(L,t));else if(K&&Gv(M,t));else if(!H[t]||W[t]){if(!(Ue(e)&&(V.tagNameCheck instanceof RegExp&&Gv(V.tagNameCheck,e)||V.tagNameCheck instanceof Function&&V.tagNameCheck(e))&&(V.attributeNameCheck instanceof RegExp&&Gv(V.attributeNameCheck,t)||V.attributeNameCheck instanceof Function&&V.attributeNameCheck(t))||"is"===t&&V.allowCustomizedBuiltInElements&&(V.tagNameCheck instanceof RegExp&&Gv(V.tagNameCheck,n)||V.tagNameCheck instanceof Function&&V.tagNameCheck(n))))return!1}else if(ue[t]);else if(Gv(U,qv(n,F,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Wv(n,"data:")||!de[e])if(Y&&!Gv(I,qv(n,F,"")));else if(n)return!1;return!0},Ue=function(e){return e.indexOf("-")>0},ze=function(e){var t,n,o,r;Me("beforeSanitizeAttributes",e,null);var s=e.attributes;if(s){var a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:H};for(r=s.length;r--;){var i=t=s[r],l=i.name,d=i.namespaceURI;n="value"===l?t.value:Kv(t.value),o=B(l);var c=n;if(a.attrName=o,a.attrValue=n,a.keepAttr=!0,a.forceKeepAttr=void 0,Me("uponSanitizeAttribute",e,a),n=a.attrValue,!a.forceKeepAttr)if(a.keepAttr)if(Gv(/\/>/i,n))Te(l,e);else{X&&(n=qv(n,D," "),n=qv(n,P," "));var u=B(e.nodeName);if(Fe(u,o,n)){if(n!==c)try{d?e.setAttributeNS(d,l,n):e.setAttribute(l,n)}catch(t){Te(l,e)}}else Te(l,e)}else Te(l,e)}Me("afterSanitizeAttributes",e,null)}},je=function e(t){var n,o=De(t);for(Me("beforeSanitizeShadowDOM",t,null);n=o.nextNode();)Me("uponSanitizeShadowNode",n,null),Ie(n)||(n.content instanceof s&&e(n.content),ze(n));Me("afterSanitizeShadowDOM",t,null)};return n.sanitize=function(e,r){var a,l,d,c,u;if((be=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Le(e)){if("function"!=typeof e.toString)throw Yv("toString is not a function");if("string"!=typeof(e=e.toString()))throw Yv("dirty is not a string, aborting")}if(!n.isSupported){if("object"===xv(t.toStaticHTML)||"function"==typeof t.toStaticHTML){if("string"==typeof e)return t.toStaticHTML(e);if(Le(e))return t.toStaticHTML(e.outerHTML)}return e}if(J||ke(r),n.removed=[],"string"==typeof e&&(se=!1),se){if(e.nodeName){var m=B(e.nodeName);if(!z[m]||q[m])throw Yv("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof i)1===(l=(a=Be("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===l.nodeName||"HTML"===l.nodeName?a=l:a.appendChild(l);else{if(!ee&&!X&&!Q&&-1===e.indexOf("<"))return w&&ne?w.createHTML(e):e;if(!(a=Be(e)))return ee?null:ne?x:""}a&&Z&&Oe(a.firstChild);for(var f=De(se?e:a);d=f.nextNode();)3===d.nodeType&&d===c||Ie(d)||(d.content instanceof s&&je(d.content),ze(d),c=d);if(c=null,se)return e;if(ee){if(te)for(u=_.call(a.ownerDocument);a.firstChild;)u.appendChild(a.firstChild);else u=a;return H.shadowroot&&(u=R.call(o,u,!0)),u}var g=Q?a.outerHTML:a.innerHTML;return Q&&z["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&Gv(vy,a.ownerDocument.doctype.name)&&(g="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+g),X&&(g=qv(g,D," "),g=qv(g,P," ")),w&&ne?w.createHTML(g):g},n.setConfig=function(e){ke(e),J=!0},n.clearConfig=function(){Ce=null,J=!1},n.isValidAttribute=function(e,t,n){Ce||ke({});var o=B(e),r=B(t);return Fe(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&(O[e]=O[e]||[],Hv(O[e],t))},n.removeHook=function(e){if(O[e])return jv(O[e])},n.removeHooks=function(e){O[e]&&(O[e]=[])},n.removeAllHooks=function(){O={}},n}();const xy=Dt.each,ky=Dt.trim,Ey=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],Sy={ftp:21,http:80,https:443,mailto:25},_y=["img","video"],Ny=(e,t,n)=>{const o=(e=>{try{return decodeURIComponent(e)}catch(t){return unescape(e)}})(t).replace(/\s/g,"");return!e.allow_script_urls&&(!!/((java|vb)script|mhtml):/i.test(o)||!e.allow_html_data_urls&&(/^data:image\//i.test(o)?((e,t)=>C(e)?!e:!C(t)||!H(_y,t))(e.allow_svg_data_urls,n)&&/^data:image\/svg\+xml/i.test(o):/^data:/i.test(o)))};class Ry{constructor(e,t={}){this.path="",this.directory="",e=ky(e),this.settings=t;const n=t.base_uri,o=this;if(/^([\w\-]+):([^\/]{2})/i.test(e)||/^\s*#/.test(e))return void(o.source=e);const r=0===e.indexOf("//");if(0!==e.indexOf("/")||r||(e=(n&&n.protocol||"http")+"://mce_host"+e),!/^[\w\-]*:?\/\//.test(e)){const t=n?n.path:new Ry(document.location.href).directory;if(""===(null==n?void 0:n.protocol))e="//mce_host"+o.toAbsPath(t,e);else{const r=/([^#?]*)([#?]?.*)/.exec(e);r&&(e=(n&&n.protocol||"http")+"://mce_host"+o.toAbsPath(t,r[1])+r[2])}}e=e.replace(/@@/g,"(mce_at)");const s=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?(\[[a-zA-Z0-9:.%]+\]|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e);s&&xy(Ey,((e,t)=>{let n=s[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n})),n&&(o.protocol||(o.protocol=n.protocol),o.userInfo||(o.userInfo=n.userInfo),o.port||"mce_host"!==o.host||(o.port=n.port),o.host&&"mce_host"!==o.host||(o.host=n.host),o.source=""),r&&(o.protocol="")}static parseDataUri(e){let t;const n=decodeURIComponent(e).split(","),o=/data:([^;]+)/.exec(n[0]);return o&&(t=o[1]),{type:t,data:n[1]}}static isDomSafe(e,t,n={}){if(n.allow_script_urls)return!0;{const o=Ks.decode(e).replace(/[\s\u0000-\u001F]+/g,"");return!Ny(n,o,t)}}static getDocumentBaseUrl(e){var t;let n;return n=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?null!==(t=e.href)&&void 0!==t?t:"":e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(n)&&(n=n.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(n)||(n+="/")),n}setPath(e){const t=/^(.*?)\/?(\w+)?$/.exec(e);t&&(this.path=t[0],this.directory=t[1],this.file=t[2]),this.source="",this.getURI()}toRelative(e){if("./"===e)return e;const t=new Ry(e,{base_uri:this});if("mce_host"!==t.host&&this.host!==t.host&&t.host||this.port!==t.port||this.protocol!==t.protocol&&""!==t.protocol)return t.getURI();const n=this.getURI(),o=t.getURI();if(n===o||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===o)return n;let r=this.toRelPath(this.path,t.path);return t.query&&(r+="?"+t.query),t.anchor&&(r+="#"+t.anchor),r}toAbsolute(e,t){const n=new Ry(e,{base_uri:this});return n.getURI(t&&this.isSameOrigin(n))}isSameOrigin(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;const t=this.protocol?Sy[this.protocol]:null;if(t&&(this.port||t)==(e.port||t))return!0}return!1}toRelPath(e,t){let n,o,r=0,s="";const a=e.substring(0,e.lastIndexOf("/")).split("/"),i=t.split("/");if(a.length>=i.length)for(n=0,o=a.length;n<o;n++)if(n>=i.length||a[n]!==i[n]){r=n+1;break}if(a.length<i.length)for(n=0,o=i.length;n<o;n++)if(n>=a.length||a[n]!==i[n]){r=n+1;break}if(1===r)return t;for(n=0,o=a.length-(r-1);n<o;n++)s+="../";for(n=r-1,o=i.length;n<o;n++)s+=n!==r-1?"/"+i[n]:i[n];return s}toAbsPath(e,t){let n=0;const o=/\/$/.test(t)?"/":"",r=e.split("/"),s=t.split("/"),a=[];xy(r,(e=>{e&&a.push(e)}));const i=[];for(let e=s.length-1;e>=0;e--)0!==s[e].length&&"."!==s[e]&&(".."!==s[e]?n>0?n--:i.push(s[e]):n++);const l=a.length-n;let d;return d=l<=0?oe(i).join("/"):a.slice(0,l).join("/")+"/"+oe(i).join("/"),0!==d.indexOf("/")&&(d="/"+d),o&&d.lastIndexOf("/")!==d.length-1&&(d+=o),d}getURI(e=!1){let t;return this.source&&!e||(t="",e||(this.protocol?t+=this.protocol+"://":t+="//",this.userInfo&&(t+=this.userInfo+"@"),this.host&&(t+=this.host),this.port&&(t+=":"+this.port)),this.path&&(t+=this.path),this.query&&(t+="?"+this.query),this.anchor&&(t+="#"+this.anchor),this.source=t),this.source}}const Ay=Dt.makeMap("src,href,data,background,action,formaction,poster,xlink:href"),Oy="data-mce-type";let Ty=0;const By=(e,t,n,o)=>{var r,s,a,i;const l=t.validate,d=n.getSpecialElements();8===e.nodeType&&!t.allow_conditional_comments&&/^\[if/i.test(null!==(r=e.nodeValue)&&void 0!==r?r:"")&&(e.nodeValue=" "+e.nodeValue);const c=null!==(s=null==o?void 0:o.tagName)&&void 0!==s?s:e.nodeName.toLowerCase();if(1!==e.nodeType||"body"===c)return;const u=bn(e),f=en(u,Oy),g=Jt(u,"data-mce-bogus");if(!f&&m(g))return void("all"===g?yo(u):Co(u));const p=n.getElementRule(c);if(!l||p){if(C(o)&&(o.allowedTags[c]=!0),l&&p&&!f){if(q(null!==(a=p.attributesForced)&&void 0!==a?a:[],(e=>{Xt(u,e.name,"{$uid}"===e.value?"mce_"+Ty++:e.value)})),q(null!==(i=p.attributesDefault)&&void 0!==i?i:[],(e=>{en(u,e.name)||Xt(u,e.name,"{$uid}"===e.value?"mce_"+Ty++:e.value)})),p.attributesRequired&&!$(p.attributesRequired,(e=>en(u,e))))return void Co(u);if(p.removeEmptyAttrs&&(e=>{const t=e.dom.attributes;return null==t||0===t.length})(u))return void Co(u);p.outputName&&p.outputName!==c&&((e,t)=>{const n=((e,t)=>{const n=pn(t),o=nn(e);return Qt(n,o),n})(e,t);fo(e,n);const o=Pn(e);bo(n,o),yo(e)})(u,p.outputName)}}else ke(d,c)?yo(u):Co(u)},Dy=(e,t,n,o,r)=>!(o in Ay&&Ny(e,r,n))&&(!e.validate||t.isValid(n,o)||He(o,"data-")||He(o,"aria-")),Py=(e,t)=>e.hasAttribute(Oy)&&("id"===t||"class"===t||"style"===t),Ly=(e,t)=>e in t.getBoolAttrs(),My=(e,t,n)=>{const{attributes:o}=e;for(let r=o.length-1;r>=0;r--){const s=o[r],a=s.name,i=s.value;Dy(t,n,e.tagName.toLowerCase(),a,i)||Py(e,a)?Ly(a,n)&&e.setAttribute(a,a):e.removeAttribute(a)}},Iy=(e,t)=>{const n=wy();return n.addHook("uponSanitizeElement",((n,o)=>{By(n,e,t,o)})),n.addHook("uponSanitizeAttribute",((n,o)=>{const r=n.tagName.toLowerCase(),{attrName:s,attrValue:a}=o;o.keepAttr=Dy(e,t,r,s,a),o.keepAttr?(o.allowedAttributes[s]=!0,Ly(s,t)&&(o.attrValue=s),e.allow_svg_data_urls&&He(a,"data:image/svg+xml")&&(o.forceKeepAttr=!0)):Py(n,s)&&(o.forceKeepAttr=!0)})),n},Fy=Dt.makeMap,Uy=Dt.extend,zy=(e,t,n)=>{const o=e.name,r=o in n&&"title"!==o&&"textarea"!==o,s=t.childNodes;for(let t=0,o=s.length;t<o;t++){const o=s[t],a=new Rg(o.nodeName.toLowerCase(),o.nodeType);if(zo(o)){const e=o.attributes;for(let t=0,n=e.length;t<n;t++){const n=e[t];a.attr(n.name,n.value)}}else Yo(o)?(a.value=o.data,r&&(a.raw=!0)):(Jo(o)||Xo(o)||Qo(o))&&(a.value=o.data);zy(a,o,n),e.append(a)}},jy=(e={},t=aa())=>{const n=fv(),o=fv(),r={validate:!0,root_name:"body",sanitize:!0,...e},s=new DOMParser,a=((e,t)=>{if(e.sanitize){const n=Iy(e,t);return(t,o)=>{n.sanitize(t,((e,t)=>{const n={IN_PLACE:!0,ALLOW_UNKNOWN_PROTOCOLS:!0,ALLOWED_TAGS:["#comment","#cdata-section","body"],ALLOWED_ATTR:[]};return n.PARSER_MEDIA_TYPE=t,e.allow_script_urls?n.ALLOWED_URI_REGEXP=/.*/:e.allow_html_data_urls&&(n.ALLOWED_URI_REGEXP=/^(?!(\w+script|mhtml):)/i),n})(e,o)),n.removed=[]}}return(n,o)=>{const r=document.createNodeIterator(n,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_COMMENT|NodeFilter.SHOW_TEXT);let s;for(;s=r.nextNode();)By(s,e,t),zo(s)&&My(s,e,t)}})(r,t),i=n.addFilter,l=n.getFilters,d=n.removeFilter,c=o.addFilter,u=o.getFilters,f=o.removeFilter,g=(e,n)=>{const o=m(n.attr(Oy)),r=1===n.type&&!ke(e,n.name)&&!((e,t)=>1===t.type&&ws(e,t.name)&&m(t.attr(ms)))(t,n);return 3===n.type||r&&!o},p={schema:t,addAttributeFilter:c,getAttributeFilters:u,removeAttributeFilter:f,addNodeFilter:i,getNodeFilters:l,removeNodeFilter:d,parse:(e,n={})=>{var o;const i=r.validate,d=null!==(o=n.context)&&void 0!==o?o:r.root_name,c=((e,n,o="html")=>{const r="xhtml"===o?"application/xhtml+xml":"text/html",i=ke(t.getSpecialElements(),n.toLowerCase()),l=i?`<${n}>${e}</${n}>`:e,d="xhtml"===o?`<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>${l}</body></html>`:`<body>${l}</body>`,c=s.parseFromString(d,r).body;return a(c,r),i?c.firstChild:c})(e,d,n.format);vs(t,c);const m=new Rg(d,11);zy(m,c,t.getSpecialElements()),c.innerHTML="";const[f,p]=((e,t,n,o)=>{const r=n.validate,s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=Uy(Fy("script,style,head,html,body,title,meta,param"),t.getBlockElements()),l=ra(t),d=/[ \t\r\n]+/g,c=/^[ \t\r\n]+/,u=/[ \t\r\n]+$/,m=e=>{let t=e.parent;for(;C(t);){if(t.name in a)return!0;t=t.parent}return!1},f=e=>e.name in i&&!Ss(t,e),g=(t,n)=>{const r=n?t.prev:t.next;return!C(r)&&!y(t.parent)&&f(t.parent)&&(t.parent!==e||!0===o.isRootContent)};return[e=>{var t;if(3===e.type&&!m(e)){let n=null!==(t=e.value)&&void 0!==t?t:"";n=n.replace(d," "),(((e,t)=>C(e)&&(t(e)||"br"===e.name))(e.prev,f)||g(e,!0))&&(n=n.replace(c,"")),0===n.length?e.remove():e.value=n}},e=>{var n;if(1===e.type){const n=t.getElementRule(e.name);if(r&&n){const r=Uh(t,s,a,e);n.paddInEmptyBlock&&r&&(e=>{let n=e;for(;C(n);){if(n.name in l)return Uh(t,s,a,n);n=n.parent}return!1})(e)?Ih(o,f,e):n.removeEmpty&&r?f(e)?e.remove():e.unwrap():n.paddEmpty&&(r||(e=>{var t;return Fh(e,"#text")&&(null===(t=null==e?void 0:e.firstChild)||void 0===t?void 0:t.value)===cr})(e))&&Ih(o,f,e)}}else if(3===e.type&&!m(e)){let t=null!==(n=e.value)&&void 0!==n?n:"";(e.next&&f(e.next)||g(e,!1))&&(t=t.replace(u,"")),0===t.length?e.remove():e.value=t}}]})(m,t,r,n),h=[],b=i?e=>((e,n)=>{$h(t,e)&&n.push(e)})(e,h):E,v={nodes:{},attributes:{}},w=e=>Ph(l(),u(),e,v);if(((e,t,n)=>{const o=[];for(let n=e,r=n;n;r=n,n=n.walk()){const s=n;q(t,(e=>e(s))),y(s.parent)&&s!==e?n=r:o.push(s)}for(let e=o.length-1;e>=0;e--){const t=o[e];q(n,(e=>e(t)))}})(m,[f,w],[p,b]),h.reverse(),i&&h.length>0)if(n.context){const{pass:e,fail:o}=K(h,(e=>e.parent===m));Hh(o,t,m,w),n.invalid=e.length>0}else Hh(h,t,m,w);const x=((e,t)=>{var n;const o=null!==(n=t.forced_root_block)&&void 0!==n?n:e.forced_root_block;return!1===o?"":!0===o?"p":o})(r,n);return x&&("body"===m.name||n.isRootContent)&&((e,n)=>{const o=Uy(Fy("script,style,head,html,body,title,meta,param"),t.getBlockElements()),s=/^[ \t\r\n]+/,a=/[ \t\r\n]+$/;let i=e.firstChild,l=null;const d=e=>{var t,n;e&&(i=e.firstChild,i&&3===i.type&&(i.value=null===(t=i.value)||void 0===t?void 0:t.replace(s,"")),i=e.lastChild,i&&3===i.type&&(i.value=null===(n=i.value)||void 0===n?void 0:n.replace(a,"")))};if(t.isValidChild(e.name,n.toLowerCase())){for(;i;){const t=i.next;g(o,i)?(l||(l=new Rg(n,1),l.attr(r.forced_root_block_attrs),e.insert(l,i)),l.append(i)):(d(l),l=null),i=t}d(l)}})(m,x),n.invalid||Lh(v,n),m}};return((e,t)=>{const n=e.schema;t.remove_trailing_brs&&e.addNodeFilter("br",((e,t,o)=>{const r=Dt.extend({},n.getBlockElements()),s=n.getNonEmptyElements(),a=n.getWhitespaceElements();r.body=1;const i=e=>e.name in r&&Ss(n,e);for(let t=0,l=e.length;t<l;t++){let l=e[t],d=l.parent;if(d&&r[d.name]&&l===d.lastChild){let e=l.prev;for(;e;){const t=e.name;if("span"!==t||"bookmark"!==e.attr("data-mce-type")){"br"===t&&(l=null);break}e=e.prev}if(l&&(l.remove(),Uh(n,s,a,d))){const e=n.getElementRule(d.name);e&&(e.removeEmpty?d.remove():e.paddEmpty&&Ih(o,i,d))}}else{let e=l;for(;d&&d.firstChild===e&&d.lastChild===e&&(e=d,!r[d.name]);)d=d.parent;if(e===d){const e=new Rg("#text",3);e.value=cr,l.replace(e)}}}})),e.addAttributeFilter("href",(e=>{let n=e.length;const o=e=>{const t=e?Dt.trim(e):"";return/\b(noopener)\b/g.test(t)?t:(e=>e.split(" ").filter((e=>e.length>0)).concat(["noopener"]).sort().join(" "))(t)};if(!t.allow_unsafe_link_target)for(;n--;){const t=e[n];"a"===t.name&&"_blank"===t.attr("target")&&t.attr("rel",o(t.attr("rel")))}})),t.allow_html_in_named_anchor||e.addAttributeFilter("id,name",(e=>{let t,n,o,r,s=e.length;for(;s--;)if(r=e[s],"a"===r.name&&r.firstChild&&!r.attr("href"))for(o=r.parent,t=r.lastChild;t&&o;)n=t.prev,o.insert(t,r),t=n})),t.fix_list_elements&&e.addNodeFilter("ul,ol",(e=>{let t,n,o=e.length;for(;o--;)if(t=e[o],n=t.parent,n&&("ul"===n.name||"ol"===n.name))if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{const e=new Rg("li",1);e.attr("style","list-style-type: none"),t.wrap(e)}}));const o=n.getValidClasses();t.validate&&o&&e.addAttributeFilter("class",(e=>{var t;let n=e.length;for(;n--;){const r=e[n],s=null!==(t=r.attr("class"))&&void 0!==t?t:"",a=Dt.explode(s," ");let i="";for(let e=0;e<a.length;e++){const t=a[e];let n=!1,s=o["*"];s&&s[t]&&(n=!0),s=o[r.name],!n&&s&&s[t]&&(n=!0),n&&(i&&(i+=" "),i+=t)}i.length||(i=null),r.attr("class",i)}})),((e,t)=>{const{blob_cache:n}=t;if(n){const t=e=>{const t=e.attr("src");(e=>e.attr("src")===At.transparentSrc||C(e.attr("data-mce-placeholder")))(e)||(e=>C(e.attr("data-mce-bogus")))(e)||y(t)||wv(n,t,!0).each((t=>{e.attr("src",t.blobUri())}))};e.addAttributeFilter("src",(e=>q(e,t)))}})(e,t)})(p,r),((e,t,n)=>{t.inline_styles&&gv(e,t,n)})(p,r,t),p},Hy=(e,t,n)=>{const o=(e=>Zh(e)?Fg({validate:!1}).serialize(e):e)(e),r=t(o);if(r.isDefaultPrevented())return r;if(Zh(e)){if(r.content!==o){const t=jy({validate:!1,forced_root_block:!1,sanitize:n}).parse(r.content,{context:e.name});return{...r,content:t}}return{...r,content:e}}return r},$y=(e,t)=>{if(t.no_events)return tl.value(t);{const n=((e,t)=>e.dispatch("BeforeGetContent",t))(e,t);return n.isDefaultPrevented()?tl.error(Hm(e,{content:"",...n}).content):tl.value(n)}},Vy=(e,t,n)=>{if(n.no_events)return t;{const o=Hy(t,(t=>Hm(e,{...n,content:t})),Qd(e));return o.content}},qy=(e,t)=>{if(t.no_events)return tl.value(t);{const n=Hy(t.content,(n=>((e,t)=>e.dispatch("BeforeSetContent",t))(e,{...t,content:n})),Qd(e));return n.isDefaultPrevented()?(jm(e,n),tl.error(void 0)):tl.value(n)}},Wy=(e,t,n)=>{n.no_events||jm(e,{...n,content:t})},Ky=(e,t,n)=>({element:e,width:t,rows:n}),Gy=(e,t)=>({element:e,cells:t}),Yy=(e,t)=>({x:e,y:t}),Xy=(e,t)=>Zt(e,t).bind(Xe).getOr(1),Qy=(e,t,n)=>{const o=e.rows;return!!(o[n]?o[n].cells:[])[t]},Jy=e=>X(e,((e,t)=>t.cells.length>e?t.cells.length:e),0),Zy=(e,t)=>{const n=e.rows;for(let e=0;e<n.length;e++){const o=n[e].cells;for(let n=0;n<o.length;n++)if(xn(o[n],t))return I.some(Yy(n,e))}return I.none()},eC=(e,t,n,o,r)=>{const s=[],a=e.rows;for(let e=n;e<=r;e++){const n=a[e].cells,r=t<o?n.slice(t,o+1):n.slice(o,t+1);s.push(Gy(a[e].element,r))}return s},tC=e=>((e,t)=>{const n=Za(e.element),o=pn("tbody");return bo(o,t),po(n,o),n})(e,(e=>V(e.rows,(e=>{const t=V(e.cells,(e=>{const t=ei(e);return tn(t,"colspan"),tn(t,"rowspan"),t})),n=Za(e.element);return bo(n,t),n})))(e)),nC=(e,t)=>{const n=bn(t.commonAncestorContainer),o=sp(n,e),r=G(o,Nr),s=((e,t)=>J(e,(e=>"li"===jt(e)&&$u(e,t))).fold(N([]),(t=>(e=>J(e,(e=>"ul"===jt(e)||"ol"===jt(e))))(e).map((e=>{const t=pn(jt(e)),n=ye(co(e),((e,t)=>He(t,"list-style")));return so(t,n),[pn("li"),t]})).getOr([]))))(o,t),a=r.concat(s.length?s:(e=>xr(e)?Nn(e).filter(wr).fold(N([]),(t=>[e,t])):wr(e)?[e]:[])(n));return V(a,Za)},oC=()=>cf([]),rC=(e,t)=>((e,t)=>Qn(t,"table",O(xn,e)))(e,t[0]).bind((e=>{const n=t[0],o=t[t.length-1],r=(e=>{const t=Ky(Za(e),0,[]);return q(Lo(e,"tr"),((e,n)=>{q(Lo(e,"td,th"),((o,r)=>{((e,t,n,o,r)=>{const s=Xy(r,"rowspan"),a=Xy(r,"colspan"),i=e.rows;for(let e=n;e<n+s;e++){i[e]||(i[e]=Gy(ei(o),[]));for(let o=t;o<t+a;o++)i[e].cells[o]=e===n&&o===t?r:Za(r)}})(t,((e,t,n)=>{for(;Qy(e,t,n);)t++;return t})(t,r,n),n,e,o)}))})),Ky(t.element,Jy(t.rows),t.rows)})(e);return((e,t,n)=>Zy(e,t).bind((t=>Zy(e,n).map((n=>((e,t,n)=>{const o=t.x,r=t.y,s=n.x,a=n.y,i=r<a?eC(e,o,r,s,a):eC(e,o,a,s,r);return Ky(e.element,Jy(i),i)})(e,t,n))))))(r,n,o).map((e=>cf([tC(e)])))})).getOrThunk(oC),sC=(e,t)=>{const n=Fu(t,e);return n.length>0?rC(e,n):((e,t)=>t.length>0&&t[0].collapsed?oC():((e,t)=>((e,t)=>{const n=X(t,((e,t)=>(po(t,e),t)),e);return t.length>0?cf([n]):n})(bn(t.cloneContents()),nC(e,t)))(e,t[0]))(e,t)},aC=(e,t)=>t>=0&&t<e.length&&Ru(e.charAt(t)),iC=e=>Dr(e.innerText),lC=e=>zo(e)?e.outerHTML:Yo(e)?Ks.encodeRaw(e.data,!1):Jo(e)?"\x3c!--"+e.data+"--\x3e":"",dC=(e,t)=>(((e,t)=>{let n=0;q(e,(e=>{0===e[0]?n++:1===e[0]?(((e,t,n)=>{const o=(e=>{let t;const n=document.createElement("div"),o=document.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)o.appendChild(t);return o})(t);if(e.hasChildNodes()&&n<e.childNodes.length){const t=e.childNodes[n];e.insertBefore(o,t)}else e.appendChild(o)})(t,e[1],n),n++):2===e[0]&&((e,t)=>{if(e.hasChildNodes()&&t<e.childNodes.length){const n=e.childNodes[t];e.removeChild(n)}})(t,n)}))})(((e,t)=>{const n=e.length+t.length+2,o=new Array(n),r=new Array(n),s=(n,o,r,a,l)=>{const d=i(n,o,r,a);if(null===d||d.start===o&&d.diag===o-a||d.end===n&&d.diag===n-r){let s=n,i=r;for(;s<o||i<a;)s<o&&i<a&&e[s]===t[i]?(l.push([0,e[s]]),++s,++i):o-n>a-r?(l.push([2,e[s]]),++s):(l.push([1,t[i]]),++i)}else{s(n,d.start,r,d.start-d.diag,l);for(let t=d.start;t<d.end;++t)l.push([0,e[t]]);s(d.end,o,d.end-d.diag,a,l)}},a=(n,o,r,s)=>{let a=n;for(;a-o<s&&a<r&&e[a]===t[a-o];)++a;return((e,t,n)=>({start:e,end:t,diag:n}))(n,a,o)},i=(n,s,i,l)=>{const d=s-n,c=l-i;if(0===d||0===c)return null;const u=d-c,m=c+d,f=(m%2==0?m:m+1)/2;let g,p,h,b,v;for(o[1+f]=n,r[1+f]=s+1,g=0;g<=f;++g){for(p=-g;p<=g;p+=2){for(h=p+f,p===-g||p!==g&&o[h-1]<o[h+1]?o[h]=o[h+1]:o[h]=o[h-1]+1,b=o[h],v=b-n+i-p;b<s&&v<l&&e[b]===t[v];)o[h]=++b,++v;if(u%2!=0&&u-g<=p&&p<=u+g&&r[h-u]<=o[h])return a(r[h-u],p+n-i,s,l)}for(p=u-g;p<=u+g;p+=2){for(h=p+f-u,p===u-g||p!==u+g&&r[h+1]<=r[h-1]?r[h]=r[h+1]-1:r[h]=r[h-1],b=r[h]-1,v=b-n+i-p;b>=n&&v>=i&&e[b]===t[v];)r[h]=b--,v--;if(u%2==0&&-g<=p&&p<=g&&r[h]<=o[h+u])return a(r[h],p+n-i,s,l)}}return null},l=[];return s(0,e.length,0,t.length,l),l})(V(ce(t.childNodes),lC),e),t),t),cC=Pe((()=>document.implementation.createHTMLDocument("undo"))),uC=e=>{const t=(n=e.getBody(),G(V(ce(n.childNodes),lC),(e=>e.length>0)));var n;const o=te(t,(t=>{const n=Tg(e.serializer,t);return n.length>0?[n]:[]})),r=o.join("");return(e=>-1!==e.indexOf("</iframe>"))(r)?(e=>({type:"fragmented",fragments:e,content:"",bookmark:null,beforeBookmark:null}))(o):(e=>({type:"complete",fragments:null,content:e,bookmark:null,beforeBookmark:null}))(r)},mC=(e,t,n)=>{const o=n?t.beforeBookmark:t.bookmark;"fragmented"===t.type?dC(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw",no_selection:!C(o)||!hu(o)||!o.isFakeCaret}),o&&(e.selection.moveToBookmark(o),e.selection.scrollIntoView())},fC=e=>"fragmented"===e.type?e.fragments.join(""):e.content,gC=e=>{const t=pn("body",cC());return ko(t,fC(e)),q(Lo(t,"*[data-mce-bogus]"),Co),xo(t)},pC=(e,t)=>!(!e||!t)&&(!!((e,t)=>fC(e)===fC(t))(e,t)||((e,t)=>gC(e)===gC(t))(e,t)),hC=e=>0===e.get(),bC=(e,t,n)=>{hC(n)&&(e.typing=t)},vC=(e,t)=>{e.typing&&(bC(e,!1,t),e.add())},yC=e=>({init:{bindEvents:E},undoManager:{beforeChange:(t,n)=>((e,t,n)=>{hC(t)&&n.set(Ji(e.selection))})(e,t,n),add:(t,n,o,r,s,a)=>((e,t,n,o,r,s,a)=>{const i=uC(e),l=Dt.extend(s||{},i);if(!hC(o)||e.removed)return null;const d=t.data[n.get()];if(e.dispatch("BeforeAddUndo",{level:l,lastLevel:d,originalEvent:a}).isDefaultPrevented())return null;if(d&&pC(d,l))return null;t.data[n.get()]&&r.get().each((e=>{t.data[n.get()].beforeBookmark=e}));const c=hd(e);if(c&&t.data.length>c){for(let e=0;e<t.data.length-1;e++)t.data[e]=t.data[e+1];t.data.length--,n.set(t.data.length)}l.bookmark=Ji(e.selection),n.get()<t.data.length-1&&(t.data.length=n.get()+1),t.data.push(l),n.set(t.data.length-1);const u={level:l,lastLevel:d,originalEvent:a};return n.get()>0?(e.setDirty(!0),e.dispatch("AddUndo",u),e.dispatch("change",u)):e.dispatch("AddUndo",u),l})(e,t,n,o,r,s,a),undo:(t,n,o)=>((e,t,n,o)=>{let r;return t.typing&&(t.add(),t.typing=!1,bC(t,!1,n)),o.get()>0&&(o.set(o.get()-1),r=t.data[o.get()],mC(e,r,!0),e.setDirty(!0),e.dispatch("Undo",{level:r})),r})(e,t,n,o),redo:(t,n)=>((e,t,n)=>{let o;return t.get()<n.length-1&&(t.set(t.get()+1),o=n[t.get()],mC(e,o,!1),e.setDirty(!0),e.dispatch("Redo",{level:o})),o})(e,t,n),clear:(t,n)=>((e,t,n)=>{t.data=[],n.set(0),t.typing=!1,e.dispatch("ClearUndos")})(e,t,n),reset:e=>(e=>{e.clear(),e.add()})(e),hasUndo:(t,n)=>((e,t,n)=>n.get()>0||t.typing&&t.data[0]&&!pC(uC(e),t.data[0]))(e,t,n),hasRedo:(e,t)=>((e,t)=>t.get()<e.data.length-1&&!e.typing)(e,t),transact:(e,t,n)=>((e,t,n)=>(vC(e,t),e.beforeChange(),e.ignore(n),e.add()))(e,t,n),ignore:(e,t)=>((e,t)=>{try{e.set(e.get()+1),t()}finally{e.set(e.get()-1)}})(e,t),extra:(t,n,o,r)=>((e,t,n,o,r)=>{if(t.transact(o)){const o=t.data[n.get()].bookmark,s=t.data[n.get()-1];mC(e,s,!0),t.transact(r)&&(t.data[n.get()-1].beforeBookmark=o)}})(e,t,n,o,r)},formatter:{match:(t,n,o,r)=>cb(e,t,n,o,r),matchAll:(t,n)=>((e,t,n)=>{const o=[],r={},s=e.selection.getStart();return e.dom.getParent(s,(s=>{for(let a=0;a<t.length;a++){const i=t[a];!r[i]&&db(e,s,i,n)&&(r[i]=!0,o.push(i))}}),e.dom.getRoot()),o})(e,t,n),matchNode:(t,n,o,r)=>db(e,t,n,o,r),canApply:t=>((e,t)=>{const n=e.formatter.get(t),o=e.dom;if(n){const t=e.selection.getStart(),r=dm(o,t);for(let e=n.length-1;e>=0;e--){const t=n[e];if(!mm(t))return!0;for(let e=r.length-1;e>=0;e--)if(o.is(r[e],t.selector))return!0}}return!1})(e,t),closest:t=>((e,t)=>{const n=t=>xn(t,bn(e.getBody()));return I.from(e.selection.getStart(!0)).bind((o=>ob(bn(o),(n=>ue(t,(t=>((t,n)=>db(e,t.dom,n)?I.some(n):I.none())(n,t)))),n))).getOrNull()})(e,t),apply:(t,n,o)=>av(e,t,n,o),remove:(t,n,o,r)=>nv(e,t,n,o,r),toggle:(t,n,o)=>((e,t,n,o)=>{const r=e.formatter.get(t);r&&(!cb(e,t,n,o)||"toggle"in r[0]&&!r[0].toggle?av(e,t,n,o):nv(e,t,n,o))})(e,t,n,o),formatChanged:(t,n,o,r,s)=>((e,t,n,o,r,s)=>(((e,t,n,o,r,s)=>{const a=t.get();q(n.split(","),(t=>{const n=xe(a,t).getOrThunk((()=>{const e={withSimilar:{state:Aa(!1),similar:!0,callbacks:[]},withoutSimilar:{state:Aa(!1),similar:!1,callbacks:[]},withVars:[]};return a[t]=e,e})),i=()=>{const n=cv(e);return dv(e,n,t,r,s).isSome()};if(v(s)){const e=r?n.withSimilar:n.withoutSimilar;e.callbacks.push(o),1===e.callbacks.length&&e.state.set(i())}else n.withVars.push({state:Aa(i()),similar:r,vars:s,callback:o})})),t.set(a)})(e,t,n,o,r,s),{unbind:()=>((e,t,n)=>{const o=e.get();q(t.split(","),(e=>xe(o,e).each((t=>{o[e]={withSimilar:{...t.withSimilar,callbacks:G(t.withSimilar.callbacks,(e=>e!==n))},withoutSimilar:{...t.withoutSimilar,callbacks:G(t.withoutSimilar.callbacks,(e=>e!==n))},withVars:G(t.withVars,(e=>e.callback!==n))}})))),e.set(o)})(t,n,o)}))(e,t,n,o,r,s)},editor:{getContent:t=>((e,t)=>I.from(e.getBody()).fold(N("tree"===t.format?new Rg("body",11):""),(n=>Lg(e,t,n))))(e,t),setContent:(t,n)=>((e,t,n)=>I.from(e.getBody()).map((o=>Zh(t)?((e,t,n,o)=>{Mh(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);const r=Fg({validate:!1},e.schema).serialize(n),s=Sr(bn(t))?r:Dt.trim(r);return eb(e,s,o.no_selection),{content:n,html:s}})(e,o,t,n):((e,t,n,o)=>{if(0===n.length||/^\s+$/.test(n)){const r='<br data-mce-bogus="1">';"TABLE"===t.nodeName?n="<tr><td>"+r+"</td></tr>":/^(UL|OL)$/.test(t.nodeName)&&(n="<li>"+r+"</li>");const s=kl(e);return e.schema.isValidChild(t.nodeName.toLowerCase(),s.toLowerCase())?(n=r,n=e.dom.createHTML(s,El(e),n)):n||(n=r),eb(e,n,o.no_selection),{content:n,html:n}}{"raw"!==o.format&&(n=Fg({validate:!1},e.schema).serialize(e.parser.parse(n,{isRootContent:!0,insert:!0})));const r=Sr(bn(t))?n:Dt.trim(n);return eb(e,r,o.no_selection),{content:r,html:r}}})(e,o,t,n))).getOr({content:t,html:Zh(n.content)?"":n.content}))(e,t,n),insertContent:(t,n)=>Jh(e,t,n),addVisual:t=>((e,t)=>{const n=e.dom,o=C(t)?t:e.getBody();q(n.select("table,a",o),(t=>{switch(t.nodeName){case"TABLE":const o=Ed(e),r=n.getAttrib(t,"border");r&&"0"!==r||!e.hasVisual?n.removeClass(t,o):n.addClass(t,o);break;case"A":if(!n.getAttrib(t,"href")){const o=n.getAttrib(t,"name")||t.id,r=Sd(e);o&&e.hasVisual?n.addClass(t,r):n.removeClass(t,r)}}})),e.dispatch("VisualAid",{element:t,hasVisual:e.hasVisual})})(e,t)},selection:{getContent:(t,n)=>((e,t,n={})=>{const o=((e,t)=>({...e,format:t,get:!0,selection:!0,getInner:!0}))(n,t);return $y(e,o).fold(R,(t=>{const n=((e,t)=>{if("text"===t.format)return(e=>I.from(e.selection.getRng()).map((t=>{var n;const o=I.from(e.dom.getParent(t.commonAncestorContainer,e.dom.isBlock)),r=e.getBody(),s=(e=>e.map((e=>e.nodeName)).getOr("div").toLowerCase())(o),a=bn(t.cloneContents());Dg(a),Pg(a);const i=e.dom.add(r,s,{"data-mce-bogus":"all",style:"overflow: hidden; opacity: 0;"},a.dom),l=iC(i),d=Dr(null!==(n=i.textContent)&&void 0!==n?n:"");if(e.dom.remove(i),aC(d,0)||aC(d,d.length-1)){const e=o.getOr(r),t=iC(e),n=t.indexOf(l);return-1===n?l:(aC(t,n-1)?" ":"")+l+(aC(t,n+l.length)?" ":"")}return l})).getOr(""))(e);{const n=((e,t)=>{const n=e.selection.getRng(),o=e.dom.create("body"),r=e.selection.getSel(),s=vg(e,Iu(r)),a=t.contextual?sC(bn(e.getBody()),s).dom:n.cloneContents();return a&&o.appendChild(a),e.selection.serializer.serialize(o,t)})(e,t);return"tree"===t.format?n:e.selection.isCollapsed()?"":n}})(e,t);return Vy(e,n,t)}))})(e,t,n)},autocompleter:{addDecoration:t=>xg(e,t),removeDecoration:()=>((e,t)=>kg(t).each((t=>{const n=e.selection.getBookmark();Co(t),e.selection.moveToBookmark(n)})))(e,bn(e.getBody()))},raw:{getModel:()=>I.none()}}),CC=e=>ke(e.plugins,"rtc"),wC=e=>e.rtcInstance?e.rtcInstance:yC(e),xC=e=>{const t=e.rtcInstance;if(t)return t;throw new Error("Failed to get RTC instance not yet initialized.")},kC=e=>xC(e).init.bindEvents(),EC=e=>0===e.dom.length?(yo(e),I.none()):I.some(e),SC=(e,t,n,o)=>{e.bind((e=>((o?Mp:Lp)(e.dom,o?e.dom.length:0),t.filter(qt).map((t=>((e,t,n,o)=>{const r=e.dom,s=t.dom,a=o?r.length:s.length;o?(Ip(r,s,!1,!o),n.setStart(s,a)):(Ip(s,r,!1,!o),n.setEnd(s,a))})(e,t,n,o)))))).orThunk((()=>{const e=((e,t)=>e.filter((e=>Mm.isBookmarkNode(e.dom))).bind(t?Tn:On))(t,o).or(t).filter(qt);return e.map((e=>((e,t)=>{Nn(e).each((n=>{const o=e.dom;t&&_p(n,Ti(o,0))?Lp(o,0):!t&&Np(n,Ti(o,o.length))&&Mp(o,o.length)}))})(e,o)))}))},_C=(e,t,n)=>{if(ke(e,t)){const o=G(e[t],(e=>e!==n));0===o.length?delete e[t]:e[t]=o}};const NC=e=>!(!e||!e.ownerDocument)&&kn(bn(e.ownerDocument),bn(e)),RC=(e,t,n,o)=>{let r,s;const{selectorChangedWithUnbind:a}=((e,t)=>{let n,o;const r=(t,n)=>J(n,(n=>e.is(n,t))),s=t=>e.getParents(t,void 0,e.getRoot());return{selectorChangedWithUnbind:(e,a)=>(n||(n={},o={},t.on("NodeChange",(e=>{const t=e.element,a=s(t),i={};ge(n,((e,t)=>{r(t,a).each((n=>{o[t]||(q(e,(e=>{e(!0,{node:n,selector:t,parents:a})})),o[t]=e),i[t]=e}))})),ge(o,((e,n)=>{i[n]||(delete o[n],q(e,(e=>{e(!1,{node:t,selector:n,parents:a})})))}))}))),n[e]||(n[e]=[]),n[e].push(a),r(e,s(t.selection.getStart())).each((()=>{o[e]=n[e]})),{unbind:()=>{_C(n,e,a),_C(o,e,a)}})}})(e,o),i=(e,t)=>((e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,selection:!0,content:t}))(n,t);qy(e,o).each((t=>{const n=((e,t)=>{if("raw"!==t.format){const n=e.selection.getRng(),o=e.dom.getParent(n.commonAncestorContainer,e.dom.isBlock),r=o?{context:o.nodeName.toLowerCase()}:{},s=e.parser.parse(t.content,{forced_root_block:!1,...r,...t});return Fg({validate:!1},e.schema).serialize(s)}return t.content})(e,t),o=e.selection.getRng();((e,t)=>{const n=I.from(t.firstChild).map(bn),o=I.from(t.lastChild).map(bn);e.deleteContents(),e.insertNode(t);const r=n.bind(On).filter(qt).bind(EC),s=o.bind(Tn).filter(qt).bind(EC);SC(r,n,e,!0),SC(s,o,e,!1),e.collapse(!1)})(o,o.createContextualFragment(n)),e.selection.setRng(o),$f(e,o),Wy(e,n,t)}))})(o,e,t),l=e=>{const t=c();t.collapse(!!e),u(t)},d=()=>t.getSelection?t.getSelection():t.document.selection,c=()=>{let n;const a=(e,t,n)=>{try{return t.compareBoundaryPoints(e,n)}catch(e){return-1}},i=t.document;if(C(o.bookmark)&&!mg(o)){const e=eg(o);if(e.isSome())return e.map((e=>vg(o,[e])[0])).getOr(i.createRange())}try{const e=d();e&&!Uo(e.anchorNode)&&(n=e.rangeCount>0?e.getRangeAt(0):i.createRange(),n=vg(o,[n])[0])}catch(e){}if(n||(n=i.createRange()),Zo(n.startContainer)&&n.collapsed){const t=e.getRoot();n.setStart(t,0),n.setEnd(t,0)}return r&&s&&(0===a(n.START_TO_START,n,r)&&0===a(n.END_TO_END,n,r)?n=s:(r=null,s=null)),n},u=(e,t)=>{if(!(e=>!!e&&NC(e.startContainer)&&NC(e.endContainer))(e))return;const n=d();if(e=o.dispatch("SetSelectionRange",{range:e,forward:t}).range,n){s=e;try{n.removeAllRanges(),n.addRange(e)}catch(e){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),r=n.rangeCount>0?n.getRangeAt(0):null}if(!e.collapsed&&e.startContainer===e.endContainer&&(null==n?void 0:n.setBaseAndExtent)&&e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()){const t=e.startContainer.childNodes[e.startOffset];t&&"IMG"===t.nodeName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(t,0,t,1))}o.dispatch("AfterSetSelectionRange",{range:e,forward:t})},m=()=>{const t=d(),n=null==t?void 0:t.anchorNode,o=null==t?void 0:t.focusNode;if(!t||!n||!o||Uo(n)||Uo(o))return!0;const r=e.createRng(),s=e.createRng();try{r.setStart(n,t.anchorOffset),r.collapse(!0),s.setStart(o,t.focusOffset),s.collapse(!0)}catch(e){return!0}return r.compareBoundaryPoints(r.START_TO_START,s)<=0},f={dom:e,win:t,serializer:n,editor:o,expand:(t={type:"word"})=>u(kf(e).expand(c(),t)),collapse:l,setCursorLocation:(t,n)=>{const r=e.createRng();C(t)&&C(n)?(r.setStart(t,n),r.setEnd(t,n),u(r),l(!1)):(Vu(e,r,o.getBody(),!0),u(r))},getContent:e=>((e,t={})=>((e,t,n)=>xC(e).selection.getContent(t,n))(e,t.format?t.format:"html",t))(o,e),setContent:i,getBookmark:(e,t)=>g.getBookmark(e,t),moveToBookmark:e=>g.moveToBookmark(e),select:(t,n)=>(((e,t,n)=>I.from(t).bind((t=>I.from(t.parentNode).map((o=>{const r=e.nodeIndex(t),s=e.createRng();return s.setStart(o,r),s.setEnd(o,r+1),n&&(Vu(e,s,t,!0),Vu(e,s,t,!1)),s})))))(e,t,n).each(u),t),isCollapsed:()=>{const e=c(),t=d();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isEditable:()=>{const t=c(),n=o.getBody().querySelectorAll('[data-mce-selected="1"]');return n.length>0?ne(n,(t=>e.isEditable(t.parentElement))):t.startContainer===t.endContainer?e.isEditable(t.startContainer):e.isEditable(t.startContainer)&&e.isEditable(t.endContainer)},isForward:m,setNode:t=>(i(e.getOuterHTML(t)),t),getNode:()=>((e,t)=>{if(!t)return e;let n=t.startContainer,o=t.endContainer;const r=t.startOffset,s=t.endOffset;let a=t.commonAncestorContainer;t.collapsed||(n===o&&s-r<2&&n.hasChildNodes()&&(a=n.childNodes[r]),Yo(n)&&Yo(o)&&(n=n.length===r?bg(n.nextSibling,!0):n.parentNode,o=0===s?bg(o.previousSibling,!1):o.parentNode,n&&n===o&&(a=n)));const i=Yo(a)?a.parentNode:a;return zo(i)?i:e})(o.getBody(),c()),getSel:d,setRng:u,getRng:c,getStart:e=>pg(o.getBody(),c(),e),getEnd:e=>hg(o.getBody(),c(),e),getSelectedBlocks:(t,n)=>((e,t,n,o)=>{const r=[],s=e.getRoot(),a=e.getParent(n||pg(s,t,t.collapsed),e.isBlock),i=e.getParent(o||hg(s,t,t.collapsed),e.isBlock);if(a&&a!==s&&r.push(a),a&&i&&a!==i){let t;const n=new Io(a,s);for(;(t=n.next())&&t!==i;)e.isBlock(t)&&r.push(t)}return i&&a!==i&&i!==s&&r.push(i),r})(e,c(),t,n),normalize:()=>{const t=c(),n=d();if(!(Iu(n).length>1)&&qu(o)){const n=Cf(e,t);return n.each((e=>{u(e,m())})),n.getOr(t)}return t},selectorChanged:(e,t)=>(a(e,t),f),selectorChangedWithUnbind:a,getScrollContainer:()=>{let t,n=e.getRoot();for(;n&&"BODY"!==n.nodeName;){if(n.scrollHeight>n.clientHeight){t=n;break}n=n.parentNode}return t},scrollIntoView:(e,t)=>{C(e)?((e,t,n)=>{(e.inline?zf:Hf)(e,t,n)})(o,e,t):$f(o,c(),t)},placeCaretAt:(e,t)=>u(mf(e,t,o.getDoc())),getBoundingClientRect:()=>{const e=c();return e.collapsed?Ti.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:()=>{t=r=s=null,p.destroy()}},g=Mm(f),p=Ym(f,o);return f.bookmarkManager=g,f.controlSelection=p,f},AC=(e,t,n)=>{-1===Dt.inArray(t,n)&&(e.addAttributeFilter(n,((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)})),t.push(n))},OC=(e,t)=>{const n=["data-mce-selected"],o=t&&t.dom?t.dom:_a.DOM,r=t&&t.schema?t.schema:aa(e);e.entity_encoding=e.entity_encoding||"named",e.remove_trailing_brs=!("remove_trailing_brs"in e)||e.remove_trailing_brs;const s=jy(e,r);return((e,t,n)=>{e.addAttributeFilter("data-mce-tabindex",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];o.attr("tabindex",o.attr("data-mce-tabindex")),o.attr(t,null)}})),e.addAttributeFilter("src,href,style",((e,o)=>{const r="data-mce-"+o,s=t.url_converter,a=t.url_converter_scope;let i=e.length;for(;i--;){const t=e[i];let l=t.attr(r);void 0!==l?(t.attr(o,l.length>0?l:null),t.attr(r,null)):(l=t.attr(o),"style"===o?l=n.serializeStyle(n.parseStyle(l),t.name):s&&(l=s.call(a,l,o,t.name)),t.attr(o,l.length>0?l:null))}})),e.addAttributeFilter("class",(e=>{let t=e.length;for(;t--;){const n=e[t];let o=n.attr("class");o&&(o=o.replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),n.attr("class",o.length>0?o:null))}})),e.addAttributeFilter("data-mce-type",((e,t,n)=>{let o=e.length;for(;o--;){const t=e[o];if("bookmark"===t.attr("data-mce-type")&&!n.cleanup){const e=I.from(t.firstChild).exists((e=>{var t;return!Br(null!==(t=e.value)&&void 0!==t?t:"")}));e?t.unwrap():t.remove()}}})),e.addNodeFilter("noscript",(e=>{var t;let n=e.length;for(;n--;){const o=e[n].firstChild;o&&(o.value=Ks.decode(null!==(t=o.value)&&void 0!==t?t:""))}})),e.addNodeFilter("script,style",((e,n)=>{var o;const r=e=>e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"");let s=e.length;for(;s--;){const a=e[s],i=a.firstChild,l=null!==(o=null==i?void 0:i.value)&&void 0!==o?o:"";if("script"===n){const e=a.attr("type");e&&a.attr("type","mce-no/type"===e?null:e.replace(/^mce\-/,"")),"xhtml"===t.element_format&&i&&l.length>0&&(i.value="// <![CDATA[\n"+r(l)+"\n// ]]>")}else"xhtml"===t.element_format&&i&&l.length>0&&(i.value="\x3c!--\n"+r(l)+"\n--\x3e")}})),e.addNodeFilter("#comment",(e=>{let o=e.length;for(;o--;){const r=e[o],s=r.value;t.preserve_cdata&&0===(null==s?void 0:s.indexOf("[CDATA["))?(r.name="#cdata",r.type=4,r.value=n.decode(s.replace(/^\[CDATA\[|\]\]$/g,""))):0===(null==s?void 0:s.indexOf("mce:protected "))&&(r.name="#text",r.type=3,r.raw=!0,r.value=unescape(s).substr(14))}})),e.addNodeFilter("xml:namespace,input",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];7===o.type?o.remove():1===o.type&&("input"!==t||o.attr("type")||o.attr("type","text"))}})),e.addAttributeFilter("data-mce-type",(t=>{q(t,(t=>{"format-caret"===t.attr("data-mce-type")&&(t.isEmpty(e.schema.getNonEmptyElements())?t.remove():t.unwrap())}))})),e.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-block,data-mce-type,data-mce-resize,data-mce-placeholder",((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)}))})(s,e,o),{schema:r,addNodeFilter:s.addNodeFilter,addAttributeFilter:s.addAttributeFilter,serialize:(n,a={})=>{const i={format:"html",...a},l=((e,t,n)=>((e,t)=>C(e)&&e.hasEventListeners("PreProcess")&&!t.no_events)(e,n)?((e,t,n)=>{let o;const r=e.dom;let s=t.cloneNode(!0);const a=document.implementation;if(a.createHTMLDocument){const e=a.createHTMLDocument("");Dt.each("BODY"===s.nodeName?s.childNodes:[s],(t=>{e.body.appendChild(e.importNode(t,!0))})),s="BODY"!==s.nodeName?e.body.firstChild:e.body,o=r.doc,r.doc=e}return((e,t)=>{e.dispatch("PreProcess",t)})(e,{...n,node:s}),o&&(r.doc=o),s})(e,t,n):t)(t,n,i),d=((e,t,n)=>{const o=Dr(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||Sr(bn(t))?o:Dt.trim(o)})(o,l,i),c=((e,t,n)=>{const o=n.selection?{forced_root_block:!1,...n}:n,r=e.parse(t,o);return(e=>{const t=e=>"br"===(null==e?void 0:e.name),n=e.lastChild;if(t(n)){const e=n.prev;t(e)&&(n.remove(),e.remove())}})(r),r})(s,d,i);return"tree"===i.format?c:((e,t,n,o,r)=>{const s=((e,t,n)=>Fg(e,t).serialize(n))(t,n,o);return((e,t,n)=>{if(!t.no_events&&e){const o=((e,t)=>e.dispatch("PostProcess",t))(e,{...t,content:n});return o.content}return n})(e,r,s)})(t,e,r,c,i)},addRules:r.addValidElements,setRules:r.setValidElements,addTempAttr:O(AC,s,n),getTempAttrs:N(n),getNodeFilters:s.getNodeFilters,getAttributeFilters:s.getAttributeFilters,removeNodeFilter:s.removeNodeFilter,removeAttributeFilter:s.removeAttributeFilter}},TC=(e,t)=>{const n=OC(e,t);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs,getNodeFilters:n.getNodeFilters,getAttributeFilters:n.getAttributeFilters,removeNodeFilter:n.removeNodeFilter,removeAttributeFilter:n.removeAttributeFilter}},BC=(e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,content:t}))(n,t);return qy(e,o).map((t=>{const n=((e,t,n)=>wC(e).editor.setContent(t,n))(e,t.content,t);return Wy(e,n.html,t),n.content})).getOr(t)},DC="autoresize_on_init,content_editable_state,padd_empty_with_br,block_elements,boolean_attributes,editor_deselector,editor_selector,elements,file_browser_callback_types,filepicker_validator_handler,force_hex_style_colors,force_p_newlines,gecko_spellcheck,images_dataimg_filter,media_scripts,mode,move_caret_before_on_enter_elements,non_empty_elements,self_closing_elements,short_ended_elements,special,spellchecker_select_languages,spellchecker_whitelist,tab_focus,tabfocus_elements,table_responsive_width,text_block_elements,text_inline_elements,toolbar_drawer,types,validate,whitespace_elements,paste_enable_default_filters,paste_filter_drop,paste_word_valid_elements,paste_retain_style_properties,paste_convert_word_fake_lists".split(","),PC="template_cdate_classes,template_mdate_classes,template_selected_content_classes,template_preview_replace_values,template_replace_values,templates,template_cdate_format,template_mdate_format".split(","),LC="bbcode,colorpicker,contextmenu,fullpage,legacyoutput,spellchecker,textcolor".split(","),MC=[{name:"template",replacedWith:"Advanced Template"}],IC=(e,t)=>{const n=G(t,(t=>ke(e,t)));return ae(n)},FC=e=>{const t=IC(e,DC),n=e.forced_root_block;return!1!==n&&""!==n||t.push("forced_root_block (false only)"),ae(t)},UC=e=>IC(e,PC),zC=(e,t)=>{const n=Dt.makeMap(e.plugins," "),o=G(t,(e=>ke(n,e)));return ae(o)},jC=e=>zC(e,LC),HC=e=>zC(e,MC.map((e=>e.name))),$C=e=>J(MC,(t=>t.name===e)).fold((()=>e),(t=>`${e}, replaced by ${t.replacedWith}`)),VC=_a.DOM,qC=e=>I.from(e).each((e=>e.destroy())),WC=(()=>{const e={};return{add:(t,n)=>{e[t]=n},get:t=>e[t]?e[t]:{icons:{}},has:t=>ke(e,t)}})(),KC=Pa.ModelManager,GC=(e,t)=>t.dom[e],YC=(e,t)=>parseInt(ao(t,e),10),XC=O(GC,"clientWidth"),QC=O(GC,"clientHeight"),JC=O(YC,"margin-top"),ZC=O(YC,"margin-left"),ew=e=>{const t=[],n=()=>{const t=e.theme;return t&&t.getNotificationManagerImpl?t.getNotificationManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:e,close:e,getArgs:e}})()},o=()=>I.from(t[0]),r=()=>{q(t,(e=>{e.reposition()}))},s=e=>{Z(t,(t=>t===e)).each((e=>{t.splice(e,1)}))},a=(a,i=!0)=>e.removed||!(e=>{return(t=e.inline?e.getBody():e.getContentAreaContainer(),I.from(t).map(bn)).map(Kn).getOr(!1);var t})(e)?{}:(i&&e.dispatch("BeforeOpenNotification",{notification:a}),J(t,(e=>{return t=n().getArgs(e),o=a,!(t.type!==o.type||t.text!==o.text||t.progressBar||t.timeout||o.progressBar||o.timeout);var t,o})).getOrThunk((()=>{e.editorManager.setActive(e);const i=n().open(a,(()=>{s(i),r(),o().fold((()=>e.focus()),(e=>Vf(bn(e.getEl()))))}));return(e=>{t.push(e)})(i),r(),e.dispatch("OpenNotification",{notification:{...i}}),i}))),i=N(t);return(e=>{e.on("SkinLoaded",(()=>{const t=ed(e);t&&a({text:t,type:"warning",timeout:0},!1),r()})),e.on("show ResizeEditor ResizeWindow NodeChange",(()=>{requestAnimationFrame(r)})),e.on("remove",(()=>{q(t.slice(),(e=>{n().close(e)}))}))})(e),{open:a,close:()=>{o().each((e=>{n().close(e),s(e),r()}))},getNotifications:i}},tw=Pa.PluginManager,nw=Pa.ThemeManager,ow=e=>{let t=[];const n=()=>{const t=e.theme;return t&&t.getWindowManagerImpl?t.getWindowManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a WindowManager implementation.")};return{open:e,openUrl:e,alert:e,confirm:e,close:e}})()},o=(e,t)=>(...n)=>t?t.apply(e,n):void 0,r=n=>{(t=>{e.dispatch("CloseWindow",{dialog:t})})(n),t=G(t,(e=>e!==n)),0===t.length&&e.focus()},s=n=>{e.editorManager.setActive(e),Zf(e),e.ui.show();const o=n();return(n=>{t.push(n),(t=>{e.dispatch("OpenWindow",{dialog:t})})(n)})(o),o};return e.on("remove",(()=>{q(t,(e=>{n().close(e)}))})),{open:(e,t)=>s((()=>n().open(e,t,r))),openUrl:e=>s((()=>n().openUrl(e,r))),alert:(e,t,r)=>{const s=n();s.alert(e,o(r||s,t))},confirm:(e,t,r)=>{const s=n();s.confirm(e,o(r||s,t))},close:()=>{I.from(t[t.length-1]).each((e=>{n().close(e),r(e)}))}}},rw=(e,t)=>{e.notificationManager.open({type:"error",text:t})},sw=(e,t)=>{e._skinLoaded?rw(e,t):e.on("SkinLoaded",(()=>{rw(e,t)}))},aw=(e,t,n)=>{Fm(e,t,{message:n}),console.error(n)},iw=(e,t,n)=>n?`Failed to load ${e}: ${n} from url ${t}`:`Failed to load ${e} url: ${t}`,lw=(e,...t)=>{const n=window.console;n&&(n.error?n.error(e,...t):n.log(e,...t))},dw=(e,t)=>{const n=e.editorManager.baseURL+"/skins/content",o=`content${e.editorManager.suffix}.css`;return V(t,(t=>(e=>/^[a-z0-9\-]+$/i.test(e))(t)&&!e.inline?`${n}/${t}/${o}`:e.documentBaseURI.toAbsolute(t)))},cw=(e,t)=>{const n={};return{findAll:(o,r=M)=>{const s=G((e=>e?ce(e.getElementsByTagName("img")):[])(o),(t=>{const n=t.src;return!t.hasAttribute("data-mce-bogus")&&!t.hasAttribute("data-mce-placeholder")&&!(!n||n===At.transparentSrc)&&(He(n,"blob:")?!e.isUploaded(n)&&r(t):!!He(n,"data:")&&r(t))})),a=V(s,(e=>{const o=e.src;if(ke(n,o))return n[o].then((t=>m(t)?t:{image:e,blobInfo:t.blobInfo}));{const r=((e,t)=>{const n=()=>Promise.reject("Invalid data URI");if(He(t,"blob:")){const s=e.getByUri(t);return C(s)?Promise.resolve(s):(o=t,He(o,"blob:")?(e=>fetch(e).then((e=>e.ok?e.blob():Promise.reject())).catch((()=>Promise.reject({message:`Cannot convert ${e} to Blob. Resource might not exist or is inaccessible.`,uriType:"blob"}))))(o):He(o,"data:")?(r=o,new Promise(((e,t)=>{pv(r).bind((({type:e,data:t,base64Encoded:n})=>hv(e,t,n))).fold((()=>t("Invalid data URI")),e)}))):Promise.reject("Unknown URI format")).then((t=>bv(t).then((o=>yv(o,!1,(n=>I.some(Cv(e,t,n)))).getOrThunk(n)))))}var o,r;return He(t,"data:")?wv(e,t).fold(n,(e=>Promise.resolve(e))):Promise.reject("Unknown image data format")})(t,o).then((t=>(delete n[o],{image:e,blobInfo:t}))).catch((e=>(delete n[o],e)));return n[o]=r,r}}));return Promise.all(a)}}},uw=()=>{let e={};const t=(e,t)=>({status:e,resultUri:t}),n=t=>t in e;return{hasBlobUri:n,getResultUri:t=>{const n=e[t];return n?n.resultUri:null},isPending:t=>!!n(t)&&1===e[t].status,isUploaded:t=>!!n(t)&&2===e[t].status,markPending:n=>{e[n]=t(1,null)},markUploaded:(n,o)=>{e[n]=t(2,o)},removeFailed:t=>{delete e[t]},destroy:()=>{e={}}}};let mw=0;const fw=(e,t)=>{const n={},o=(e,n)=>new Promise(((o,r)=>{const s=new XMLHttpRequest;s.open("POST",t.url),s.withCredentials=t.credentials,s.upload.onprogress=e=>{n(e.loaded/e.total*100)},s.onerror=()=>{r("Image upload failed due to a XHR Transport error. Code: "+s.status)},s.onload=()=>{if(s.status<200||s.status>=300)return void r("HTTP Error: "+s.status);const e=JSON.parse(s.responseText);var n,a;e&&m(e.location)?o((n=t.basePath,a=e.location,n?n.replace(/\/$/,"")+"/"+a.replace(/^\//,""):a)):r("Invalid JSON: "+s.responseText)};const a=new FormData;a.append("file",e.blob(),e.filename()),s.send(a)})),r=w(t.handler)?t.handler:o,s=(e,t)=>({url:t,blobInfo:e,status:!0}),a=(e,t)=>({url:"",blobInfo:e,status:!1,error:t}),i=(e,t)=>{Dt.each(n[e],(e=>{e(t)})),delete n[e]};return{upload:(l,d)=>t.url||r!==o?((t,o)=>(t=Dt.grep(t,(t=>!e.isUploaded(t.blobUri()))),Promise.all(Dt.map(t,(t=>e.isPending(t.blobUri())?(e=>{const t=e.blobUri();return new Promise((e=>{n[t]=n[t]||[],n[t].push(e)}))})(t):((t,n,o)=>(e.markPending(t.blobUri()),new Promise((r=>{let l,d;try{const c=()=>{l&&(l.close(),d=E)},u=n=>{c(),e.markUploaded(t.blobUri(),n),i(t.blobUri(),s(t,n)),r(s(t,n))},f=n=>{c(),e.removeFailed(t.blobUri()),i(t.blobUri(),a(t,n)),r(a(t,n))};d=e=>{e<0||e>100||I.from(l).orThunk((()=>I.from(o).map(D))).each((t=>{l=t,t.progressBar.value(e)}))},n(t,d).then(u,(e=>{f(m(e)?{message:e}:e)}))}catch(e){r(a(t,e))}}))))(t,r,o))))))(l,d):new Promise((e=>{e([])}))}},gw=e=>()=>e.notificationManager.open({text:e.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0}),pw=(e,t)=>fw(t,{url:Ll(e),basePath:Ml(e),credentials:Il(e),handler:Fl(e)}),hw=e=>{const t=(()=>{let e=[];const t=e=>{if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");const t=e.id||"blobid"+mw+++(()=>{const e=()=>Math.round(4294967295*Math.random()).toString(36);return"s"+(new Date).getTime().toString(36)+e()+e()+e()})(),n=e.name||t,o=e.blob;var r;return{id:N(t),name:N(n),filename:N(e.filename||n+"."+(r=o.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png","image/apng":"apng","image/avif":"avif","image/svg+xml":"svg","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"}[r.toLowerCase()]||"dat")),blob:N(o),base64:N(e.base64),blobUri:N(e.blobUri||URL.createObjectURL(o)),uri:N(e.uri)}},n=t=>J(e,t).getOrUndefined(),o=e=>n((t=>t.id()===e));return{create:(e,n,o,r,s)=>{if(m(e))return t({id:e,name:r,filename:s,blob:n,base64:o});if(f(e))return t(e);throw new Error("Unknown input type")},add:t=>{o(t.id())||e.push(t)},get:o,getByUri:e=>n((t=>t.blobUri()===e)),getByData:(e,t)=>n((n=>n.base64()===e&&n.blob().type===t)),findFirst:n,removeByUri:t=>{e=G(e,(e=>e.blobUri()!==t||(URL.revokeObjectURL(e.blobUri()),!1)))},destroy:()=>{q(e,(e=>{URL.revokeObjectURL(e.blobUri())})),e=[]}}})();let n,o;const r=uw(),s=[],a=t=>n=>e.selection?t(n):[],i=(e,t,n)=>{let o=0;do{o=e.indexOf(t,o),-1!==o&&(e=e.substring(0,o)+n+e.substr(o+t.length),o+=n.length-t.length+1)}while(-1!==o);return e},l=(e,t,n)=>{const o=`src="${n}"${n===At.transparentSrc?' data-mce-placeholder="1"':""}`;return e=i(e,`src="${t}"`,o),i(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},d=(t,n)=>{q(e.undoManager.data,(e=>{"fragmented"===e.type?e.fragments=V(e.fragments,(e=>l(e,t,n))):e.content=l(e.content,t,n)}))},c=()=>(n||(n=pw(e,r)),p().then(a((o=>{const r=V(o,(e=>e.blobInfo));return n.upload(r,gw(e)).then(a((n=>{const r=[];let s=!1;const a=V(n,((n,a)=>{const{blobInfo:i,image:l}=o[a];let c=!1;return n.status&&Bl(e)?(n.url&&!je(l.src,n.url)&&(s=!0),t.removeByUri(l.src),CC(e)||((t,n)=>{const o=e.convertURL(n,"src");var r;d(t.src,n),Qt(bn(t),{src:Tl(e)?(r=n,r+(-1===r.indexOf("?")?"?":"&")+(new Date).getTime()):n,"data-mce-src":o})})(l,n.url)):n.error&&(n.error.remove&&(d(l.src,At.transparentSrc),r.push(l),c=!0),((e,t)=>{sw(e,Da.translate(["Failed to upload image: {0}",t]))})(e,n.error.message)),{element:l,status:n.status,uploadUri:n.url,blobInfo:i,removed:c}}));return r.length>0&&!CC(e)?e.undoManager.transact((()=>{q(r,(n=>{e.dom.remove(n),t.removeByUri(n.src)}))})):s&&e.undoManager.dispatchChange(),a})))})))),u=()=>Ol(e)?c():Promise.resolve([]),g=e=>ne(s,(t=>t(e))),p=()=>(o||(o=cw(r,t)),o.findAll(e.getBody(),g).then(a((t=>{const n=G(t,(t=>m(t)?(sw(e,t),!1):"blob"!==t.uriType));return CC(e)||q(n,(e=>{d(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")})),n})))),h=n=>n.replace(/src="(blob:[^"]+)"/g,((n,o)=>{const s=r.getResultUri(o);if(s)return'src="'+s+'"';let a=t.getByUri(o);return a||(a=X(e.editorManager.get(),((e,t)=>e||t.editorUpload&&t.editorUpload.blobCache.getByUri(o)),void 0)),a?'src="data:'+a.blob().type+";base64,"+a.base64()+'"':n}));return e.on("SetContent",(()=>{Ol(e)?u():p()})),e.on("RawSaveContent",(e=>{e.content=h(e.content)})),e.on("GetContent",(e=>{e.source_view||"raw"===e.format||"tree"===e.format||(e.content=h(e.content))})),e.on("PostRender",(()=>{e.parser.addNodeFilter("img",(e=>{q(e,(e=>{const n=e.attr("src");if(!n||t.getByUri(n))return;const o=r.getResultUri(n);o&&e.attr("src",o)}))}))})),{blobCache:t,addFilter:e=>{s.push(e)},uploadImages:c,uploadImagesAuto:u,scanForImages:p,destroy:()=>{t.destroy(),r.destroy(),o=n=null}}},bw={remove_similar:!0,inherit:!1},vw={selector:"td,th",...bw},yw={tablecellbackgroundcolor:{styles:{backgroundColor:"%value"},...vw},tablecellverticalalign:{styles:{"vertical-align":"%value"},...vw},tablecellbordercolor:{styles:{borderColor:"%value"},...vw},tablecellclass:{classes:["%value"],...vw},tableclass:{selector:"table",classes:["%value"],...bw},tablecellborderstyle:{styles:{borderStyle:"%value"},...vw},tablecellborderwidth:{styles:{borderWidth:"%value"},...vw}},Cw=N(yw),ww=Dt.each,xw=_a.DOM,kw=e=>C(e)&&f(e),Ew=(e,t)=>{const n=t&&t.schema||aa({}),o=e=>{const t=m(e)?{name:e,classes:[],attrs:{}}:e,n=xw.create(t.name);return((e,t)=>{t.classes.length>0&&xw.addClass(e,t.classes.join(" ")),xw.setAttribs(e,t.attrs)})(n,t),n},r=(e,t,s)=>{let a;const i=t[0],l=kw(i)?i.name:void 0,d=((e,t)=>{const o=n.getElementRule(e.nodeName.toLowerCase()),r=null==o?void 0:o.parentsRequired;return!(!r||!r.length)&&(t&&H(r,t)?t:r[0])})(e,l);if(d)l===d?(a=i,t=t.slice(1)):a=d;else if(i)a=i,t=t.slice(1);else if(!s)return e;const c=a?o(a):xw.create("div");c.appendChild(e),s&&Dt.each(s,(t=>{const n=o(t);c.insertBefore(n,e)}));const u=kw(a)?a.siblings:void 0;return r(c,t,u)},s=xw.create("div");if(e.length>0){const t=e[0],n=o(t),a=kw(t)?t.siblings:void 0;s.appendChild(r(n,e.slice(1),a))}return s},Sw=e=>{let t="div";const n={name:t,classes:[],attrs:{},selector:e=Dt.trim(e)};return"*"!==e&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,((e,t,o,r,s)=>{switch(t){case"#":n.attrs.id=o;break;case".":n.classes.push(o);break;case":":-1!==Dt.inArray("checked disabled enabled read-only required".split(" "),o)&&(n.attrs[o]=o)}if("["===r){const e=s.match(/([\w\-]+)(?:\=\"([^\"]+))?/);e&&(n.attrs[e[1]]=e[2])}return""}))),n.name=t||"div",n},_w=(e,t)=>{let n="",o=id(e);if(""===o)return"";const r=e=>m(e)?e.replace(/%(\w+)/g,""):"",s=(t,n)=>xw.getStyle(null!=n?n:e.getBody(),t,!0);if(m(t)){const n=e.formatter.get(t);if(!n)return"";t=n[0]}if("preview"in t){const e=t.preview;if(!1===e)return"";o=e||o}let a,i=t.block||t.inline||"span";const l=(d=t.selector,m(d)?(d=(d=d.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Dt.map(d.split(/(?:>|\s+(?![^\[\]]+\]))/),(e=>{const t=Dt.map(e.split(/(?:~\+|~|\+)/),Sw),n=t.pop();return t.length&&(n.siblings=t),n})).reverse()):[]);var d;l.length>0?(l[0].name||(l[0].name=i),i=t.selector,a=Ew(l,e)):a=Ew([i],e);const c=xw.select(i,a)[0]||a.firstChild;ww(t.styles,((e,t)=>{const n=r(e);n&&xw.setStyle(c,t,n)})),ww(t.attributes,((e,t)=>{const n=r(e);n&&xw.setAttrib(c,t,n)})),ww(t.classes,(e=>{const t=r(e);xw.hasClass(c,t)||xw.addClass(c,t)})),e.dispatch("PreviewFormats"),xw.setStyles(a,{position:"absolute",left:-65535}),e.getBody().appendChild(a);const u=s("fontSize"),f=/px$/.test(u)?parseInt(u,10):0;return ww(o.split(" "),(e=>{let t=s(e,c);if(!("background-color"===e&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(t)&&(t=s(e),"#ffffff"===Mu(t).toLowerCase())||"color"===e&&"#000000"===Mu(t).toLowerCase())){if("font-size"===e&&/em|%$/.test(t)){if(0===f)return;t=parseFloat(t)/(/%$/.test(t)?100:1)*f+"px"}"border"===e&&t&&(n+="padding:0 2px;"),n+=e+":"+t+";"}})),e.dispatch("AfterPreviewFormats"),xw.remove(a),n},Nw=e=>{const t=(e=>{const t={},n=(e,o)=>{e&&(m(e)?(p(o)||(o=[o]),q(o,(e=>{v(e.deep)&&(e.deep=!mm(e)),v(e.split)&&(e.split=!mm(e)||fm(e)),v(e.remove)&&mm(e)&&!fm(e)&&(e.remove="none"),mm(e)&&fm(e)&&(e.mixed=!0,e.block_expand=!0),m(e.classes)&&(e.classes=e.classes.split(/\s+/))})),t[e]=o):ge(e,((e,t)=>{n(t,e)})))};return n((e=>{const t=e.dom,n=e.schema.type,o={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"left"},inherit:!1,preview:!1},{selector:"img,audio,video",collapsed:!1,styles:{float:"left"},preview:"font-family font-size"},{selector:"table",collapsed:!1,styles:{marginLeft:"0px",marginRight:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"left"}}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"},{selector:".mce-preview-object",ceFalseOverride:!0,styles:{display:"table",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{marginLeft:"auto",marginRight:"auto"},preview:!1}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{float:"right"},preview:"font-family font-size"},{selector:"table",collapsed:!1,styles:{marginRight:"0px",marginLeft:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"right"},preview:!1}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"justify"},inherit:!1,preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all",preserve_attributes:["class","style"]}],italic:[{inline:"em",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all",preserve_attributes:["class","style"]}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all",preserve_attributes:["class","style"]}],strikethrough:(()=>{const e={inline:"span",styles:{textDecoration:"line-through"},exact:!0},t={inline:"strike",remove:"all",preserve_attributes:["class","style"]},o={inline:"s",remove:"all",preserve_attributes:["class","style"]};return"html4"!==n?[o,e,t]:[e,o,t]})(),forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},lineheight:{selector:"h1,h2,h3,h4,h5,h6,p,li,td,th,div",styles:{lineHeight:"%value"}},fontsize_class:{inline:"span",attributes:{class:"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:(e,t,n)=>zo(e)&&e.hasAttribute("href"),onformat:(e,n,o)=>{Dt.each(o,((n,o)=>{t.setAttrib(e,o,n)}))}},lang:{inline:"span",clear_child_styles:!0,remove_similar:!0,attributes:{lang:"%value","data-mce-lang":e=>{var t;return null!==(t=null==e?void 0:e.customValue)&&void 0!==t?t:null}}},removeformat:[{selector:"b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Dt.each("p h1 h2 h3 h4 h5 h6 div address pre dt dd samp".split(/\s/),(e=>{o[e]={block:e,remove:"all"}})),o})(e)),n(Cw()),n(ad(e)),{get:e=>C(e)?t[e]:t,has:e=>ke(t,e),register:n,unregister:e=>(e&&t[e]&&delete t[e],t)}})(e),n=Aa({});return(e=>{e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(let t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])})(e),(e=>{e.on("mouseup keydown",(t=>{((e,t)=>{const n=e.selection,o=e.getBody();hb(e,null,!1),8!==t&&46!==t||!n.isCollapsed()||n.getStart().innerHTML!==ub||hb(e,gu(o,n.getStart())),37!==t&&39!==t||hb(e,gu(o,n.getStart()))})(e,t.keyCode)}))})(e),CC(e)||((e,t)=>{e.set({}),t.on("NodeChange",(n=>{uv(t,n.element,e.get())})),t.on("FormatApply FormatRemove",(n=>{const o=I.from(n.node).map((e=>Gu(e)?e:e.startContainer)).bind((e=>zo(e)?I.some(e):I.from(e.parentElement))).getOrThunk((()=>lv(t)));uv(t,o,e.get())}))})(n,e),{get:t.get,has:t.has,register:t.register,unregister:t.unregister,apply:(t,n,o)=>{((e,t,n,o)=>{xC(e).formatter.apply(t,n,o)})(e,t,n,o)},remove:(t,n,o,r)=>{((e,t,n,o,r)=>{xC(e).formatter.remove(t,n,o,r)})(e,t,n,o,r)},toggle:(t,n,o)=>{((e,t,n,o)=>{xC(e).formatter.toggle(t,n,o)})(e,t,n,o)},match:(t,n,o,r)=>((e,t,n,o,r)=>xC(e).formatter.match(t,n,o,r))(e,t,n,o,r),closest:t=>((e,t)=>xC(e).formatter.closest(t))(e,t),matchAll:(t,n)=>((e,t,n)=>xC(e).formatter.matchAll(t,n))(e,t,n),matchNode:(t,n,o,r)=>((e,t,n,o,r)=>xC(e).formatter.matchNode(t,n,o,r))(e,t,n,o,r),canApply:t=>((e,t)=>xC(e).formatter.canApply(t))(e,t),formatChanged:(t,o,r,s)=>((e,t,n,o,r,s)=>xC(e).formatter.formatChanged(t,n,o,r,s))(e,n,t,o,r,s),getCssText:O(_w,e)}},Rw=e=>{switch(e.toLowerCase()){case"undo":case"redo":case"mcefocus":return!0;default:return!1}},Aw=e=>{const t=Ma(),n=Aa(0),o=Aa(0),r={data:[],typing:!1,beforeChange:()=>{((e,t,n)=>{xC(e).undoManager.beforeChange(t,n)})(e,n,t)},add:(s,a)=>((e,t,n,o,r,s,a)=>xC(e).undoManager.add(t,n,o,r,s,a))(e,r,o,n,t,s,a),dispatchChange:()=>{e.setDirty(!0);const t=uC(e);t.bookmark=Ji(e.selection),e.dispatch("change",{level:t,lastLevel:ie(r.data,o.get()).getOrUndefined()})},undo:()=>((e,t,n,o)=>xC(e).undoManager.undo(t,n,o))(e,r,n,o),redo:()=>((e,t,n)=>xC(e).undoManager.redo(t,n))(e,o,r.data),clear:()=>{((e,t,n)=>{xC(e).undoManager.clear(t,n)})(e,r,o)},reset:()=>{((e,t)=>{xC(e).undoManager.reset(t)})(e,r)},hasUndo:()=>((e,t,n)=>xC(e).undoManager.hasUndo(t,n))(e,r,o),hasRedo:()=>((e,t,n)=>xC(e).undoManager.hasRedo(t,n))(e,r,o),transact:t=>((e,t,n,o)=>xC(e).undoManager.transact(t,n,o))(e,r,n,t),ignore:t=>{((e,t,n)=>{xC(e).undoManager.ignore(t,n)})(e,n,t)},extra:(t,n)=>{((e,t,n,o,r)=>{xC(e).undoManager.extra(t,n,o,r)})(e,r,o,t,n)}};return CC(e)||((e,t,n)=>{const o=Aa(!1),r=e=>{bC(t,!1,n),t.add({},e)};e.on("init",(()=>{t.add()})),e.on("BeforeExecCommand",(e=>{const o=e.command;Rw(o)||(vC(t,n),t.beforeChange())})),e.on("ExecCommand",(e=>{const t=e.command;Rw(t)||r(e)})),e.on("ObjectResizeStart cut",(()=>{t.beforeChange()})),e.on("SaveContent ObjectResized blur",r),e.on("dragend",r),e.on("keyup",(n=>{const s=n.keyCode;n.isDefaultPrevented()||((s>=33&&s<=36||s>=37&&s<=40||45===s||n.ctrlKey)&&(r(),e.nodeChanged()),46!==s&&8!==s||e.nodeChanged(),o.get()&&t.typing&&!pC(uC(e),t.data[0])&&(e.isDirty()||e.setDirty(!0),e.dispatch("TypingUndo"),o.set(!1),e.nodeChanged()))})),e.on("keydown",(e=>{const s=e.keyCode;if(e.isDefaultPrevented())return;if(s>=33&&s<=36||s>=37&&s<=40||45===s)return void(t.typing&&r(e));const a=e.ctrlKey&&!e.altKey||e.metaKey;!(s<16||s>20)||224===s||91===s||t.typing||a||(t.beforeChange(),bC(t,!0,n),t.add({},e),o.set(!0))})),e.on("mousedown",(e=>{t.typing&&r(e)})),e.on("input",(e=>{var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data||(e=>"insertFromPaste"===e.inputType||"insertFromDrop"===e.inputType)(e))&&r(e)})),e.on("AddUndo Undo Redo ClearUndos",(t=>{t.isDefaultPrevented()||e.nodeChanged()}))})(e,r,n),(e=>{e.addShortcut("meta+z","","Undo"),e.addShortcut("meta+y,meta+shift+z","","Redo")})(e),r},Ow=[9,27,Vm.HOME,Vm.END,19,20,44,144,145,33,34,45,16,17,18,91,92,93,Vm.DOWN,Vm.UP,Vm.LEFT,Vm.RIGHT].concat(At.browser.isFirefox()?[224]:[]),Tw="data-mce-placeholder",Bw=e=>"keydown"===e.type||"keyup"===e.type,Dw=e=>{const t=e.keyCode;return t===Vm.BACKSPACE||t===Vm.DELETE},Pw=(e,t)=>({from:e,to:t}),Lw=(e,t)=>{const n=bn(e),o=bn(t.container());return th(n,o).map((e=>((e,t)=>({block:e,position:t}))(e,t)))},Mw=(e,t)=>Xn(t,(e=>Er(e)||or(e.dom)),(t=>xn(t,e))).filter(Vt).getOr(e),Iw=e=>{const t=(e=>{const t=Pn(e);return Z(t,br).fold(N(t),(e=>t.slice(0,e)))})(e);return q(t,yo),t},Fw=(e,t)=>{const n=sp(t,e);return J(n.reverse(),(e=>us(e))).each(yo)},Uw=(e,t,n,o)=>{if(us(n))return Ar(n),cu(n.dom);0===G(Bn(o),(e=>!us(e))).length&&us(t)&&mo(o,pn("br"));const r=du(n.dom,Ti.before(o.dom));return q(Iw(t),(e=>{mo(o,e)})),Fw(e,t),r},zw=(e,t,n)=>{if(us(n)){if(us(t)){const e=e=>{const t=(e,n)=>Mn(e).fold((()=>n),(e=>vr(e)?t(e,n.concat(Za(e))):n));return t(e,[])},o=Y(e(n),((e,t)=>(ho(e,t),t)),Rr());vo(t),po(t,o)}return yo(n),cu(t.dom)}const o=uu(n.dom);return q(Iw(t),(e=>{po(n,e)})),Fw(e,t),o},jw=(e,t)=>{iu(e,t.dom).bind((e=>I.from(e.getNode()))).map(bn).filter(yr).each(yo)},Hw=(e,t,n)=>(jw(!0,t),jw(!1,n),((e,t)=>kn(t,e)?((e,t)=>{const n=sp(t,e);return I.from(n[n.length-1])})(t,e):I.none())(t,n).fold(O(zw,e,t,n),O(Uw,e,t,n))),$w=(e,t,n,o)=>t?Hw(e,o,n):Hw(e,n,o),Vw=(e,t)=>{const n=bn(e.getBody()),o=((e,t,n)=>n.collapsed?((e,t,n)=>{const o=Lw(e,Ti.fromRangeStart(n)),r=o.bind((n=>ru(t,e,n.position).bind((n=>Lw(e,n).map((n=>((e,t,n)=>tr(n.position.getNode())&&!us(n.block)?iu(!1,n.block.dom).bind((o=>o.isEqual(n.position)?ru(t,e,o).bind((t=>Lw(e,t))):I.some(n))).getOr(n):n)(e,t,n)))))));return Lt(o,r,Pw).filter((t=>(e=>!xn(e.from.block,e.to.block))(t)&&((e,t)=>{const n=bn(e);return xn(Mw(n,t.from.block),Mw(n,t.to.block))})(e,t)&&(e=>!1===rr(e.from.block.dom)&&!1===rr(e.to.block.dom))(t)&&(e=>{const t=e=>Cr(e)||Cs(e.dom);return t(e.from.block)&&t(e.to.block)})(t)))})(e,t,n):I.none())(n.dom,t,e.selection.getRng()).map((o=>()=>{$w(n,t,o.from.block,o.to.block).each((t=>{e.selection.setRng(t.toRange())}))}));return o},qw=(e,t)=>{const n=bn(t),o=O(xn,e);return Yn(n,Er,o).isSome()},Ww=e=>{const t=bn(e.getBody());return((e,t)=>{const n=du(e.dom,Ti.fromRangeStart(t)).isNone(),o=lu(e.dom,Ti.fromRangeEnd(t)).isNone();return!((e,t)=>qw(e,t.startContainer)||qw(e,t.endContainer))(e,t)&&n&&o})(t,e.selection.getRng())?(e=>I.some((()=>{e.setContent(""),e.selection.setCursorLocation()})))(e):((e,t)=>{const n=t.getRng();return Lt(th(e,bn(n.startContainer)),th(e,bn(n.endContainer)),((o,r)=>xn(o,r)?I.none():I.some((()=>{n.deleteContents(),$w(e,!0,o,r).each((e=>{t.setRng(e.toRange())}))})))).getOr(I.none())})(t,e.selection)},Kw=(e,t)=>e.selection.isCollapsed()?I.none():Ww(e),Gw=(e,t,n,o,r)=>I.from(t._selectionOverrides.showCaret(e,n,o,r)),Yw=(e,t)=>e.dispatch("BeforeObjectSelected",{target:t}).isDefaultPrevented()?I.none():I.some((e=>{const t=e.ownerDocument.createRange();return t.selectNode(e),t})(t)),Xw=(e,t,n)=>t.collapsed?((e,t,n)=>{const o=Ic(1,e.getBody(),t),r=Ti.fromRangeStart(o),s=r.getNode();if(gc(s))return Gw(1,e,s,!r.isAtEnd(),!1);const a=r.getNode(!0);if(gc(a))return Gw(1,e,a,!1,!1);const i=Oh(e.dom.getRoot(),r.getNode());return gc(i)?Gw(1,e,i,!1,n):I.none()})(e,t,n).getOr(t):t,Qw=e=>np(e)||Jg(e),Jw=e=>op(e)||Zg(e),Zw=(e,t,n,o,r,s)=>{Gw(o,e,s.getNode(!r),r,!0).each((n=>{if(t.collapsed){const e=t.cloneRange();r?e.setEnd(n.startContainer,n.startOffset):e.setStart(n.endContainer,n.endOffset),e.deleteContents()}else t.deleteContents();e.selection.setRng(n)})),((e,t)=>{Yo(t)&&0===t.data.length&&e.remove(t)})(e.dom,n)},ex=(e,t)=>((e,t)=>{const n=e.selection.getRng();if(!Yo(n.commonAncestorContainer))return I.none();const o=t?$c.Forwards:$c.Backwards,r=eu(e.getBody()),s=O(jc,t?r.next:r.prev),a=t?Qw:Jw,i=Uc(o,e.getBody(),n),l=s(i),d=l?Yp(t,l):l;if(!d||!Hc(i,d))return I.none();if(a(d))return I.some((()=>Zw(e,n,i.getNode(),o,t,d)));const c=s(d);return c&&a(c)&&Hc(d,c)?I.some((()=>Zw(e,n,i.getNode(),o,t,c))):I.none()})(e,t),tx=(e,t)=>{const n=e.getBody();return t?cu(n).filter(np):uu(n).filter(op)},nx=e=>{const t=e.selection.getRng();return!t.collapsed&&(tx(e,!0).exists((e=>e.isEqual(Ti.fromRangeStart(t))))||tx(e,!1).exists((e=>e.isEqual(Ti.fromRangeEnd(t)))))},ox=nl([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),rx=(e,t,n)=>ru(t,e,n).bind((o=>{return r=o.getNode(),C(r)&&(Er(bn(r))||xr(bn(r)))||((e,t,n,o)=>{const r=t=>vr(bn(t))&&!Oc(n,o,e);return Fc(!t,n).fold((()=>Fc(t,o).fold(L,r)),r)})(e,t,n,o)?I.none():t&&rr(o.getNode())||!t&&rr(o.getNode(!0))?((e,t,n,o)=>{const r=o.getNode(!t);return th(bn(e),bn(n.getNode())).map((e=>us(e)?ox.remove(e.dom):ox.moveToElement(r))).orThunk((()=>I.some(ox.moveToElement(r))))})(e,t,n,o):t&&op(n)||!t&&np(n)?I.some(ox.moveToPosition(o)):I.none();var r})),sx=(e,t)=>I.from(Oh(e.getBody(),t)),ax=(e,t)=>{const n=e.selection.getNode();return sx(e,n).filter(rr).fold((()=>((e,t,n)=>{const o=Ic(t?1:-1,e,n),r=Ti.fromRangeStart(o),s=bn(e);return!t&&op(r)?I.some(ox.remove(r.getNode(!0))):t&&np(r)?I.some(ox.remove(r.getNode())):!t&&np(r)&&bp(s,r)?vp(s,r).map((e=>ox.remove(e.getNode()))):t&&op(r)&&hp(s,r)?yp(s,r).map((e=>ox.remove(e.getNode()))):((e,t,n)=>((e,t)=>{const n=t.getNode(!e),o=e?"after":"before";return zo(n)&&n.getAttribute("data-mce-caret")===o})(t,n)?((e,t)=>y(t)?I.none():e&&rr(t.nextSibling)?I.some(ox.moveToElement(t.nextSibling)):!e&&rr(t.previousSibling)?I.some(ox.moveToElement(t.previousSibling)):I.none())(t,n.getNode(!t)).orThunk((()=>rx(e,t,n))):rx(e,t,n).bind((t=>((e,t,n)=>n.fold((e=>I.some(ox.remove(e))),(e=>I.some(ox.moveToElement(e))),(n=>Oc(t,n,e)?I.none():I.some(ox.moveToPosition(n)))))(e,n,t))))(e,t,r)})(e.getBody(),t,e.selection.getRng()).map((n=>()=>n.fold(((e,t)=>n=>(e._selectionOverrides.hideFakeCaret(),qp(e,t,bn(n)),!0))(e,t),((e,t)=>n=>{const o=t?Ti.before(n):Ti.after(n);return e.selection.setRng(o.toRange()),!0})(e,t),(e=>t=>(e.selection.setRng(t.toRange()),!0))(e))))),(()=>I.some(E)))},ix=e=>{const t=e.dom,n=e.selection,o=Oh(e.getBody(),n.getNode());if(or(o)&&t.isBlock(o)&&t.isEmpty(o)){const e=t.create("br",{"data-mce-bogus":"1"});t.setHTML(o,""),o.appendChild(e),n.setRng(Ti.before(e).toRange())}return!0},lx=(e,t)=>e.selection.isCollapsed()?ax(e,t):((e,t)=>{const n=e.selection.getNode();return rr(n)&&!sr(n)?sx(e,n.parentNode).filter(rr).fold((()=>I.some((()=>{var n;n=bn(e.getBody()),q(Lo(n,".mce-offscreen-selection"),yo),qp(e,t,bn(e.selection.getNode())),nh(e)}))),(()=>I.some(E))):nx(e)?I.some((()=>{rh(e,e.selection.getRng(),bn(e.getBody()))})):I.none()})(e,t),dx=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=Ti.fromRangeStart(e.selection.getRng());return ru(t,e.getBody(),n).filter((e=>t?Xg(e):Qg(e))).bind((e=>Tc(t?0:-1,e))).map((t=>()=>e.selection.select(t)))})(e,t):I.none(),cx=Yo,ux=e=>cx(e)&&e.data[0]===Tr,mx=e=>cx(e)&&e.data[e.data.length-1]===Tr,fx=e=>{var t;return(null!==(t=e.ownerDocument)&&void 0!==t?t:document).createTextNode(Tr)},gx=(e,t)=>e?(e=>{var t;if(cx(e.previousSibling))return mx(e.previousSibling)||e.previousSibling.appendData(Tr),e.previousSibling;if(cx(e))return ux(e)||e.insertData(0,Tr),e;{const n=fx(e);return null===(t=e.parentNode)||void 0===t||t.insertBefore(n,e),n}})(t):(e=>{var t,n;if(cx(e.nextSibling))return ux(e.nextSibling)||e.nextSibling.insertData(0,Tr),e.nextSibling;if(cx(e))return mx(e)||e.appendData(Tr),e;{const o=fx(e);return e.nextSibling?null===(t=e.parentNode)||void 0===t||t.insertBefore(o,e.nextSibling):null===(n=e.parentNode)||void 0===n||n.appendChild(o),o}})(t),px=O(gx,!0),hx=O(gx,!1),bx=(e,t)=>Yo(e.container())?gx(t,e.container()):gx(t,e.getNode()),vx=(e,t)=>{const n=t.get();return n&&e.container()===n&&Ir(n)},yx=(e,t)=>t.fold((t=>{ic(e.get());const n=px(t);return e.set(n),I.some(Ti(n,n.length-1))}),(t=>cu(t).map((t=>{if(vx(t,e)){const t=e.get();return Ti(t,1)}{ic(e.get());const n=bx(t,!0);return e.set(n),Ti(n,1)}}))),(t=>uu(t).map((t=>{if(vx(t,e)){const t=e.get();return Ti(t,t.length-1)}{ic(e.get());const n=bx(t,!1);return e.set(n),Ti(n,n.length-1)}}))),(t=>{ic(e.get());const n=hx(t);return e.set(n),I.some(Ti(n,1))})),Cx=(e,t)=>{for(let n=0;n<e.length;n++){const o=e[n].apply(null,t);if(o.isSome())return o}return I.none()},wx=nl([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),xx=(e,t)=>Ac(t,e)||e,kx=(e,t,n)=>{const o=Xp(n),r=xx(t,o.container());return Gp(e,r,o).fold((()=>lu(r,o).bind(O(Gp,e,r)).map((e=>wx.before(e)))),I.none)},Ex=(e,t)=>null===gu(e,t),Sx=(e,t,n)=>Gp(e,t,n).filter(O(Ex,t)),_x=(e,t,n)=>{const o=Qp(n);return Sx(e,t,o).bind((e=>du(e,o).isNone()?I.some(wx.start(e)):I.none()))},Nx=(e,t,n)=>{const o=Xp(n);return Sx(e,t,o).bind((e=>lu(e,o).isNone()?I.some(wx.end(e)):I.none()))},Rx=(e,t,n)=>{const o=Qp(n),r=xx(t,o.container());return Gp(e,r,o).fold((()=>du(r,o).bind(O(Gp,e,r)).map((e=>wx.after(e)))),I.none)},Ax=e=>{return t=Tx(e),!("rtl"===_a.DOM.getStyle(t,"direction",!0)||(e=>Wp.test(e))(null!==(n=t.textContent)&&void 0!==n?n:""));var t,n},Ox=(e,t,n)=>Cx([kx,_x,Nx,Rx],[e,t,n]).filter(Ax),Tx=e=>e.fold(R,R,R,R),Bx=e=>e.fold(N("before"),N("start"),N("end"),N("after")),Dx=e=>e.fold(wx.before,wx.before,wx.after,wx.after),Px=e=>e.fold(wx.start,wx.start,wx.end,wx.end),Lx=(e,t,n,o,r,s)=>Lt(Gp(t,n,o),Gp(t,n,r),((t,o)=>t!==o&&((e,t,n)=>{const o=Ac(t,e),r=Ac(n,e);return C(o)&&o===r})(n,t,o)?wx.after(e?t:o):s)).getOr(s),Mx=(e,t)=>e.fold(M,(e=>{return o=t,!(Bx(n=e)===Bx(o)&&Tx(n)===Tx(o));var n,o})),Ix=(e,t)=>e?t.fold(S(I.some,wx.start),I.none,S(I.some,wx.after),I.none):t.fold(I.none,S(I.some,wx.before),I.none,S(I.some,wx.end)),Fx=(e,t,n)=>{const o=e?1:-1;return t.setRng(Ti(n.container(),n.offset()+o).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0};var Ux;!function(e){e[e.Br=0]="Br",e[e.Block=1]="Block",e[e.Wrap=2]="Wrap",e[e.Eol=3]="Eol"}(Ux||(Ux={}));const zx=(e,t)=>e===$c.Backwards?oe(t):t,jx=(e,t,n)=>e===$c.Forwards?t.next(n):t.prev(n),Hx=(e,t,n,o)=>tr(o.getNode(t===$c.Forwards))?Ux.Br:!1===Oc(n,o)?Ux.Block:Ux.Wrap,$x=(e,t,n,o)=>{const r=eu(n);let s=o;const a=[];for(;s;){const n=jx(t,r,s);if(!n)break;if(tr(n.getNode(!1)))return t===$c.Forwards?{positions:zx(t,a).concat([n]),breakType:Ux.Br,breakAt:I.some(n)}:{positions:zx(t,a),breakType:Ux.Br,breakAt:I.some(n)};if(n.isVisible()){if(e(s,n)){const e=Hx(0,t,s,n);return{positions:zx(t,a),breakType:e,breakAt:I.some(n)}}a.push(n),s=n}else s=n}return{positions:zx(t,a),breakType:Ux.Eol,breakAt:I.none()}},Vx=(e,t,n,o)=>t(n,o).breakAt.map((o=>{const r=t(n,o).positions;return e===$c.Backwards?r.concat(o):[o].concat(r)})).getOr([]),qx=(e,t)=>X(e,((e,n)=>e.fold((()=>I.some(n)),(o=>Lt(le(o.getClientRects()),le(n.getClientRects()),((e,r)=>{const s=Math.abs(t-e.left);return Math.abs(t-r.left)<=s?n:o})).or(e)))),I.none()),Wx=(e,t)=>le(t.getClientRects()).bind((t=>qx(e,t.left))),Kx=O($x,Ti.isAbove,-1),Gx=O($x,Ti.isBelow,1),Yx=O(Vx,-1,Kx),Xx=O(Vx,1,Gx),Qx=(e,t)=>Wx(Yx(e,t),t),Jx=(e,t)=>Wx(Xx(e,t),t),Zx=rr,ek=(e,t)=>Math.abs(e.left-t),tk=(e,t)=>Math.abs(e.right-t),nk=(e,t)=>Te(e,((e,n)=>{const o=Math.min(ek(e,t),tk(e,t)),r=Math.min(ek(n,t),tk(n,t));return r===o&&Ee(n,"node")&&Zx(n.node)||r<o?n:e})),ok=e=>{const t=t=>V(t,(t=>{const n=ri(t);return n.node=e,n}));if(zo(e))return t(e.getClientRects());if(Yo(e)){const n=e.ownerDocument.createRange();return n.setStart(e,0),n.setEnd(e,e.data.length),t(n.getClientRects())}return[]},rk=e=>te(e,ok);var sk;!function(e){e[e.Up=-1]="Up",e[e.Down=1]="Down"}(sk||(sk={}));const ak=(e,t,n,o,r,s)=>{let a=0;const i=[],l=o=>{let s=rk([o]);-1===e&&(s=s.reverse());for(let e=0;e<s.length;e++){const o=s[e];if(!n(o,d)){if(i.length>0&&t(o,De(i))&&a++,o.line=a,r(o))return!0;i.push(o)}}return!1},d=De(s.getClientRects());if(!d)return i;const c=s.getNode();return c&&(l(c),((e,t,n,o)=>{let r=o;for(;r=Rc(r,e,ns,t);)if(n(r))return})(e,o,l,c)),i},ik=O(ak,sk.Up,ii,li),lk=O(ak,sk.Down,li,ii),dk=e=>De(e.getClientRects()),ck=e=>t=>((e,t)=>t.line>e)(e,t),uk=e=>t=>((e,t)=>t.line===e)(e,t),mk=(e,t)=>{e.selection.setRng(t),$f(e,e.selection.getRng())},fk=(e,t,n)=>I.some(Xw(e,t,n)),gk=(e,t,n,o,r,s)=>{const a=t===$c.Forwards,i=eu(e.getBody()),l=O(jc,a?i.next:i.prev),d=a?o:r;if(!n.collapsed){const o=ci(n);if(s(o))return Gw(t,e,o,t===$c.Backwards,!1);if(nx(e)){const e=n.cloneRange();return e.collapse(t===$c.Backwards),I.from(e)}}const c=Uc(t,e.getBody(),n);if(d(c))return Yw(e,c.getNode(!a));let u=l(c);const m=qr(n);if(!u)return m?I.some(n):I.none();if(u=Yp(a,u),d(u))return Gw(t,e,u.getNode(!a),a,!1);const f=l(u);return f&&d(f)&&Hc(u,f)?Gw(t,e,f.getNode(!a),a,!1):m?fk(e,u.toRange(),!1):I.none()},pk=(e,t,n,o,r,s)=>{const a=Uc(t,e.getBody(),n),i=De(a.getClientRects()),l=t===sk.Down,d=e.getBody();if(!i)return I.none();if(nx(e)){const e=l?Ti.fromRangeEnd(n):Ti.fromRangeStart(n);return(l?Jx:Qx)(d,e).orThunk((()=>I.from(e))).map((e=>e.toRange()))}const c=(l?lk:ik)(d,ck(1),a),u=G(c,uk(1)),m=i.left,f=nk(u,m);if(f&&s(f.node)){const n=Math.abs(m-f.left),o=Math.abs(m-f.right);return Gw(t,e,f.node,n<o,!1)}let g;if(g=o(a)?a.getNode():r(a)?a.getNode(!0):ci(n),g){const n=((e,t,n,o)=>{const r=eu(t);let s,a,i,l;const d=[];let c=0;1===e?(s=r.next,a=li,i=ii,l=Ti.after(o)):(s=r.prev,a=ii,i=li,l=Ti.before(o));const u=dk(l);do{if(!l.isVisible())continue;const e=dk(l);if(i(e,u))continue;d.length>0&&a(e,De(d))&&c++;const t=ri(e);if(t.position=l,t.line=c,n(t))return d;d.push(t)}while(l=s(l));return d})(t,d,ck(1),g);let o=nk(G(n,uk(1)),m);if(o)return fk(e,o.position.toRange(),!1);if(o=De(G(n,uk(0))),o)return fk(e,o.position.toRange(),!1)}return 0===u.length?hk(e,l).filter(l?r:o).map((t=>Xw(e,t.toRange(),!1))):I.none()},hk=(e,t)=>{const n=e.selection.getRng(),o=t?Ti.fromRangeEnd(n):Ti.fromRangeStart(n),r=(s=o.container(),a=e.getBody(),Yn(bn(s),(e=>hc(e.dom)),(e=>e.dom===a)).map((e=>e.dom)).getOr(a));var s,a;if(t){const e=Gx(r,o);return de(e.positions)}{const e=Kx(r,o);return le(e.positions)}},bk=(e,t,n)=>hk(e,t).filter(n).exists((t=>(e.selection.setRng(t.toRange()),!0))),vk=(e,t)=>{const n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},yk=(e,t)=>{e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},Ck=(e,t,n)=>yx(t,n).map((t=>(vk(e,t),n))),wk=(e,t,n)=>{const o=e.getBody(),r=((e,t,n)=>{const o=Ti.fromRangeStart(e);if(e.collapsed)return o;{const r=Ti.fromRangeEnd(e);return n?du(t,r).getOr(r):lu(t,o).getOr(o)}})(e.selection.getRng(),o,n);return((e,t,n,o)=>{const r=Yp(e,o),s=Ox(t,n,r);return Ox(t,n,r).bind(O(Ix,e)).orThunk((()=>((e,t,n,o,r)=>{const s=Yp(e,r);return ru(e,n,s).map(O(Yp,e)).fold((()=>o.map(Dx)),(r=>Ox(t,n,r).map(O(Lx,e,t,n,s,r)).filter(O(Mx,o)))).filter(Ax)})(e,t,n,s,o)))})(n,O(Kp,e),o,r).bind((n=>Ck(e,t,n)))},xk=(e,t,n)=>!!sd(e)&&wk(e,t,n).isSome(),kk=(e,t,n)=>!!sd(t)&&((e,t)=>{const n=t.selection.getRng(),o=e?Ti.fromRangeEnd(n):Ti.fromRangeStart(n);return!!(e=>w(e.selection.getSel().modify))(t)&&(e&&zr(o)?Fx(!0,t.selection,o):!(e||!jr(o))&&Fx(!1,t.selection,o))})(e,t),Ek=e=>{const t=Aa(null),n=O(Kp,e);return e.on("NodeChange",(o=>{sd(e)&&(((e,t,n)=>{const o=V(Lo(bn(t.getRoot()),'*[data-mce-selected="inline-boundary"]'),(e=>e.dom)),r=G(o,e),s=G(n,e);q(re(r,s),O(yk,!1)),q(re(s,r),O(yk,!0))})(n,e.dom,o.parents),((e,t)=>{const n=t.get();if(e.selection.isCollapsed()&&!e.composing&&n){const o=Ti.fromRangeStart(e.selection.getRng());Ti.isTextPosition(o)&&!(e=>zr(e)||jr(e))(o)&&(vk(e,ac(n,o)),t.set(null))}})(e,t),((e,t,n,o)=>{if(t.selection.isCollapsed()){const r=G(o,e);q(r,(o=>{const r=Ti.fromRangeStart(t.selection.getRng());Ox(e,t.getBody(),r).bind((e=>Ck(t,n,e)))}))}})(n,e,t,o.parents))})),t},Sk=O(kk,!0),_k=O(kk,!1),Nk=(e,t,n)=>{if(sd(e)){const o=hk(e,t).getOrThunk((()=>{const n=e.selection.getRng();return t?Ti.fromRangeEnd(n):Ti.fromRangeStart(n)}));return Ox(O(Kp,e),e.getBody(),o).exists((t=>{const o=Dx(t);return yx(n,o).exists((t=>(vk(e,t),!0)))}))}return!1},Rk=(e,t)=>n=>yx(t,n).map((t=>()=>vk(e,t))),Ak=(e,t,n,o)=>{const r=e.getBody(),s=O(Kp,e);e.undoManager.ignore((()=>{e.selection.setRng(((e,t)=>{const n=document.createRange();return n.setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n})(n,o)),Zp(e),Ox(s,r,Ti.fromRangeStart(e.selection.getRng())).map(Px).bind(Rk(e,t)).each(P)})),e.nodeChanged()},Ok=(e,t,n)=>{if(e.selection.isCollapsed()&&sd(e)){const o=Ti.fromRangeStart(e.selection.getRng());return((e,t,n,o)=>{const r=((e,t)=>Ac(t,e)||e)(e.getBody(),o.container()),s=O(Kp,e),a=Ox(s,r,o);return a.bind((e=>n?e.fold(N(I.some(Px(e))),I.none,N(I.some(Dx(e))),I.none):e.fold(I.none,N(I.some(Dx(e))),I.none,N(I.some(Px(e)))))).map(Rk(e,t)).getOrThunk((()=>{const i=su(n,r,o),l=i.bind((e=>Ox(s,r,e)));return Lt(a,l,(()=>Gp(s,r,o).bind((t=>(e=>Lt(cu(e),uu(e),((t,n)=>{const o=Yp(!0,t),r=Yp(!1,n);return lu(e,o).forall((e=>e.isEqual(r)))})).getOr(!0))(t)?I.some((()=>{qp(e,n,bn(t))})):I.none())))).getOrThunk((()=>l.bind((()=>i.map((r=>()=>{n?Ak(e,t,o,r):Ak(e,t,r,o)}))))))}))})(e,t,n,o)}return I.none()},Tk=(e,t)=>{const n=bn(e.getBody()),o=bn(e.selection.getStart()),r=sp(o,n);return Z(r,t).fold(N(r),(e=>r.slice(0,e)))},Bk=(e,t)=>{const n=O(xb,e);return te(t,(e=>n(e)?[e.dom]:[]))},Dk=e=>{const t=(e=>Tk(e,br))(e);return Bk(e,t)},Pk=(e,t)=>{const n=(e=>Tk(e,(e=>br(e)||(e=>Fn(e)>1)(e))))(e);return de(n).bind((o=>{const r=Ti.fromRangeStart(e.selection.getRng());return((e,t,n)=>Lt(cu(n),uu(n),((o,r)=>{const s=Yp(!0,o),a=Yp(!1,r),i=Yp(!1,t);return e?lu(n,i).exists((e=>e.isEqual(a)&&t.isEqual(s))):du(n,i).exists((e=>e.isEqual(s)&&t.isEqual(a)))})).getOr(!0))(t,r,o.dom)&&!kb(o)?I.some((()=>((e,t,n,o)=>{const r=Bk(t,o);if(0===r.length)qp(t,e,n);else{const e=wb(n.dom,r);t.selection.setRng(e.toRange())}})(t,e,o,n))):I.none()}))},Lk=(e,t)=>{const n=e.selection.getStart(),o=((e,t)=>{const n=t.parentElement;return tr(t)&&!h(n)&&e.dom.isEmpty(n)})(e,n)||kb(bn(n))?wb(n,t):((e,t)=>{const{caretContainer:n,caretPosition:o}=Cb(t);return e.selection.getRng().insertNode(n.dom),o})(e,t);e.selection.setRng(o.toRange())},Mk=e=>Yo(e.startContainer),Ik=e=>{const t=e.selection.getRng();return(e=>0===e.startOffset&&Mk(e))(t)&&((e,t)=>{const n=t.startContainer.parentElement;return!h(n)&&xb(e,bn(n))})(e,t)&&(e=>(e=>(e=>{const t=e.startContainer.parentNode,n=e.endContainer.parentNode;return!h(t)&&!h(n)&&t.isEqualNode(n)})(e)&&(e=>{const t=e.endContainer;return e.endOffset===(Yo(t)?t.length:t.childNodes.length)})(e))(e)||(e=>!e.endContainer.isEqualNode(e.commonAncestorContainer))(e))(t)},Fk=(e,t)=>e.selection.isCollapsed()?Pk(e,t):(e=>{if(Ik(e)){const t=Dk(e);return I.some((()=>{Zp(e),((e,t)=>{const n=re(t,Dk(e));n.length>0&&Lk(e,n)})(e,t)}))}return I.none()})(e),Uk=e=>((e,t,n)=>Yn(e,(e=>fu(e.dom)),n).isSome())(e,0,br),zk=e=>((e=>{const t=e.selection.getRng();return t.collapsed&&(Mk(t)||e.dom.isEmpty(t.startContainer))&&!(e=>Uk(bn(e.selection.getStart())))(e)})(e)&&Lk(e,[]),!0),jk=(e,t,n)=>C(n)?I.some((()=>{e._selectionOverrides.hideFakeCaret(),qp(e,t,bn(n))})):I.none(),Hk=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=t?Jg:Zg,o=t?$c.Forwards:$c.Backwards,r=Uc(o,e.getBody(),e.selection.getRng());return n(r)?jk(e,t,r.getNode(!t)):I.from(Yp(t,r)).filter((e=>n(e)&&Hc(r,e))).bind((n=>jk(e,t,n.getNode(!t))))})(e,t):((e,t)=>{const n=e.selection.getNode();return ir(n)?jk(e,t,n):I.none()})(e,t),$k=e=>Xe(null!=e?e:"").getOr(0),Vk=(e,t)=>(e||"table"===jt(t)?"margin":"padding")+("rtl"===ao(t,"direction")?"-right":"-left"),qk=e=>{const t=Kk(e);return!e.mode.isReadOnly()&&(t.length>1||((e,t)=>ne(t,(t=>{const n=Vk($l(e),t),o=lo(t,n).map($k).getOr(0);return"false"!==e.dom.getContentEditable(t.dom)&&o>0})))(e,t))},Wk=e=>wr(e)||xr(e),Kk=e=>G(wo(e.selection.getSelectedBlocks()),(e=>!Wk(e)&&!(e=>Nn(e).exists(Wk))(e)&&Xn(e,(e=>or(e.dom)||rr(e.dom))).exists((e=>or(e.dom))))),Gk=(e,t)=>{var n,o;const{dom:r}=e,s=Vl(e),a=null!==(o=null===(n=/[a-z%]+$/i.exec(s))||void 0===n?void 0:n[0])&&void 0!==o?o:"px",i=$k(s),l=$l(e);q(Kk(e),(e=>{((e,t,n,o,r,s)=>{const a=Vk(n,bn(s)),i=$k(e.getStyle(s,a));if("outdent"===t){const t=Math.max(0,i-o);e.setStyle(s,a,t?t+r:"")}else{const t=i+o+r;e.setStyle(s,a,t)}})(r,t,l,i,a,e.dom)}))},Yk=e=>Gk(e,"outdent"),Xk=e=>{if(e.selection.isCollapsed()&&qk(e)){const t=e.dom,n=e.selection.getRng(),o=Ti.fromRangeStart(n),r=t.getParent(n.startContainer,t.isBlock);if(null!==r&&cp(bn(r),o))return I.some((()=>Yk(e)))}return I.none()},Qk=(e,t,n)=>ue([Xk,lx,ex,(e,n)=>Ok(e,t,n),Vw,Ah,dx,Hk,Kw,Fk],(t=>t(e,n))).filter((t=>e.selection.isEditable())),Jk=(e,t)=>{e.addCommand("delete",(()=>{((e,t)=>{Qk(e,t,!1).fold((()=>{Zp(e),nh(e)}),P)})(e,t)})),e.addCommand("forwardDelete",(()=>{((e,t)=>{Qk(e,t,!0).fold((()=>(e=>Jp(e,"ForwardDelete"))(e)),P)})(e,t)}))},Zk=e=>void 0===e.touches||1!==e.touches.length?I.none():I.some(e.touches[0]),eE=(e,t)=>ke(e,t.nodeName),tE=(e,t)=>!!Yo(t)||!!zo(t)&&!eE(e.getBlockElements(),t)&&!_u(t)&&!ks(e,t),nE=(e,t)=>{if(Yo(t)){if(0===t.data.length)return!0;if(/^\s+$/.test(t.data)&&(!t.nextSibling||eE(e,t.nextSibling)))return!0}return!1},oE=e=>e.dom.create(kl(e),El(e)),rE=e=>{const t=e.dom,n=e.selection,o=e.schema,r=o.getBlockElements(),s=n.getStart(),a=e.getBody();let i,l,d=!1;const c=kl(e);if(!s||!zo(s))return;const u=a.nodeName.toLowerCase();if(!o.isValidChild(u,c.toLowerCase())||((e,t,n)=>$(rp(bn(n),bn(t)),(t=>eE(e,t.dom))))(r,a,s))return;const m=n.getRng(),{startContainer:f,startOffset:g,endContainer:p,endOffset:h}=m,b=mg(e);let v=a.firstChild;for(;v;)if(zo(v)&&ys(o,v),tE(o,v)){if(nE(r,v)){l=v,v=v.nextSibling,t.remove(l);continue}i||(i=oE(e),a.insertBefore(i,v),d=!0),l=v,v=v.nextSibling,i.appendChild(l)}else i=null,v=v.nextSibling;d&&b&&(m.setStart(f,g),m.setEnd(p,h),n.setRng(m),e.nodeChanged())},sE=(e,t,n)=>{const o=bn(oE(e)),r=Rr();po(o,r),n(t,o);const s=document.createRange();return s.setStartBefore(r.dom),s.setEndBefore(r.dom),s},aE=e=>t=>-1!==(" "+t.attr("class")+" ").indexOf(e),iE=(e,t,n)=>function(o){const r=arguments,s=r[r.length-2],a=s>0?t.charAt(s-1):"";if('"'===a)return o;if(">"===a){const e=t.lastIndexOf("<",s);if(-1!==e&&-1!==t.substring(e,s).indexOf('contenteditable="false"'))return o}return'<span class="'+n+'" data-mce-content="'+e.dom.encode(r[0])+'">'+e.dom.encode("string"==typeof r[1]?r[1]:r[0])+"</span>"},lE=(e,t)=>{t.hasAttribute("data-mce-caret")&&(Vr(t),e.selection.setRng(e.selection.getRng()),e.selection.scrollIntoView(t))},dE=(e,t)=>{const n=(e=>Jn(bn(e.getBody()),"*[data-mce-caret]").map((e=>e.dom)).getOrNull())(e);if(n)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void lE(e,n)):void(Ur(n)&&(lE(e,n),e.undoManager.add()))},cE=rr,uE=(e,t,n)=>{const o=eu(e.getBody()),r=O(jc,1===t?o.next:o.prev);if(n.collapsed){const o=e.dom.getParent(n.startContainer,"PRE");if(!o)return;if(!r(Ti.fromRangeStart(n))){const n=bn((e=>{const t=e.dom.create(kl(e));return t.innerHTML='<br data-mce-bogus="1">',t})(e));1===t?fo(bn(o),n):mo(bn(o),n),e.selection.select(n.dom,!0),e.selection.collapse()}}},mE=(e,t)=>((e,t)=>{const n=t?$c.Forwards:$c.Backwards,o=e.selection.getRng();return((e,t,n)=>gk(t,e,n,np,op,cE))(n,e,o).orThunk((()=>(uE(e,n,o),I.none())))})(e,t).exists((t=>(mk(e,t),!0))),fE=(e,t)=>((e,t)=>{const n=t?1:-1,o=e.selection.getRng();return((e,t,n)=>pk(t,e,n,(e=>np(e)||ep(e)),(e=>op(e)||tp(e)),cE))(n,e,o).orThunk((()=>(uE(e,n,o),I.none())))})(e,t).exists((t=>(mk(e,t),!0))),gE=(e,t)=>bk(e,t,t?op:np),pE=(e,t)=>tx(e,!t).map((n=>{const o=n.toRange(),r=e.selection.getRng();return t?o.setStart(r.startContainer,r.startOffset):o.setEnd(r.endContainer,r.endOffset),o})).exists((t=>(mk(e,t),!0))),hE=e=>H(["figcaption"],jt(e)),bE=(e,t)=>{const n=bn(e.getBody()),o=Ti.fromRangeStart(e.selection.getRng());return((e,t)=>{const n=O(xn,t);return Xn(bn(e.container()),br,n).filter(hE)})(o,n).exists((()=>{if(((e,t,n)=>t?((e,t)=>Gx(e,t).breakAt.isNone())(e.dom,n):((e,t)=>Kx(e,t).breakAt.isNone())(e.dom,n))(n,t,o)){const o=sE(e,n,t?po:go);return e.selection.setRng(o),!0}return!1}))},vE=(e,t)=>!!e.selection.isCollapsed()&&bE(e,t),yE={shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0},CE=(e,t)=>t.keyCode===e.keyCode&&t.shiftKey===e.shiftKey&&t.altKey===e.altKey&&t.ctrlKey===e.ctrlKey&&t.metaKey===e.metaKey,wE=(e,...t)=>()=>e.apply(null,t),xE=(e,t)=>J(((e,t)=>te((e=>V(e,(e=>({...yE,...e}))))(e),(e=>CE(e,t)?[e]:[])))(e,t),(e=>e.action())),kE=(e,t)=>ue(((e,t)=>te((e=>V(e,(e=>({...yE,...e}))))(e),(e=>CE(e,t)?[e]:[])))(e,t),(e=>e.action())),EE=(e,t)=>{const n=t?$c.Forwards:$c.Backwards,o=e.selection.getRng();return gk(e,n,o,Jg,Zg,ir).exists((t=>(mk(e,t),!0)))},SE=(e,t)=>{const n=t?1:-1,o=e.selection.getRng();return pk(e,n,o,Jg,Zg,ir).exists((t=>(mk(e,t),!0)))},_E=(e,t)=>bk(e,t,t?Zg:Jg),NE=nl([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),RE={...NE,none:e=>NE.none(e)},AE=(e,t,n)=>te(Pn(e),(e=>Cn(e,t)?n(e)?[e]:[]:AE(e,t,n))),OE=(e,t)=>Zn(e,"table",t),TE=(e,t,n,o,r=M)=>{const s=1===o;if(!s&&n<=0)return RE.first(e[0]);if(s&&n>=e.length-1)return RE.last(e[e.length-1]);{const s=n+o,a=e[s];return r(a)?RE.middle(t,a):TE(e,t,s,o,r)}},BE=(e,t)=>OE(e,t).bind((t=>{const n=AE(t,"th,td",M);return Z(n,(t=>xn(e,t))).map((e=>({index:e,all:n})))})),DE=(e,t,n,o,r)=>{const s=Lo(bn(n),"td,th,caption").map((e=>e.dom)),a=G(((e,t)=>te(t,(t=>{const n=((e,t)=>({left:e.left-t,top:e.top-t,right:e.right+-2,bottom:e.bottom+-2,width:e.width+t,height:e.height+t}))(ri(t.getBoundingClientRect()),-1);return[{x:n.left,y:e(n),cell:t},{x:n.right,y:e(n),cell:t}]})))(e,s),(e=>t(e,r)));return((e,t,n)=>X(e,((e,o)=>e.fold((()=>I.some(o)),(e=>{const r=Math.sqrt(Math.abs(e.x-t)+Math.abs(e.y-n)),s=Math.sqrt(Math.abs(o.x-t)+Math.abs(o.y-n));return I.some(s<r?o:e)}))),I.none()))(a,o,r).map((e=>e.cell))},PE=O(DE,(e=>e.bottom),((e,t)=>e.y<t)),LE=O(DE,(e=>e.top),((e,t)=>e.y>t)),ME=(e,t,n)=>{const o=e(t,n);return(e=>e.breakType===Ux.Wrap&&0===e.positions.length)(o)||!tr(n.getNode())&&(e=>e.breakType===Ux.Br&&1===e.positions.length)(o)?!((e,t,n)=>n.breakAt.exists((n=>e(t,n).breakAt.isSome())))(e,t,o):o.breakAt.isNone()},IE=O(ME,Kx),FE=O(ME,Gx),UE=(e,t,n,o)=>{const r=e.selection.getRng(),s=t?1:-1;return!(!fc()||!((e,t,n)=>{const o=Ti.fromRangeStart(t);return iu(!e,n).exists((e=>e.isEqual(o)))})(t,r,n)||(Gw(s,e,n,!t,!1).each((t=>{mk(e,t)})),0))},zE=(e,t,n)=>{const o=((e,t)=>{const n=t.getNode(e);return Wo(n)?I.some(n):I.none()})(!!t,n),r=!1===t;o.fold((()=>mk(e,n.toRange())),(o=>iu(r,e.getBody()).filter((e=>e.isEqual(n))).fold((()=>mk(e,n.toRange())),(n=>((e,t,n)=>{t.undoManager.transact((()=>{const o=e?fo:mo,r=sE(t,bn(n),o);mk(t,r)}))})(t,e,o)))))},jE=(e,t,n,o)=>{const r=e.selection.getRng(),s=Ti.fromRangeStart(r),a=e.getBody();if(!t&&IE(o,s)){const o=((e,t,n)=>((e,t)=>le(t.getClientRects()).bind((t=>PE(e,t.left,t.top))).bind((e=>{return Wx(uu(n=e).map((e=>Kx(n,e).positions.concat(e))).getOr([]),t);var n})))(t,n).orThunk((()=>le(n.getClientRects()).bind((n=>qx(Yx(e,Ti.before(t)),n.left))))).getOr(Ti.before(t)))(a,n,s);return zE(e,t,o),!0}if(t&&FE(o,s)){const o=((e,t,n)=>((e,t)=>de(t.getClientRects()).bind((t=>LE(e,t.left,t.top))).bind((e=>{return Wx(cu(n=e).map((e=>[e].concat(Gx(n,e).positions))).getOr([]),t);var n})))(t,n).orThunk((()=>le(n.getClientRects()).bind((n=>qx(Xx(e,Ti.after(t)),n.left))))).getOr(Ti.after(t)))(a,n,s);return zE(e,t,o),!0}return!1},HE=(e,t,n)=>I.from(e.dom.getParent(e.selection.getNode(),"td,th")).bind((o=>I.from(e.dom.getParent(o,"table")).map((r=>n(e,t,r,o))))).getOr(!1),$E=(e,t)=>HE(e,t,UE),VE=(e,t)=>HE(e,t,jE),qE=(e,t,n)=>n.fold(I.none,I.none,((e,t)=>{return(n=t,((e,t)=>{const n=e=>{for(let o=0;o<e.childNodes.length;o++){const r=bn(e.childNodes[o]);if(t(r))return I.some(r);const s=n(e.childNodes[o]);if(s.isSome())return s}return I.none()};return n(e.dom)})(n,Cg)).map((e=>(e=>{const t=sf.exact(e,0,e,0);return uf(t)})(e)));var n}),(n=>(e.execCommand("mceTableInsertRowAfter"),WE(e,t,n)))),WE=(e,t,n)=>{return qE(e,t,(r=eo,BE(o=n,void 0).fold((()=>RE.none(o)),(e=>TE(e.all,o,e.index,1,r)))));var o,r},KE=(e,t,n)=>{return qE(e,t,(r=eo,BE(o=n,void 0).fold((()=>RE.none()),(e=>TE(e.all,o,e.index,-1,r)))));var o,r},GE=(e,t)=>{const n=["table","li","dl"],o=bn(e.getBody()),r=e=>{const t=jt(e);return xn(e,o)||H(n,t)},s=e.selection.getRng();return((e,t)=>((e,t,n=L)=>n(t)?I.none():H(e,jt(t))?I.some(t):Qn(t,e.join(","),(e=>Cn(e,"table")||n(e))))(["td","th"],e,t))(bn(t?s.endContainer:s.startContainer),r).map((n=>(OE(n,r).each((t=>{e.model.table.clearSelectedCells(t.dom)})),e.selection.collapse(!t),(t?WE:KE)(e,r,n).each((t=>{e.selection.setRng(t)})),!0))).getOr(!1)},YE=(e,t)=>({container:e,offset:t}),XE=_a.DOM,QE=e=>t=>e===t?-1:0,JE=(e,t,n)=>{if(Yo(e)&&t>=0)return I.some(YE(e,t));{const o=ni(XE);return I.from(o.backwards(e,t,QE(e),n)).map((e=>YE(e.container,e.container.data.length)))}},ZE=(e,t,n)=>{if(!Yo(e))return I.none();const o=e.data;if(t>=0&&t<=o.length)return I.some(YE(e,t));{const o=ni(XE);return I.from(o.backwards(e,t,QE(e),n)).bind((e=>{const o=e.container.data;return ZE(e.container,t+o.length,n)}))}},eS=(e,t,n)=>{if(!Yo(e))return I.none();const o=e.data;if(t<=o.length)return I.some(YE(e,t));{const r=ni(XE);return I.from(r.forwards(e,t,QE(e),n)).bind((e=>eS(e.container,t-o.length,n)))}},tS=(e,t,n,o,r)=>{const s=ni(e,(e=>t=>e.isBlock(t)||H(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===e.getContentEditable(t))(e));return I.from(s.backwards(t,n,o,r))},nS=e=>Dr(e.toString().replace(/\u00A0/g," ")),oS=e=>""!==e&&-1!==" \xa0\f\n\r\t\v".indexOf(e),rS=(e,t)=>e.substring(t.length),sS=(e,t,n,o=0)=>{return(r=bn(t.startContainer),Zn(r,wg)).fold((()=>((e,t,n,o=0)=>{if(!(r=t).collapsed||!Yo(r.startContainer))return I.none();var r;const s={text:"",offset:0},a=e.getParent(t.startContainer,e.isBlock)||e.getRoot();return tS(e,t.startContainer,t.startOffset,((e,t,o)=>(s.text=o+s.text,s.offset+=t,((e,t,n)=>{let o;const r=n.charAt(0);for(o=t-1;o>=0;o--){const s=e.charAt(o);if(oS(s))return I.none();if(r===s&&je(e,n,o,t))break}return I.some(o)})(s.text,s.offset,n).getOr(t))),a).bind((e=>{const r=t.cloneRange();if(r.setStart(e.container,e.offset),r.setEnd(t.endContainer,t.endOffset),r.collapsed)return I.none();const s=nS(r);return 0!==s.lastIndexOf(n)||rS(s,n).length<o?I.none():I.some({text:rS(s,n),range:r,trigger:n})}))})(e,t,n,o)),(t=>{const o=e.createRng();o.selectNode(t.dom);const r=nS(o);return I.some({range:o,text:rS(r,n),trigger:n})}));var r},aS=e=>{if((e=>3===e.nodeType)(e))return YE(e,e.data.length);{const t=e.childNodes;return t.length>0?aS(t[t.length-1]):YE(e,t.length)}},iS=(e,t)=>{const n=e.childNodes;return n.length>0&&t<n.length?iS(n[t],0):n.length>0&&(e=>1===e.nodeType)(e)&&n.length===t?aS(n[n.length-1]):YE(e,t)},lS=(e,t,n,o={})=>{var r;const s=t(),a=null!==(r=e.selection.getRng().startContainer.nodeValue)&&void 0!==r?r:"",i=G(s.lookupByTrigger(n.trigger),(t=>n.text.length>=t.minChars&&t.matches.getOrThunk((()=>(e=>t=>{const n=iS(t.startContainer,t.startOffset);return!((e,t)=>{var n;const o=null!==(n=e.getParent(t.container,e.isBlock))&&void 0!==n?n:e.getRoot();return tS(e,t.container,t.offset,((e,t)=>0===t?-1:t),o).filter((e=>{const t=e.container.data.charAt(e.offset-1);return!oS(t)})).isSome()})(e,n)})(e.dom)))(n.range,a,n.text)));if(0===i.length)return I.none();const l=Promise.all(V(i,(e=>e.fetch(n.text,e.maxResults,o).then((t=>({matchText:n.text,items:t,columns:e.columns,onAction:e.onAction,highlightOn:e.highlightOn}))))));return I.some({lookupData:l,context:n})};var dS;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(dS||(dS={}));const cS=(e,t,n)=>e.stype===dS.Error?t(e.serror):n(e.svalue),uS=e=>({stype:dS.Value,svalue:e}),mS=e=>({stype:dS.Error,serror:e}),fS=cS,gS=e=>f(e)&&me(e).length>100?" removed due to size":JSON.stringify(e,null,2),pS=(e,t)=>mS([{path:e,getErrorInfo:t}]),hS=(e,t)=>({extract:(n,o)=>xe(o,e).fold((()=>((e,t)=>pS(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(n,e)),(e=>((e,t,n,o)=>xe(n,o).fold((()=>((e,t,n)=>pS(e,(()=>'The chosen schema: "'+n+'" did not exist in branches: '+gS(t))))(e,n,o)),(n=>n.extract(e.concat(["branch: "+o]),t))))(n,o,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+me(t)}),bS=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const n={};for(let o=0;o<t.length;o++){const r=t[o];for(const t in r)ke(r,t)&&(n[t]=e(n[t],r[t]))}return n},vS=bS(((e,t)=>g(e)&&g(t)?vS(e,t):t)),yS=(bS(((e,t)=>t)),e=>({tag:"defaultedThunk",process:N(e)})),CS=e=>{const t=(e=>{const t=[],n=[];return q(e,(e=>{cS(e,(e=>n.push(e)),(e=>t.push(e)))})),{values:t,errors:n}})(e);return t.errors.length>0?(n=t.errors,S(mS,ee)(n)):uS(t.values);var n},wS=(e,t,n)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return n(e.newKey,e.instantiator)}},xS=e=>({extract:(t,n)=>{return o=e(n),r=e=>((e,t)=>pS(e,N(t)))(t,e),o.stype===dS.Error?r(o.serror):o;var o,r},toString:N("val")}),kS=xS(uS),ES=(e,t,n,o)=>o(xe(e,t).getOrThunk((()=>n(e)))),SS=(e,t,n,o,r)=>{const s=e=>r.extract(t.concat([o]),e),a=e=>e.fold((()=>uS(I.none())),(e=>{const n=r.extract(t.concat([o]),e);return s=n,a=I.some,s.stype===dS.Value?{stype:dS.Value,svalue:a(s.svalue)}:s;var s,a}));switch(e.tag){case"required":return((e,t,n,o)=>xe(t,n).fold((()=>((e,t,n)=>pS(e,(()=>'Could not find valid *required* value for "'+t+'" in '+gS(n))))(e,n,t)),o))(t,n,o,s);case"defaultedThunk":return ES(n,o,e.process,s);case"option":return((e,t,n)=>n(xe(e,t)))(n,o,a);case"defaultedOptionThunk":return((e,t,n,o)=>o(xe(e,t).map((t=>!0===t?n(e):t))))(n,o,e.process,a);case"mergeWithThunk":return ES(n,o,N({}),(t=>{const o=vS(e.process(n),t);return s(o)}))}},_S=e=>({extract:(t,n)=>((e,t,n)=>{const o={},r=[];for(const s of n)wS(s,((n,s,a,i)=>{const l=SS(a,e,t,n,i);fS(l,(e=>{r.push(...e)}),(e=>{o[s]=e}))}),((e,n)=>{o[e]=n(t)}));return r.length>0?mS(r):uS(o)})(t,n,e),toString:()=>{const t=V(e,(e=>wS(e,((e,t,n,o)=>e+" -> "+o.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),NS=e=>({extract:(t,n)=>{const o=V(n,((n,o)=>e.extract(t.concat(["["+o+"]"]),n)));return CS(o)},toString:()=>"array("+e.toString()+")"}),RS=(e,t,n)=>{return o=((e,t,n)=>((e,t)=>e.stype===dS.Error?{stype:dS.Error,serror:t(e.serror)}:e)(t.extract([e],n),(e=>({input:n,errors:e}))))(e,t,n),cS(o,tl.error,tl.value);var o},AS=(e,t)=>hS(e,pe(t,_S)),OS=N(kS),TS=(e,t)=>xS((n=>{const o=typeof n;return e(n)?uS(n):mS(`Expected type: ${t} but got: ${o}`)})),BS=TS(x,"number"),DS=TS(m,"string"),PS=TS(b,"boolean"),LS=TS(w,"function"),MS=(e,t,n,o)=>({tag:"field",key:e,newKey:t,presence:n,prop:o}),IS=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),FS=(e,t)=>MS(e,e,{tag:"required",process:{}},t),US=e=>FS(e,DS),zS=e=>FS(e,LS),jS=(e,t)=>MS(e,e,{tag:"option",process:{}},t),HS=e=>jS(e,DS),$S=(e,t,n)=>MS(e,e,yS(t),n),VS=(e,t)=>$S(e,t,BS),qS=(e,t,n)=>$S(e,t,(e=>{return t=t=>H(e,t)?tl.value(t):tl.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`),xS((e=>t(e).fold(mS,uS)));var t})(n)),WS=(e,t)=>$S(e,t,PS),KS=(e,t)=>$S(e,t,LS),GS=US("type"),YS=zS("fetch"),XS=zS("onAction"),QS=KS("onSetup",(()=>E)),JS=HS("text"),ZS=HS("icon"),e_=HS("tooltip"),t_=HS("label"),n_=WS("active",!1),o_=WS("enabled",!0),r_=WS("primary",!1),s_=e=>((e,t)=>$S("type",t,DS))(0,e),a_=_S([GS,US("trigger"),VS("minChars",1),(1,((e,t)=>MS(e,e,yS(1),OS()))("columns")),VS("maxResults",10),("matches",jS("matches",LS)),YS,XS,(i_=DS,$S("highlightOn",[],NS(i_)))]);var i_;const l_=[o_,e_,ZS,JS,QS],d_=[n_].concat(l_),c_=[KS("predicate",L),qS("scope","node",["node","editor"]),qS("position","selection",["node","selection","line"])],u_=l_.concat([s_("contextformbutton"),r_,XS,IS("original",R)]),m_=d_.concat([s_("contextformbutton"),r_,XS,IS("original",R)]),f_=l_.concat([s_("contextformbutton")]),g_=d_.concat([s_("contextformtogglebutton")]),p_=AS("type",{contextformbutton:u_,contextformtogglebutton:m_});_S([s_("contextform"),KS("initValue",N("")),t_,((e,t)=>MS(e,e,{tag:"required",process:{}},NS(t)))("commands",p_),jS("launch",AS("type",{contextformbutton:f_,contextformtogglebutton:g_}))].concat(c_));const h_=e=>{const t=e.ui.registry.getAll().popups,n=pe(t,(e=>{return(t=e,RS("Autocompleter",a_,{trigger:t.ch,...t})).fold((e=>{throw new Error("Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:N("... (only showing first ten failures)")}]):e;return V(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})((t=e).errors).join("\n")+"\n\nInput object: "+gS(t.input));var t}),R);var t})),o=Se(Ce(n,(e=>e.trigger))),r=we(n);return{dataset:n,triggers:o,lookupByTrigger:e=>G(r,(t=>t.trigger===e))}},b_=e=>{const t=Ma(),n=Aa(!1),o=t.isSet,r=()=>{o()&&((e=>{xC(e).autocompleter.removeDecoration()})(e),(e=>{e.dispatch("AutocompleterEnd")})(e),n.set(!1),t.clear())},s=Pe((()=>h_(e))),a=a=>{(n=>t.get().map((t=>sS(e.dom,e.selection.getRng(),t.trigger).bind((t=>lS(e,s,t,n))))).getOrThunk((()=>((e,t)=>{const n=t(),o=e.selection.getRng();return((e,t,n)=>ue(n.triggers,(n=>sS(e,t,n))))(e.dom,o,n).bind((n=>lS(e,t,n)))})(e,s))))(a).fold(r,(s=>{(n=>{o()||(((e,t)=>{xC(e).autocompleter.addDecoration(t)})(e,n.range),t.set({trigger:n.trigger,matchLength:n.text.length}))})(s.context),s.lookupData.then((o=>{t.get().map((a=>{const i=s.context;a.trigger===i.trigger&&(i.text.length-a.matchLength>=10?r():(t.set({...a,matchLength:i.text.length}),n.get()?((e,t)=>{e.dispatch("AutocompleterUpdate",t)})(e,{lookupData:o}):(n.set(!0),((e,t)=>{e.dispatch("AutocompleterStart",t)})(e,{lookupData:o}))))}))}))}))};e.addCommand("mceAutocompleterReload",((e,t)=>{const n=f(t)?t.fetchOptions:{};a(n)})),e.addCommand("mceAutocompleterClose",r),((e,t)=>{const n=Fa(t.load,50);e.on("keypress compositionend",(e=>{27!==e.which&&n.throttle()})),e.on("keydown",(e=>{const o=e.which;8===o?n.throttle():27===o&&t.cancelIfNecessary()})),e.on("remove",n.cancel)})(e,{cancelIfNecessary:r,load:a})},v_=e=>(t,n,o={})=>{const r=t.getBody(),s={bubbles:!0,composed:!0,data:null,isComposing:!1,detail:0,view:null,target:r,currentTarget:r,eventPhase:Event.AT_TARGET,originalTarget:r,explicitOriginalTarget:r,isTrusted:!1,srcElement:r,cancelable:!1,preventDefault:E,inputType:n},a=da(new InputEvent(e));return t.dispatch(e,{...a,...s,...o})},y_=v_("input"),C_=v_("beforeinput"),w_=(e,t)=>{const n=e.dom,o=e.schema.getMoveCaretBeforeOnEnterElements();if(!t)return;if(/^(LI|DT|DD)$/.test(t.nodeName)){const e=(e=>{for(;e;){if(zo(e)||Yo(e)&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}return null})(t.firstChild);e&&/^(UL|OL|DL)$/.test(e.nodeName)&&t.insertBefore(n.doc.createTextNode(cr),t.firstChild)}const r=n.createRng();if(t.normalize(),t.hasChildNodes()){const e=new Io(t,t);let n,s=t;for(;n=e.current();){if(Yo(n)){r.setStart(n,0),r.setEnd(n,0);break}if(o[n.nodeName.toLowerCase()]){r.setStartBefore(n),r.setEndBefore(n);break}s=n,n=e.next()}n||(r.setStart(s,0),r.setEnd(s,0))}else tr(t)?t.nextSibling&&n.isBlock(t.nextSibling)?(r.setStartBefore(t),r.setEndBefore(t)):(r.setStartAfter(t),r.setEndAfter(t)):(r.setStart(t,0),r.setEnd(t,0));e.selection.setRng(r),$f(e,r)},x_=(e,t)=>{const n=e.getRoot();let o,r=t;for(;r!==n&&r&&"false"!==e.getContentEditable(r);)"true"===e.getContentEditable(r)&&(o=r),r=r.parentNode;return r!==n?o:n},k_=e=>I.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock)),E_=(e,t)=>{const n=null==e?void 0:e.parentNode;return C(n)&&n.nodeName===t},S_=e=>C(e)&&/^(OL|UL|LI)$/.test(e.nodeName),__=e=>{const t=e.parentNode;return C(n=t)&&/^(LI|DT|DD)$/.test(n.nodeName)?t:e;var n},N_=(e,t,n)=>{let o=e[n?"firstChild":"lastChild"];for(;o&&!zo(o);)o=o[n?"nextSibling":"previousSibling"];return o===t},R_=(e,t)=>t&&"A"===t.nodeName&&e.isEmpty(t),A_=e=>{e.innerHTML='<br data-mce-bogus="1">'},O_=(e,t)=>e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t,T_=(e,t)=>C(t)&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&e.isEditable(t.parentNode)&&"false"!==e.getContentEditable(t),B_=(e,t,n)=>Yo(t)?e?1===n&&t.data.charAt(n-1)===Tr?0:n:n===t.data.length-1&&t.data.charAt(n)===Tr?t.data.length:n:n,D_=(e,t)=>{kl(e).toLowerCase()===t.tagName.toLowerCase()&&((e,t,n)=>{const o=e.dom;I.from(n.style).map(o.parseStyle).each((e=>{const n={...co(bn(t)),...e};o.setStyles(t,n)}));const r=I.from(n.class).map((e=>e.split(/\s+/))),s=I.from(t.className).map((e=>G(e.split(/\s+/),(e=>""!==e))));Lt(r,s,((e,n)=>{const r=G(n,(t=>!H(e,t))),s=[...e,...r];o.setAttrib(t,"class",s.join(" "))}));const a=["style","class"],i=ye(n,((e,t)=>!H(a,t)));o.setAttribs(t,i)})(e,t,El(e))},P_={insert:(e,t)=>{let n,o,r,s,a=!1;const i=e.dom,l=e.schema,d=l.getNonEmptyElements(),c=e.selection.getRng(),u=kl(e),f=t=>{let o=n;const s=l.getTextInlineElements();let a;a=t||"TABLE"===r||"HR"===r?i.create(t||u):w.cloneNode(!1);let d=a;if(!1===Rl(e))i.setAttrib(a,"style",null),i.setAttrib(a,"class",null);else do{if(s[o.nodeName]){if(fu(o)||_u(o))continue;const e=o.cloneNode(!1);i.setAttrib(e,"id",""),a.hasChildNodes()?(e.appendChild(a.firstChild),a.appendChild(e)):(d=e,a.appendChild(e))}}while((o=o.parentNode)&&o!==v);return D_(e,a),A_(d),a},g=e=>{const t=B_(e,n,o);if(Yo(n)&&(e?t>0:t<n.data.length))return!1;if(n.parentNode===w&&a&&!e)return!0;if(e&&zo(n)&&n===w.firstChild)return!0;if(O_(n,"TABLE")||O_(n,"HR"))return a&&!e||!a&&e;const r=new Io(n,w);let s;for(Yo(n)&&(e&&0===t?r.prev():e||t!==n.data.length||r.next());s=r.current();){if(zo(s)){if(!s.getAttribute("data-mce-bogus")){const e=s.nodeName.toLowerCase();if(d[e]&&"br"!==e)return!1}}else if(Yo(s)&&!rs(s.data))return!1;e?r.prev():r.next()}return!0},p=()=>{let t;return t=/^(H[1-6]|PRE|FIGURE)$/.test(r)&&"HGROUP"!==x?f(u):f(),((e,t)=>{const n=Al(e);return!y(t)&&(m(n)?H(Dt.explode(n),t.nodeName.toLowerCase()):n)})(e,s)&&T_(i,s)&&i.isEmpty(w)?t=i.split(s,w):i.insertAfter(t,w),w_(e,t),t};Cf(i,c).each((e=>{c.setStart(e.startContainer,e.startOffset),c.setEnd(e.endContainer,e.endOffset)})),n=c.startContainer,o=c.startOffset;const h=!(!t||!t.shiftKey),b=!(!t||!t.ctrlKey);zo(n)&&n.hasChildNodes()&&(a=o>n.childNodes.length-1,n=n.childNodes[Math.min(o,n.childNodes.length-1)]||n,o=a&&Yo(n)?n.data.length:0);const v=x_(i,n);if(!v||((e,t)=>{const n=e.dom.getParent(t,"ol,ul,dl");return null!==n&&"false"===e.dom.getContentEditableParent(n)})(e,n))return;h||(n=((e,t,n,o,r)=>{var s,a;const i=e.dom,l=null!==(s=x_(i,o))&&void 0!==s?s:i.getRoot();let d=i.getParent(o,i.isBlock);if(!d||!T_(i,d)){if(d=d||l,!d.hasChildNodes()){const o=i.create(t);return D_(e,o),d.appendChild(o),n.setStart(o,0),n.setEnd(o,0),o}let s,c=o;for(;c&&c.parentNode!==d;)c=c.parentNode;for(;c&&!i.isBlock(c);)s=c,c=c.previousSibling;const u=null===(a=null==s?void 0:s.parentElement)||void 0===a?void 0:a.nodeName;if(s&&u&&e.schema.isValidChild(u,t.toLowerCase())){const a=s.parentNode,l=i.create(t);for(D_(e,l),a.insertBefore(l,s),c=s;c&&!i.isBlock(c);){const e=c.nextSibling;l.appendChild(c),c=e}n.setStart(o,r),n.setEnd(o,r)}}return o})(e,u,c,n,o));let w=i.getParent(n,i.isBlock)||i.getRoot();s=C(null==w?void 0:w.parentNode)?i.getParent(w.parentNode,i.isBlock):null,r=w?w.nodeName.toUpperCase():"";const x=s?s.nodeName.toUpperCase():"";if("LI"!==x||b||(w=s,s=s.parentNode,r=x),/^(LI|DT|DD)$/.test(r)&&zo(s)&&i.isEmpty(w))return void((e,t,n,o,r)=>{const s=e.dom,a=e.selection.getRng(),i=n.parentNode;if(n===e.getBody()||!i)return;var l;S_(l=n)&&S_(l.parentNode)&&(r="LI");let d=t(r);if(N_(n,o,!0)&&N_(n,o,!1))if(E_(n,"LI")){const e=__(n);s.insertAfter(d,e),(e=>{var t;return(null===(t=e.parentNode)||void 0===t?void 0:t.firstChild)===e})(n)?s.remove(e):s.remove(n)}else s.replace(d,n);else if(N_(n,o,!0))E_(n,"LI")?(s.insertAfter(d,__(n)),d.appendChild(s.doc.createTextNode(" ")),d.appendChild(n)):i.insertBefore(d,n),s.remove(o);else if(N_(n,o,!1))s.insertAfter(d,__(n)),s.remove(o);else{n=__(n);const e=a.cloneRange();e.setStartAfter(o),e.setEndAfter(n);const t=e.extractContents();"LI"===r&&((e,t)=>e.firstChild&&"LI"===e.firstChild.nodeName)(t)?(d=t.firstChild,s.insertAfter(t,n)):(s.insertAfter(t,n),s.insertAfter(d,n)),s.remove(o)}w_(e,d)})(e,f,s,w,u);if(w===e.getBody()||!T_(i,w))return;const k=w.parentNode;let E;if(Mr(w))E=Vr(w),i.isEmpty(w)&&A_(w),D_(e,E),w_(e,E);else if(g(!1))E=p();else if(g(!0)&&k)E=k.insertBefore(f(),w),w_(e,O_(w,"HR")?E:w);else{const t=(e=>{const t=e.cloneRange();return t.setStart(e.startContainer,B_(!0,e.startContainer,e.startOffset)),t.setEnd(e.endContainer,B_(!1,e.endContainer,e.endOffset)),t})(c).cloneRange();t.setEndAfter(w);const n=t.extractContents();(e=>{q(Po(bn(e),qt),(e=>{const t=e.dom;t.nodeValue=Dr(t.data)}))})(n),(e=>{let t=e;do{Yo(t)&&(t.data=t.data.replace(/^[\r\n]+/,"")),t=t.firstChild}while(t)})(n),E=n.firstChild,i.insertAfter(n,w),((e,t,n)=>{var o;const r=[];if(!n)return;let s=n;for(;s=s.firstChild;){if(e.isBlock(s))return;zo(s)&&!t[s.nodeName.toLowerCase()]&&r.push(s)}let a=r.length;for(;a--;)s=r[a],(!s.hasChildNodes()||s.firstChild===s.lastChild&&""===(null===(o=s.firstChild)||void 0===o?void 0:o.nodeValue)||R_(e,s))&&e.remove(s)})(i,d,E),((e,t)=>{t.normalize();const n=t.lastChild;(!n||zo(n)&&/^(left|right)$/gi.test(e.getStyle(n,"float",!0)))&&e.add(t,"br")})(i,w),i.isEmpty(w)&&A_(w),E.normalize(),i.isEmpty(E)?(i.remove(E),p()):(D_(e,E),w_(e,E))}i.setAttrib(E,"id",""),e.dispatch("NewBlock",{newBlock:E})},fakeEventName:"insertParagraph"},L_=(e,t,n)=>{const o=e.dom.createRng();n?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),e.selection.setRng(o),$f(e,o)},M_=(e,t)=>{const n=pn("br");mo(bn(t),n),e.undoManager.add()},I_=(e,t)=>{F_(e.getBody(),t)||fo(bn(t),pn("br"));const n=pn("br");fo(bn(t),n),L_(e,n.dom,!1),e.undoManager.add()},F_=(e,t)=>{return n=Ti.after(t),!!tr(n.getNode())||lu(e,Ti.after(t)).map((e=>tr(e.getNode()))).getOr(!1);var n},U_=e=>e&&"A"===e.nodeName&&"href"in e,z_=e=>e.fold(L,U_,U_,L),j_=(e,t)=>{t.fold(E,O(M_,e),O(I_,e),E)},H_={insert:(e,t)=>{const n=(e=>{const t=O(Kp,e),n=Ti.fromRangeStart(e.selection.getRng());return Ox(t,e.getBody(),n).filter(z_)})(e);n.isSome()?n.each(O(j_,e)):((e,t)=>{const n=e.selection,o=e.dom,r=n.getRng();let s,a=!1;Cf(o,r).each((e=>{r.setStart(e.startContainer,e.startOffset),r.setEnd(e.endContainer,e.endOffset)}));let i=r.startOffset,l=r.startContainer;if(zo(l)&&l.hasChildNodes()){const e=i>l.childNodes.length-1;l=l.childNodes[Math.min(i,l.childNodes.length-1)]||l,i=e&&Yo(l)?l.data.length:0}let d=o.getParent(l,o.isBlock);const c=d&&d.parentNode?o.getParent(d.parentNode,o.isBlock):null,u=c?c.nodeName.toUpperCase():"",m=!(!t||!t.ctrlKey);"LI"!==u||m||(d=c),Yo(l)&&i>=l.data.length&&(((e,t,n)=>{const o=new Io(t,n);let r;const s=e.getNonEmptyElements();for(;r=o.next();)if(s[r.nodeName.toLowerCase()]||Yo(r)&&r.length>0)return!0;return!1})(e.schema,l,d||o.getRoot())||(s=o.create("br"),r.insertNode(s),r.setStartAfter(s),r.setEndAfter(s),a=!0)),s=o.create("br"),Di(o,r,s),L_(e,s,a),e.undoManager.add()})(e,t)},fakeEventName:"insertLineBreak"},$_=(e,t)=>k_(e).filter((e=>t.length>0&&Cn(bn(e),t))).isSome(),V_=nl([{br:[]},{block:[]},{none:[]}]),q_=(e,t)=>(e=>$_(e,Nl(e)))(e),W_=e=>(t,n)=>(e=>k_(e).filter((e=>xr(bn(e)))).isSome())(t)===e,K_=(e,t)=>(n,o)=>{const r=(e=>k_(e).fold(N(""),(e=>e.nodeName.toUpperCase())))(n)===e.toUpperCase();return r===t},G_=e=>{const t=x_(e.dom,e.selection.getStart());return y(t)},Y_=e=>K_("pre",e),X_=e=>(t,n)=>xl(t)===e,Q_=(e,t)=>(e=>$_(e,_l(e)))(e),J_=(e,t)=>t,Z_=e=>{const t=kl(e),n=x_(e.dom,e.selection.getStart());return C(n)&&e.schema.isValidChild(n.nodeName,t)},eN=(e,t)=>(n,o)=>X(e,((e,t)=>e&&t(n,o)),!0)?I.some(t):I.none(),tN=(e,t,n)=>{t.selection.isCollapsed()||(e=>{e.execCommand("delete")})(t),C(n)&&C_(t,e.fakeEventName).isDefaultPrevented()||(e.insert(t,n),C(n)&&y_(t,e.fakeEventName))},nN=(e,t)=>{const n=()=>tN(H_,e,t),o=()=>tN(P_,e,t),r=((e,t)=>Cx([eN([q_],V_.none()),eN([Y_(!0),G_],V_.none()),eN([K_("summary",!0)],V_.br()),eN([Y_(!0),X_(!1),J_],V_.br()),eN([Y_(!0),X_(!1)],V_.block()),eN([Y_(!0),X_(!0),J_],V_.block()),eN([Y_(!0),X_(!0)],V_.br()),eN([W_(!0),J_],V_.br()),eN([W_(!0)],V_.block()),eN([Q_],V_.br()),eN([J_],V_.br()),eN([Z_],V_.block())],[e,!(!t||!t.shiftKey)]).getOr(V_.none()))(e,t);switch(Sl(e)){case"linebreak":r.fold(n,n,E);break;case"block":r.fold(o,o,E);break;case"invert":r.fold(o,n,E);break;default:r.fold(n,o,E)}},oN=xt(),rN=e=>e.stopImmediatePropagation(),sN=e=>e.keyCode===Vm.PAGE_UP||e.keyCode===Vm.PAGE_DOWN,aN=(e,t,n)=>{n&&!e.get()?t.on("NodeChange",rN,!0):!n&&e.get()&&t.off("NodeChange",rN),e.set(n)},iN=(e,t)=>{const n=t.container(),o=t.offset();return Yo(n)?(n.insertData(o,e),I.some(Ti(n,o+e.length))):zc(t).map((n=>{const o=hn(e);return t.isAtEnd()?fo(n,o):mo(n,o),Ti(o.dom,e.length)}))},lN=O(iN,cr),dN=O(iN," "),cN=(e,t)=>n=>((e,t)=>!Ep(t)&&(((e,t)=>((e,t)=>du(e.dom,t).isNone())(e,t)||((e,t)=>lu(e.dom,t).isNone())(e,t)||cp(e,t)||up(e,t)||bp(e,t)||hp(e,t))(e,t)||xp(e,t)||kp(e,t)))(e,n)?lN(t):dN(t),uN=e=>{const t=Ti.fromRangeStart(e.selection.getRng()),n=bn(e.getBody());if(e.selection.isCollapsed()){const o=O(Kp,e),r=Ti.fromRangeStart(e.selection.getRng());return Ox(o,e.getBody(),r).bind((e=>t=>t.fold((t=>du(e.dom,Ti.before(t))),(e=>cu(e)),(e=>uu(e)),(t=>lu(e.dom,Ti.after(t)))))(n)).map((o=>()=>cN(n,t)(o).each((e=>t=>(e.selection.setRng(t.toRange()),e.nodeChanged(),!0))(e))))}return I.none()},mN=e=>Zd(e)?[{keyCode:Vm.TAB,action:wE(GE,e,!0)},{keyCode:Vm.TAB,shiftKey:!0,action:wE(GE,e,!1)}]:[],fN=e=>{if(e.addShortcut("Meta+P","","mcePrint"),b_(e),CC(e))return Aa(null);{const t=Ek(e);return(e=>{e.on("keyup compositionstart",O(dE,e))})(e),((e,t)=>{e.on("keydown",(n=>{n.isDefaultPrevented()||((e,t,n)=>{const o=At.os.isMacOS()||At.os.isiOS();xE([{keyCode:Vm.RIGHT,action:wE(mE,e,!0)},{keyCode:Vm.LEFT,action:wE(mE,e,!1)},{keyCode:Vm.UP,action:wE(fE,e,!1)},{keyCode:Vm.DOWN,action:wE(fE,e,!0)},...o?[{keyCode:Vm.UP,action:wE(pE,e,!1),metaKey:!0,shiftKey:!0},{keyCode:Vm.DOWN,action:wE(pE,e,!0),metaKey:!0,shiftKey:!0}]:[],{keyCode:Vm.RIGHT,action:wE($E,e,!0)},{keyCode:Vm.LEFT,action:wE($E,e,!1)},{keyCode:Vm.UP,action:wE(VE,e,!1)},{keyCode:Vm.DOWN,action:wE(VE,e,!0)},{keyCode:Vm.RIGHT,action:wE(EE,e,!0)},{keyCode:Vm.LEFT,action:wE(EE,e,!1)},{keyCode:Vm.UP,action:wE(SE,e,!1)},{keyCode:Vm.DOWN,action:wE(SE,e,!0)},{keyCode:Vm.RIGHT,action:wE(xk,e,t,!0)},{keyCode:Vm.LEFT,action:wE(xk,e,t,!1)},{keyCode:Vm.RIGHT,ctrlKey:!o,altKey:o,action:wE(Sk,e,t)},{keyCode:Vm.LEFT,ctrlKey:!o,altKey:o,action:wE(_k,e,t)},{keyCode:Vm.UP,action:wE(vE,e,!1)},{keyCode:Vm.DOWN,action:wE(vE,e,!0)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{let n=!1;e.on("keydown",(o=>{n=o.keyCode===Vm.BACKSPACE,o.isDefaultPrevented()||((e,t,n)=>{const o=n.keyCode===Vm.BACKSPACE?"deleteContentBackward":"deleteContentForward";kE([{keyCode:Vm.BACKSPACE,action:wE(Xk,e)},{keyCode:Vm.BACKSPACE,action:wE(lx,e,!1)},{keyCode:Vm.DELETE,action:wE(lx,e,!0)},{keyCode:Vm.BACKSPACE,action:wE(ex,e,!1)},{keyCode:Vm.DELETE,action:wE(ex,e,!0)},{keyCode:Vm.BACKSPACE,action:wE(Ok,e,t,!1)},{keyCode:Vm.DELETE,action:wE(Ok,e,t,!0)},{keyCode:Vm.BACKSPACE,action:wE(Ah,e,!1)},{keyCode:Vm.DELETE,action:wE(Ah,e,!0)},{keyCode:Vm.BACKSPACE,action:wE(dx,e,!1)},{keyCode:Vm.DELETE,action:wE(dx,e,!0)},{keyCode:Vm.BACKSPACE,action:wE(Hk,e,!1)},{keyCode:Vm.DELETE,action:wE(Hk,e,!0)},{keyCode:Vm.BACKSPACE,action:wE(Kw,e,!1)},{keyCode:Vm.DELETE,action:wE(Kw,e,!0)},{keyCode:Vm.BACKSPACE,action:wE(Vw,e,!1)},{keyCode:Vm.DELETE,action:wE(Vw,e,!0)},{keyCode:Vm.BACKSPACE,action:wE(Fk,e,!1)},{keyCode:Vm.DELETE,action:wE(Fk,e,!0)}],n).filter((t=>e.selection.isEditable())).each((t=>{n.preventDefault(),C_(e,o).isDefaultPrevented()||(t(),y_(e,o))}))})(e,t,o)})),e.on("keyup",(t=>{t.isDefaultPrevented()||((e,t,n)=>{const o=xt(),r=o.os,s=o.browser,a=r.isMacOS()?[{keyCode:Vm.BACKSPACE,altKey:!0,action:wE(zk,e)},{keyCode:Vm.DELETE,altKey:!0,action:wE(zk,e)}]:[{keyCode:Vm.BACKSPACE,ctrlKey:!0,action:wE(zk,e)},{keyCode:Vm.DELETE,ctrlKey:!0,action:wE(zk,e)}];r.isMacOS()&&n&&a.push({keyCode:s.isFirefox()?224:91,action:wE(zk,e)}),xE([{keyCode:Vm.BACKSPACE,action:wE(ix,e)},{keyCode:Vm.DELETE,action:wE(ix,e)},...a],t)})(e,t,n),n=!1}))})(e,t),(e=>{e.on("keydown",(t=>{t.keyCode===Vm.ENTER&&((e,t)=>{var n;t.isDefaultPrevented()||(t.preventDefault(),(n=e.undoManager).typing&&(n.typing=!1,n.add()),e.undoManager.transact((()=>{nN(e,t)})))})(e,t)}))})(e),(e=>{e.on("keydown",(t=>{t.isDefaultPrevented()||((e,t)=>{kE([{keyCode:Vm.SPACEBAR,action:wE(uN,e)}],t).each((n=>{t.preventDefault(),C_(e,"insertText",{data:" "}).isDefaultPrevented()||(n(),y_(e,"insertText",{data:" "}))}))})(e,t)}))})(e),(e=>{e.on("input",(t=>{t.isComposing||(e=>{const t=bn(e.getBody());e.selection.isCollapsed()&&Dp(t,Ti.fromRangeStart(e.selection.getRng())).each((t=>{e.selection.setRng(t.toRange())}))})(e)}))})(e),(e=>{e.on("keydown",(t=>{t.isDefaultPrevented()||((e,t)=>{xE([...mN(e)],t).each((e=>{t.preventDefault()}))})(e,t)}))})(e),((e,t)=>{e.on("keydown",(n=>{n.isDefaultPrevented()||((e,t,n)=>{const o=At.os.isMacOS()||At.os.isiOS();xE([{keyCode:Vm.END,action:wE(gE,e,!0)},{keyCode:Vm.HOME,action:wE(gE,e,!1)},...o?[]:[{keyCode:Vm.HOME,action:wE(pE,e,!1),ctrlKey:!0,shiftKey:!0},{keyCode:Vm.END,action:wE(pE,e,!0),ctrlKey:!0,shiftKey:!0}],{keyCode:Vm.END,action:wE(_E,e,!0)},{keyCode:Vm.HOME,action:wE(_E,e,!1)},{keyCode:Vm.END,action:wE(Nk,e,!0,t)},{keyCode:Vm.HOME,action:wE(Nk,e,!1,t)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{if(oN.os.isMacOS())return;const n=Aa(!1);e.on("keydown",(t=>{sN(t)&&aN(n,e,!0)})),e.on("keyup",(o=>{o.isDefaultPrevented()||((e,t,n)=>{xE([{keyCode:Vm.PAGE_UP,action:wE(Nk,e,!1,t)},{keyCode:Vm.PAGE_DOWN,action:wE(Nk,e,!0,t)}],n)})(e,t,o),sN(o)&&n.get()&&(aN(n,e,!1),e.nodeChanged())}))})(e,t),t}};class gN{constructor(e){let t;this.lastPath=[],this.editor=e;const n=this;"onselectionchange"in e.getDoc()||e.on("NodeChange click mouseup keyup focus",(n=>{const o=e.selection.getRng(),r={startContainer:o.startContainer,startOffset:o.startOffset,endContainer:o.endContainer,endOffset:o.endOffset};"nodechange"!==n.type&&ff(r,t)||e.dispatch("SelectionChange"),t=r})),e.on("contextmenu",(()=>{e.dispatch("SelectionChange")})),e.on("SelectionChange",(()=>{const t=e.selection.getStart(!0);t&&qu(e)&&!n.isSameElementPath(t)&&e.dom.isChildOf(t,e.getBody())&&e.nodeChanged({selectionChange:!0})})),e.on("mouseup",(t=>{!t.isDefaultPrevented()&&qu(e)&&("IMG"===e.selection.getNode().nodeName?ng.setEditorTimeout(e,(()=>{e.nodeChanged()})):e.nodeChanged())}))}nodeChanged(e={}){const t=this.editor.selection;let n;if(this.editor.initialized&&t&&!bd(this.editor)&&!this.editor.mode.isReadOnly()){const o=this.editor.getBody();n=t.getStart(!0)||o,n.ownerDocument===this.editor.getDoc()&&this.editor.dom.isChildOf(n,o)||(n=o);const r=[];this.editor.dom.getParent(n,(e=>e===o||(r.push(e),!1))),this.editor.dispatch("NodeChange",{...e,element:n,parents:r})}}isSameElementPath(e){let t;const n=this.editor,o=oe(n.dom.getParents(e,M,n.getBody()));if(o.length===this.lastPath.length){for(t=o.length;t>=0&&o[t]===this.lastPath[t];t--);if(-1===t)return this.lastPath=o,!0}return this.lastPath=o,!1}}const pN=N("x-tinymce/html"),hN="\x3c!-- x-tinymce/html --\x3e",bN=e=>hN+e,vN=e=>-1!==e.indexOf(hN),yN="%MCEPASTEBIN%",CN=e=>e.dom.get("mcepastebin"),wN=e=>C(e)&&"mcepastebin"===e.id,xN=e=>e===yN,kN=(e,t)=>(Dt.each(t,(t=>{e=u(t,RegExp)?e.replace(t,""):e.replace(t[0],t[1])})),e),EN=e=>kN(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,(e,t,n)=>t||n?cr:" "],/<br class="Apple-interchange-newline">/g,/<br>$/i]),SN=(e,t)=>({content:e,cancelled:t}),_N=(e,t)=>(e.insertContent(t,{merge:Ud(e),paste:!0}),!0),NN=e=>/^https?:\/\/[\w\-\/+=.,!;:&%@^~(){}?#]+$/i.test(e),RN=(e,t,n)=>!(e.selection.isCollapsed()||!NN(t))&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.execCommand("mceInsertLink",!1,t)})),!0))(e,t,n),AN=(e,t,n)=>!!((e,t)=>NN(t)&&$(Jd(e),(e=>$e(t.toLowerCase(),`.${e.toLowerCase()}`))))(e,t)&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.insertContent('<img src="'+t+'">')})),!0))(e,t,n),ON=(e=>{let t=0;return()=>"mceclip"+t++})(),TN=(e,t,n,o)=>{const r=((e,t,n)=>((e,t,n)=>{const o=((e,t,n)=>e.dispatch("PastePreProcess",{content:t,internal:n}))(e,t,n),r=((e,t)=>{const n=jy({sanitize:Qd(e)},e.schema);n.addNodeFilter("meta",(e=>{Dt.each(e,(e=>{e.remove()}))}));const o=n.parse(t,{forced_root_block:!1,isRootContent:!0});return Fg({validate:!0},e.schema).serialize(o)})(e,o.content);return e.hasEventListeners("PastePostProcess")&&!o.isDefaultPrevented()?((e,t,n)=>{const o=e.dom.create("div",{style:"display:none"},t),r=((e,t,n)=>e.dispatch("PastePostProcess",{node:t,internal:n}))(e,o,n);return SN(r.node.innerHTML,r.isDefaultPrevented())})(e,r,n):SN(r,o.isDefaultPrevented())})(e,t,n))(e,t,n);r.cancelled||((e,t,n)=>{n||!zd(e)?_N(e,t):((e,t)=>{Dt.each([RN,AN,_N],(n=>!n(e,t,_N)))})(e,t)})(e,r.content,o)},BN=(e,t,n)=>{const o=n||vN(t);TN(e,(e=>e.replace(hN,""))(t),o,!1)},DN=(e,t)=>{const n=e.dom.encode(t).replace(/\r\n/g,"\n"),o=((e,t,n)=>{const o=e.split(/\n\n/),r=((e,t)=>{let n="<"+e;const o=Ce(t,((e,t)=>t+'="'+Ks.encodeAllRaw(e)+'"'));return o.length&&(n+=" "+o.join(" ")),n+">"})(t,n),s="</"+t+">",a=V(o,(e=>e.split(/\n/).join("<br />")));return 1===a.length?a[0]:V(a,(e=>r+e+s)).join("")})(as(n,Hd(e)),kl(e),El(e));TN(e,o,!1,!0)},PN=e=>{const t={};if(e&&e.types)for(let n=0;n<e.types.length;n++){const o=e.types[n];try{t[o]=e.getData(o)}catch(e){t[o]=""}}return t},LN=(e,t)=>t in e&&e[t].length>0,MN=e=>LN(e,"text/html")||LN(e,"text/plain"),IN=(e,t,n)=>{const o="paste"===t.type?t.clipboardData:t.dataTransfer;var r;if(Pd(e)&&o){const s=((e,t)=>{const n=t.items?te(ce(t.items),(e=>"file"===e.kind?[e.getAsFile()]:[])):[],o=t.files?ce(t.files):[];return G(n.length>0?n:o,(e=>{const t=Jd(e);return e=>He(e.type,"image/")&&$(t,(t=>(e=>{const t=e.toLowerCase(),n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"};return Dt.hasOwn(n,t)?"image/"+n[t]:"image/"+t})(t)===e.type))})(e))})(e,o);if(s.length>0)return t.preventDefault(),(r=s,Promise.all(V(r,(e=>bv(e).then((t=>({file:e,uri:t}))))))).then((t=>{n&&e.selection.setRng(n),q(t,(t=>{((e,t)=>{pv(t.uri).each((({data:n,type:o,base64Encoded:r})=>{const s=r?n:btoa(n),a=t.file,i=e.editorUpload.blobCache,l=i.getByData(s,o),d=null!=l?l:((e,t,n,o)=>{const r=ON(),s=Tl(e)&&C(n.name),a=s?((e,t)=>{const n=t.match(/([\s\S]+?)(?:\.[a-z0-9.]+)$/i);return C(n)?e.dom.encode(n[1]):void 0})(e,n.name):r,i=s?n.name:void 0,l=t.create(r,n,o,a,i);return t.add(l),l})(e,i,a,s);BN(e,`<img src="${d.blobUri()}">`,!1)}))})(e,t)}))})),!0}return!1},FN=(e,t,n,o)=>{let r=EN(n);const s=LN(t,pN())||vN(n),a=!s&&(e=>!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(e))(r),i=NN(r);(xN(r)||!r.length||a&&!i)&&(o=!0),(o||i)&&(r=LN(t,"text/plain")&&a?t["text/plain"]:(e=>{const t=aa(),n=jy({},t);let o="";const r=t.getVoidElements(),s=Dt.makeMap("script noscript style textarea video audio iframe object"," "),a=t.getBlockElements(),i=e=>{const n=e.name,l=e;if("br"!==n){if("wbr"!==n)if(r[n]&&(o+=" "),s[n])o+=" ";else{if(3===e.type&&(o+=e.value),!(e.name in t.getVoidElements())){let t=e.firstChild;if(t)do{i(t)}while(t=t.next)}a[n]&&l.next&&(o+="\n","p"===n&&(o+="\n"))}}else o+="\n"};return e=kN(e,[/<!\[[^\]]+\]>/g]),i(n.parse(e)),o})(r)),xN(r)||(o?DN(e,r):BN(e,r,s))},UN=(e,t,n)=>{((e,t,n)=>{let o;e.on("keydown",(e=>{(e=>Vm.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode)(e)&&!e.isDefaultPrevented()&&(o=e.shiftKey&&86===e.keyCode)})),e.on("paste",(r=>{if(r.isDefaultPrevented()||(e=>{var t,n;return At.os.isAndroid()&&0===(null===(n=null===(t=e.clipboardData)||void 0===t?void 0:t.items)||void 0===n?void 0:n.length)})(r))return;const s="text"===n.get()||o;o=!1;const a=PN(r.clipboardData);!MN(a)&&IN(e,r,t.getLastRng()||e.selection.getRng())||(LN(a,"text/html")?(r.preventDefault(),FN(e,a,a["text/html"],s)):LN(a,"text/plain")&&LN(a,"text/uri-list")?(r.preventDefault(),FN(e,a,a["text/plain"],s)):(t.create(),ng.setEditorTimeout(e,(()=>{const n=t.getHtml();t.remove(),FN(e,a,n,s)}),0)))}))})(e,t,n),(e=>{const t=e=>He(e,"webkit-fake-url"),n=e=>He(e,"data:");e.parser.addNodeFilter("img",((o,r,s)=>{if(!Pd(e)&&(e=>{var t;return!0===(null===(t=e.data)||void 0===t?void 0:t.paste)})(s))for(const r of o){const o=r.attr("src");m(o)&&!r.attr("data-mce-object")&&o!==At.transparentSrc&&(t(o)||!$d(e)&&n(o))&&r.remove()}}))})(e)},zN=(e,t,n,o)=>{((e,t,n)=>{if(!e)return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(pN(),t),!0}catch(e){return!1}})(e.clipboardData,t.html,t.text)?(e.preventDefault(),o()):n(t.html,o)},jN=e=>(t,n)=>{const{dom:o,selection:r}=e,s=o.create("div",{contenteditable:"false","data-mce-bogus":"all"}),a=o.create("div",{contenteditable:"true"},t);o.setStyles(s,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),s.appendChild(a),o.add(e.getBody(),s);const i=r.getRng();a.focus();const l=o.createRng();l.selectNodeContents(a),r.setRng(l),ng.setEditorTimeout(e,(()=>{r.setRng(i),o.remove(s),n()}),0)},HN=e=>({html:bN(e.selection.getContent({contextual:!0})),text:e.selection.getContent({format:"text"})}),$N=e=>!e.selection.isCollapsed()||(e=>!!e.dom.getParent(e.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",e.getBody()))(e),VN=(e,t)=>{var n,o;return kf.getCaretRangeFromPoint(null!==(n=t.clientX)&&void 0!==n?n:0,null!==(o=t.clientY)&&void 0!==o?o:0,e.getDoc())},qN=(e,t)=>{e.focus(),t&&e.selection.setRng(t)},WN=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,KN=e=>Dt.trim(e).replace(WN,Mu).toLowerCase(),GN=(e,t,n)=>{const o=Id(e);if(n||"all"===o||!Fd(e))return t;const r=o?o.split(/[, ]/):[];if(r&&"none"!==o){const n=e.dom,o=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,((e,t,s,a)=>{const i=n.parseStyle(n.decode(s)),l={};for(let e=0;e<r.length;e++){const t=i[r[e]];let s=t,a=n.getStyle(o,r[e],!0);/color/.test(r[e])&&(s=KN(s),a=KN(a)),a!==s&&(l[r[e]]=t)}const d=n.serializeStyle(l,"span");return d?t+' style="'+d+'"'+a:t+a}))}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,((e,t,n,o)=>t+' style="'+n+'"'+o)),t},YN=e=>{const t=Aa(!1),n=Aa(jd(e)?"text":"html"),o=(e=>{const t=Aa(null);return{create:()=>((e,t)=>{const{dom:n,selection:o}=e,r=e.getBody();t.set(o.getRng());const s=n.add(e.getBody(),"div",{id:"mcepastebin",class:"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},yN);At.browser.isFirefox()&&n.setStyle(s,"left","rtl"===n.getStyle(r,"direction",!0)?65535:-65535),n.bind(s,"beforedeactivate focusin focusout",(e=>{e.stopPropagation()})),s.focus(),o.select(s,!0)})(e,t),remove:()=>((e,t)=>{const n=e.dom;if(CN(e)){let o;const r=t.get();for(;o=CN(e);)n.remove(o),n.unbind(o);r&&e.selection.setRng(r)}t.set(null)})(e,t),getEl:()=>CN(e),getHtml:()=>(e=>{const t=e.dom,n=(e,n)=>{e.appendChild(n),t.remove(n,!0)},[o,...r]=G(e.getBody().childNodes,wN);q(r,(e=>{n(o,e)}));const s=t.select("div[id=mcepastebin]",o);for(let e=s.length-1;e>=0;e--){const r=t.create("div");o.insertBefore(r,s[e]),n(r,s[e])}return o?o.innerHTML:""})(e),getLastRng:t.get}})(e);(e=>{(At.browser.isChromium()||At.browser.isSafari())&&((e,t)=>{e.on("PastePreProcess",(n=>{n.content=t(e,n.content,n.internal)}))})(e,GN)})(e),((e,t)=>{e.addCommand("mceTogglePlainTextPaste",(()=>{((e,t)=>{"text"===t.get()?(t.set("html"),$m(e,!1)):(t.set("text"),$m(e,!0)),e.focus()})(e,t)})),e.addCommand("mceInsertClipboardContent",((t,n)=>{n.html&&BN(e,n.html,n.internal),n.text&&DN(e,n.text)}))})(e,n),(e=>{const t=t=>n=>{t(e,n)},n=Ld(e);w(n)&&e.on("PastePreProcess",t(n));const o=Md(e);w(o)&&e.on("PastePostProcess",t(o))})(e),e.on("PreInit",(()=>{(e=>{e.on("cut",(e=>t=>{!t.isDefaultPrevented()&&$N(e)&&zN(t,HN(e),jN(e),(()=>{if(At.browser.isChromium()||At.browser.isFirefox()){const t=e.selection.getRng();ng.setEditorTimeout(e,(()=>{e.selection.setRng(t),e.execCommand("Delete")}),0)}else e.execCommand("Delete")}))})(e)),e.on("copy",(e=>t=>{!t.isDefaultPrevented()&&$N(e)&&zN(t,HN(e),jN(e),E)})(e))})(e),((e,t)=>{Dd(e)&&e.on("dragend dragover draggesture dragdrop drop drag",(e=>{e.preventDefault(),e.stopPropagation()})),Pd(e)||e.on("drop",(e=>{const t=e.dataTransfer;t&&(e=>$(e.files,(e=>/^image\//.test(e.type))))(t)&&e.preventDefault()})),e.on("drop",(n=>{if(n.isDefaultPrevented())return;const o=VN(e,n);if(y(o))return;const r=PN(n.dataTransfer),s=LN(r,pN());if((!MN(r)||(e=>{const t=e["text/plain"];return!!t&&0===t.indexOf("file://")})(r))&&IN(e,n,o))return;const a=r[pN()],i=a||r["text/html"]||r["text/plain"],l=((e,t,n,o)=>{const r=e.getParent(n,(e=>ks(t,e)));if(r&&ke(o,"text/html")){const e=(new DOMParser).parseFromString(o["text/html"],"text/html").body;return!h(e.querySelector(r.nodeName.toLowerCase()))}return!1})(e.dom,e.schema,o.startContainer,r);t.get()&&!l||i&&(n.preventDefault(),ng.setEditorTimeout(e,(()=>{e.undoManager.transact((()=>{a&&e.execCommand("Delete"),qN(e,o);const t=EN(i);r["text/html"]?BN(e,t,s):DN(e,t)}))})))})),e.on("dragstart",(e=>{t.set(!0)})),e.on("dragover dragend",(n=>{Pd(e)&&!t.get()&&(n.preventDefault(),qN(e,VN(e,n))),"dragend"===n.type&&t.set(!1)}))})(e,t),UN(e,o,n)}))},XN=tr,QN=Yo,JN=e=>rr(e.dom),ZN=e=>t=>xn(bn(e),t),eR=(e,t)=>Xn(bn(e),JN,ZN(t)),tR=(e,t,n)=>{const o=new Io(e,t),r=n?o.next.bind(o):o.prev.bind(o);let s=e;for(let t=n?e:r();t&&!XN(t);t=r())es(t)&&(s=t);return s},nR=e=>{const t=((e,t)=>{const n=Ti.fromRangeStart(e).getNode(),o=((e,t)=>Xn(bn(e),(e=>(e=>or(e.dom))(e)||br(e)),ZN(t)).getOr(bn(t)).dom)(n,t),r=tR(n,o,!1),s=tR(n,o,!0),a=document.createRange();return eR(r,o).fold((()=>{QN(r)?a.setStart(r,0):a.setStartBefore(r)}),(e=>a.setStartBefore(e.dom))),eR(s,o).fold((()=>{QN(s)?a.setEnd(s,s.data.length):a.setEndAfter(s)}),(e=>a.setEndAfter(e.dom))),a})(e.selection.getRng(),e.getBody());e.selection.setRng(Vh(t))};var oR;!function(e){e.Before="before",e.After="after"}(oR||(oR={}));const rR=(e,t)=>Math.abs(e.left-t),sR=(e,t)=>Math.abs(e.right-t),aR=(e,t)=>(e=>X(e,((e,t)=>e.fold((()=>I.some(t)),(e=>{const n=Math.min(t.left,e.left),o=Math.min(t.top,e.top),r=Math.max(t.right,e.right),s=Math.max(t.bottom,e.bottom);return I.some({top:o,right:r,bottom:s,left:n,width:r-n,height:s-o})}))),I.none()))(G(e,(e=>{return(n=t)>=(o=e).top&&n<=o.bottom;var n,o}))).fold((()=>[[],e]),(t=>{const{pass:n,fail:o}=K(e,(e=>((e,t)=>{const n=((e,t)=>Math.max(0,Math.min(e.bottom,t.bottom)-Math.max(e.top,t.top)))(e,t)/Math.min(e.height,t.height);return((e,t)=>e.top<t.bottom&&e.bottom>t.top)(e,t)&&n>.5})(e,t)));return[n,o]})),iR=(e,t,n)=>t>e.left&&t<e.right?0:Math.min(Math.abs(e.left-t),Math.abs(e.right-t)),lR=(e,t,n)=>{const o=e=>es(e.node)?I.some(e):zo(e.node)?lR(ce(e.node.childNodes),t,n):I.none(),r=(e,r)=>{const s=ae(e,((e,o)=>r(e,t,n)-r(o,t,n)));return((e,r)=>{if(e.length>=2){const s=o(e[0]).getOr(e[0]),a=o(e[1]).getOr(e[1]);if(Math.abs(r(s,t,n)-r(a,t,n))<2){if(Yo(s.node))return I.some(s);if(Yo(a.node))return I.some(a)}}return I.none()})(s,r).orThunk((()=>ue(s,o)))},[s,a]=aR(rk(e),n),{pass:i,fail:l}=K(a,(e=>e.top<n));return r(s,iR).orThunk((()=>r(l,di))).orThunk((()=>r(i,di)))},dR=(e,t,n)=>((e,t,n)=>{const o=bn(e),r=Sn(o),s=vn(r,t,n).filter((e=>kn(o,e))).getOr(o);return((e,t,n,o)=>{const r=(t,s)=>{const a=G(t.dom.childNodes,T((e=>zo(e)&&e.classList.contains("mce-drag-container"))));return s.fold((()=>lR(a,n,o)),(e=>{const t=G(a,(t=>t!==e.dom));return lR(t,n,o)})).orThunk((()=>(xn(t,e)?I.none():Rn(t)).bind((e=>r(e,I.some(t))))))};return r(t,I.none())})(o,s,t,n)})(e,t,n).filter((e=>pc(e.node))).map((e=>((e,t)=>({node:e.node,position:rR(e,t)<sR(e,t)?oR.Before:oR.After}))(e,t))),cR=e=>{var t,n;const o=e.getBoundingClientRect(),r=e.ownerDocument,s=r.documentElement,a=r.defaultView;return{top:o.top+(null!==(t=null==a?void 0:a.scrollY)&&void 0!==t?t:0)-s.clientTop,left:o.left+(null!==(n=null==a?void 0:a.scrollX)&&void 0!==n?n:0)-s.clientLeft}},uR=e=>({target:e,srcElement:e}),mR=e=>(t,n)=>((e,t,n)=>({...t,dataTransfer:null,type:e,...n}))(e,t,uR(n)),fR=mR("dragstart"),gR=mR("drop"),pR=e=>((e,t)=>{const n=B("Function not supported on simulated event.");return{bubbles:!0,cancelBubble:!1,cancelable:!0,composed:!1,currentTarget:null,defaultPrevented:!1,eventPhase:0,isTrusted:!0,returnValue:!1,srcElement:null,target:null,timeStamp:0,type:"dragend",composedPath:n,initEvent:n,preventDefault:E,stopImmediatePropagation:E,stopPropagation:E,AT_TARGET:window.Event.AT_TARGET,BUBBLING_PHASE:window.Event.BUBBLING_PHASE,CAPTURING_PHASE:window.Event.CAPTURING_PHASE,NONE:window.Event.NONE,altKey:!1,button:0,buttons:0,clientX:0,clientY:0,ctrlKey:!1,metaKey:!1,movementX:0,movementY:0,offsetX:0,offsetY:0,pageX:0,pageY:0,relatedTarget:null,screenX:0,screenY:0,shiftKey:!1,x:0,y:0,detail:0,view:null,which:0,initUIEvent:n,initMouseEvent:n,getModifierState:n,dataTransfer:null,...t}})(0,uR(e)),hR=mR("dragend"),bR=rr,vR=((...e)=>t=>{for(let n=0;n<e.length;n++)if(e[n](t))return!0;return!1})(bR,or),yR=(e,t,n,o)=>{const r=e.dom,s=t.cloneNode(!0);r.setStyles(s,{width:n,height:o}),r.setAttrib(s,"data-mce-selected",null);const a=r.create("div",{class:"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return r.setStyles(a,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:o}),r.setStyles(s,{margin:0,boxSizing:"border-box"}),a.appendChild(s),a},CR=(e,t)=>n=>()=>{const o="left"===e?n.scrollX:n.scrollY;n.scroll({[e]:o+t,behavior:"smooth"})},wR=CR("left",-32),xR=CR("left",32),kR=CR("top",-32),ER=CR("top",32),SR=e=>{e&&e.parentNode&&e.parentNode.removeChild(e)},_R=(e,t)=>{const n=Ia(((e,n)=>((e,t,n)=>{e._selectionOverrides.hideFakeCaret(),dR(e.getBody(),t,n).fold((()=>e.selection.placeCaretAt(t,n)),(o=>{const r=e._selectionOverrides.showCaret(1,o.node,o.position===oR.Before,!1);r?e.selection.setRng(r):e.selection.placeCaretAt(t,n)}))})(t,e,n)),0);t.on("remove",n.cancel);const o=e;return r=>e.on((e=>{const s=Math.max(Math.abs(r.screenX-e.screenX),Math.abs(r.screenY-e.screenY));if(!e.dragging&&s>10){if(t.dispatch("dragstart",fR(r,e.element)).isDefaultPrevented())return;e.dragging=!0,t.focus()}if(e.dragging){const s=r.currentTarget===t.getDoc().documentElement,l=((e,t)=>({pageX:t.pageX-e.relX,pageY:t.pageY+5}))(e,((e,t)=>{return n=(e=>e.inline?cR(e.getBody()):{left:0,top:0})(e),o=(e=>{const t=e.getBody();return e.inline?{left:t.scrollLeft,top:t.scrollTop}:{left:0,top:0}})(e),r=((e,t)=>{if(t.target.ownerDocument!==e.getDoc()){const n=cR(e.getContentAreaContainer()),o=(e=>{const t=e.getBody(),n=e.getDoc().documentElement,o={left:t.scrollLeft,top:t.scrollTop},r={left:t.scrollLeft||n.scrollLeft,top:t.scrollTop||n.scrollTop};return e.inline?o:r})(e);return{left:t.pageX-n.left+o.left,top:t.pageY-n.top+o.top}}return{left:t.pageX,top:t.pageY}})(e,t),{pageX:r.left-n.left+o.left,pageY:r.top-n.top+o.top};var n,o,r})(t,r));a=e.ghost,i=t.getBody(),a.parentNode!==i&&i.appendChild(a),((e,t,n,o,r,s,a,i,l,d,c,u)=>{let m=0,f=0;e.style.left=t.pageX+"px",e.style.top=t.pageY+"px",t.pageX+n>r&&(m=t.pageX+n-r),t.pageY+o>s&&(f=t.pageY+o-s),e.style.width=n-m+"px",e.style.height=o-f+"px";const g=l.clientHeight,p=l.clientWidth,h=a+l.getBoundingClientRect().top,b=i+l.getBoundingClientRect().left;c.on((e=>{e.intervalId.clear(),e.dragging&&u&&(a+8>=g?e.intervalId.set(ER(d)):a-8<=0?e.intervalId.set(kR(d)):i+8>=p?e.intervalId.set(xR(d)):i-8<=0?e.intervalId.set(wR(d)):h+16>=window.innerHeight?e.intervalId.set(ER(window)):h-16<=0?e.intervalId.set(kR(window)):b+16>=window.innerWidth?e.intervalId.set(xR(window)):b-16<=0&&e.intervalId.set(wR(window)))}))})(e.ghost,l,e.width,e.height,e.maxX,e.maxY,r.clientY,r.clientX,t.getContentAreaContainer(),t.getWin(),o,s),n.throttle(r.clientX,r.clientY)}var a,i}))},NR=(e,t,n)=>{e.on((e=>{if(e.intervalId.clear(),e.dragging){const o=n.fold((()=>pR(e.element)),(t=>hR(t,e.element)));t.dispatch("dragend",o)}})),RR(e)},RR=e=>{e.on((e=>{e.intervalId.clear(),SR(e.ghost)})),e.clear()},AR=e=>{const t=Ma(),n=_a.DOM,o=document,r=((e,t)=>n=>{if((e=>0===e.button)(n)){const o=J(t.dom.getParents(n.target),vR).getOr(null);if(C(o)&&((e,t,n)=>bR(n)&&n!==t&&e.isEditable(n.parentElement))(t.dom,t.getBody(),o)){const r=t.dom.getPos(o),s=t.getBody(),a=t.getDoc().documentElement;e.set({element:o,dragging:!1,screenX:n.screenX,screenY:n.screenY,maxX:(t.inline?s.scrollWidth:a.offsetWidth)-2,maxY:(t.inline?s.scrollHeight:a.offsetHeight)-2,relX:n.pageX-r.x,relY:n.pageY-r.y,width:o.offsetWidth,height:o.offsetHeight,ghost:yR(t,o,o.offsetWidth,o.offsetHeight),intervalId:La(100)})}}})(t,e),s=_R(t,e),a=((e,t)=>n=>{e.on((e=>{var o;if(e.intervalId.clear(),e.dragging){if(((e,t,n)=>!y(t)&&t!==n&&!e.dom.isChildOf(t,n)&&e.dom.isEditable(t))(t,(e=>{const t=e.getSel();if(C(t)){const e=t.getRangeAt(0).startContainer;return Yo(e)?e.parentNode:e}return null})(t.selection),e.element)){const r=(e=>{const t=e.cloneNode(!0);return t.removeAttribute("data-mce-selected"),t})(e.element),s=null!==(o=t.getDoc().elementFromPoint(n.clientX,n.clientY))&&void 0!==o?o:t.getBody();t.dispatch("drop",gR(n,s)).isDefaultPrevented()||t.undoManager.transact((()=>{((e,t)=>{const n=e.getParent(t.parentNode,e.isBlock);SR(t),n&&n!==e.getRoot()&&e.isEmpty(n)&&Ar(bn(n))})(t.dom,e.element),t.insertContent(t.dom.getOuterHTML(r)),t._selectionOverrides.hideFakeCaret()}))}t.dispatch("dragend",hR(n,t.getBody()))}})),RR(e)})(t,e),i=((e,t)=>n=>NR(e,t,I.some(n)))(t,e);e.on("mousedown",r),e.on("mousemove",s),e.on("mouseup",a),n.bind(o,"mousemove",s),n.bind(o,"mouseup",i),e.on("remove",(()=>{n.unbind(o,"mousemove",s),n.unbind(o,"mouseup",i)})),e.on("keydown",(n=>{n.keyCode===Vm.ESC&&NR(t,e,I.none())}))},OR=rr,TR=(e,t)=>Oh(e.getBody(),t),BR=e=>{const t=e.selection,n=e.dom,o=e.getBody(),r=mc(e,o,n.isBlock,(()=>mg(e))),s="sel-"+n.uniqueId(),a="data-mce-selected";let i;const l=e=>e!==o&&(OR(e)||ir(e))&&n.isChildOf(e,o)&&n.isEditable(e.parentNode),d=(n,o,s,a=!0)=>e.dispatch("ShowCaret",{target:o,direction:n,before:s}).isDefaultPrevented()?null:(a&&t.scrollIntoView(o,-1===n),r.show(s,o)),c=e=>Fr(e)||Hr(e)||$r(e),u=e=>c(e.startContainer)||c(e.endContainer),m=t=>{const o=e.schema.getVoidElements(),r=n.createRng(),s=t.startContainer,a=t.startOffset,i=t.endContainer,l=t.endOffset;return ke(o,s.nodeName.toLowerCase())?0===a?r.setStartBefore(s):r.setStartAfter(s):r.setStart(s,a),ke(o,i.nodeName.toLowerCase())?0===l?r.setEndBefore(i):r.setEndAfter(i):r.setEnd(i,l),r},f=(r,c)=>{if(!r)return null;if(r.collapsed){if(!u(r)){const e=c?1:-1,t=Uc(e,o,r),s=t.getNode(!c);if(C(s)){if(pc(s))return d(e,s,!!c&&!t.isAtEnd(),!1);if(Ir(s)&&rr(s.nextSibling)){const e=n.createRng();return e.setStart(s,0),e.setEnd(s,0),e}}const a=t.getNode(c);if(C(a)){if(pc(a))return d(e,a,!c&&!t.isAtEnd(),!1);if(Ir(a)&&rr(a.previousSibling)){const e=n.createRng();return e.setStart(a,1),e.setEnd(a,1),e}}}return null}let m=r.startContainer,f=r.startOffset;const g=r.endOffset;if(Yo(m)&&0===f&&OR(m.parentNode)&&(m=m.parentNode,f=n.nodeIndex(m),m=m.parentNode),!zo(m))return null;if(g===f+1&&m===r.endContainer){const o=m.childNodes[f];if(l(o))return(o=>{const r=o.cloneNode(!0),l=e.dispatch("ObjectSelected",{target:o,targetClone:r});if(l.isDefaultPrevented())return null;const d=((o,r)=>{const a=bn(e.getBody()),i=e.getDoc(),l=Jn(a,"#"+s).getOrThunk((()=>{const e=gn('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>',i);return Xt(e,"id",s),po(a,e),e})),d=n.createRng();vo(l),bo(l,[hn(cr,i),bn(r),hn(cr,i)]),d.setStart(l.dom.firstChild,1),d.setEnd(l.dom.lastChild,0),so(l,{top:n.getPos(o,e.getBody()).y+"px"}),Vf(l);const c=t.getSel();return c&&(c.removeAllRanges(),c.addRange(d)),d})(o,l.targetClone),c=bn(o);return q(Lo(bn(e.getBody()),"*[data-mce-selected]"),(e=>{xn(c,e)||tn(e,a)})),n.getAttrib(o,a)||o.setAttribute(a,"1"),i=o,p(),d})(o)}return null},g=()=>{i&&i.removeAttribute(a),Jn(bn(e.getBody()),"#"+s).each(yo),i=null},p=()=>{r.hide()};return CC(e)||(e.on("click",(t=>{n.isEditable(t.target)||(t.preventDefault(),e.focus())})),e.on("blur NewBlock",g),e.on("ResizeWindow FullscreenStateChanged",r.reposition),e.on("tap",(t=>{const n=t.target,o=TR(e,n);OR(o)?(t.preventDefault(),Yw(e,o).each(f)):l(n)&&Yw(e,n).each(f)}),!0),e.on("mousedown",(r=>{const s=r.target;if(s!==o&&"HTML"!==s.nodeName&&!n.isChildOf(s,o))return;if(!((e,t,n)=>{const o=bn(e.getBody()),r=e.inline?o:bn(Sn(o).dom.documentElement),s=((e,t,n,o)=>{const r=(e=>e.dom.getBoundingClientRect())(t);return{x:n-(e?r.left+t.dom.clientLeft+ZC(t):0),y:o-(e?r.top+t.dom.clientTop+JC(t):0)}})(e.inline,r,t,n);return((e,t,n)=>{const o=XC(e),r=QC(e);return t>=0&&n>=0&&t<=o&&n<=r})(r,s.x,s.y)})(e,r.clientX,r.clientY))return;g(),p();const a=TR(e,s);OR(a)?(r.preventDefault(),Yw(e,a).each(f)):dR(o,r.clientX,r.clientY).each((n=>{var o;r.preventDefault(),(o=d(1,n.node,n.position===oR.Before,!1))&&t.setRng(o),zo(a)?a.focus():e.getBody().focus()}))})),e.on("keypress",(e=>{Vm.modifierPressed(e)||OR(t.getNode())&&e.preventDefault()})),e.on("GetSelectionRange",(e=>{let t=e.range;if(i){if(!i.parentNode)return void(i=null);t=t.cloneRange(),t.selectNode(i),e.range=t}})),e.on("SetSelectionRange",(e=>{e.range=m(e.range);const t=f(e.range,e.forward);t&&(e.range=t)})),e.on("AfterSetSelectionRange",(e=>{const t=e.range,o=t.startContainer.parentElement;var r;u(t)||zo(r=o)&&"mcepastebin"===r.id||p(),(e=>C(e)&&n.hasClass(e,"mce-offscreen-selection"))(o)||g()})),(e=>{AR(e),xd(e)&&(e=>{const t=t=>{if(!t.isDefaultPrevented()){const n=t.dataTransfer;n&&(H(n.types,"Files")||n.files.length>0)&&(t.preventDefault(),"drop"===t.type&&sw(e,"Dropped file type is not supported"))}},n=n=>{ag(e,n.target)&&t(n)},o=()=>{const o=_a.DOM,r=e.dom,s=document,a=e.inline?e.getBody():e.getDoc(),i=["drop","dragover"];q(i,(e=>{o.bind(s,e,n),r.bind(a,e,t)})),e.on("remove",(()=>{q(i,(e=>{o.unbind(s,e,n),r.unbind(a,e,t)}))}))};e.on("init",(()=>{ng.setEditorTimeout(e,o,0)}))})(e)})(e),(e=>{const t=Ia((()=>{if(!e.removed&&e.getBody().contains(document.activeElement)){const t=e.selection.getRng();if(t.collapsed){const n=Xw(e,t,!1);e.selection.setRng(n)}}}),0);e.on("focus",(()=>{t.throttle()})),e.on("blur",(()=>{t.cancel()}))})(e),(e=>{e.on("init",(()=>{e.on("focusin",(t=>{const n=t.target;if(ir(n)){const t=Oh(e.getBody(),n),o=rr(t)?t:n;e.selection.getNode()!==o&&Yw(e,o).each((t=>e.selection.setRng(t)))}}))}))})(e)),{showCaret:d,showBlockCaretContainer:e=>{e.hasAttribute("data-mce-caret")&&(Vr(e),t.scrollIntoView(e))},hideFakeCaret:p,destroy:()=>{r.destroy(),i=null}}},DR=(e,t)=>{let n=t;for(let t=e.previousSibling;Yo(t);t=t.previousSibling)n+=t.data.length;return n},PR=(e,t,n,o,r)=>{if(Yo(n)&&(o<0||o>n.data.length))return[];const s=r&&Yo(n)?[DR(n,o)]:[o];let a=n;for(;a!==t&&a.parentNode;)s.push(e.nodeIndex(a,r)),a=a.parentNode;return a===t?s.reverse():[]},LR=(e,t,n,o,r,s,a=!1)=>({start:PR(e,t,n,o,a),end:PR(e,t,r,s,a)}),MR=(e,t)=>{const n=t.slice(),o=n.pop();return x(o)?X(n,((e,t)=>e.bind((e=>I.from(e.childNodes[t])))),I.some(e)).bind((e=>Yo(e)&&(o<0||o>e.data.length)?I.none():I.some({node:e,offset:o}))):I.none()},IR=(e,t)=>MR(e,t.start).bind((({node:n,offset:o})=>MR(e,t.end).map((({node:e,offset:t})=>{const r=document.createRange();return r.setStart(n,o),r.setEnd(e,t),r})))),FR=(e,t,n)=>{if(t&&e.isEmpty(t)&&!n(t)){const o=t.parentNode;e.remove(t),FR(e,o,n)}},UR=(e,t,n,o=!0)=>{const r=t.startContainer.parentNode,s=t.endContainer.parentNode;t.deleteContents(),o&&!n(t.startContainer)&&(Yo(t.startContainer)&&0===t.startContainer.data.length&&e.remove(t.startContainer),Yo(t.endContainer)&&0===t.endContainer.data.length&&e.remove(t.endContainer),FR(e,r,n),r!==s&&FR(e,s,n))},zR=(e,t)=>I.from(e.dom.getParent(t.startContainer,e.dom.isBlock)),jR=(e,t,n)=>{const o=e.dynamicPatternsLookup({text:n,block:t});return{...e,blockPatterns:al(o).concat(e.blockPatterns),inlinePatterns:il(o).concat(e.inlinePatterns)}},HR=(e,t,n,o)=>{const r=e.createRng();return r.setStart(t,0),r.setEnd(n,o),r.toString()},$R=(e,t,n)=>{((e,t,n)=>{if(Yo(e)&&0>=e.length)return I.some(YE(e,0));{const t=ni(XE);return I.from(t.forwards(e,0,QE(e),n)).map((e=>YE(e.container,0)))}})(t,0,t).each((o=>{const r=o.container;eS(r,n.start.length,t).each((n=>{const o=e.createRng();o.setStart(r,0),o.setEnd(n.container,n.offset),UR(e,o,(e=>e===t))}));const s=bn(r),a=fr(s);/^\s[^\s]/.test(a)&&((e,t)=>{mr.set(e,t)})(s,a.slice(1))}))},VR=(e,t)=>e.create("span",{"data-mce-type":"bookmark",id:t}),qR=(e,t)=>{const n=e.createRng();return n.setStartAfter(t.start),n.setEndBefore(t.end),n},WR=(e,t,n)=>{const o=IR(e.getRoot(),n).getOrDie("Unable to resolve path range"),r=o.startContainer,s=o.endContainer,a=0===o.endOffset?s:s.splitText(o.endOffset),i=0===o.startOffset?r:r.splitText(o.startOffset),l=i.parentNode;return{prefix:t,end:a.parentNode.insertBefore(VR(e,t+"-end"),a),start:l.insertBefore(VR(e,t+"-start"),i)}},KR=(e,t,n)=>{FR(e,e.get(t.prefix+"-end"),n),FR(e,e.get(t.prefix+"-start"),n)},GR=e=>0===e.start.length,YR=(e,t,n,o)=>{const r=t.start;var s;return tS(e,o.container,o.offset,(s=r,(e,t)=>{const n=e.data.substring(0,t),o=n.lastIndexOf(s.charAt(s.length-1)),r=n.lastIndexOf(s);return-1!==r?r+s.length:-1!==o?o+1:-1}),n).bind((o=>{var s,a;const i=null!==(a=null===(s=n.textContent)||void 0===s?void 0:s.indexOf(r))&&void 0!==a?a:-1;if(-1!==i&&o.offset>=i+r.length){const t=e.createRng();return t.setStart(o.container,o.offset-r.length),t.setEnd(o.container,o.offset),I.some(t)}{const s=o.offset-r.length;return ZE(o.container,s,n).map((t=>{const n=e.createRng();return n.setStart(t.container,t.offset),n.setEnd(o.container,o.offset),n})).filter((e=>e.toString()===r)).orThunk((()=>YR(e,t,n,YE(o.container,0))))}}))},XR=(e,t,n,o)=>{const r=e.dom,s=r.getRoot(),a=n.pattern,i=n.position.container,l=n.position.offset;return ZE(i,l-n.pattern.end.length,t).bind((d=>{const c=LR(r,s,d.container,d.offset,i,l,o);if(GR(a))return I.some({matches:[{pattern:a,startRng:c,endRng:c}],position:d});{const i=QR(e,n.remainingPatterns,d.container,d.offset,t,o),l=i.getOr({matches:[],position:d}),u=l.position,m=((e,t,n,o,r,s=!1)=>{if(0===t.start.length&&!s){const t=e.createRng();return t.setStart(n,o),t.setEnd(n,o),I.some(t)}return JE(n,o,r).bind((n=>YR(e,t,r,n).bind((e=>{var t;if(s){if(e.endContainer===n.container&&e.endOffset===n.offset)return I.none();if(0===n.offset&&(null===(t=e.endContainer.textContent)||void 0===t?void 0:t.length)===e.endOffset)return I.none()}return I.some(e)}))))})(r,a,u.container,u.offset,t,i.isNone());return m.map((e=>{const t=((e,t,n,o=!1)=>LR(e,t,n.startContainer,n.startOffset,n.endContainer,n.endOffset,o))(r,s,e,o);return{matches:l.matches.concat([{pattern:a,startRng:t,endRng:c}]),position:YE(e.startContainer,e.startOffset)}}))}}))},QR=(e,t,n,o,r,s)=>{const a=e.dom;return JE(n,o,a.getRoot()).bind((i=>{const l=HR(a,r,n,o);for(let a=0;a<t.length;a++){const d=t[a];if(!$e(l,d.end))continue;const c=t.slice();c.splice(a,1);const u=XR(e,r,{pattern:d,remainingPatterns:c,position:i},s);if(u.isNone()&&o>0)return QR(e,t,n,o-1,r,s);if(u.isSome())return u}return I.none()}))},JR=(e,t,n)=>{e.selection.setRng(n),"inline-format"===t.type?q(t.format,(t=>{e.formatter.apply(t)})):e.execCommand(t.cmd,!1,t.value)},ZR=(e,t,n,o,r,s)=>{var a;return((e,t)=>{const n=ne(e,(e=>$(t,(t=>e.pattern.start===t.pattern.start&&e.pattern.end===t.pattern.end))));return e.length===t.length?n?e:t:e.length>t.length?e:t})(QR(e,r.inlinePatterns,n,o,t,s).fold((()=>[]),(e=>e.matches)),QR(e,(a=r.inlinePatterns,ae(a,((e,t)=>t.end.length-e.end.length))),n,o,t,s).fold((()=>[]),(e=>e.matches)))},eA=(e,t)=>{if(0===t.length)return;const n=e.dom,o=e.selection.getBookmark(),r=((e,t)=>{const n=Qa("mce_textpattern"),o=Y(t,((t,o)=>{const r=WR(e,n+`_end${t.length}`,o.endRng);return t.concat([{...o,endMarker:r}])}),[]);return Y(o,((t,r)=>{const s=o.length-t.length-1,a=GR(r.pattern)?r.endMarker:WR(e,n+`_start${s}`,r.startRng);return t.concat([{...r,startMarker:a}])}),[])})(n,t);q(r,(t=>{const o=n.getParent(t.startMarker.start,n.isBlock),r=e=>e===o;GR(t.pattern)?((e,t,n,o)=>{const r=qR(e.dom,n);UR(e.dom,r,o),JR(e,t,r)})(e,t.pattern,t.endMarker,r):((e,t,n,o,r)=>{const s=e.dom,a=qR(s,o),i=qR(s,n);UR(s,i,r),UR(s,a,r);const l={prefix:n.prefix,start:n.end,end:o.start},d=qR(s,l);JR(e,t,d)})(e,t.pattern,t.startMarker,t.endMarker,r),KR(n,t.endMarker,r),KR(n,t.startMarker,r)})),e.selection.moveToBookmark(o)},tA=(e,t)=>{const n=e.selection.getRng();return zR(e,n).map((o=>{var r;const s=Math.max(0,n.startOffset),a=jR(t,o,null!==(r=o.textContent)&&void 0!==r?r:""),i=ZR(e,o,n.startContainer,s,a,!0),l=((e,t,n,o)=>{var r;const s=e.dom,a=kl(e);if(!s.is(t,a))return[];const i=null!==(r=t.textContent)&&void 0!==r?r:"";return((e,t)=>{const n=(e=>ae(e,((e,t)=>t.start.length-e.start.length)))(e),o=t.replace(cr," ");return J(n,(e=>0===t.indexOf(e.start)||0===o.indexOf(e.start)))})(n.blockPatterns,i).map((e=>Dt.trim(i).length===e.start.length?[]:[{pattern:e,range:LR(s,s.getRoot(),t,0,t,0,true)}])).getOr([])})(e,o,a);return(l.length>0||i.length>0)&&(e.undoManager.add(),e.undoManager.extra((()=>{e.execCommand("mceInsertNewLine")}),(()=>{e.insertContent(dr),eA(e,i),((e,t)=>{if(0===t.length)return;const n=e.selection.getBookmark();q(t,(t=>((e,t)=>{const n=e.dom,o=t.pattern,r=IR(n.getRoot(),t.range).getOrDie("Unable to resolve path range");return zR(e,r).each((t=>{"block-format"===o.type?((e,t)=>{const n=t.get(e);return p(n)&&le(n).exists((e=>ke(e,"block")))})(o.format,e.formatter)&&e.undoManager.transact((()=>{$R(e.dom,t,o),e.formatter.apply(o.format)})):"block-command"===o.type&&e.undoManager.transact((()=>{$R(e.dom,t,o),e.execCommand(o.cmd,!1,o.value)}))})),!0})(e,t))),e.selection.moveToBookmark(n)})(e,l);const t=e.selection.getRng(),n=JE(t.startContainer,t.startOffset,e.dom.getRoot());e.execCommand("mceInsertNewLine"),n.each((t=>{const n=t.container;n.data.charAt(t.offset-1)===dr&&(n.deleteData(t.offset-1,1),FR(e.dom,n.parentNode,(t=>t===e.dom.getRoot())))}))})),!0)})).getOr(!1)},nA=(e,t,n)=>{for(let o=0;o<e.length;o++)if(n(e[o],t))return!0;return!1},oA=e=>{const t=Dt.each,n=Vm.BACKSPACE,o=Vm.DELETE,r=e.dom,s=e.selection,a=e.parser,i=At.browser,l=i.isFirefox(),d=i.isChromium()||i.isSafari(),c=At.deviceType.isiPhone()||At.deviceType.isiPad(),u=At.os.isMacOS()||At.os.isiOS(),m=(t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},f=e=>e.isDefaultPrevented(),g=()=>{e.shortcuts.add("meta+a",null,"SelectAll")},p=()=>{e.inline||r.bind(e.getDoc(),"mousedown mouseup",(t=>{let n;if(t.target===e.getDoc().documentElement)if(n=s.getRng(),e.getBody().focus(),"mousedown"===t.type){if(Fr(n.startContainer))return;s.placeCaretAt(t.clientX,t.clientY)}else s.setRng(n)}))},h=()=>{Range.prototype.getClientRects||e.on("mousedown",(t=>{if(!f(t)&&"HTML"===t.target.nodeName){const t=e.getBody();t.blur(),ng.setEditorTimeout(e,(()=>{t.focus()}))}}))},b=()=>{const t=Sd(e);e.on("click",(n=>{const o=n.target;/^(IMG|HR)$/.test(o.nodeName)&&r.isEditable(o.parentNode)&&(n.preventDefault(),e.selection.select(o),e.nodeChanged()),"A"===o.nodeName&&r.hasClass(o,t)&&0===o.childNodes.length&&r.isEditable(o.parentNode)&&(n.preventDefault(),s.select(o))}))},v=()=>{e.on("keydown",(e=>{if(!f(e)&&e.keyCode===n&&s.isCollapsed()&&0===s.getRng().startOffset){const t=s.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}return!0}))},y=()=>{vd(e)||e.on("BeforeExecCommand mousedown",(()=>{m("StyleWithCSS",!1),m("enableInlineTableEditing",!1),Xl(e)||m("enableObjectResizing",!1)}))},C=()=>{e.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}")},w=()=>{e.inline||e.on("keydown",(()=>{document.activeElement===document.body&&e.getWin().focus()}))},x=()=>{e.inline||(e.contentStyles.push("body {min-height: 150px}"),e.on("click",(t=>{let n;"HTML"===t.target.nodeName&&(n=e.selection.getRng(),e.getBody().focus(),e.selection.setRng(n),e.selection.normalize(),e.nodeChanged())})))},k=()=>{u&&e.on("keydown",(t=>{!Vm.metaKeyPressed(t)||t.shiftKey||37!==t.keyCode&&39!==t.keyCode||(t.preventDefault(),e.selection.getSel().modify("move",37===t.keyCode?"backward":"forward","lineboundary"))}))},S=()=>{e.on("click",(e=>{let t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)})),e.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")},_=()=>{e.on("init",(()=>{e.dom.bind(e.getBody(),"submit",(e=>{e.preventDefault()}))}))},N=E;return CC(e)?(d&&(p(),b(),_(),g(),c&&(w(),x(),S())),l&&(h(),y(),C(),k())):(e.on("keydown",(t=>{if(f(t)||t.keyCode!==Vm.BACKSPACE)return;let n=s.getRng();const o=n.startContainer,a=n.startOffset,i=r.getRoot();let l=o;if(n.collapsed&&0===a){for(;l.parentNode&&l.parentNode.firstChild===l&&l.parentNode!==i;)l=l.parentNode;"BLOCKQUOTE"===l.nodeName&&(e.formatter.toggle("blockquote",void 0,l),n=r.createRng(),n.setStart(o,0),n.setEnd(o,0),s.setRng(n))}})),(()=>{const t=e=>{const t=r.create("body"),n=e.cloneContents();return t.appendChild(n),s.serializer.serialize(t,{format:"html"})};e.on("keydown",(s=>{const a=s.keyCode;if(!f(s)&&(a===o||a===n)&&e.selection.isEditable()){const n=e.selection.isCollapsed(),o=e.getBody();if(n&&!r.isEmpty(o))return;if(!n&&!(n=>{const o=t(n),s=r.createRng();return s.selectNode(e.getBody()),o===t(s)})(e.selection.getRng()))return;s.preventDefault(),e.setContent(""),o.firstChild&&r.isBlock(o.firstChild)?e.selection.setCursorLocation(o.firstChild,0):e.selection.setCursorLocation(o,0),e.nodeChanged()}}))})(),At.windowsPhone||e.on("keyup focusin mouseup",(t=>{Vm.modifierPressed(t)||(e=>{const t=e.getBody(),n=e.selection.getRng();return n.startContainer===n.endContainer&&n.startContainer===t&&0===n.startOffset&&n.endOffset===t.childNodes.length})(e)||s.normalize()}),!0),d&&(p(),b(),e.on("init",(()=>{m("DefaultParagraphSeparator",kl(e))})),_(),v(),a.addNodeFilter("br",(e=>{let t=e.length;for(;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()})),c?(w(),x(),S()):g()),l&&(e.on("keydown",(t=>{if(!f(t)&&t.keyCode===n){if(!e.getBody().getElementsByTagName("hr").length)return;if(s.isCollapsed()&&0===s.getRng().startOffset){const e=s.getNode(),n=e.previousSibling;if("HR"===e.nodeName)return r.remove(e),void t.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(r.remove(n),t.preventDefault())}}})),h(),(()=>{const n=()=>{const n=r.getAttribs(s.getStart().cloneNode(!1));return()=>{const o=s.getStart();o!==e.getBody()&&(r.setAttrib(o,"style",null),t(n,(e=>{o.setAttributeNode(e.cloneNode(!0))})))}},o=()=>!s.isCollapsed()&&r.getParent(s.getStart(),r.isBlock)!==r.getParent(s.getEnd(),r.isBlock);e.on("keypress",(t=>{let r;return!(!(f(t)||8!==t.keyCode&&46!==t.keyCode)&&o()&&(r=n(),e.getDoc().execCommand("delete",!1),r(),t.preventDefault(),1))})),r.bind(e.getDoc(),"cut",(t=>{if(!f(t)&&o()){const t=n();ng.setEditorTimeout(e,(()=>{t()}))}}))})(),y(),e.on("SetContent ExecCommand",(e=>{"setcontent"!==e.type&&"mceInsertLink"!==e.command||t(r.select("a:not([data-mce-block])"),(e=>{var t;let n=e.parentNode;const o=r.getRoot();if((null==n?void 0:n.lastChild)===e){for(;n&&!r.isBlock(n);){if((null===(t=n.parentNode)||void 0===t?void 0:t.lastChild)!==n||n===o)return;n=n.parentNode}r.add(n,"br",{"data-mce-bogus":1})}}))})),C(),k(),v())),{refreshContentEditable:N,isHidden:()=>{if(!l||e.removed)return!1;const t=e.selection.getSel();return!t||!t.rangeCount||0===t.rangeCount}}},rA=_a.DOM,sA=e=>e.inline?e.getElement().nodeName.toLowerCase():void 0,aA=e=>ye(e,(e=>!1===v(e))),iA=e=>{const t=e.options.get,n=e.editorUpload.blobCache;return aA({allow_conditional_comments:t("allow_conditional_comments"),allow_html_data_urls:t("allow_html_data_urls"),allow_svg_data_urls:t("allow_svg_data_urls"),allow_html_in_named_anchor:t("allow_html_in_named_anchor"),allow_script_urls:t("allow_script_urls"),allow_unsafe_link_target:t("allow_unsafe_link_target"),convert_fonts_to_spans:t("convert_fonts_to_spans"),fix_list_elements:t("fix_list_elements"),font_size_legacy_values:t("font_size_legacy_values"),forced_root_block:t("forced_root_block"),forced_root_block_attrs:t("forced_root_block_attrs"),preserve_cdata:t("preserve_cdata"),remove_trailing_brs:t("remove_trailing_brs"),inline_styles:t("inline_styles"),root_name:sA(e),sanitize:t("xss_sanitization"),validate:!0,blob_cache:n,document:e.getDoc()})},lA=e=>{const t=e.options.get;return aA({custom_elements:t("custom_elements"),extended_valid_elements:t("extended_valid_elements"),invalid_elements:t("invalid_elements"),invalid_styles:t("invalid_styles"),schema:t("schema"),valid_children:t("valid_children"),valid_classes:t("valid_classes"),valid_elements:t("valid_elements"),valid_styles:t("valid_styles"),verify_html:t("verify_html"),padd_empty_block_inline_children:t("format_empty_lines")})},dA=e=>e.inline?e.ui.styleSheetLoader:e.dom.styleSheetLoader,cA=e=>{const t=dA(e),n=Kl(e),o=e.contentCSS,r=()=>{t.unloadAll(o),e.inline||e.ui.styleSheetLoader.unloadAll(n)},s=()=>{e.removed?r():e.on("remove",r)};if(e.contentStyles.length>0){let t="";Dt.each(e.contentStyles,(e=>{t+=e+"\r\n"})),e.dom.addStyle(t)}const a=Promise.all(((e,t,n)=>{const o=[dA(e).loadAll(t)];return e.inline?o:o.concat([e.ui.styleSheetLoader.loadAll(n)])})(e,o,n)).then(s).catch(s),i=Wl(e);return i&&((e,t)=>{const n=bn(e.getBody()),o=$n(Hn(n)),r=pn("style");Xt(r,"type","text/css"),po(r,hn(t)),po(o,r),e.on("remove",(()=>{yo(r)}))})(e,i),a},uA=e=>{!0!==e.removed&&((e=>{CC(e)||e.load({initial:!0,format:"html"}),e.startContent=e.getContent({format:"raw"})})(e),(e=>{e.bindPendingEventDelegates(),e.initialized=!0,(e=>{e.dispatch("Init")})(e),e.focus(!0),(e=>{const t=e.dom.getRoot();e.inline||qu(e)&&e.selection.getStart(!0)!==t||cu(t).each((t=>{const n=t.getNode(),o=Wo(n)?cu(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e),e.nodeChanged({initial:!0});const t=Rd(e);w(t)&&t.call(e,e),(e=>{const t=Od(e);t&&ng.setEditorTimeout(e,(()=>{let n;n=!0===t?e:e.editorManager.get(t),n&&!n.destroyed&&(n.focus(),n.selection.scrollIntoView())}),100)})(e)})(e))},mA=e=>{const t=e.getElement();let n=e.getDoc();e.inline&&(rA.addClass(t,"mce-content-body"),e.contentDocument=n=document,e.contentWindow=window,e.bodyElement=t,e.contentAreaContainer=t);const o=e.getBody();o.disabled=!0,e.readonly=vd(e),e.readonly||(e.inline&&"static"===rA.getStyle(o,"position",!0)&&(o.style.position="relative"),o.contentEditable="true"),o.disabled=!1,e.editorUpload=hw(e),e.schema=aa(lA(e)),e.dom=_a(n,{keep_values:!0,url_converter:e.convertURL,url_converter_scope:e,update_styles:!0,root_element:e.inline?e.getBody():null,collect:e.inline,schema:e.schema,contentCssCors:Ul(e),referrerPolicy:zl(e),onSetAttrib:t=>{e.dispatch("SetAttrib",t)}}),e.parser=(e=>{const t=jy(iA(e),e.schema);return t.addAttributeFilter("src,href,style,tabindex",((t,n)=>{const o=e.dom,r="data-mce-"+n;let s=t.length;for(;s--;){const a=t[s];let i=a.attr(n);if(i&&!a.attr(r)){if(0===i.indexOf("data:")||0===i.indexOf("blob:"))continue;"style"===n?(i=o.serializeStyle(o.parseStyle(i),a.name),i.length||(i=null),a.attr(r,i),a.attr(n,i)):"tabindex"===n?(a.attr(r,i),a.attr(n,null)):a.attr(r,e.convertURL(i,n,a.name))}}})),t.addNodeFilter("script",(e=>{let t=e.length;for(;t--;){const n=e[t],o=n.attr("type")||"no/type";0!==o.indexOf("mce-")&&n.attr("type","mce-"+o)}})),Yd(e)&&t.addNodeFilter("#cdata",(t=>{var n;let o=t.length;for(;o--;){const r=t[o];r.type=8,r.name="#comment",r.value="[CDATA["+e.dom.encode(null!==(n=r.value)&&void 0!==n?n:"")+"]]"}})),t.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",(t=>{let n=t.length;const o=e.schema.getNonEmptyElements();for(;n--;){const e=t[n];e.isEmpty(o)&&0===e.getAll("br").length&&e.append(new Rg("br",1))}})),t})(e),e.serializer=TC((e=>{const t=e.options.get;return{...iA(e),...lA(e),...aA({url_converter:t("url_converter"),url_converter_scope:t("url_converter_scope"),element_format:t("element_format"),entities:t("entities"),entity_encoding:t("entity_encoding"),indent:t("indent"),indent_after:t("indent_after"),indent_before:t("indent_before")})}})(e),e),e.selection=RC(e.dom,e.getWin(),e.serializer,e),e.annotator=Lm(e),e.formatter=Nw(e),e.undoManager=Aw(e),e._nodeChangeDispatcher=new gN(e),e._selectionOverrides=BR(e),(e=>{const t=Ma(),n=Aa(!1),o=Fa((t=>{e.dispatch("longpress",{...t,type:"longpress"}),n.set(!0)}),400);e.on("touchstart",(e=>{Zk(e).each((r=>{o.cancel();const s={x:r.clientX,y:r.clientY,target:e.target};o.throttle(e),n.set(!1),t.set(s)}))}),!0),e.on("touchmove",(r=>{o.cancel(),Zk(r).each((o=>{t.on((r=>{((e,t)=>{const n=Math.abs(e.clientX-t.x),o=Math.abs(e.clientY-t.y);return n>5||o>5})(o,r)&&(t.clear(),n.set(!1),e.dispatch("longpresscancel"))}))}))}),!0),e.on("touchend touchcancel",(r=>{o.cancel(),"touchcancel"!==r.type&&t.get().filter((e=>e.target.isEqualNode(r.target))).each((()=>{n.get()?r.preventDefault():e.dispatch("tap",{...r,type:"tap"})}))}),!0)})(e),(e=>{(e=>{e.on("click",(t=>{e.dom.getParent(t.target,"details")&&t.preventDefault()}))})(e),(e=>{e.parser.addNodeFilter("details",(e=>{q(e,(e=>{e.attr("data-mce-open",e.attr("open")),e.attr("open","open")}))})),e.serializer.addNodeFilter("details",(e=>{q(e,(e=>{const t=e.attr("data-mce-open");e.attr("open",m(t)?t:null),e.attr("data-mce-open",null)}))}))})(e)})(e),(e=>{const t="contenteditable",n=" "+Dt.trim(Kd(e))+" ",o=" "+Dt.trim(Wd(e))+" ",r=aE(n),s=aE(o),a=Gd(e);a.length>0&&e.on("BeforeSetContent",(t=>{((e,t,n)=>{let o=t.length,r=n.content;if("raw"!==n.format){for(;o--;)r=r.replace(t[o],iE(e,r,Wd(e)));n.content=r}})(e,a,t)})),e.parser.addAttributeFilter("class",(e=>{let n=e.length;for(;n--;){const o=e[n];r(o)?o.attr(t,"true"):s(o)&&o.attr(t,"false")}})),e.serializer.addAttributeFilter(t,(e=>{let n=e.length;for(;n--;){const o=e[n];(r(o)||s(o))&&(a.length>0&&o.attr("data-mce-content")?(o.name="#text",o.type=3,o.raw=!0,o.value=o.attr("data-mce-content")):o.attr(t,null))}}))})(e),CC(e)||((e=>{e.on("mousedown",(t=>{t.detail>=3&&(t.preventDefault(),nR(e))}))})(e),(e=>{(e=>{const t=[",",".",";",":","!","?"],n=[32],o=()=>{return t=Vd(e),n=qd(e),{inlinePatterns:il(t),blockPatterns:al(t),dynamicPatternsLookup:n};var t,n},r=()=>(e=>e.options.isSet("text_patterns_lookup"))(e);e.on("keydown",(t=>{if(13===t.keyCode&&!Vm.modifierPressed(t)&&e.selection.isCollapsed()){const n=o();(n.inlinePatterns.length>0||n.blockPatterns.length>0||r())&&tA(e,n)&&t.preventDefault()}}),!0);const s=()=>{if(e.selection.isCollapsed()){const t=o();(t.inlinePatterns.length>0||r())&&((e,t)=>{const n=e.selection.getRng();zR(e,n).map((o=>{const r=Math.max(0,n.startOffset-1),s=HR(e.dom,o,n.startContainer,r),a=jR(t,o,s),i=ZR(e,o,n.startContainer,r,a,!1);i.length>0&&e.undoManager.transact((()=>{eA(e,i)}))}))})(e,t)}};e.on("keyup",(e=>{nA(n,e,((e,t)=>e===t.keyCode&&!Vm.modifierPressed(t)))&&s()})),e.on("keypress",(n=>{nA(t,n,((e,t)=>e.charCodeAt(0)===t.charCode))&&ng.setEditorTimeout(e,s)}))})(e)})(e));const r=fN(e);Jk(e,r),(e=>{e.on("NodeChange",O(rE,e))})(e),(e=>{var t;const n=e.dom,o=kl(e),r=null!==(t=Jl(e))&&void 0!==t?t:"",s=(t,a)=>{if((e=>{if(Bw(e)){const t=e.keyCode;return!Dw(e)&&(Vm.metaKeyPressed(e)||e.altKey||t>=112&&t<=123||H(Ow,t))}return!1})(t))return;const i=e.getBody(),l=!(e=>Bw(e)&&!(Dw(e)||"keyup"===e.type&&229===e.keyCode))(t)&&((e,t,n)=>{if(us(bn(t),!1)){const o=t.firstElementChild;return!o||!e.getStyle(t.firstElementChild,"padding-left")&&!e.getStyle(t.firstElementChild,"padding-right")&&n===o.nodeName.toLowerCase()}return!1})(n,i,o);(""!==n.getAttrib(i,Tw)!==l||a)&&(n.setAttrib(i,Tw,l?r:null),n.setAttrib(i,"aria-placeholder",l?r:null),((e,t)=>{e.dispatch("PlaceholderToggle",{state:t})})(e,l),e.on(l?"keydown":"keyup",s),e.off(l?"keyup":"keydown",s))};Ge(r)&&e.on("init",(t=>{s(t,!0),e.on("change SetContent ExecCommand",s),e.on("paste",(t=>ng.setEditorTimeout(e,(()=>s(t)))))}))})(e),YN(e);const s=(e=>{const t=e;return(e=>xe(e.plugins,"rtc").bind((e=>I.from(e.setup))))(e).fold((()=>(t.rtcInstance=yC(e),I.none())),(e=>(t.rtcInstance=(()=>{const e=N(null),t=N("");return{init:{bindEvents:E},undoManager:{beforeChange:E,add:e,undo:e,redo:e,clear:E,reset:E,hasUndo:L,hasRedo:L,transact:e,ignore:E,extra:E},formatter:{match:L,matchAll:N([]),matchNode:N(void 0),canApply:L,closest:t,apply:E,remove:E,toggle:E,formatChanged:N({unbind:E})},editor:{getContent:t,setContent:N({content:"",html:""}),insertContent:N(""),addVisual:E},selection:{getContent:t},autocompleter:{addDecoration:E,removeDecoration:E},raw:{getModel:N(I.none())}}})(),I.some((()=>e().then((e=>(t.rtcInstance=(e=>{const t=e=>f(e)?e:{},{init:n,undoManager:o,formatter:r,editor:s,selection:a,autocompleter:i,raw:l}=e;return{init:{bindEvents:n.bindEvents},undoManager:{beforeChange:o.beforeChange,add:o.add,undo:o.undo,redo:o.redo,clear:o.clear,reset:o.reset,hasUndo:o.hasUndo,hasRedo:o.hasRedo,transact:(e,t,n)=>o.transact(n),ignore:(e,t)=>o.ignore(t),extra:(e,t,n,r)=>o.extra(n,r)},formatter:{match:(e,n,o,s)=>r.match(e,t(n),s),matchAll:r.matchAll,matchNode:r.matchNode,canApply:e=>r.canApply(e),closest:e=>r.closest(e),apply:(e,n,o)=>r.apply(e,t(n)),remove:(e,n,o,s)=>r.remove(e,t(n)),toggle:(e,n,o)=>r.toggle(e,t(n)),formatChanged:(e,t,n,o,s)=>r.formatChanged(t,n,o,s)},editor:{getContent:e=>s.getContent(e),setContent:(e,t)=>({content:s.setContent(e,t),html:""}),insertContent:(e,t)=>(s.insertContent(e),""),addVisual:s.addVisual},selection:{getContent:(e,t)=>a.getContent(t)},autocompleter:{addDecoration:i.addDecoration,removeDecoration:i.removeDecoration},raw:{getModel:()=>I.some(l.getRawModel())}}})(e),e.rtc.isRemote))))))))})(e);(e=>{const t=e.getDoc(),n=e.getBody();(e=>{e.dispatch("PreInit")})(e),Td(e)||(t.body.spellcheck=!1,rA.setAttrib(n,"spellcheck","false")),e.quirks=oA(e),(e=>{e.dispatch("PostRender")})(e);const o=Gl(e);void 0!==o&&(n.dir=o);const r=Bd(e);r&&e.on("BeforeSetContent",(e=>{Dt.each(r,(t=>{e.content=e.content.replace(t,(e=>"\x3c!--mce:protected "+escape(e)+"--\x3e"))}))})),e.on("SetContent",(()=>{e.addVisual(e.getBody())})),e.on("compositionstart compositionend",(t=>{e.composing="compositionstart"===t.type}))})(e),s.fold((()=>{cA(e).then((()=>uA(e)))}),(t=>{e.setProgressState(!0),cA(e).then((()=>{t().then((t=>{e.setProgressState(!1),uA(e),kC(e)}),(t=>{e.notificationManager.open({type:"error",text:String(t)}),uA(e),kC(e)}))}))}))},fA=M,gA=_a.DOM,pA=_a.DOM,hA=(e,t)=>({editorContainer:e,iframeContainer:t,api:{}}),bA=e=>{const t=e.getElement();return e.inline?hA(null):(e=>{const t=pA.create("div");return pA.insertAfter(t,e),hA(t,t)})(t)},vA=async e=>{e.dispatch("ScriptsLoaded"),(e=>{const t=Dt.trim(Dl(e)),n=e.ui.registry.getAll().icons,o={...WC.get("default").icons,...WC.get(t).icons};ge(o,((t,o)=>{ke(n,o)||e.ui.registry.addIcon(o,t)}))})(e),(e=>{const t=td(e);if(m(t)){const n=nw.get(t);e.theme=n(e,nw.urls[t])||{},w(e.theme.init)&&e.theme.init(e,nw.urls[t]||e.documentBaseUrl.replace(/\/$/,""))}else e.theme={}})(e),(e=>{const t=od(e),n=KC.get(t);e.model=n(e,KC.urls[t])})(e),(e=>{const t=[];q(Cd(e),(n=>{((e,t,n)=>{const o=tw.get(n),r=tw.urls[n]||e.documentBaseUrl.replace(/\/$/,"");if(n=Dt.trim(n),o&&-1===Dt.inArray(t,n)){if(e.plugins[n])return;try{const s=o(e,r)||{};e.plugins[n]=s,w(s.init)&&(s.init(e,r),t.push(n))}catch(t){((e,t,n)=>{const o=Da.translate(["Failed to initialize plugin: {0}",t]);Fm(e,"PluginLoadError",{message:o}),lw(o,n),sw(e,o)})(e,n,t)}}})(e,t,(e=>e.replace(/^\-/,""))(n))}))})(e);const t=await(e=>{const t=e.getElement();return e.orgDisplay=t.style.display,m(td(e))?(e=>{const t=e.theme.renderUI;return t?t():bA(e)})(e):w(td(e))?(e=>{const t=e.getElement(),n=td(e)(e,t);return n.editorContainer.nodeType&&(n.editorContainer.id=n.editorContainer.id||e.id+"_parent"),n.iframeContainer&&n.iframeContainer.nodeType&&(n.iframeContainer.id=n.iframeContainer.id||e.id+"_iframecontainer"),n.height=n.iframeHeight?n.iframeHeight:t.offsetHeight,n})(e):bA(e)})(e);((e,t)=>{const n={show:I.from(t.show).getOr(E),hide:I.from(t.hide).getOr(E),isEnabled:I.from(t.isEnabled).getOr(M),setEnabled:n=>{e.mode.isReadOnly()||I.from(t.setEnabled).each((e=>e(n)))}};e.ui={...e.ui,...n}})(e,I.from(t.api).getOr({})),e.editorContainer=t.editorContainer,(e=>{e.contentCSS=e.contentCSS.concat((e=>dw(e,ql(e)))(e),(e=>dw(e,Kl(e)))(e))})(e),e.inline?mA(e):((e,t)=>{((e,t)=>{const n=e.translate("Rich Text Area"),o=Zt(bn(e.getElement()),"tabindex").bind(Xe),r=((e,t,n,o)=>{const r=pn("iframe");return o.each((e=>Xt(r,"tabindex",e))),Qt(r,n),Qt(r,{id:e+"_ifr",frameBorder:"0",allowTransparency:"true",title:t}),dn(r,"tox-edit-area__iframe"),r})(e.id,n,hl(e),o).dom;r.onload=()=>{r.onload=null,e.dispatch("load")},e.contentAreaContainer=t.iframeContainer,e.iframeElement=r,e.iframeHTML=(e=>{let t=bl(e)+"<html><head>";vl(e)!==e.documentBaseUrl&&(t+='<base href="'+e.documentBaseURI.getURI()+'" />'),t+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />';const n=yl(e),o=Cl(e),r=e.translate(_d(e));return wl(e)&&(t+='<meta http-equiv="Content-Security-Policy" content="'+wl(e)+'" />'),t+=`</head><body id="${n}" class="mce-content-body ${o}" data-id="${e.id}" aria-label="${r}"><br></body></html>`,t})(e),gA.add(t.iframeContainer,r)})(e,t),t.editorContainer&&(t.editorContainer.style.display=e.orgDisplay,e.hidden=gA.isHidden(t.editorContainer)),e.getElement().style.display="none",gA.setAttrib(e.id,"aria-hidden","true"),e.getElement().style.visibility=e.orgVisibility,(e=>{const t=e.iframeElement,n=(o=bn(t),Eo(o,"load",fA,(()=>{n.unbind(),e.contentDocument=t.contentDocument,mA(e)})));var o;if(At.browser.isFirefox()){const t=e.getDoc();t.open(),t.write(e.iframeHTML),t.close()}else t.srcdoc=e.iframeHTML})(e)})(e,{editorContainer:t.editorContainer,iframeContainer:t.iframeContainer})},yA=_a.DOM,CA=e=>"-"===e.charAt(0),wA=(e,t,n)=>I.from(t).filter((e=>Ge(e)&&!WC.has(e))).map((t=>({url:`${e.editorManager.baseURL}/icons/${t}/icons${n}.js`,name:I.some(t)}))),xA=(e,t)=>{const n=Ra.ScriptLoader,o=()=>{!e.removed&&(e=>{const t=td(e);return!m(t)||C(nw.get(t))})(e)&&(e=>{const t=od(e);return C(KC.get(t))})(e)&&vA(e)};((e,t)=>{const n=td(e);if(m(n)&&!CA(n)&&!ke(nw.urls,n)){const o=nd(e),r=o?e.documentBaseURI.toAbsolute(o):`themes/${n}/theme${t}.js`;nw.load(n,r).catch((()=>{((e,t,n)=>{aw(e,"ThemeLoadError",iw("theme",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=od(e);if("plugin"!==n&&!ke(KC.urls,n)){const o=rd(e),r=m(o)?e.documentBaseURI.toAbsolute(o):`models/${n}/model${t}.js`;KC.load(n,r).catch((()=>{((e,t,n)=>{aw(e,"ModelLoadError",iw("model",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=jl(t),o=Hl(t);if(!Da.hasCode(n)&&"en"!==n){const r=Ge(o)?o:`${t.editorManager.baseURL}/langs/${n}.js`;e.add(r).catch((()=>{((e,t,n)=>{aw(e,"LanguageLoadError",iw("language",t,n))})(t,r,n)}))}})(n,e),((e,t,n)=>{const o=wA(t,"default",n),r=(e=>I.from(Pl(e)).filter(Ge).map((e=>({url:e,name:I.none()}))))(t).orThunk((()=>wA(t,Dl(t),"")));q((e=>{const t=[],n=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(n);return t})([o,r]),(n=>{e.add(n.url).catch((()=>{((e,t,n)=>{aw(e,"IconsLoadError",iw("icons",t,n))})(t,n.url,n.name.getOrUndefined())}))}))})(n,e,t),((e,t)=>{const n=(t,n)=>{tw.load(t,n).catch((()=>{((e,t,n)=>{aw(e,"PluginLoadError",iw("plugin",t,n))})(e,n,t)}))};ge(wd(e),((t,o)=>{n(o,t),e.options.set("plugins",Cd(e).concat(o))})),q(Cd(e),(e=>{!(e=Dt.trim(e))||tw.urls[e]||CA(e)||n(e,`plugins/${e}/plugin${t}.js`)}))})(e,t),n.loadQueue().then(o,o)},kA=xt().deviceType,EA=kA.isPhone(),SA=kA.isTablet(),_A=e=>{if(y(e))return[];{const t=p(e)?e:e.split(/[ ,]/),n=V(t,qe);return G(n,Ge)}},NA=(e,t)=>{const n=((t,n)=>{const o={},r={};return ve(t,((t,n)=>H(e,n)),be(o),be(r)),{t:o,f:r}})(t);return o=n.t,r=n.f,{sections:N(o),options:N(r)};var o,r},RA=(e,t)=>ke(e.sections(),t),AA=(e,t)=>({table_grid:!1,object_resizing:!1,resize:!1,toolbar_mode:xe(e,"toolbar_mode").getOr("scrolling"),toolbar_sticky:!1,...t?{menubar:!1}:{}}),OA=(e,t)=>{var n;const o=null!==(n=t.external_plugins)&&void 0!==n?n:{};return e&&e.external_plugins?Dt.extend({},e.external_plugins,o):o},TA=(e,t,n,o,r)=>{var s;const a=e?{mobile:AA(null!==(s=r.mobile)&&void 0!==s?s:{},t)}:{},i=NA(["mobile"],vS(a,r)),l=Dt.extend(n,o,i.options(),((e,t)=>e&&RA(t,"mobile"))(e,i)?((e,t,n={})=>{const o=e.sections(),r=xe(o,t).getOr({});return Dt.extend({},n,r)})(i,"mobile"):{},{external_plugins:OA(o,i.options())});return((e,t,n,o)=>{const r=_A(n.forced_plugins),s=_A(o.plugins),a=((e,t)=>RA(e,t)?e.sections()[t]:{})(t,"mobile"),i=((e,t,n,o)=>e&&RA(t,"mobile")?o:n)(e,t,s,a.plugins?_A(a.plugins):s),l=((e,t)=>[..._A(e),..._A(t)])(r,i);return Dt.extend(o,{forced_plugins:r,plugins:l})})(e,i,o,l)},BA=e=>{(e=>{const t=t=>()=>{q("left,center,right,justify".split(","),(n=>{t!==n&&e.formatter.remove("align"+n)})),"none"!==t&&((t,n)=>{e.formatter.toggle(t,void 0),e.nodeChanged()})("align"+t)};e.editorCommands.addCommands({JustifyLeft:t("left"),JustifyCenter:t("center"),JustifyRight:t("right"),JustifyFull:t("justify"),JustifyNone:t("none")})})(e),(e=>{const t=t=>()=>{const n=e.selection,o=n.isCollapsed()?[e.dom.getParent(n.getNode(),e.dom.isBlock)]:n.getSelectedBlocks();return $(o,(n=>C(e.formatter.matchNode(n,t))))};e.editorCommands.addCommands({JustifyLeft:t("alignleft"),JustifyCenter:t("aligncenter"),JustifyRight:t("alignright"),JustifyFull:t("alignjustify")},"state")})(e)},DA=(e,t)=>{const n=e.selection,o=e.dom;return/^ | $/.test(t)?((e,t,n)=>{const o=bn(e.getRoot());return n=_p(o,Ti.fromRangeStart(t))?n.replace(/^ /,"&nbsp;"):n.replace(/^&nbsp;/," "),Np(o,Ti.fromRangeEnd(t))?n.replace(/(&nbsp;| )(<br( \/)>)?$/,"&nbsp;"):n.replace(/&nbsp;(<br( \/)?>)?$/," ")})(o,n.getRng(),t):t},PA=(e,t)=>{if(e.selection.isEditable()){const{content:n,details:o}=(e=>{if("string"!=typeof e){const t=Dt.extend({paste:e.paste,data:{paste:e.paste}},e);return{content:e.content,details:t}}return{content:e,details:{}}})(t);qy(e,{...o,content:DA(e,n),format:"html",set:!1,selection:!0}).each((t=>{const n=((e,t,n)=>wC(e).editor.insertContent(t,n))(e,t.content,o);Wy(e,n,t),e.addVisual()}))}},LA={"font-size":"size","font-family":"face"},MA=Gt("font"),IA=e=>(t,n)=>I.from(n).map(bn).filter(Vt).bind((n=>((e,t,n)=>ob(bn(n),(t=>(t=>lo(t,e).orThunk((()=>MA(t)?xe(LA,e).bind((e=>Zt(t,e))):I.none())))(t)),(e=>xn(bn(t),e))))(e,t,n.dom).or(((e,t)=>I.from(_a.DOM.getStyle(t,e,!0)))(e,n.dom)))).getOr(""),FA=IA("font-size"),UA=S((e=>e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")),IA("font-family")),zA=e=>cu(e.getBody()).bind((e=>{const t=e.container();return I.from(Yo(t)?t.parentNode:t)})),jA=(e,t)=>((e,t)=>(e=>I.from(e.selection.getRng()).bind((t=>{const n=e.getBody();return t.startContainer===n&&0===t.startOffset?I.none():I.from(e.selection.getStart(!0))})))(e).orThunk(O(zA,e)).map(bn).filter(Vt).bind(t))(e,_(I.some,t)),HA=(e,t)=>{if(/^[0-9.]+$/.test(t)){const n=parseInt(t,10);if(n>=1&&n<=7){const o=(e=>Dt.explode(e.options.get("font_size_style_values")))(e),r=(e=>Dt.explode(e.options.get("font_size_classes")))(e);return r.length>0?r[n-1]||t:o[n-1]||t}return t}return t},$A=e=>{const t=e.split(/\s*,\s*/);return V(t,(e=>-1===e.indexOf(" ")||He(e,'"')||He(e,"'")?e:`'${e}'`)).join(",")},VA=e=>{BA(e),(e=>{e.editorCommands.addCommands({"Cut,Copy,Paste":t=>{const n=e.getDoc();let o;try{n.execCommand(t)}catch(e){o=!0}if("paste"!==t||n.queryCommandEnabled(t)||(o=!0),o||!n.queryCommandSupported(t)){let t=e.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");(At.os.isMacOS()||At.os.isiOS())&&(t=t.replace(/Ctrl\+/g,"\u2318+")),e.notificationManager.open({text:t,type:"error"})}}})})(e),(e=>{e.editorCommands.addCommands({mceAddUndoLevel:()=>{e.undoManager.add()},mceEndUndoLevel:()=>{e.undoManager.add()},Undo:()=>{e.undoManager.undo()},Redo:()=>{e.undoManager.redo()}})})(e),(e=>{e.editorCommands.addCommands({mceSelectNodeDepth:(t,n,o)=>{let r=0;e.dom.getParent(e.selection.getNode(),(t=>!zo(t)||r++!==o||(e.selection.select(t),!1)),e.getBody())},mceSelectNode:(t,n,o)=>{e.selection.select(o)},selectAll:()=>{const t=e.dom.getParent(e.selection.getStart(),or);if(t){const n=e.dom.createRng();n.selectNodeContents(t),e.selection.setRng(n)}}})})(e),(e=>{e.editorCommands.addCommands({mceCleanup:()=>{const t=e.selection.getBookmark();e.setContent(e.getContent()),e.selection.moveToBookmark(t)},insertImage:(t,n,o)=>{PA(e,e.dom.createHTML("img",{src:o}))},insertHorizontalRule:()=>{e.execCommand("mceInsertContent",!1,"<hr>")},insertText:(t,n,o)=>{PA(e,e.dom.encode(o))},insertHTML:(t,n,o)=>{PA(e,o)},mceInsertContent:(t,n,o)=>{PA(e,o)},mceSetContent:(t,n,o)=>{e.setContent(o)},mceReplaceContent:(t,n,o)=>{e.execCommand("mceInsertContent",!1,o.replace(/\{\$selection\}/g,e.selection.getContent({format:"text"})))},mceNewDocument:()=>{e.setContent("")}})})(e),(e=>{const t=(t,n,o)=>{const r=m(o)?{href:o}:o,s=e.dom.getParent(e.selection.getNode(),"a");f(r)&&m(r.href)&&(r.href=r.href.replace(/ /g,"%20"),s&&r.href||e.formatter.remove("link"),r.href&&e.formatter.apply("link",r,s))};e.editorCommands.addCommands({unlink:()=>{if(e.selection.isCollapsed()){const t=e.dom.getParent(e.selection.getStart(),"a");t&&e.dom.remove(t,!0)}else e.formatter.remove("link")},mceInsertLink:t,createLink:t})})(e),(e=>{e.editorCommands.addCommands({Indent:()=>{(e=>{Gk(e,"indent")})(e)},Outdent:()=>{Yk(e)}}),e.editorCommands.addCommands({Outdent:()=>qk(e)},"state")})(e),(e=>{e.editorCommands.addCommands({insertParagraph:()=>{tN(P_,e)},mceInsertNewLine:(t,n,o)=>{nN(e,o)},InsertLineBreak:(t,n,o)=>{tN(H_,e)}})})(e),(e=>{(e=>{e.editorCommands.addCommands({"InsertUnorderedList,InsertOrderedList":t=>{e.getDoc().execCommand(t);const n=e.dom.getParent(e.selection.getNode(),"ol,ul");if(n){const t=n.parentNode;if(t&&/^(H[1-6]|P|ADDRESS|PRE)$/.test(t.nodeName)){const o=e.selection.getBookmark();e.dom.split(t,n),e.selection.moveToBookmark(o)}}}})})(e),(e=>{e.editorCommands.addCommands({"InsertUnorderedList,InsertOrderedList":t=>{const n=e.dom.getParent(e.selection.getNode(),"ul,ol");return n&&("insertunorderedlist"===t&&"UL"===n.tagName||"insertorderedlist"===t&&"OL"===n.tagName)}},"state")})(e)})(e),(e=>{(e=>{const t=(t,n)=>{e.formatter.toggle(t,n),e.nodeChanged()};e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>{t(e)},"ForeColor,HiliteColor":(e,n,o)=>{t(e,{value:o})},BackColor:(e,n,o)=>{t("hilitecolor",{value:o})},FontName:(t,n,o)=>{((e,t)=>{const n=HA(e,t);e.formatter.toggle("fontname",{value:$A(n)}),e.nodeChanged()})(e,o)},FontSize:(t,n,o)=>{((e,t)=>{e.formatter.toggle("fontsize",{value:HA(e,t)}),e.nodeChanged()})(e,o)},LineHeight:(t,n,o)=>{((e,t)=>{e.formatter.toggle("lineheight",{value:String(t)}),e.nodeChanged()})(e,o)},Lang:(e,n,o)=>{var r;t(e,{value:o.code,customValue:null!==(r=o.customCode)&&void 0!==r?r:null})},RemoveFormat:t=>{e.formatter.remove(t)},mceBlockQuote:()=>{t("blockquote")},FormatBlock:(e,n,o)=>{t(m(o)?o:"p")},mceToggleFormat:(e,n,o)=>{t(o)}})})(e),(e=>{const t=t=>e.formatter.match(t);e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>t(e),mceBlockQuote:()=>t("blockquote")},"state"),e.editorCommands.addQueryValueHandler("FontName",(()=>(e=>jA(e,(t=>UA(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("FontSize",(()=>(e=>jA(e,(t=>FA(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("LineHeight",(()=>(e=>jA(e,(t=>{const n=bn(e.getBody()),o=ob(t,(e=>lo(e,"line-height")),O(xn,n));return o.getOrThunk((()=>{const e=parseFloat(ao(t,"line-height")),n=parseFloat(ao(t,"font-size"));return String(e/n)}))})).getOr(""))(e)))})(e)})(e),(e=>{e.editorCommands.addCommands({mceRemoveNode:(t,n,o)=>{const r=null!=o?o:e.selection.getNode();if(r!==e.getBody()){const t=e.selection.getBookmark();e.dom.remove(r,!0),e.selection.moveToBookmark(t)}},mcePrint:()=>{e.getWin().print()},mceFocus:(t,n,o)=>{((e,t)=>{e.removed||(t?fg(e):(e=>{const t=e.selection,n=e.getBody();let o=t.getRng();e.quirks.refreshContentEditable(),C(e.bookmark)&&!mg(e)&&eg(e).each((t=>{e.selection.setRng(t),o=t}));const r=((e,t)=>e.dom.getParent(t,(t=>"true"===e.dom.getContentEditable(t))))(e,t.getNode());if(r&&e.dom.isChildOf(r,n))return ug(r),cg(e,o),void fg(e);e.inline||(At.browser.isOpera()||ug(n),e.getWin().focus()),(At.browser.isFirefox()||e.inline)&&(ug(n),cg(e,o)),fg(e)})(e))})(e,!0===o)},mceToggleVisualAid:()=>{e.hasVisual=!e.hasVisual,e.addVisual()}})})(e)},qA=["toggleview"],WA=e=>H(qA,e.toLowerCase());class KA{constructor(e){this.commands={state:{},exec:{},value:{}},this.editor=e}execCommand(e,t=!1,n,o){const r=this.editor,s=e.toLowerCase(),a=null==o?void 0:o.skip_focus;if(r.removed)return!1;if("mcefocus"!==s&&(/^(mceAddUndoLevel|mceEndUndoLevel)$/i.test(s)||a?(e=>{eg(e).each((t=>e.selection.setRng(t)))})(r):r.focus()),r.dispatch("BeforeExecCommand",{command:e,ui:t,value:n}).isDefaultPrevented())return!1;const i=this.commands.exec[s];return!!w(i)&&(i(s,t,n),r.dispatch("ExecCommand",{command:e,ui:t,value:n}),!0)}queryCommandState(e){if(!WA(e)&&this.editor.quirks.isHidden()||this.editor.removed)return!1;const t=e.toLowerCase(),n=this.commands.state[t];return!!w(n)&&n(t)}queryCommandValue(e){if(!WA(e)&&this.editor.quirks.isHidden()||this.editor.removed)return"";const t=e.toLowerCase(),n=this.commands.value[t];return w(n)?n(t):""}addCommands(e,t="exec"){const n=this.commands;ge(e,((e,o)=>{q(o.toLowerCase().split(","),(o=>{n[t][o]=e}))}))}addCommand(e,t,n){const o=e.toLowerCase();this.commands.exec[o]=(e,o,r)=>t.call(null!=n?n:this.editor,o,r)}queryCommandSupported(e){const t=e.toLowerCase();return!!this.commands.exec[t]}addQueryStateHandler(e,t,n){this.commands.state[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}addQueryValueHandler(e,t,n){this.commands.value[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}}const GA="data-mce-contenteditable",YA=(e,t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},XA=(e,t)=>{e.dom.contentEditable=t?"true":"false"},QA=(e,t)=>{const n=bn(e.getBody());((e,t,n)=>{mn(e,t)&&!n?un(e,t):n&&dn(e,t)})(n,"mce-content-readonly",t),t?(e.selection.controlSelection.hideResizeRect(),e._selectionOverrides.hideFakeCaret(),(e=>{I.from(e.selection.getNode()).each((e=>{e.removeAttribute("data-mce-selected")}))})(e),e.readonly=!0,XA(n,!1),q(Lo(n,'*[contenteditable="true"]'),(e=>{Xt(e,GA,"true"),XA(e,!1)}))):(e.readonly=!1,XA(n,!0),q(Lo(n,'*[data-mce-contenteditable="true"]'),(e=>{tn(e,GA),XA(e,!0)})),YA(e,"StyleWithCSS",!1),YA(e,"enableInlineTableEditing",!1),YA(e,"enableObjectResizing",!1),(e=>mg(e)||(e=>{const t=Hn(bn(e.getElement()));return Wf(t).filter((t=>!sg(t.dom)&&ag(e,t.dom))).isSome()})(e))(e)&&e.focus(),(e=>{e.selection.setRng(e.selection.getRng())})(e),e.nodeChanged())},JA=e=>e.readonly,ZA=e=>{e.parser.addAttributeFilter("contenteditable",(t=>{JA(e)&&q(t,(e=>{e.attr(GA,e.attr("contenteditable")),e.attr("contenteditable","false")}))})),e.serializer.addAttributeFilter(GA,(t=>{JA(e)&&q(t,(e=>{e.attr("contenteditable",e.attr(GA))}))})),e.serializer.addTempAttr(GA)},eO=["copy"],tO=Dt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input beforeinput contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend touchcancel"," ");class nO{constructor(e){this.bindings={},this.settings=e||{},this.scope=this.settings.scope||this,this.toggleEvent=this.settings.toggleEvent||L}static isNative(e){return!!tO[e.toLowerCase()]}fire(e,t){return this.dispatch(e,t)}dispatch(e,t){const n=e.toLowerCase(),o=ca(n,null!=t?t:{},this.scope);this.settings.beforeFire&&this.settings.beforeFire(o);const r=this.bindings[n];if(r)for(let e=0,t=r.length;e<t;e++){const t=r[e];if(!t.removed){if(t.once&&this.off(n,t.func),o.isImmediatePropagationStopped())return o;if(!1===t.func.call(this.scope,o))return o.preventDefault(),o}}return o}on(e,t,n,o){if(!1===t&&(t=L),t){const r={func:t,removed:!1};o&&Dt.extend(r,o);const s=e.toLowerCase().split(" ");let a=s.length;for(;a--;){const e=s[a];let t=this.bindings[e];t||(t=[],this.toggleEvent(e,!0)),t=n?[r,...t]:[...t,r],this.bindings[e]=t}}return this}off(e,t){if(e){const n=e.toLowerCase().split(" ");let o=n.length;for(;o--;){const r=n[o];let s=this.bindings[r];if(!r)return ge(this.bindings,((e,t)=>{this.toggleEvent(t,!1),delete this.bindings[t]})),this;if(s){if(t){const e=K(s,(e=>e.func===t));s=e.fail,this.bindings[r]=s,q(e.pass,(e=>{e.removed=!0}))}else s.length=0;s.length||(this.toggleEvent(e,!1),delete this.bindings[r])}}}else ge(this.bindings,((e,t)=>{this.toggleEvent(t,!1)})),this.bindings={};return this}once(e,t,n){return this.on(e,t,n,{once:!0})}has(e){e=e.toLowerCase();const t=this.bindings[e];return!(!t||0===t.length)}}const oO=e=>(e._eventDispatcher||(e._eventDispatcher=new nO({scope:e,toggleEvent:(t,n)=>{nO.isNative(t)&&e.toggleNativeEvent&&e.toggleNativeEvent(t,n)}})),e._eventDispatcher),rO={fire(e,t,n){return this.dispatch(e,t,n)},dispatch(e,t,n){const o=this;if(o.removed&&"remove"!==e&&"detach"!==e)return ca(e.toLowerCase(),null!=t?t:{},o);const r=oO(o).dispatch(e,t);if(!1!==n&&o.parent){let t=o.parent();for(;t&&!r.isPropagationStopped();)t.dispatch(e,r,!1),t=t.parent?t.parent():void 0}return r},on(e,t,n){return oO(this).on(e,t,n)},off(e,t){return oO(this).off(e,t)},once(e,t){return oO(this).once(e,t)},hasEventListeners(e){return oO(this).has(e)}},sO=_a.DOM;let aO;const iO=(e,t)=>{if("selectionchange"===t)return e.getDoc();if(!e.inline&&/^(?:mouse|touch|click|contextmenu|drop|dragover|dragend)/.test(t))return e.getDoc().documentElement;const n=Zl(e);return n?(e.eventRoot||(e.eventRoot=sO.select(n)[0]),e.eventRoot):e.getBody()},lO=(e,t,n)=>{(e=>!e.hidden&&!JA(e))(e)?e.dispatch(t,n):JA(e)&&((e,t)=>{if((e=>"click"===e.type)(t)&&!Vm.metaKeyPressed(t)){const n=bn(t.target);((e,t)=>Zn(t,"a",(t=>xn(t,bn(e.getBody())))).bind((e=>Zt(e,"href"))))(e,n).each((n=>{if(t.preventDefault(),/^#/.test(n)){const t=e.dom.select(`${n},[name="${ze(n,"#")}"]`);t.length&&e.selection.scrollIntoView(t[0],!0)}else window.open(n,"_blank","rel=noopener noreferrer,menubar=yes,toolbar=yes,location=yes,status=yes,resizable=yes,scrollbars=yes")}))}else(e=>H(eO,e.type))(t)&&e.dispatch(t.type,t)})(e,n)},dO=(e,t)=>{if(e.delegates||(e.delegates={}),e.delegates[t]||e.removed)return;const n=iO(e,t);if(Zl(e)){if(aO||(aO={},e.editorManager.on("removeEditor",(()=>{e.editorManager.activeEditor||aO&&(ge(aO,((t,n)=>{e.dom.unbind(iO(e,n))})),aO=null)}))),aO[t])return;const o=n=>{const o=n.target,r=e.editorManager.get();let s=r.length;for(;s--;){const e=r[s].getBody();(e===o||sO.isChildOf(o,e))&&lO(r[s],t,n)}};aO[t]=o,sO.bind(n,t,o)}else{const o=n=>{lO(e,t,n)};sO.bind(n,t,o),e.delegates[t]=o}},cO={...rO,bindPendingEventDelegates(){const e=this;Dt.each(e._pendingNativeEvents,(t=>{dO(e,t)}))},toggleNativeEvent(e,t){const n=this;"focus"!==e&&"blur"!==e&&(n.removed||(t?n.initialized?dO(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&n.delegates&&(n.dom.unbind(iO(n,e),e,n.delegates[e]),delete n.delegates[e])))},unbindAllNativeEvents(){const e=this,t=e.getBody(),n=e.dom;e.delegates&&(ge(e.delegates,((t,n)=>{e.dom.unbind(iO(e,n),n,t)})),delete e.delegates),!e.inline&&t&&n&&(t.onload=null,n.unbind(e.getWin()),n.unbind(e.getDoc())),n&&(n.unbind(t),n.unbind(e.getContainer()))}},uO=e=>m(e)?{value:e.split(/[ ,]/),valid:!0}:k(e,m)?{value:e,valid:!0}:{valid:!1,message:"The value must be a string[] or a comma/space separated string."},mO=(e,t)=>e+(Ye(t.message)?"":`. ${t.message}`),fO=e=>e.valid,gO=(e,t,n="")=>{const o=t(e);return b(o)?o?{value:e,valid:!0}:{valid:!1,message:n}:o},pO=["design","readonly"],hO=(e,t,n,o)=>{const r=n[t.get()],s=n[o];try{s.activate()}catch(e){return void console.error(`problem while activating editor mode ${o}:`,e)}r.deactivate(),r.editorReadOnly!==s.editorReadOnly&&QA(e,s.editorReadOnly),t.set(o),((e,t)=>{e.dispatch("SwitchMode",{mode:t})})(e,o)},bO=Dt.each,vO=Dt.explode,yO={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},CO=Dt.makeMap("alt,ctrl,shift,meta,access"),wO=e=>{const t={},n=At.os.isMacOS()||At.os.isiOS();bO(vO(e.toLowerCase(),"+"),(e=>{(e=>e in CO)(e)?t[e]=!0:/^[0-9]{2,}$/.test(e)?t.keyCode=parseInt(e,10):(t.charCode=e.charCodeAt(0),t.keyCode=yO[e]||e.toUpperCase().charCodeAt(0))}));const o=[t.keyCode];let r;for(r in CO)t[r]?o.push(r):t[r]=!1;return t.id=o.join(","),t.access&&(t.alt=!0,n?t.ctrl=!0:t.shift=!0),t.meta&&(n?t.meta=!0:(t.ctrl=!0,t.meta=!1)),t};class xO{constructor(e){this.shortcuts={},this.pendingPatterns=[],this.editor=e;const t=this;e.on("keyup keypress keydown",(e=>{!t.hasModifier(e)&&!t.isFunctionKey(e)||e.isDefaultPrevented()||(bO(t.shortcuts,(n=>{t.matchShortcut(e,n)&&(t.pendingPatterns=n.subpatterns.slice(0),"keydown"===e.type&&t.executeShortcutAction(n))})),t.matchShortcut(e,t.pendingPatterns[0])&&(1===t.pendingPatterns.length&&"keydown"===e.type&&t.executeShortcutAction(t.pendingPatterns[0]),t.pendingPatterns.shift()))}))}add(e,t,n,o){const r=this,s=r.normalizeCommandFunc(n);return bO(vO(Dt.trim(e)),(e=>{const n=r.createShortcut(e,t,s,o);r.shortcuts[n.id]=n})),!0}remove(e){const t=this.createShortcut(e);return!!this.shortcuts[t.id]&&(delete this.shortcuts[t.id],!0)}normalizeCommandFunc(e){const t=this,n=e;return"string"==typeof n?()=>{t.editor.execCommand(n,!1,null)}:Dt.isArray(n)?()=>{t.editor.execCommand(n[0],n[1],n[2])}:n}createShortcut(e,t,n,o){const r=Dt.map(vO(e,">"),wO);return r[r.length-1]=Dt.extend(r[r.length-1],{func:n,scope:o||this.editor}),Dt.extend(r[0],{desc:this.editor.translate(t),subpatterns:r.slice(1)})}hasModifier(e){return e.altKey||e.ctrlKey||e.metaKey}isFunctionKey(e){return"keydown"===e.type&&e.keyCode>=112&&e.keyCode<=123}matchShortcut(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)}executeShortcutAction(e){return e.func?e.func.call(e.scope):null}}const kO=()=>{const e=(()=>{const e={},t={},n={},o={},r={},s={},a={},i={},l=(e,t)=>(n,o)=>{e[n.toLowerCase()]={...o,type:t}};return{addButton:l(e,"button"),addGroupToolbarButton:l(e,"grouptoolbarbutton"),addToggleButton:l(e,"togglebutton"),addMenuButton:l(e,"menubutton"),addSplitButton:l(e,"splitbutton"),addMenuItem:l(t,"menuitem"),addNestedMenuItem:l(t,"nestedmenuitem"),addToggleMenuItem:l(t,"togglemenuitem"),addAutocompleter:l(n,"autocompleter"),addContextMenu:l(r,"contextmenu"),addContextToolbar:l(s,"contexttoolbar"),addContextForm:l(s,"contextform"),addSidebar:l(a,"sidebar"),addView:l(i,"views"),addIcon:(e,t)=>o[e.toLowerCase()]=t,getAll:()=>({buttons:e,menuItems:t,icons:o,popups:n,contextMenus:r,contextToolbars:s,sidebars:a,views:i})}})();return{addAutocompleter:e.addAutocompleter,addButton:e.addButton,addContextForm:e.addContextForm,addContextMenu:e.addContextMenu,addContextToolbar:e.addContextToolbar,addIcon:e.addIcon,addMenuButton:e.addMenuButton,addMenuItem:e.addMenuItem,addNestedMenuItem:e.addNestedMenuItem,addSidebar:e.addSidebar,addSplitButton:e.addSplitButton,addToggleButton:e.addToggleButton,addGroupToolbarButton:e.addGroupToolbarButton,addToggleMenuItem:e.addToggleMenuItem,addView:e.addView,getAll:e.getAll}},EO=_a.DOM,SO=Dt.extend,_O=Dt.each;class NO{constructor(e,t,n){this.plugins={},this.contentCSS=[],this.contentStyles=[],this.loadedCSS={},this.isNotDirty=!1,this.composing=!1,this.destroyed=!1,this.hasHiddenInput=!1,this.iframeElement=null,this.initialized=!1,this.readonly=!1,this.removed=!1,this.startContent="",this._pendingNativeEvents=[],this._skinLoaded=!1,this.editorManager=n,this.documentBaseUrl=n.documentBaseURL,SO(this,cO);const o=this;this.id=e,this.hidden=!1;const r=((e,t)=>TA(EA||SA,EA,t,e,t))(n.defaultOptions,t);this.options=((e,t)=>{const n={},o={},r=(e,t,n)=>{const r=gO(t,n);return fO(r)?(o[e]=r.value,!0):(console.warn(mO(`Invalid value passed for the ${e} option`,r)),!1)},s=e=>ke(n,e);return{register:(e,s)=>{const a=(e=>m(e.processor))(s)?(e=>{const t=(()=>{switch(e){case"array":return p;case"boolean":return b;case"function":return w;case"number":return x;case"object":return f;case"string":return m;case"string[]":return uO;case"object[]":return e=>k(e,f);case"regexp":return e=>u(e,RegExp);default:return M}})();return n=>gO(n,t,`The value must be a ${e}.`)})(s.processor):s.processor,i=((e,t,n)=>{if(!v(t)){const o=gO(t,n);if(fO(o))return o.value;console.error(mO(`Invalid default value passed for the "${e}" option`,o))}})(e,s.default,a);n[e]={...s,default:i,processor:a},xe(o,e).orThunk((()=>xe(t,e))).each((t=>r(e,t,a)))},isRegistered:s,get:e=>xe(o,e).orThunk((()=>xe(n,e).map((e=>e.default)))).getOrUndefined(),set:(e,t)=>{if(s(e)){const o=n[e];return o.immutable?(console.error(`"${e}" is an immutable option and cannot be updated`),!1):r(e,t,o.processor)}return console.warn(`"${e}" is not a registered option. Ensure the option has been registered before setting a value.`),!1},unset:e=>{const t=s(e);return t&&delete o[e],t},isSet:e=>ke(o,e)}})(0,r),(e=>{const t=e.options.register;t("id",{processor:"string",default:e.id}),t("selector",{processor:"string"}),t("target",{processor:"object"}),t("suffix",{processor:"string"}),t("cache_suffix",{processor:"string"}),t("base_url",{processor:"string"}),t("referrer_policy",{processor:"string",default:""}),t("language_load",{processor:"boolean",default:!0}),t("inline",{processor:"boolean",default:!1}),t("iframe_attrs",{processor:"object",default:{}}),t("doctype",{processor:"string",default:"<!DOCTYPE html>"}),t("document_base_url",{processor:"string",default:e.documentBaseUrl}),t("body_id",{processor:pl(e,"tinymce"),default:"tinymce"}),t("body_class",{processor:pl(e),default:""}),t("content_security_policy",{processor:"string",default:""}),t("br_in_pre",{processor:"boolean",default:!0}),t("forced_root_block",{processor:e=>{const t=m(e)&&Ge(e);return t?{value:e,valid:t}:{valid:!1,message:"Must be a non-empty string."}},default:"p"}),t("forced_root_block_attrs",{processor:"object",default:{}}),t("newline_behavior",{processor:e=>{const t=H(["block","linebreak","invert","default"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: block, linebreak, invert or default."}},default:"default"}),t("br_newline_selector",{processor:"string",default:".mce-toc h2,figcaption,caption"}),t("no_newline_selector",{processor:"string",default:""}),t("keep_styles",{processor:"boolean",default:!0}),t("end_container_on_empty_block",{processor:e=>b(e)||m(e)?{valid:!0,value:e}:{valid:!1,message:"Must be boolean or a string"},default:"blockquote"}),t("font_size_style_values",{processor:"string",default:"xx-small,x-small,small,medium,large,x-large,xx-large"}),t("font_size_legacy_values",{processor:"string",default:"xx-small,small,medium,large,x-large,xx-large,300%"}),t("font_size_classes",{processor:"string",default:""}),t("automatic_uploads",{processor:"boolean",default:!0}),t("images_reuse_filename",{processor:"boolean",default:!1}),t("images_replace_blob_uris",{processor:"boolean",default:!0}),t("icons",{processor:"string",default:""}),t("icons_url",{processor:"string",default:""}),t("images_upload_url",{processor:"string",default:""}),t("images_upload_base_path",{processor:"string",default:""}),t("images_upload_credentials",{processor:"boolean",default:!1}),t("images_upload_handler",{processor:"function"}),t("language",{processor:"string",default:"en"}),t("language_url",{processor:"string",default:""}),t("entity_encoding",{processor:"string",default:"named"}),t("indent",{processor:"boolean",default:!0}),t("indent_before",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_after",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_use_margin",{processor:"boolean",default:!1}),t("indentation",{processor:"string",default:"40px"}),t("content_css",{processor:e=>{const t=!1===e||m(e)||k(e,m);return t?m(e)?{value:V(e.split(","),qe),valid:t}:p(e)?{value:e,valid:t}:!1===e?{value:[],valid:t}:{value:e,valid:t}:{valid:!1,message:"Must be false, a string or an array of strings."}},default:ud(e)?[]:["default"]}),t("content_style",{processor:"string"}),t("content_css_cors",{processor:"boolean",default:!1}),t("font_css",{processor:e=>{const t=m(e)||k(e,m);return t?{value:p(e)?e:V(e.split(","),qe),valid:t}:{valid:!1,message:"Must be a string or an array of strings."}},default:[]}),t("inline_boundaries",{processor:"boolean",default:!0}),t("inline_boundaries_selector",{processor:"string",default:"a[href],code,span.mce-annotation"}),t("object_resizing",{processor:e=>{const t=b(e)||m(e);return t?!1===e||dl.isiPhone()||dl.isiPad()?{value:"",valid:t}:{value:!0===e?"table,img,figure.image,div,video,iframe":e,valid:t}:{valid:!1,message:"Must be boolean or a string"}},default:!cl}),t("resize_img_proportional",{processor:"boolean",default:!0}),t("event_root",{processor:"object"}),t("service_message",{processor:"string"}),t("theme",{processor:e=>!1===e||m(e)||w(e),default:"silver"}),t("theme_url",{processor:"string"}),t("formats",{processor:"object"}),t("format_empty_lines",{processor:"boolean",default:!1}),t("format_noneditable_selector",{processor:"string",default:""}),t("preview_styles",{processor:e=>{const t=!1===e||m(e);return t?{value:!1===e?"":e,valid:t}:{valid:!1,message:"Must be false or a string"}},default:"font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow"}),t("custom_ui_selector",{processor:"string",default:""}),t("hidden_input",{processor:"boolean",default:!0}),t("submit_patch",{processor:"boolean",default:!0}),t("encoding",{processor:"string"}),t("add_form_submit_trigger",{processor:"boolean",default:!0}),t("add_unload_trigger",{processor:"boolean",default:!0}),t("custom_undo_redo_levels",{processor:"number",default:0}),t("disable_nodechange",{processor:"boolean",default:!1}),t("readonly",{processor:"boolean",default:!1}),t("plugins",{processor:"string[]",default:[]}),t("external_plugins",{processor:"object"}),t("forced_plugins",{processor:"string[]"}),t("model",{processor:"string",default:e.hasPlugin("rtc")?"plugin":"dom"}),t("model_url",{processor:"string"}),t("block_unsupported_drop",{processor:"boolean",default:!0}),t("visual",{processor:"boolean",default:!0}),t("visual_table_class",{processor:"string",default:"mce-item-table"}),t("visual_anchor_class",{processor:"string",default:"mce-item-anchor"}),t("iframe_aria_text",{processor:"string",default:"Rich Text Area. Press ALT-0 for help."}),t("setup",{processor:"function"}),t("init_instance_callback",{processor:"function"}),t("url_converter",{processor:"function",default:e.convertURL}),t("url_converter_scope",{processor:"object",default:e}),t("urlconverter_callback",{processor:"function"}),t("allow_conditional_comments",{processor:"boolean",default:!1}),t("allow_html_data_urls",{processor:"boolean",default:!1}),t("allow_svg_data_urls",{processor:"boolean"}),t("allow_html_in_named_anchor",{processor:"boolean",default:!1}),t("allow_script_urls",{processor:"boolean",default:!1}),t("allow_unsafe_link_target",{processor:"boolean",default:!1}),t("convert_fonts_to_spans",{processor:"boolean",default:!0,deprecated:!0}),t("fix_list_elements",{processor:"boolean",default:!1}),t("preserve_cdata",{processor:"boolean",default:!1}),t("remove_trailing_brs",{processor:"boolean"}),t("inline_styles",{processor:"boolean",default:!0,deprecated:!0}),t("element_format",{processor:"string",default:"html"}),t("entities",{processor:"string"}),t("schema",{processor:"string",default:"html5"}),t("convert_urls",{processor:"boolean",default:!0}),t("relative_urls",{processor:"boolean",default:!0}),t("remove_script_host",{processor:"boolean",default:!0}),t("custom_elements",{processor:"string"}),t("extended_valid_elements",{processor:"string"}),t("invalid_elements",{processor:"string"}),t("invalid_styles",{processor:gl}),t("valid_children",{processor:"string"}),t("valid_classes",{processor:gl}),t("valid_elements",{processor:"string"}),t("valid_styles",{processor:gl}),t("verify_html",{processor:"boolean",default:!0}),t("auto_focus",{processor:e=>m(e)||!0===e}),t("browser_spellcheck",{processor:"boolean",default:!1}),t("protect",{processor:"array"}),t("images_file_types",{processor:"string",default:"jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp"}),t("deprecation_warnings",{processor:"boolean",default:!0}),t("a11y_advanced_options",{processor:"boolean",default:!1}),t("api_key",{processor:"string"}),t("paste_block_drop",{processor:"boolean",default:!1}),t("paste_data_images",{processor:"boolean",default:!0}),t("paste_preprocess",{processor:"function"}),t("paste_postprocess",{processor:"function"}),t("paste_webkit_styles",{processor:"string",default:"none"}),t("paste_remove_styles_if_webkit",{processor:"boolean",default:!0}),t("paste_merge_formats",{processor:"boolean",default:!0}),t("smart_paste",{processor:"boolean",default:!0}),t("paste_as_text",{processor:"boolean",default:!1}),t("paste_tab_spaces",{processor:"number",default:4}),t("text_patterns",{processor:e=>k(e,f)||!1===e?{value:ll(!1===e?[]:e),valid:!0}:{valid:!1,message:"Must be an array of objects or false."},default:[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"#",format:"h1"},{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:"1. ",cmd:"InsertOrderedList"},{start:"* ",cmd:"InsertUnorderedList"},{start:"- ",cmd:"InsertUnorderedList"}]}),t("text_patterns_lookup",{processor:e=>{return w(e)?{value:(t=e,e=>{const n=t(e);return ll(n)}),valid:!0}:{valid:!1,message:"Must be a single function"};var t},default:e=>[]}),t("noneditable_class",{processor:"string",default:"mceNonEditable"}),t("editable_class",{processor:"string",default:"mceEditable"}),t("noneditable_regexp",{processor:e=>k(e,ml)?{value:e,valid:!0}:ml(e)?{value:[e],valid:!0}:{valid:!1,message:"Must be a RegExp or an array of RegExp."},default:[]}),t("table_tab_navigation",{processor:"boolean",default:!0}),t("highlight_on_focus",{processor:"boolean",default:!1}),t("xss_sanitization",{processor:"boolean",default:!0}),e.on("ScriptsLoaded",(()=>{t("directionality",{processor:"string",default:Da.isRtl()?"rtl":void 0}),t("placeholder",{processor:"string",default:ul.getAttrib(e.getElement(),"placeholder")})}))})(o);const s=this.options.get;s("deprecation_warnings")&&((e,t)=>{((e,t)=>{const n=FC(e),o=jC(t),r=o.length>0,s=n.length>0,a="mobile"===t.theme;if(r||s||a){const e="\n- ",t=a?`\n\nThemes:${e}mobile`:"",i=r?`\n\nPlugins:${e}${o.join(e)}`:"",l=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled and have been removed in TinyMCE 6.0. These features will no longer work and should be removed from the TinyMCE configuration. See https://www.tiny.cloud/docs/tinymce/6/migration-from-5x/ for more information."+t+i+l)}})(e,t),((e,t)=>{const n=UC(e),o=HC(t),r=o.length>0,s=n.length>0;if(r||s){const e="\n- ",t=r?`\n\nPlugins:${e}${o.map($C).join(e)}`:"",a=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled but will be removed soon."+t+a)}})(e,t)})(t,r);const a=s("suffix");a&&(n.suffix=a),this.suffix=n.suffix;const i=s("base_url");i&&n._setBaseUrl(i),this.baseUri=n.baseURI;const l=zl(o);l&&(Ra.ScriptLoader._setReferrerPolicy(l),_a.DOM.styleSheetLoader._setReferrerPolicy(l));const d=yd(o);C(d)&&_a.DOM.styleSheetLoader._setContentCssCors(d),Pa.languageLoad=s("language_load"),Pa.baseURL=n.baseURL,this.setDirty(!1),this.documentBaseURI=new Ry(vl(o),{base_uri:this.baseUri}),this.baseURI=this.baseUri,this.inline=ud(o),this.hasVisual=kd(o),this.shortcuts=new xO(this),this.editorCommands=new KA(this),VA(this);const c=s("cache_suffix");c&&(At.cacheSuffix=c.replace(/^[\?\&]+/,"")),this.ui={registry:kO(),styleSheetLoader:void 0,show:E,hide:E,setEnabled:E,isEnabled:M},this.mode=(e=>{const t=Aa("design"),n=Aa({design:{activate:E,deactivate:E,editorReadOnly:!1},readonly:{activate:E,deactivate:E,editorReadOnly:!0}});return(e=>{e.serializer?ZA(e):e.on("PreInit",(()=>{ZA(e)}))})(e),(e=>{e.on("ShowCaret",(t=>{JA(e)&&t.preventDefault()})),e.on("ObjectSelected",(t=>{JA(e)&&t.preventDefault()}))})(e),{isReadOnly:()=>JA(e),set:o=>((e,t,n,o)=>{if(o!==n.get()){if(!ke(t,o))throw new Error(`Editor mode '${o}' is invalid`);e.initialized?hO(e,n,t,o):e.on("init",(()=>hO(e,n,t,o)))}})(e,n.get(),t,o),get:()=>t.get(),register:(e,t)=>{n.set(((e,t,n)=>{if(H(pO,t))throw new Error(`Cannot override default mode ${t}`);return{...e,[t]:{...n,deactivate:()=>{try{n.deactivate()}catch(e){console.error(`problem while deactivating editor mode ${t}:`,e)}}}}})(n.get(),e,t))}}})(o),n.dispatch("SetupEditor",{editor:this});const g=Nd(o);w(g)&&g.call(o,o)}render(){(e=>{const t=e.id;Da.setCode(jl(e));const n=()=>{yA.unbind(window,"ready",n),e.render()};if(!ha.Event.domLoaded)return void yA.bind(window,"ready",n);if(!e.getElement())return;const o=bn(e.getElement()),r=nn(o);e.on("remove",(()=>{W(o.dom.attributes,(e=>tn(o,e.name))),Qt(o,r)})),e.ui.styleSheetLoader=((e,t)=>Os.forElement(e,{contentCssCors:yd(t),referrerPolicy:zl(t)}))(o,e),ud(e)?e.inline=!0:(e.orgVisibility=e.getElement().style.visibility,e.getElement().style.visibility="hidden");const s=e.getElement().form||yA.getParent(t,"form");s&&(e.formElement=s,md(e)&&!Go(e.getElement())&&(yA.insertAfter(yA.create("input",{type:"hidden",name:t}),t),e.hasHiddenInput=!0),e.formEventDelegate=t=>{e.dispatch(t.type,t)},yA.bind(s,"submit reset",e.formEventDelegate),e.on("reset",(()=>{e.resetContent()})),!fd(e)||s.submit.nodeType||s.submit.length||s._mceOldSubmit||(s._mceOldSubmit=s.submit,s.submit=()=>(e.editorManager.triggerSave(),e.setDirty(!1),s._mceOldSubmit(s)))),e.windowManager=ow(e),e.notificationManager=ew(e),(e=>"xml"===e.options.get("encoding"))(e)&&e.on("GetContent",(e=>{e.save&&(e.content=yA.encode(e.content))})),gd(e)&&e.on("submit",(()=>{e.initialized&&e.save()})),pd(e)&&(e._beforeUnload=()=>{!e.initialized||e.destroyed||e.isHidden()||e.save({format:"raw",no_events:!0,set_dirty:!1})},e.editorManager.on("BeforeUnload",e._beforeUnload)),e.editorManager.add(e),xA(e,e.suffix)})(this)}focus(e){this.execCommand("mceFocus",!1,e)}hasFocus(){return mg(this)}translate(e){return Da.translate(e)}getParam(e,t,n){const o=this.options;return o.isRegistered(e)||(C(n)?o.register(e,{processor:n,default:t}):o.register(e,{processor:M,default:t})),o.isSet(e)||v(t)?o.get(e):t}hasPlugin(e,t){return!(!H(Cd(this),e)||t&&void 0===tw.get(e))}nodeChanged(e){this._nodeChangeDispatcher.nodeChanged(e)}addCommand(e,t,n){this.editorCommands.addCommand(e,t,n)}addQueryStateHandler(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)}addQueryValueHandler(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)}addShortcut(e,t,n,o){this.shortcuts.add(e,t,n,o)}execCommand(e,t,n,o){return this.editorCommands.execCommand(e,t,n,o)}queryCommandState(e){return this.editorCommands.queryCommandState(e)}queryCommandValue(e){return this.editorCommands.queryCommandValue(e)}queryCommandSupported(e){return this.editorCommands.queryCommandSupported(e)}show(){const e=this;e.hidden&&(e.hidden=!1,e.inline?e.getBody().contentEditable="true":(EO.show(e.getContainer()),EO.hide(e.id)),e.load(),e.dispatch("show"))}hide(){const e=this;e.hidden||(e.save(),e.inline?(e.getBody().contentEditable="false",e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(EO.hide(e.getContainer()),EO.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.dispatch("hide"))}isHidden(){return this.hidden}setProgressState(e,t){this.dispatch("ProgressState",{state:e,time:t})}load(e={}){const t=this,n=t.getElement();if(t.removed)return"";if(n){const o={...e,load:!0},r=Go(n)?n.value:n.innerHTML,s=t.setContent(r,o);return o.no_events||t.dispatch("LoadContent",{...o,element:n}),s}return""}save(e={}){const t=this;let n=t.getElement();if(!n||!t.initialized||t.removed)return"";const o={...e,save:!0,element:n};let r=t.getContent(o);const s={...o,content:r};if(s.no_events||t.dispatch("SaveContent",s),"raw"===s.format&&t.dispatch("RawSaveContent",s),r=s.content,Go(n))n.value=r;else{!e.is_removing&&t.inline||(n.innerHTML=r);const o=EO.getParent(t.id,"form");o&&_O(o.elements,(e=>e.name!==t.id||(e.value=r,!1)))}return s.element=o.element=n=null,!1!==s.set_dirty&&t.setDirty(!1),r}setContent(e,t){return BC(this,e,t)}getContent(e){return((e,t={})=>{const n=((e,t)=>({...e,format:t,get:!0,getInner:!0}))(t,t.format?t.format:"html");return $y(e,n).fold(R,(t=>{const n=((e,t)=>wC(e).editor.getContent(t))(e,t);return Vy(e,n,t)}))})(this,e)}insertContent(e,t){t&&(e=SO({content:e},t)),this.execCommand("mceInsertContent",!1,e)}resetContent(e){void 0===e?BC(this,this.startContent,{format:"raw"}):BC(this,e),this.undoManager.reset(),this.setDirty(!1),this.nodeChanged()}isDirty(){return!this.isNotDirty}setDirty(e){const t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.dispatch("dirty")}getContainer(){const e=this;return e.container||(e.container=e.editorContainer||EO.get(e.id+"_parent")),e.container}getContentAreaContainer(){return this.contentAreaContainer}getElement(){return this.targetElm||(this.targetElm=EO.get(this.id)),this.targetElm}getWin(){const e=this;if(!e.contentWindow){const t=e.iframeElement;t&&(e.contentWindow=t.contentWindow)}return e.contentWindow}getDoc(){const e=this;if(!e.contentDocument){const t=e.getWin();t&&(e.contentDocument=t.document)}return e.contentDocument}getBody(){var e,t;const n=this.getDoc();return null!==(t=null!==(e=this.bodyElement)&&void 0!==e?e:null==n?void 0:n.body)&&void 0!==t?t:null}convertURL(e,t,n){const o=this,r=o.options.get,s=Ad(o);return w(s)?s.call(o,e,n,!0,t):!r("convert_urls")||"link"===n||f(n)&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length?e:r("relative_urls")?o.documentBaseURI.toRelative(e):e=o.documentBaseURI.toAbsolute(e,r("remove_script_host"))}addVisual(e){((e,t)=>{((e,t)=>{xC(e).editor.addVisual(t)})(e,t)})(this,e)}remove(){(e=>{if(!e.removed){const{_selectionOverrides:t,editorUpload:n}=e,o=e.getBody(),r=e.getElement();o&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&C(null==r?void 0:r.nextSibling)&&VC.remove(r.nextSibling),(e=>{e.dispatch("remove")})(e),e.editorManager.remove(e),!e.inline&&o&&(e=>{VC.setStyle(e.id,"display",e.orgDisplay)})(e),(e=>{e.dispatch("detach")})(e),VC.remove(e.getContainer()),qC(t),qC(n),e.destroy()}})(this)}destroy(e){((e,t)=>{const{selection:n,dom:o}=e;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),qC(n),qC(o)),(e=>{const t=e.formElement;t&&(t._mceOldSubmit&&(t.submit=t._mceOldSubmit,delete t._mceOldSubmit),VC.unbind(t,"submit reset",e.formEventDelegate))})(e),(e=>{const t=e;t.contentAreaContainer=t.formElement=t.container=t.editorContainer=null,t.bodyElement=t.contentDocument=t.contentWindow=null,t.iframeElement=t.targetElm=null;const n=e.selection;if(n){const e=n.dom;t.selection=n.win=n.dom=e.doc=null}})(e),e.destroyed=!0):e.remove())})(this,e)}uploadImages(){return this.editorUpload.uploadImages()}_scanForImages(){return this.editorUpload.scanForImages()}}const RO=_a.DOM,AO=Dt.each;let OO,TO=!1,BO=[];const DO=e=>{const t=e.type;AO(IO.get(),(n=>{switch(t){case"scroll":n.dispatch("ScrollWindow",e);break;case"resize":n.dispatch("ResizeWindow",e)}}))},PO=e=>{if(e!==TO){const t=_a.DOM;e?(t.bind(window,"resize",DO),t.bind(window,"scroll",DO)):(t.unbind(window,"resize",DO),t.unbind(window,"scroll",DO)),TO=e}},LO=e=>{const t=BO;return BO=G(BO,(t=>e!==t)),IO.activeEditor===e&&(IO.activeEditor=BO.length>0?BO[0]:null),IO.focusedEditor===e&&(IO.focusedEditor=null),t.length!==BO.length},MO="CSS1Compat"!==document.compatMode,IO={...rO,baseURI:null,baseURL:null,defaultOptions:{},documentBaseURL:null,suffix:null,majorVersion:"6",minorVersion:"4.2",releaseDate:"2023-04-26",i18n:Da,activeEditor:null,focusedEditor:null,setup(){const e=this;let t="",n="",o=Ry.getDocumentBaseUrl(document.location);/^[^:]+:\/\/\/?[^\/]+\//.test(o)&&(o=o.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(o)||(o+="/"));const r=window.tinymce||window.tinyMCEPreInit;if(r)t=r.base||r.baseURL,n=r.suffix;else{const e=document.getElementsByTagName("script");for(let o=0;o<e.length;o++){const r=e[o].src||"";if(""===r)continue;const s=r.substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==s.indexOf(".min")&&(n=".min"),t=r.substring(0,r.lastIndexOf("/"));break}}if(!t&&document.currentScript){const e=document.currentScript.src;-1!==e.indexOf(".min")&&(n=".min"),t=e.substring(0,e.lastIndexOf("/"))}}var s;e.baseURL=new Ry(o).toAbsolute(t),e.documentBaseURL=o,e.baseURI=new Ry(e.baseURL),e.suffix=n,(s=e).on("AddEditor",O(lg,s)),s.on("RemoveEditor",O(dg,s))},overrideDefaults(e){const t=e.base_url;t&&this._setBaseUrl(t);const n=e.suffix;n&&(this.suffix=n),this.defaultOptions=e;const o=e.plugin_base_urls;void 0!==o&&ge(o,((e,t)=>{Pa.PluginManager.urls[t]=e}))},init(e){const t=this;let n;const o=Dt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," ");let r=e=>{n=e};const s=()=>{let n=0;const a=[];let i;RO.unbind(window,"ready",s),(n=>{const o=e.onpageload;o&&o.apply(t,[])})(),i=((e,t)=>{const n=[],o=w(t)?e=>$(n,(n=>t(n,e))):e=>H(n,e);for(let t=0,r=e.length;t<r;t++){const r=e[t];o(r)||n.push(r)}return n})((e=>At.browser.isIE()||At.browser.isEdge()?(lw("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tiny.cloud/docs/tinymce/6/support/#supportedwebbrowsers"),[]):MO?(lw("Failed to initialize the editor as the document is not in standards mode. TinyMCE requires standards mode."),[]):m(e.selector)?RO.select(e.selector):C(e.target)?[e.target]:[])(e)),Dt.each(i,(e=>{var n;(n=t.get(e.id))&&n.initialized&&!(n.getContainer()||n.getBody()).parentNode&&(LO(n),n.unbindAllNativeEvents(),n.destroy(!0),n.removed=!0)})),i=Dt.grep(i,(e=>!t.get(e.id))),0===i.length?r([]):AO(i,(s=>{((e,t)=>e.inline&&t.tagName.toLowerCase()in o)(e,s)?lw("Could not initialize inline editor on invalid inline target element",s):((e,o,s)=>{const l=new NO(e,o,t);a.push(l),l.on("init",(()=>{++n===i.length&&r(a)})),l.targetElm=l.targetElm||s,l.render()})((e=>{let t=e.id;return t||(t=xe(e,"name").filter((e=>!RO.get(e))).getOrThunk(RO.uniqueId),e.setAttribute("id",t)),t})(s),e,s)}))};return RO.bind(window,"ready",s),new Promise((e=>{n?e(n):r=t=>{e(t)}}))},get(e){return 0===arguments.length?BO.slice(0):m(e)?J(BO,(t=>t.id===e)).getOr(null):x(e)&&BO[e]?BO[e]:null},add(e){const t=this,n=t.get(e.id);return n===e||(null===n&&BO.push(e),PO(!0),t.activeEditor=e,t.dispatch("AddEditor",{editor:e}),OO||(OO=e=>{const n=t.dispatch("BeforeUnload");if(n.returnValue)return e.preventDefault(),e.returnValue=n.returnValue,n.returnValue},window.addEventListener("beforeunload",OO))),e},createEditor(e,t){return this.add(new NO(e,t,this))},remove(e){const t=this;let n;if(e){if(!m(e))return n=e,h(t.get(n.id))?null:(LO(n)&&t.dispatch("RemoveEditor",{editor:n}),0===BO.length&&window.removeEventListener("beforeunload",OO),n.remove(),PO(BO.length>0),n);AO(RO.select(e),(e=>{n=t.get(e.id),n&&t.remove(n)}))}else for(let e=BO.length-1;e>=0;e--)t.remove(BO[e])},execCommand(e,t,n){var o;const r=this,s=f(n)?null!==(o=n.id)&&void 0!==o?o:n.index:n;switch(e){case"mceAddEditor":if(!r.get(s)){const e=n.options;new NO(s,e,r).render()}return!0;case"mceRemoveEditor":{const e=r.get(s);return e&&e.remove(),!0}case"mceToggleEditor":{const e=r.get(s);return e?(e.isHidden()?e.show():e.hide(),!0):(r.execCommand("mceAddEditor",!1,n),!0)}}return!!r.activeEditor&&r.activeEditor.execCommand(e,t,n)},triggerSave:()=>{AO(BO,(e=>{e.save()}))},addI18n:(e,t)=>{Da.add(e,t)},translate:e=>Da.translate(e),setActive(e){const t=this.activeEditor;this.activeEditor!==e&&(t&&t.dispatch("deactivate",{relatedTarget:e}),e.dispatch("activate",{relatedTarget:t})),this.activeEditor=e},_setBaseUrl(e){this.baseURL=new Ry(this.documentBaseURL).toAbsolute(e.replace(/\/+$/,"")),this.baseURI=new Ry(this.baseURL)}};IO.setup();const FO=(()=>{const e=Ma();return{FakeClipboardItem:e=>({items:e,types:me(e),getType:t=>xe(e,t).getOrUndefined()}),write:t=>{e.set(t)},read:()=>e.get().getOrUndefined(),clear:e.clear}})(),UO=Math.min,zO=Math.max,jO=Math.round,HO=(e,t,n)=>{let o=t.x,r=t.y;const s=e.w,a=e.h,i=t.w,l=t.h,d=(n||"").split("");return"b"===d[0]&&(r+=l),"r"===d[1]&&(o+=i),"c"===d[0]&&(r+=jO(l/2)),"c"===d[1]&&(o+=jO(i/2)),"b"===d[3]&&(r-=a),"r"===d[4]&&(o-=s),"c"===d[3]&&(r-=jO(a/2)),"c"===d[4]&&(o-=jO(s/2)),$O(o,r,s,a)},$O=(e,t,n,o)=>({x:e,y:t,w:n,h:o}),VO={inflate:(e,t,n)=>$O(e.x-t,e.y-n,e.w+2*t,e.h+2*n),relativePosition:HO,findBestRelativePosition:(e,t,n,o)=>{for(let r=0;r<o.length;r++){const s=HO(e,t,o[r]);if(s.x>=n.x&&s.x+s.w<=n.w+n.x&&s.y>=n.y&&s.y+s.h<=n.h+n.y)return o[r]}return null},intersect:(e,t)=>{const n=zO(e.x,t.x),o=zO(e.y,t.y),r=UO(e.x+e.w,t.x+t.w),s=UO(e.y+e.h,t.y+t.h);return r-n<0||s-o<0?null:$O(n,o,r-n,s-o)},clamp:(e,t,n)=>{let o=e.x,r=e.y,s=e.x+e.w,a=e.y+e.h;const i=t.x+t.w,l=t.y+t.h,d=zO(0,t.x-o),c=zO(0,t.y-r),u=zO(0,s-i),m=zO(0,a-l);return o+=d,r+=c,n&&(s+=d,a+=c,o-=u,r-=m),s-=u,a-=m,$O(o,r,s-o,a-r)},create:$O,fromClientRect:e=>$O(e.left,e.top,e.width,e.height)},qO=(()=>{const e={},t={};return{load:(n,o)=>{const r=`Script at URL "${o}" failed to load`,s=`Script at URL "${o}" did not call \`tinymce.Resource.add('${n}', data)\` within 1 second`;if(void 0!==e[n])return e[n];{const a=new Promise(((e,a)=>{const i=((e,t,n=1e3)=>{let o=!1,r=null;const s=e=>(...t)=>{o||(o=!0,null!==r&&(clearTimeout(r),r=null),e.apply(null,t))},a=s(e),i=s(t);return{start:(...e)=>{o||null!==r||(r=setTimeout((()=>i.apply(null,e)),n))},resolve:a,reject:i}})(e,a);t[n]=i.resolve,Ra.ScriptLoader.loadScript(o).then((()=>i.start(s)),(()=>i.reject(r)))}));return e[n]=a,a}},add:(n,o)=>{void 0!==t[n]&&(t[n](o),delete t[n]),e[n]=Promise.resolve(o)},unload:t=>{delete e[t]}}})();let WO;try{const e="__storage_test__";WO=window.localStorage,WO.setItem(e,e),WO.removeItem(e)}catch(e){WO=(()=>{let e={},t=[];const n={getItem:t=>e[t]||null,setItem:(n,o)=>{t.push(n),e[n]=String(o)},key:e=>t[e],removeItem:n=>{t=t.filter((e=>e===n)),delete e[n]},clear:()=>{t=[],e={}},length:0};return Object.defineProperty(n,"length",{get:()=>t.length,configurable:!1,enumerable:!1}),n})()}const KO={geom:{Rect:VO},util:{Delay:ng,Tools:Dt,VK:Vm,URI:Ry,EventDispatcher:nO,Observable:rO,I18n:Da,LocalStorage:WO,ImageUploader:e=>{const t=uw(),n=pw(e,t);return{upload:(t,o=!0)=>n.upload(t,o?gw(e):void 0)}}},dom:{EventUtils:ha,TreeWalker:Io,TextSeeker:ni,DOMUtils:_a,ScriptLoader:Ra,RangeUtils:kf,Serializer:TC,StyleSheetLoader:As,ControlSelection:Ym,BookmarkManager:Mm,Selection:RC,Event:ha.Event},html:{Styles:ia,Entities:Ks,Node:Rg,Schema:aa,DomParser:jy,Writer:Ig,Serializer:Fg},Env:At,AddOnManager:Pa,Annotator:Lm,Formatter:Nw,UndoManager:Aw,EditorCommands:KA,WindowManager:ow,NotificationManager:ew,EditorObservable:cO,Shortcuts:xO,Editor:NO,FocusManager:tg,EditorManager:IO,DOM:_a.DOM,ScriptLoader:Ra.ScriptLoader,PluginManager:tw,ThemeManager:nw,ModelManager:KC,IconManager:WC,Resource:qO,FakeClipboard:FO,trim:Dt.trim,isArray:Dt.isArray,is:Dt.is,toArray:Dt.toArray,makeMap:Dt.makeMap,each:Dt.each,map:Dt.map,grep:Dt.grep,inArray:Dt.inArray,extend:Dt.extend,walk:Dt.walk,resolve:Dt.resolve,explode:Dt.explode,_addCacheSuffix:Dt._addCacheSuffix},GO=Dt.extend(IO,KO);(e=>{window.tinymce=e,window.tinyMCE=e})(GO),(e=>{if("object"==typeof module)try{module.exports=e}catch(e){}})(GO)}();