{"version": 3, "file": "tom-select.popular.min.js", "sources": ["../../src/contrib/microevent.ts", "../../node_modules/@orchidjs/unicode-variants/dist/esm/regex.js", "../../node_modules/@orchidjs/unicode-variants/dist/esm/strings.js", "../../node_modules/@orchidjs/unicode-variants/dist/esm/index.js", "../../node_modules/@orchidjs/sifter/dist/esm/utils.js", "../../node_modules/@orchidjs/sifter/dist/esm/sifter.js", "../../node_modules/@orchidjs/sifter/lib/utils.ts", "../../src/vanilla.ts", "../../src/contrib/highlight.ts", "../../src/constants.ts", "../../src/defaults.ts", "../../src/utils.ts", "../../src/getSettings.ts", "../../src/tom-select.ts", "../../src/contrib/microplugin.ts", "../../src/tom-select.popular.ts", "../../src/plugins/caret_position/plugin.ts", "../../src/plugins/dropdown_input/plugin.ts", "../../src/plugins/no_backspace_delete/plugin.ts", "../../src/plugins/remove_button/plugin.ts", "../../src/plugins/restore_on_backspace/plugin.ts"], "sourcesContent": ["/**\n * MicroEvent - to make any js object an event emitter\n *\n * - pure javascript - server compatible, browser compatible\n * - dont rely on the browser doms\n * - super simple - you get it immediatly, no mistery, no magic involved\n *\n * <AUTHOR> (https://github.com/jero<PERSON>)\n */\n\ntype TCallback = (...args:any) => any;\n\n/**\n * Execute callback for each event in space separated list of event names\n *\n */\nfunction forEvents(events:string,callback:(event:string)=>any){\n\tevents.split(/\\s+/).forEach((event) =>{\n\t\tcallback(event);\n\t});\n}\n\nexport default class MicroEvent{\n\n\tpublic _events: {[key:string]:TCallback[]};\n\n\tconstructor(){\n\t\tthis._events = {};\n\t}\n\n\ton(events:string, fct:TCallback){\n\t\tforEvents(events,(event) => {\n\t\t\tconst event_array = this._events[event] || [];\n\t\t\tevent_array.push(fct);\n\t\t\tthis._events[event] = event_array;\n\t\t});\n\t}\n\n\toff(events:string, fct:TCallback){\n\t\tvar n = arguments.length;\n\t\tif( n === 0 ){\n\t\t\tthis._events = {};\n\t\t\treturn;\n\t\t}\n\n\t\tforEvents(events,(event) => {\n\n\t\t\tif (n === 1){\n\t\t\t\tdelete this._events[event];\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tconst event_array = this._events[event];\n\t\t\tif( event_array === undefined ) return;\n\n\t\t\tevent_array.splice(event_array.indexOf(fct), 1);\n\t\t\tthis._events[event] = event_array;\n\t\t});\n\t}\n\n\ttrigger(events:string, ...args:any){\n\t\tvar self = this;\n\n\t\tforEvents(events,(event) => {\n\t\t\tconst event_array = self._events[event];\n\t\t\tif( event_array === undefined ) return;\n\t\t\tevent_array.forEach(fct => {\n\t\t\t\tfct.apply(self, args );\n\t\t\t});\n\n\t\t});\n\t}\n};\n", "/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */\n/**\n * Convert array of strings to a regular expression\n *\tex ['ab','a'] => (?:ab|a)\n * \tex ['a','b'] => [ab]\n * @param {string[]} chars\n * @return {string}\n */\nconst arrayToPattern = chars => {\n  chars = chars.filter(Boolean);\n\n  if (chars.length < 2) {\n    return chars[0] || '';\n  }\n\n  return maxValueLength(chars) == 1 ? '[' + chars.join('') + ']' : '(?:' + chars.join('|') + ')';\n};\n/**\n * @param {string[]} array\n * @return {string}\n */\n\nconst sequencePattern = array => {\n  if (!hasDuplicates(array)) {\n    return array.join('');\n  }\n\n  let pattern = '';\n  let prev_char_count = 0;\n\n  const prev_pattern = () => {\n    if (prev_char_count > 1) {\n      pattern += '{' + prev_char_count + '}';\n    }\n  };\n\n  array.forEach((char, i) => {\n    if (char === array[i - 1]) {\n      prev_char_count++;\n      return;\n    }\n\n    prev_pattern();\n    pattern += char;\n    prev_char_count = 1;\n  });\n  prev_pattern();\n  return pattern;\n};\n/**\n * Convert array of strings to a regular expression\n *\tex ['ab','a'] => (?:ab|a)\n * \tex ['a','b'] => [ab]\n * @param {Set<string>} chars\n * @return {string}\n */\n\nconst setToPattern = chars => {\n  let array = toArray(chars);\n  return arrayToPattern(array);\n};\n/**\n *\n * https://stackoverflow.com/questions/7376598/in-javascript-how-do-i-check-if-an-array-has-duplicate-values\n * @param {any[]} array\n */\n\nconst hasDuplicates = array => {\n  return new Set(array).size !== array.length;\n};\n/**\n * https://stackoverflow.com/questions/63006601/why-does-u-throw-an-invalid-escape-error\n * @param {string} str\n * @return {string}\n */\n\nconst escape_regex = str => {\n  return (str + '').replace(/([\\$\\(\\)\\*\\+\\.\\?\\[\\]\\^\\{\\|\\}\\\\])/gu, '\\\\$1');\n};\n/**\n * Return the max length of array values\n * @param {string[]} array\n *\n */\n\nconst maxValueLength = array => {\n  return array.reduce((longest, value) => Math.max(longest, unicodeLength(value)), 0);\n};\n/**\n * @param {string} str\n */\n\nconst unicodeLength = str => {\n  return toArray(str).length;\n};\n/**\n * @param {any} p\n * @return {any[]}\n */\n\nconst toArray = p => Array.from(p);\n\nexport { arrayToPattern, escape_regex, hasDuplicates, maxValueLength, sequencePattern, setToPattern, toArray, unicodeLength };\n//# sourceMappingURL=regex.js.map\n", "/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */\n/**\n * Get all possible combinations of substrings that add up to the given string\n * https://stackoverflow.com/questions/30169587/find-all-the-combination-of-substrings-that-add-up-to-the-given-string\n * @param {string} input\n * @return {string[][]}\n */\nconst allSubstrings = input => {\n  if (input.length === 1) return [[input]];\n  /** @type {string[][]} */\n\n  let result = [];\n  const start = input.substring(1);\n  const suba = allSubstrings(start);\n  suba.forEach(function (subresult) {\n    let tmp = subresult.slice(0);\n    tmp[0] = input.charAt(0) + tmp[0];\n    result.push(tmp);\n    tmp = subresult.slice(0);\n    tmp.unshift(input.charAt(0));\n    result.push(tmp);\n  });\n  return result;\n};\n\nexport { allSubstrings };\n//# sourceMappingURL=strings.js.map\n", "/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */\nimport { toArray, setToPattern, escape_regex, arrayToPattern, sequencePattern } from './regex.js';\nexport { escape_regex } from './regex.js';\nimport { allSubstrings } from './strings.js';\n\n/**\n * @typedef {{[key:string]:string}} TUnicodeMap\n * @typedef {{[key:string]:Set<string>}} TUnicodeSets\n * @typedef {[[number,number]]} TCodePoints\n * @typedef {{folded:string,composed:string,code_point:number}} TCodePointObj\n * @typedef {{start:number,end:number,length:number,substr:string}} TSequencePart\n */\n/** @type {TCodePoints} */\n\nconst code_points = [[0, 65535]];\nconst accent_pat = '[\\u0300-\\u036F\\u{b7}\\u{2be}\\u{2bc}]';\n/** @type {TUnicodeMap} */\n\nlet unicode_map;\n/** @type {RegExp} */\n\nlet multi_char_reg;\nconst max_char_length = 3;\n/** @type {TUnicodeMap} */\n\nconst latin_convert = {};\n/** @type {TUnicodeMap} */\n\nconst latin_condensed = {\n  '/': '⁄∕',\n  '0': '߀',\n  \"a\": \"ⱥɐɑ\",\n  \"aa\": \"ꜳ\",\n  \"ae\": \"æǽǣ\",\n  \"ao\": \"ꜵ\",\n  \"au\": \"ꜷ\",\n  \"av\": \"ꜹꜻ\",\n  \"ay\": \"ꜽ\",\n  \"b\": \"ƀɓƃ\",\n  \"c\": \"ꜿƈȼↄ\",\n  \"d\": \"đɗɖᴅƌꮷԁɦ\",\n  \"e\": \"ɛǝᴇɇ\",\n  \"f\": \"ꝼƒ\",\n  \"g\": \"ǥɠꞡᵹꝿɢ\",\n  \"h\": \"ħⱨⱶɥ\",\n  \"i\": \"ɨı\",\n  \"j\": \"ɉȷ\",\n  \"k\": \"ƙⱪꝁꝃꝅꞣ\",\n  \"l\": \"łƚɫⱡꝉꝇꞁɭ\",\n  \"m\": \"ɱɯϻ\",\n  \"n\": \"ꞥƞɲꞑᴎлԉ\",\n  \"o\": \"øǿɔɵꝋꝍᴑ\",\n  \"oe\": \"œ\",\n  \"oi\": \"ƣ\",\n  \"oo\": \"ꝏ\",\n  \"ou\": \"ȣ\",\n  \"p\": \"ƥᵽꝑꝓꝕρ\",\n  \"q\": \"ꝗꝙɋ\",\n  \"r\": \"ɍɽꝛꞧꞃ\",\n  \"s\": \"ßȿꞩꞅʂ\",\n  \"t\": \"ŧƭʈⱦꞇ\",\n  \"th\": \"þ\",\n  \"tz\": \"ꜩ\",\n  \"u\": \"ʉ\",\n  \"v\": \"ʋꝟʌ\",\n  \"vy\": \"ꝡ\",\n  \"w\": \"ⱳ\",\n  \"y\": \"ƴɏỿ\",\n  \"z\": \"ƶȥɀⱬꝣ\",\n  \"hv\": \"ƕ\"\n};\n\nfor (let latin in latin_condensed) {\n  let unicode = latin_condensed[latin] || '';\n\n  for (let i = 0; i < unicode.length; i++) {\n    let char = unicode.substring(i, i + 1);\n    latin_convert[char] = latin;\n  }\n}\n\nconst convert_pat = new RegExp(Object.keys(latin_convert).join('|') + '|' + accent_pat, 'gu');\n/**\n * Initialize the unicode_map from the give code point ranges\n *\n * @param {TCodePoints=} _code_points\n */\n\nconst initialize = _code_points => {\n  if (unicode_map !== undefined) return;\n  unicode_map = generateMap(_code_points || code_points);\n};\n/**\n * Helper method for normalize a string\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize\n * @param {string} str\n * @param {string} form\n */\n\nconst normalize = (str, form = 'NFKD') => str.normalize(form);\n/**\n * Remove accents without reordering string\n * calling str.normalize('NFKD') on \\u{594}\\u{595}\\u{596} becomes \\u{596}\\u{594}\\u{595}\n * via https://github.com/krisk/Fuse/issues/133#issuecomment-318692703\n * @param {string} str\n * @return {string}\n */\n\nconst asciifold = str => {\n  return toArray(str).reduce(\n  /**\n   * @param {string} result\n   * @param {string} char\n   */\n  (result, char) => {\n    return result + _asciifold(char);\n  }, '');\n};\n/**\n * @param {string} str\n * @return {string}\n */\n\nconst _asciifold = str => {\n  str = normalize(str).toLowerCase().replace(convert_pat, (\n  /** @type {string} */\n  char) => {\n    return latin_convert[char] || '';\n  }); //return str;\n\n  return normalize(str, 'NFC');\n};\n/**\n * Generate a list of unicode variants from the list of code points\n * @param {TCodePoints} code_points\n * @yield {TCodePointObj}\n */\n\nfunction* generator(code_points) {\n  for (const [code_point_min, code_point_max] of code_points) {\n    for (let i = code_point_min; i <= code_point_max; i++) {\n      let composed = String.fromCharCode(i);\n      let folded = asciifold(composed);\n\n      if (folded == composed.toLowerCase()) {\n        continue;\n      } // skip when folded is a string longer than 3 characters long\n      // bc the resulting regex patterns will be long\n      // eg:\n      // folded صلى الله عليه وسلم length 18 code point 65018\n      // folded جل جلاله length 8 code point 65019\n\n\n      if (folded.length > max_char_length) {\n        continue;\n      }\n\n      if (folded.length == 0) {\n        continue;\n      }\n\n      yield {\n        folded: folded,\n        composed: composed,\n        code_point: i\n      };\n    }\n  }\n}\n/**\n * Generate a unicode map from the list of code points\n * @param {TCodePoints} code_points\n * @return {TUnicodeSets}\n */\n\nconst generateSets = code_points => {\n  /** @type {{[key:string]:Set<string>}} */\n  const unicode_sets = {};\n  /**\n   * @param {string} folded\n   * @param {string} to_add\n   */\n\n  const addMatching = (folded, to_add) => {\n    /** @type {Set<string>} */\n    const folded_set = unicode_sets[folded] || new Set();\n    const patt = new RegExp('^' + setToPattern(folded_set) + '$', 'iu');\n\n    if (to_add.match(patt)) {\n      return;\n    }\n\n    folded_set.add(escape_regex(to_add));\n    unicode_sets[folded] = folded_set;\n  };\n\n  for (let value of generator(code_points)) {\n    addMatching(value.folded, value.folded);\n    addMatching(value.folded, value.composed);\n  }\n\n  return unicode_sets;\n};\n/**\n * Generate a unicode map from the list of code points\n * ae => (?:(?:ae|Æ|Ǽ|Ǣ)|(?:A|Ⓐ|Ａ...)(?:E|ɛ|Ⓔ...))\n *\n * @param {TCodePoints} code_points\n * @return {TUnicodeMap}\n */\n\nconst generateMap = code_points => {\n  /** @type {TUnicodeSets} */\n  const unicode_sets = generateSets(code_points);\n  /** @type {TUnicodeMap} */\n\n  const unicode_map = {};\n  /** @type {string[]} */\n\n  let multi_char = [];\n\n  for (let folded in unicode_sets) {\n    let set = unicode_sets[folded];\n\n    if (set) {\n      unicode_map[folded] = setToPattern(set);\n    }\n\n    if (folded.length > 1) {\n      multi_char.push(escape_regex(folded));\n    }\n  }\n\n  multi_char.sort((a, b) => b.length - a.length);\n  const multi_char_patt = arrayToPattern(multi_char);\n  multi_char_reg = new RegExp('^' + multi_char_patt, 'u');\n  return unicode_map;\n};\n/**\n * Map each element of an array from it's folded value to all possible unicode matches\n * @param {string[]} strings\n * @param {number} min_replacement\n * @return {string}\n */\n\nconst mapSequence = (strings, min_replacement = 1) => {\n  let chars_replaced = 0;\n  strings = strings.map(str => {\n    if (unicode_map[str]) {\n      chars_replaced += str.length;\n    }\n\n    return unicode_map[str] || str;\n  });\n\n  if (chars_replaced >= min_replacement) {\n    return sequencePattern(strings);\n  }\n\n  return '';\n};\n/**\n * Convert a short string and split it into all possible patterns\n * Keep a pattern only if min_replacement is met\n *\n * 'abc'\n * \t\t=> [['abc'],['ab','c'],['a','bc'],['a','b','c']]\n *\t\t=> ['abc-pattern','ab-c-pattern'...]\n *\n *\n * @param {string} str\n * @param {number} min_replacement\n * @return {string}\n */\n\nconst substringsToPattern = (str, min_replacement = 1) => {\n  min_replacement = Math.max(min_replacement, str.length - 1);\n  return arrayToPattern(allSubstrings(str).map(sub_pat => {\n    return mapSequence(sub_pat, min_replacement);\n  }));\n};\n/**\n * Convert an array of sequences into a pattern\n * [{start:0,end:3,length:3,substr:'iii'}...] => (?:iii...)\n *\n * @param {Sequence[]} sequences\n * @param {boolean} all\n */\n\nconst sequencesToPattern = (sequences, all = true) => {\n  let min_replacement = sequences.length > 1 ? 1 : 0;\n  return arrayToPattern(sequences.map(sequence => {\n    let seq = [];\n    const len = all ? sequence.length() : sequence.length() - 1;\n\n    for (let j = 0; j < len; j++) {\n      seq.push(substringsToPattern(sequence.substrs[j] || '', min_replacement));\n    }\n\n    return sequencePattern(seq);\n  }));\n};\n/**\n * Return true if the sequence is already in the sequences\n * @param {Sequence} needle_seq\n * @param {Sequence[]} sequences\n */\n\n\nconst inSequences = (needle_seq, sequences) => {\n  for (const seq of sequences) {\n    if (seq.start != needle_seq.start || seq.end != needle_seq.end) {\n      continue;\n    }\n\n    if (seq.substrs.join('') !== needle_seq.substrs.join('')) {\n      continue;\n    }\n\n    let needle_parts = needle_seq.parts;\n    /**\n     * @param {TSequencePart} part\n     */\n\n    const filter = part => {\n      for (const needle_part of needle_parts) {\n        if (needle_part.start === part.start && needle_part.substr === part.substr) {\n          return false;\n        }\n\n        if (part.length == 1 || needle_part.length == 1) {\n          continue;\n        } // check for overlapping parts\n        // a = ['::=','==']\n        // b = ['::','===']\n        // a = ['r','sm']\n        // b = ['rs','m']\n\n\n        if (part.start < needle_part.start && part.end > needle_part.start) {\n          return true;\n        }\n\n        if (needle_part.start < part.start && needle_part.end > part.start) {\n          return true;\n        }\n      }\n\n      return false;\n    };\n\n    let filtered = seq.parts.filter(filter);\n\n    if (filtered.length > 0) {\n      continue;\n    }\n\n    return true;\n  }\n\n  return false;\n};\n\nclass Sequence {\n  constructor() {\n    /** @type {TSequencePart[]} */\n    this.parts = [];\n    /** @type {string[]} */\n\n    this.substrs = [];\n    this.start = 0;\n    this.end = 0;\n  }\n  /**\n   * @param {TSequencePart|undefined} part\n   */\n\n\n  add(part) {\n    if (part) {\n      this.parts.push(part);\n      this.substrs.push(part.substr);\n      this.start = Math.min(part.start, this.start);\n      this.end = Math.max(part.end, this.end);\n    }\n  }\n\n  last() {\n    return this.parts[this.parts.length - 1];\n  }\n\n  length() {\n    return this.parts.length;\n  }\n  /**\n   * @param {number} position\n   * @param {TSequencePart} last_piece\n   */\n\n\n  clone(position, last_piece) {\n    let clone = new Sequence();\n    let parts = JSON.parse(JSON.stringify(this.parts));\n    let last_part = parts.pop();\n\n    for (const part of parts) {\n      clone.add(part);\n    }\n\n    let last_substr = last_piece.substr.substring(0, position - last_part.start);\n    let clone_last_len = last_substr.length;\n    clone.add({\n      start: last_part.start,\n      end: last_part.start + clone_last_len,\n      length: clone_last_len,\n      substr: last_substr\n    });\n    return clone;\n  }\n\n}\n/**\n * Expand a regular expression pattern to include unicode variants\n * \teg /a/ becomes /aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐɑAⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ/\n *\n * Issue:\n *  ﺊﺋ [ 'ﺊ = \\\\u{fe8a}', 'ﺋ = \\\\u{fe8b}' ]\n *\tbecomes:\tئئ [ 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}', 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}' ]\n *\n *\tİĲ = IIJ = ⅡJ\n *\n * \t1/2/4\n *\n * @param {string} str\n * @return {string|undefined}\n */\n\n\nconst getPattern = str => {\n  initialize();\n  str = asciifold(str);\n  let pattern = '';\n  let sequences = [new Sequence()];\n\n  for (let i = 0; i < str.length; i++) {\n    let substr = str.substring(i);\n    let match = substr.match(multi_char_reg);\n    const char = str.substring(i, i + 1);\n    const match_str = match ? match[0] : null; // loop through sequences\n    // add either the char or multi_match\n\n    let overlapping = [];\n    let added_types = new Set();\n\n    for (const sequence of sequences) {\n      const last_piece = sequence.last();\n\n      if (!last_piece || last_piece.length == 1 || last_piece.end <= i) {\n        // if we have a multi match\n        if (match_str) {\n          const len = match_str.length;\n          sequence.add({\n            start: i,\n            end: i + len,\n            length: len,\n            substr: match_str\n          });\n          added_types.add('1');\n        } else {\n          sequence.add({\n            start: i,\n            end: i + 1,\n            length: 1,\n            substr: char\n          });\n          added_types.add('2');\n        }\n      } else if (match_str) {\n        let clone = sequence.clone(i, last_piece);\n        const len = match_str.length;\n        clone.add({\n          start: i,\n          end: i + len,\n          length: len,\n          substr: match_str\n        });\n        overlapping.push(clone);\n      } else {\n        // don't add char\n        // adding would create invalid patterns: 234 => [2,34,4]\n        added_types.add('3');\n      }\n    } // if we have overlapping\n\n\n    if (overlapping.length > 0) {\n      // ['ii','iii'] before ['i','i','iii']\n      overlapping = overlapping.sort((a, b) => {\n        return a.length() - b.length();\n      });\n\n      for (let clone of overlapping) {\n        // don't add if we already have an equivalent sequence\n        if (inSequences(clone, sequences)) {\n          continue;\n        }\n\n        sequences.push(clone);\n      }\n\n      continue;\n    } // if we haven't done anything unique\n    // clean up the patterns\n    // helps keep patterns smaller\n    // if str = 'r₨㎧aarss', pattern will be 446 instead of 655\n\n\n    if (i > 0 && added_types.size == 1 && !added_types.has('3')) {\n      pattern += sequencesToPattern(sequences, false);\n      let new_seq = new Sequence();\n      const old_seq = sequences[0];\n\n      if (old_seq) {\n        new_seq.add(old_seq.last());\n      }\n\n      sequences = [new_seq];\n    }\n  }\n\n  pattern += sequencesToPattern(sequences, true);\n  return pattern;\n};\n\nexport { _asciifold, asciifold, code_points, generateMap, generateSets, generator, getPattern, initialize, mapSequence, normalize, substringsToPattern, unicode_map };\n//# sourceMappingURL=index.js.map\n", "/*! sifter.js | https://github.com/orchidjs/sifter.js | Apache License (v2) */\nimport { asciifold } from '@orchidjs/unicode-variants';\n\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nconst getAttr = (obj, name) => {\n  if (!obj) return;\n  return obj[name];\n};\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\n\nconst getAttrNesting = (obj, name) => {\n  if (!obj) return;\n  var part,\n      names = name.split(\".\");\n\n  while ((part = names.shift()) && (obj = obj[part]));\n\n  return obj;\n};\n/**\n * Calculates how close of a match the\n * given value is against a search token.\n *\n */\n\nconst scoreValue = (value, token, weight) => {\n  var score, pos;\n  if (!value) return 0;\n  value = value + '';\n  if (token.regex == null) return 0;\n  pos = value.search(token.regex);\n  if (pos === -1) return 0;\n  score = token.string.length / value.length;\n  if (pos === 0) score += 0.5;\n  return score * weight;\n};\n/**\n * Cast object property to an array if it exists and has a value\n *\n */\n\nconst propToArray = (obj, key) => {\n  var value = obj[key];\n  if (typeof value == 'function') return value;\n\n  if (value && !Array.isArray(value)) {\n    obj[key] = [value];\n  }\n};\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\n\nconst iterate = (object, callback) => {\n  if (Array.isArray(object)) {\n    object.forEach(callback);\n  } else {\n    for (var key in object) {\n      if (object.hasOwnProperty(key)) {\n        callback(object[key], key);\n      }\n    }\n  }\n};\nconst cmp = (a, b) => {\n  if (typeof a === 'number' && typeof b === 'number') {\n    return a > b ? 1 : a < b ? -1 : 0;\n  }\n\n  a = asciifold(a + '').toLowerCase();\n  b = asciifold(b + '').toLowerCase();\n  if (a > b) return 1;\n  if (b > a) return -1;\n  return 0;\n};\n\nexport { cmp, getAttr, getAttrNesting, iterate, propToArray, scoreValue };\n//# sourceMappingURL=utils.js.map\n", "/*! sifter.js | https://github.com/orchidjs/sifter.js | Apache License (v2) */\nimport { iterate, cmp, propToArray, getAttrNesting, getAttr, scoreValue } from './utils.js';\nexport { cmp, getAttr, getAttrNesting, iterate, propToArray, scoreValue } from './utils.js';\nimport { escape_regex, getPattern } from '@orchidjs/unicode-variants';\nexport { getPattern } from '@orchidjs/unicode-variants';\n\n/**\n * sifter.js\n * Copyright (c) 2013–2020 Brian <PERSON> & contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\n\nclass Sifter {\n  // []|{};\n\n  /**\n   * Textually searches arrays and hashes of objects\n   * by property (or multiple properties). Designed\n   * specifically for autocomplete.\n   *\n   */\n  constructor(items, settings) {\n    this.items = void 0;\n    this.settings = void 0;\n    this.items = items;\n    this.settings = settings || {\n      diacritics: true\n    };\n  }\n\n  /**\n   * Splits a search string into an array of individual\n   * regexps to be used to match results.\n   *\n   */\n  tokenize(query, respect_word_boundaries, weights) {\n    if (!query || !query.length) return [];\n    const tokens = [];\n    const words = query.split(/\\s+/);\n    var field_regex;\n\n    if (weights) {\n      field_regex = new RegExp('^(' + Object.keys(weights).map(escape_regex).join('|') + ')\\:(.*)$');\n    }\n\n    words.forEach(word => {\n      let field_match;\n      let field = null;\n      let regex = null; // look for \"field:query\" tokens\n\n      if (field_regex && (field_match = word.match(field_regex))) {\n        field = field_match[1];\n        word = field_match[2];\n      }\n\n      if (word.length > 0) {\n        if (this.settings.diacritics) {\n          regex = getPattern(word) || null;\n        } else {\n          regex = escape_regex(word);\n        }\n\n        if (regex && respect_word_boundaries) regex = \"\\\\b\" + regex;\n      }\n\n      tokens.push({\n        string: word,\n        regex: regex ? new RegExp(regex, 'iu') : null,\n        field: field\n      });\n    });\n    return tokens;\n  }\n\n  /**\n   * Returns a function to be used to score individual results.\n   *\n   * Good matches will have a higher score than poor matches.\n   * If an item is not a match, 0 will be returned by the function.\n   *\n   * @returns {T.ScoreFn}\n   */\n  getScoreFunction(query, options) {\n    var search = this.prepareSearch(query, options);\n    return this._getScoreFunction(search);\n  }\n  /**\n   * @returns {T.ScoreFn}\n   *\n   */\n\n\n  _getScoreFunction(search) {\n    const tokens = search.tokens,\n          token_count = tokens.length;\n\n    if (!token_count) {\n      return function () {\n        return 0;\n      };\n    }\n\n    const fields = search.options.fields,\n          weights = search.weights,\n          field_count = fields.length,\n          getAttrFn = search.getAttrFn;\n\n    if (!field_count) {\n      return function () {\n        return 1;\n      };\n    }\n    /**\n     * Calculates the score of an object\n     * against the search query.\n     *\n     */\n\n\n    const scoreObject = function () {\n      if (field_count === 1) {\n        return function (token, data) {\n          const field = fields[0].field;\n          return scoreValue(getAttrFn(data, field), token, weights[field] || 1);\n        };\n      }\n\n      return function (token, data) {\n        var sum = 0; // is the token specific to a field?\n\n        if (token.field) {\n          const value = getAttrFn(data, token.field);\n\n          if (!token.regex && value) {\n            sum += 1 / field_count;\n          } else {\n            sum += scoreValue(value, token, 1);\n          }\n        } else {\n          iterate(weights, (weight, field) => {\n            sum += scoreValue(getAttrFn(data, field), token, weight);\n          });\n        }\n\n        return sum / field_count;\n      };\n    }();\n\n    if (token_count === 1) {\n      return function (data) {\n        return scoreObject(tokens[0], data);\n      };\n    }\n\n    if (search.options.conjunction === 'and') {\n      return function (data) {\n        var score,\n            sum = 0;\n\n        for (let token of tokens) {\n          score = scoreObject(token, data);\n          if (score <= 0) return 0;\n          sum += score;\n        }\n\n        return sum / token_count;\n      };\n    } else {\n      return function (data) {\n        var sum = 0;\n        iterate(tokens, token => {\n          sum += scoreObject(token, data);\n        });\n        return sum / token_count;\n      };\n    }\n  }\n\n  /**\n   * Returns a function that can be used to compare two\n   * results, for sorting purposes. If no sorting should\n   * be performed, `null` will be returned.\n   *\n   * @return function(a,b)\n   */\n  getSortFunction(query, options) {\n    var search = this.prepareSearch(query, options);\n    return this._getSortFunction(search);\n  }\n\n  _getSortFunction(search) {\n    var implicit_score,\n        sort_flds = [];\n    const self = this,\n          options = search.options,\n          sort = !search.query && options.sort_empty ? options.sort_empty : options.sort;\n\n    if (typeof sort == 'function') {\n      return sort.bind(this);\n    }\n    /**\n     * Fetches the specified sort field value\n     * from a search result item.\n     *\n     */\n\n\n    const get_field = function get_field(name, result) {\n      if (name === '$score') return result.score;\n      return search.getAttrFn(self.items[result.id], name);\n    }; // parse options\n\n\n    if (sort) {\n      for (let s of sort) {\n        if (search.query || s.field !== '$score') {\n          sort_flds.push(s);\n        }\n      }\n    } // the \"$score\" field is implied to be the primary\n    // sort field, unless it's manually specified\n\n\n    if (search.query) {\n      implicit_score = true;\n\n      for (let fld of sort_flds) {\n        if (fld.field === '$score') {\n          implicit_score = false;\n          break;\n        }\n      }\n\n      if (implicit_score) {\n        sort_flds.unshift({\n          field: '$score',\n          direction: 'desc'\n        });\n      } // without a search.query, all items will have the same score\n\n    } else {\n      sort_flds = sort_flds.filter(fld => fld.field !== '$score');\n    } // build function\n\n\n    const sort_flds_count = sort_flds.length;\n\n    if (!sort_flds_count) {\n      return null;\n    }\n\n    return function (a, b) {\n      var result, field;\n\n      for (let sort_fld of sort_flds) {\n        field = sort_fld.field;\n        let multiplier = sort_fld.direction === 'desc' ? -1 : 1;\n        result = multiplier * cmp(get_field(field, a), get_field(field, b));\n        if (result) return result;\n      }\n\n      return 0;\n    };\n  }\n\n  /**\n   * Parses a search query and returns an object\n   * with tokens and fields ready to be populated\n   * with results.\n   *\n   */\n  prepareSearch(query, optsUser) {\n    const weights = {};\n    var options = Object.assign({}, optsUser);\n    propToArray(options, 'sort');\n    propToArray(options, 'sort_empty'); // convert fields to new format\n\n    if (options.fields) {\n      propToArray(options, 'fields');\n      const fields = [];\n      options.fields.forEach(field => {\n        if (typeof field == 'string') {\n          field = {\n            field: field,\n            weight: 1\n          };\n        }\n\n        fields.push(field);\n        weights[field.field] = 'weight' in field ? field.weight : 1;\n      });\n      options.fields = fields;\n    }\n\n    return {\n      options: options,\n      query: query.toLowerCase().trim(),\n      tokens: this.tokenize(query, options.respect_word_boundaries, weights),\n      total: 0,\n      items: [],\n      weights: weights,\n      getAttrFn: options.nesting ? getAttrNesting : getAttr\n    };\n  }\n\n  /**\n   * Searches through all items and returns a sorted array of matches.\n   *\n   */\n  search(query, options) {\n    var self = this,\n        score,\n        search;\n    search = this.prepareSearch(query, options);\n    options = search.options;\n    query = search.query; // generate result scoring function\n\n    const fn_score = options.score || self._getScoreFunction(search); // perform search and sort\n\n\n    if (query.length) {\n      iterate(self.items, (item, id) => {\n        score = fn_score(item);\n\n        if (options.filter === false || score > 0) {\n          search.items.push({\n            'score': score,\n            'id': id\n          });\n        }\n      });\n    } else {\n      iterate(self.items, (_, id) => {\n        search.items.push({\n          'score': 1,\n          'id': id\n        });\n      });\n    }\n\n    const fn_sort = self._getSortFunction(search);\n\n    if (fn_sort) search.items.sort(fn_sort); // apply limits\n\n    search.total = search.items.length;\n\n    if (typeof options.limit === 'number') {\n      search.items = search.items.slice(0, options.limit);\n    }\n\n    return search;\n  }\n\n}\n\nexport { Sifter };\n//# sourceMappingURL=sifter.js.map\n", "\nimport { asciifold } from '@orchidjs/unicode-variants';\nimport * as T from './types';\n\n\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttr = (obj:{[key:string]:any}, name:string ) => {\n    if (!obj ) return;\n    return obj[name];\n};\n\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttrNesting = (obj:{[key:string]:any}, name:string ) => {\n    if (!obj ) return;\n    var part, names = name.split(\".\");\n\twhile( (part = names.shift()) && (obj = obj[part]));\n    return obj;\n};\n\n/**\n * Calculates how close of a match the\n * given value is against a search token.\n *\n */\nexport const scoreValue = (value:string, token:T.Token, weight:number ):number => {\n\tvar score, pos;\n\n\tif (!value) return 0;\n\n\tvalue = value + '';\n\tif( token.regex == null ) return 0;\n\tpos = value.search(token.regex);\n\tif (pos === -1) return 0;\n\n\tscore = token.string.length / value.length;\n\tif (pos === 0) score += 0.5;\n\n\treturn score * weight;\n};\n\n\n/**\n * Cast object property to an array if it exists and has a value\n *\n */\nexport const propToArray = (obj:{[key:string]:any}, key:string) => {\n\tvar value = obj[key];\n\n\tif( typeof value == 'function' ) return value;\n\n\tif( value && !Array.isArray(value) ){\n\t\tobj[key] = [value];\n\t}\n}\n\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n\n\n\nexport const cmp = (a:number|string, b:number|string) => {\n\tif (typeof a === 'number' && typeof b === 'number') {\n\t\treturn a > b ? 1 : (a < b ? -1 : 0);\n\t}\n\ta = asciifold(a + '').toLowerCase();\n\tb = asciifold(b + '').toLowerCase();\n\tif (a > b) return 1;\n\tif (b > a) return -1;\n\treturn 0;\n};\n", "\nimport { iterate } from '@orchidjs/sifter/lib/utils';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\11\\12\\14\\15\\40]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * highlight v3 | MIT license | <PERSON> <<EMAIL>>\n * Highlights arbitrary terms in a node.\n *\n * - Modified by <PERSON> <<EMAIL>> 2011-6-24 (added regex)\n * - Modified by <PERSON> <<EMAIL>> 2012-8-27 (cleanup)\n */\n\nimport {replaceNode} from '../vanilla';\n\n\nexport const highlight = (element:HTMLElement, regex:string|RegExp) => {\n\n\tif( regex === null ) return;\n\n\t// convet string to regex\n\tif( typeof regex === 'string' ){\n\n\t\tif( !regex.length ) return;\n\t\tregex = new RegExp(regex, 'i');\n\t}\n\n\n\t// Wrap matching part of text node with highlighting <span>, e.g.\n\t// Soccer  ->  <span class=\"highlight\">Soc</span>cer  for regex = /soc/i\n\tconst highlightText = ( node:Text ):number => {\n\n\t\tvar match = node.data.match(regex);\n\t\tif( match && node.data.length > 0 ){\n\t\t\tvar spannode\t\t= document.createElement('span');\n\t\t\tspannode.className\t= 'highlight';\n\t\t\tvar middlebit\t\t= node.splitText(match.index as number);\n\n\t\t\tmiddlebit.splitText(match[0]!.length);\n\t\t\tvar middleclone\t\t= middlebit.cloneNode(true);\n\n\t\t\tspannode.appendChild(middleclone);\n\t\t\treplaceNode(middlebit, spannode);\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn 0;\n\t};\n\n\t// Recurse element node, looking for child text nodes to highlight, unless element\n\t// is childless, <script>, <style>, or already highlighted: <span class=\"hightlight\">\n\tconst highlightChildren = ( node:Element ):void => {\n\t\tif( node.nodeType === 1 && node.childNodes && !/(script|style)/i.test(node.tagName) && ( node.className !== 'highlight' || node.tagName !== 'SPAN' ) ){\n\t\t\tArray.from(node.childNodes).forEach(element => {\n\t\t\t\thighlightRecursive(element);\n\t\t\t});\n\t\t}\n\t};\n\n\n\tconst highlightRecursive = ( node:Node|Element ):number => {\n\n\t\tif( node.nodeType === 3 ){\n\t\t\treturn highlightText(node as Text);\n\t\t}\n\n\t\thighlightChildren(node as Element);\n\n\t\treturn 0;\n\t};\n\n\thighlightRecursive( element );\n};\n\n/**\n * removeHighlight fn copied from highlight v5 and\n * edited to remove with(), pass js strict mode, and use without jquery\n */\nexport const removeHighlight = (el:HTMLElement) => {\n\tvar elements = el.querySelectorAll(\"span.highlight\");\n\tArray.prototype.forEach.call(elements, function(el:HTMLElement){\n\t\tvar parent = el.parentNode as Node;\n\t\tparent.replaceChild(el.firstChild as Node, el);\n\t\tparent.normalize();\n\t});\n};\n", "export const KEY_A\t\t\t\t= 65;\nexport const KEY_RETURN\t\t\t= 13;\nexport const KEY_ESC\t\t\t= 27;\nexport const KEY_LEFT\t\t\t= 37;\nexport const KEY_UP\t\t\t\t= 38;\nexport const KEY_RIGHT\t\t\t= 39;\nexport const KEY_DOWN\t\t\t= 40;\nexport const KEY_BACKSPACE\t\t= 8;\nexport const KEY_DELETE\t\t\t= 46;\nexport const KEY_TAB\t\t\t= 9;\n\nexport const IS_MAC      \t\t= typeof navigator === 'undefined' ? false : /Mac/.test(navigator.userAgent);\nexport const KEY_SHORTCUT\t\t= IS_MAC ? 'metaKey' : 'ctrlKey'; // ctrl key or apple key for ma\n", "\nexport default {\n\toptions: [],\n\toptgroups: [],\n\n\tplugins: [],\n\tdelimiter: ',',\n\tsplitOn: null, // regexp or string for splitting up values from a paste command\n\tpersist: true,\n\tdiacritics: true,\n\tcreate: null,\n\tcreateOnBlur: false,\n\tcreateFilter: null,\n\thighlight: true,\n\topenOnFocus: true,\n\tshouldOpen: null,\n\tmaxOptions: 50,\n\tmaxItems: null,\n\thideSelected: null,\n\tduplicates: false,\n\taddPrecedence: false,\n\tselectOnTab: false,\n\tpreload: null,\n\tallowEmptyOption: false,\n\t//closeAfterSelect: false,\n\n\tloadThrottle: 300,\n\tloadingClass: 'loading',\n\n\tdataAttr: null, //'data-data',\n\toptgroupField: 'optgroup',\n\tvalueField: 'value',\n\tlabelField: 'text',\n\tdisabledField: 'disabled',\n\toptgroupLabelField: 'label',\n\toptgroupValueField: 'value',\n\tlockOptgroupOrder: false,\n\n\tsortField: '$order',\n\tsearchField: ['text'],\n\tsearchConjunction: 'and',\n\n\tmode: null,\n\twrapperClass: 'ts-wrapper',\n\tcontrolClass: 'ts-control',\n\tdropdownClass: 'ts-dropdown',\n\tdropdownContentClass: 'ts-dropdown-content',\n\titemClass: 'item',\n\toptionClass: 'option',\n\n\tdropdownParent: null,\n\tcontrolInput: '<input type=\"text\" autocomplete=\"off\" size=\"1\" />',\n\n\tcopyClassesToDropdown: false,\n\n\tplaceholder: null,\n\thidePlaceholder: null,\n\n\tshouldLoad: function(query:string):boolean{\n\t\treturn query.length > 0;\n\t},\n\n\t/*\n\tload                 : null, // function(query, callback) { ... }\n\tscore                : null, // function(search) { ... }\n\tonInitialize         : null, // function() { ... }\n\tonChange             : null, // function(value) { ... }\n\tonItemAdd            : null, // function(value, $item) { ... }\n\tonItemRemove         : null, // function(value) { ... }\n\tonClear              : null, // function() { ... }\n\tonOptionAdd          : null, // function(value, data) { ... }\n\tonOptionRemove       : null, // function(value) { ... }\n\tonOptionClear        : null, // function() { ... }\n\tonOptionGroupAdd     : null, // function(id, data) { ... }\n\tonOptionGroupRemove  : null, // function(id) { ... }\n\tonOptionGroupClear   : null, // function() { ... }\n\tonDropdownOpen       : null, // function(dropdown) { ... }\n\tonDropdownClose      : null, // function(dropdown) { ... }\n\tonType               : null, // function(str) { ... }\n\tonDelete             : null, // function(values) { ... }\n\t*/\n\n\trender: {\n\t\t/*\n\t\titem: null,\n\t\toptgroup: null,\n\t\toptgroup_header: null,\n\t\toption: null,\n\t\toption_create: null\n\t\t*/\n\t}\n};\n", "\nimport TomSelect from './tom-select';\nimport { TomLoadCallback } from './types/index';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n", "import defaults from './defaults';\nimport { hash_key } from './utils';\nimport { TomOption, TomSettings, RecursivePartial } from './types/index';\nimport { iterate } from '@orchidjs/sifter/lib/utils';\nimport { TomInput } from './types/index';\n\n\nexport default function getSettings( input:TomInput, settings_user:RecursivePartial<TomSettings>):TomSettings{\n\tvar settings:TomSettings\t= Object.assign({}, defaults, settings_user);\n\n\tvar attr_data\t\t\t\t= settings.dataAttr;\n\tvar field_label\t\t\t\t= settings.labelField;\n\tvar field_value\t\t\t\t= settings.valueField;\n\tvar field_disabled\t\t\t= settings.disabledField;\n\tvar field_optgroup\t\t\t= settings.optgroupField;\n\tvar field_optgroup_label\t= settings.optgroupLabelField;\n\tvar field_optgroup_value\t= settings.optgroupValueField;\n\n\tvar tag_name\t\t\t\t= input.tagName.toLowerCase();\n\tvar placeholder\t\t\t\t= input.getAttribute('placeholder') || input.getAttribute('data-placeholder');\n\n\tif (!placeholder && !settings.allowEmptyOption) {\n\t\tlet option\t\t= input.querySelector('option[value=\"\"]');\n\t\tif( option ){\n\t\t\tplaceholder = option.textContent;\n\t\t}\n\n\t}\n\n\tvar settings_element:{\n\t\tplaceholder\t: null|string,\n\t\toptions\t\t: TomOption[],\n\t\toptgroups\t: TomOption[],\n\t\titems\t\t: string[],\n\t\tmaxItems\t: null|number,\n\t} = {\n\t\tplaceholder\t: placeholder,\n\t\toptions\t\t: [],\n\t\toptgroups\t: [],\n\t\titems\t\t: [],\n\t\tmaxItems\t: null,\n\t};\n\n\n\t/**\n\t * Initialize from a <select> element.\n\t *\n\t */\n\tvar init_select = () => {\n\t\tvar tagName;\n\t\tvar options = settings_element.options;\n\t\tvar optionsMap:{[key:string]:any} = {};\n\t\tvar group_count = 1;\n\n\t\tvar readData = (el:HTMLElement):TomOption => {\n\n\t\t\tvar data\t= Object.assign({},el.dataset); // get plain object from DOMStringMap\n\t\t\tvar json\t= attr_data && data[attr_data];\n\n\t\t\tif( typeof json === 'string' && json.length ){\n\t\t\t\tdata = Object.assign(data,JSON.parse(json));\n\t\t\t}\n\n\t\t\treturn data;\n\t\t};\n\n\t\tvar addOption = (option:HTMLOptionElement, group?:string) => {\n\n\t\t\tvar value = hash_key(option.value);\n\t\t\tif ( value == null ) return;\n\t\t\tif ( !value && !settings.allowEmptyOption) return;\n\n\t\t\t// if the option already exists, it's probably been\n\t\t\t// duplicated in another optgroup. in this case, push\n\t\t\t// the current group to the \"optgroup\" property on the\n\t\t\t// existing option so that it's rendered in both places.\n\t\t\tif (optionsMap.hasOwnProperty(value)) {\n\t\t\t\tif (group) {\n\t\t\t\t\tvar arr = optionsMap[value][field_optgroup];\n\t\t\t\t\tif (!arr) {\n\t\t\t\t\t\toptionsMap[value][field_optgroup] = group;\n\t\t\t\t\t} else if (!Array.isArray(arr)) {\n\t\t\t\t\t\toptionsMap[value][field_optgroup] = [arr, group];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tarr.push(group);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}else{\n\n\t\t\t\tvar option_data             = readData(option);\n\t\t\t\toption_data[field_label]    = option_data[field_label] || option.textContent;\n\t\t\t\toption_data[field_value]    = option_data[field_value] || value;\n\t\t\t\toption_data[field_disabled] = option_data[field_disabled] || option.disabled;\n\t\t\t\toption_data[field_optgroup] = option_data[field_optgroup] || group;\n\t\t\t\toption_data.$option\t\t\t= option;\n\n\t\t\t\toptionsMap[value] = option_data;\n\t\t\t\toptions.push(option_data);\n\t\t\t}\n\n\t\t\tif( option.selected ){\n\t\t\t\tsettings_element.items.push(value);\n\t\t\t}\n\t\t};\n\n\t\tvar addGroup = ( optgroup:HTMLOptGroupElement ) => {\n\t\t\tvar id:string, optgroup_data\n\n\t\t\toptgroup_data\t\t\t\t\t\t\t= readData(optgroup);\n\t\t\toptgroup_data[field_optgroup_label]\t\t= optgroup_data[field_optgroup_label] || optgroup.getAttribute('label') || '';\n\t\t\toptgroup_data[field_optgroup_value]\t\t= optgroup_data[field_optgroup_value] || group_count++;\n\t\t\toptgroup_data[field_disabled]\t\t\t= optgroup_data[field_disabled] || optgroup.disabled;\n\t\t\tsettings_element.optgroups.push(optgroup_data);\n\n\t\t\tid = optgroup_data[field_optgroup_value];\n\n\t\t\titerate(optgroup.children, (option)=>{\n\t\t\t\taddOption(option as HTMLOptionElement, id);\n\t\t\t});\n\n\t\t};\n\n\t\tsettings_element.maxItems = input.hasAttribute('multiple') ? null : 1;\n\n\t\titerate(input.children,(child)=>{\n\t\t\ttagName = child.tagName.toLowerCase();\n\t\t\tif (tagName === 'optgroup') {\n\t\t\t\taddGroup(child as HTMLOptGroupElement);\n\t\t\t} else if (tagName === 'option') {\n\t\t\t\taddOption(child as HTMLOptionElement);\n\t\t\t}\n\t\t});\n\n\t};\n\n\n\t/**\n\t * Initialize from a <input type=\"text\"> element.\n\t *\n\t */\n\tvar init_textbox = () => {\n\t\tconst data_raw = input.getAttribute(attr_data);\n\n\t\tif (!data_raw) {\n\t\t\tvar value = input.value.trim() || '';\n\t\t\tif (!settings.allowEmptyOption && !value.length) return;\n\t\t\tconst values = value.split(settings.delimiter);\n\n\t\t\titerate( values, (value) => {\n\t\t\t\tconst option:TomOption = {};\n\t\t\t\toption[field_label] = value;\n\t\t\t\toption[field_value] = value;\n\t\t\t\tsettings_element.options.push(option);\n\t\t\t});\n\t\t\tsettings_element.items = values;\n\t\t} else {\n\t\t\tsettings_element.options = JSON.parse(data_raw);\n\t\t\titerate( settings_element.options, (opt) => {\n\t\t\t\tsettings_element.items.push(opt[field_value]);\n\t\t\t});\n\t\t}\n\t};\n\n\n\tif (tag_name === 'select') {\n\t\tinit_select();\n\t} else {\n\t\tinit_textbox();\n\t}\n\n\treturn Object.assign( {}, defaults, settings_element, settings_user) as TomSettings;\n};\n", "\nimport MicroEvent from './contrib/microevent';\nimport MicroPlugin from './contrib/microplugin';\nimport { Sifter, iterate } from '@orchidjs/sifter';\nimport { escape_regex } from '@orchidjs/unicode-variants';\nimport { TomInput, TomArgObject, TomO<PERSON>, TomOptions, TomCreateFilter, TomCreateCallback, TomItem, TomSettings, TomTemplateNames, TomClearFilter, RecursivePartial } from './types/index';\nimport {highlight, removeHighlight} from './contrib/highlight';\nimport * as constants from './constants';\nimport getSettings from './getSettings';\nimport {\n\thash_key,\n\tget_hash,\n\tescape_html,\n\tdebounce_events,\n\tgetSelection,\n\tpreventDefault,\n\taddEvent,\n\tloadDebounce,\n\tisKeyDown,\n\tgetId,\n\taddSlashes,\n\tappend\n} from './utils';\n\nimport {\n\tgetDom,\n\tisHtmlString,\n\tescapeQuery,\n\ttriggerEvent,\n\tapplyCSS,\n\taddClasses,\n\tremoveClasses,\n\tparentMatch,\n\tgetTail,\n\tisEmptyObject,\n\tnodeIndex,\n\tsetAttr,\n\treplaceNode\n} from './vanilla';\n\nvar instance_i = 0;\n\nexport default class TomSelect extends MicroPlugin(MicroEvent){\n\n\tpublic control_input\t\t\t: HTMLInputElement;\n\tpublic wrapper\t\t\t\t\t: HTMLElement;\n\tpublic dropdown\t\t\t\t\t: HTMLElement;\n\tpublic control\t\t\t\t\t: HTMLElement;\n\tpublic dropdown_content\t\t\t: HTMLElement;\n\tpublic focus_node\t\t\t\t: HTMLElement;\n\n\tpublic order\t\t\t\t\t: number = 0;\n\tpublic settings\t\t\t\t\t: TomSettings;\n\tpublic input\t\t\t\t\t: TomInput;\n\tpublic tabIndex\t\t\t\t\t: number;\n\tpublic is_select_tag\t\t\t: boolean;\n\tpublic rtl\t\t\t\t\t\t: boolean;\n\tprivate inputId\t\t\t\t\t: string;\n\n\tprivate _destroy\t\t\t\t!: () => void;\n\tpublic sifter\t\t\t\t\t: Sifter;\n\n\n\tpublic isOpen\t\t\t\t\t: boolean = false;\n\tpublic isDisabled\t\t\t\t: boolean = false;\n\tpublic isRequired\t\t\t\t: boolean;\n\tpublic isInvalid\t\t\t\t: boolean = false; // @deprecated 1.8\n\tpublic isValid\t\t\t\t\t: boolean = true;\n\tpublic isLocked\t\t\t\t\t: boolean = false;\n\tpublic isFocused\t\t\t\t: boolean = false;\n\tpublic isInputHidden\t\t\t: boolean = false;\n\tpublic isSetup\t\t\t\t\t: boolean = false;\n\tpublic ignoreFocus\t\t\t\t: boolean = false;\n\tpublic ignoreHover\t\t\t\t: boolean = false;\n\tpublic hasOptions\t\t\t\t: boolean = false;\n\tpublic currentResults\t\t\t?: ReturnType<Sifter['search']>;\n\tpublic lastValue\t\t\t\t: string = '';\n\tpublic caretPos\t\t\t\t\t: number = 0;\n\tpublic loading\t\t\t\t\t: number = 0;\n\tpublic loadedSearches\t\t\t: { [key: string]: boolean } = {};\n\n\tpublic activeOption\t\t\t\t: null|HTMLElement = null;\n\tpublic activeItems\t\t\t\t: TomItem[] = [];\n\n\tpublic optgroups\t\t\t\t: TomOptions = {};\n\tpublic options\t\t\t\t\t: TomOptions = {};\n\tpublic userOptions\t\t\t\t: {[key:string]:boolean} = {};\n\tpublic items\t\t\t\t\t: string[] = [];\n\n\n\n\tconstructor( input_arg: string|TomInput, user_settings:RecursivePartial<TomSettings> ){\n\t\tsuper();\n\n\t\tinstance_i++;\n\n\t\tvar dir;\n\t\tvar input\t\t\t\t= getDom( input_arg ) as TomInput;\n\n\t\tif( input.tomselect ){\n\t\t\tthrow new Error('Tom Select already initialized on this element');\n\t\t}\n\n\n\t\tinput.tomselect\t\t\t= this;\n\n\n\t\t// detect rtl environment\n\t\tvar computedStyle\t\t= window.getComputedStyle && window.getComputedStyle(input, null);\n\t\tdir\t\t\t\t\t\t= computedStyle.getPropertyValue('direction');\n\n\t\t// setup default state\n\t\tconst settings\t\t\t= getSettings( input, user_settings );\n\t\tthis.settings\t\t\t= settings;\n\t\tthis.input\t\t\t\t= input;\n\t\tthis.tabIndex\t\t\t= input.tabIndex || 0;\n\t\tthis.is_select_tag\t\t= input.tagName.toLowerCase() === 'select';\n\t\tthis.rtl\t\t\t\t= /rtl/i.test(dir);\n\t\tthis.inputId\t\t\t= getId(input, 'tomselect-'+instance_i);\n\t\tthis.isRequired\t\t\t= input.required;\n\n\n\t\t// search system\n\t\tthis.sifter = new Sifter(this.options, {diacritics: settings.diacritics});\n\n\t\t// option-dependent defaults\n\t\tsettings.mode = settings.mode || (settings.maxItems === 1 ? 'single' : 'multi');\n\t\tif (typeof settings.hideSelected !== 'boolean') {\n\t\t\tsettings.hideSelected = settings.mode === 'multi';\n\t\t}\n\n\t\tif( typeof settings.hidePlaceholder !== 'boolean' ){\n\t\t\tsettings.hidePlaceholder = settings.mode !== 'multi';\n\t\t}\n\n\t\t// set up createFilter callback\n\t\tvar filter = settings.createFilter;\n\t\tif( typeof filter !== 'function' ){\n\n\t\t\tif( typeof filter === 'string' ){\n\t\t\t\tfilter = new RegExp(filter);\n\t\t\t}\n\n\t\t\tif( filter instanceof RegExp ){\n\t\t\t\tsettings.createFilter = (input) => (filter as RegExp).test(input);\n\t\t\t}else{\n\t\t\t\tsettings.createFilter = (value) => {\n\t\t\t\t\treturn this.settings.duplicates || !this.options[value];\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\n\t\tthis.initializePlugins(settings.plugins);\n\t\tthis.setupCallbacks();\n\t\tthis.setupTemplates();\n\n\n\t\t// Create all elements\n\t\tconst wrapper\t\t\t= getDom('<div>');\n\t\tconst control\t\t\t= getDom('<div>');\n\t\tconst dropdown\t\t\t= this._render('dropdown');\n\t\tconst dropdown_content\t= getDom(`<div role=\"listbox\" tabindex=\"-1\">`);\n\n\t\tconst classes\t\t\t= this.input.getAttribute('class') || '';\n\t\tconst inputMode\t\t\t= settings.mode;\n\n\t\tvar control_input: HTMLInputElement;\n\n\n\t\taddClasses( wrapper, settings.wrapperClass, classes, inputMode);\n\n\n\t\taddClasses(control,settings.controlClass);\n\t\tappend( wrapper, control );\n\n\n\t\taddClasses(dropdown, settings.dropdownClass, inputMode);\n\t\tif( settings.copyClassesToDropdown ){\n\t\t\taddClasses( dropdown, classes);\n\t\t}\n\n\n\t\taddClasses(dropdown_content, settings.dropdownContentClass);\n\t\tappend( dropdown, dropdown_content );\n\n\t\tgetDom( settings.dropdownParent || wrapper ).appendChild( dropdown );\n\n\n\t\t// default controlInput\n\t\tif( isHtmlString(settings.controlInput) ){\n\t\t\tcontrol_input\t\t= getDom(settings.controlInput ) as HTMLInputElement;\n\n\t\t\t// set attributes\n\t\t\tvar attrs = ['autocorrect','autocapitalize','autocomplete'];\n\t\t\titerate(attrs,(attr:string) => {\n\t\t\t\tif( input.getAttribute(attr) ){\n\t\t\t\t\tsetAttr(control_input,{[attr]:input.getAttribute(attr)});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tcontrol_input.tabIndex = -1;\n\t\t\tcontrol.appendChild( control_input );\n\t\t\tthis.focus_node\t\t= control_input;\n\n\t\t// dom element\n\t\t}else if( settings.controlInput ){\n\t\t\tcontrol_input\t\t= getDom( settings.controlInput ) as HTMLInputElement;\n\t\t\tthis.focus_node\t\t= control_input;\n\n\t\t}else{\n\t\t\tcontrol_input\t\t= getDom('<input/>') as HTMLInputElement;\n\t\t\tthis.focus_node\t\t= control;\n\t\t}\n\n\t\tthis.wrapper\t\t\t= wrapper;\n\t\tthis.dropdown\t\t\t= dropdown;\n\t\tthis.dropdown_content\t= dropdown_content;\n\t\tthis.control \t\t\t= control;\n\t\tthis.control_input\t\t= control_input;\n\n\t\tthis.setup();\n\t}\n\n\t/**\n\t * set up event bindings.\n\t *\n\t */\n\tsetup(){\n\n\t\tconst self = this;\n\t\tconst settings\t\t\t\t= self.settings;\n\t\tconst control_input\t\t\t= self.control_input;\n\t\tconst dropdown\t\t\t\t= self.dropdown;\n\t\tconst dropdown_content\t\t= self.dropdown_content;\n\t\tconst wrapper\t\t\t\t= self.wrapper;\n\t\tconst control\t\t\t\t= self.control;\n\t\tconst input\t\t\t\t\t= self.input;\n\t\tconst focus_node\t\t\t= self.focus_node;\n\t\tconst passive_event\t\t\t= { passive: true };\n\t\tconst listboxId\t\t\t\t= self.inputId +'-ts-dropdown';\n\n\n\t\tsetAttr(dropdown_content,{\n\t\t\tid: listboxId\n\t\t});\n\n\t\tsetAttr(focus_node,{\n\t\t\trole:'combobox',\n\t\t\t'aria-haspopup':'listbox',\n\t\t\t'aria-expanded':'false',\n\t\t\t'aria-controls':listboxId\n\t\t});\n\n\t\tconst control_id\t= getId(focus_node,self.inputId + '-ts-control');\n\t\tconst query\t\t\t= \"label[for='\"+escapeQuery(self.inputId)+\"']\";\n\t\tconst label\t\t\t= document.querySelector(query);\n\t\tconst label_click\t= self.focus.bind(self);\n\t\tif( label ){\n\t\t\taddEvent(label,'click', label_click );\n\t\t\tsetAttr(label,{for:control_id});\n\t\t\tconst label_id = getId(label,self.inputId+'-ts-label');\n\t\t\tsetAttr(focus_node,{'aria-labelledby':label_id});\n\t\t\tsetAttr(dropdown_content,{'aria-labelledby':label_id});\n\t\t}\n\n\t\twrapper.style.width = input.style.width;\n\n\t\tif (self.plugins.names.length) {\n\t\t\tconst classes_plugins = 'plugin-' + self.plugins.names.join(' plugin-');\n\t\t\taddClasses( [wrapper,dropdown], classes_plugins);\n\t\t}\n\n\t\tif ((settings.maxItems === null || settings.maxItems > 1) && self.is_select_tag ){\n\t\t\tsetAttr(input,{multiple:'multiple'});\n\t\t}\n\n\t\tif (settings.placeholder) {\n\t\t\tsetAttr(control_input,{placeholder:settings.placeholder});\n\t\t}\n\n\t\t// if splitOn was not passed in, construct it from the delimiter to allow pasting universally\n\t\tif (!settings.splitOn && settings.delimiter) {\n\t\t\tsettings.splitOn = new RegExp('\\\\s*' + escape_regex(settings.delimiter) + '+\\\\s*');\n\t\t}\n\n\t\t// debounce user defined load() if loadThrottle > 0\n\t\t// after initializePlugins() so plugins can create/modify user defined loaders\n\t\tif( settings.load && settings.loadThrottle ){\n\t\t\tsettings.load = loadDebounce(settings.load,settings.loadThrottle)\n\t\t}\n\n\t\tself.control_input.type\t= input.type;\n\n\t\taddEvent(dropdown,'mousemove', () => {\n\t\t\tself.ignoreHover = false;\n\t\t});\n\n\t\taddEvent(dropdown,'mouseenter', (e) => {\n\n\t\t\tvar target_match = parentMatch(e.target as HTMLElement, '[data-selectable]', dropdown);\n\t\t\tif( target_match ) self.onOptionHover( e as MouseEvent, target_match );\n\n\t\t}, {capture:true});\n\n\t\t// clicking on an option should select it\n\t\taddEvent(dropdown,'click',(evt) => {\n\t\t\tconst option = parentMatch(evt.target as HTMLElement, '[data-selectable]');\n\t\t\tif( option ){\n\t\t\t\tself.onOptionSelect( evt as MouseEvent, option );\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t}\n\t\t});\n\n\t\taddEvent(control,'click', (evt) => {\n\n\t\t\tvar target_match = parentMatch( evt.target as HTMLElement, '[data-ts-item]', control);\n\t\t\tif( target_match && self.onItemSelect(evt as MouseEvent, target_match as TomItem) ){\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// retain focus (see control_input mousedown)\n\t\t\tif( control_input.value != '' ){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tself.onClick();\n\t\t\tpreventDefault(evt,true);\n\t\t});\n\n\n\t\t// keydown on focus_node for arrow_down/arrow_up\n\t\taddEvent(focus_node,'keydown',\t\t(e) => self.onKeyDown(e as KeyboardEvent) );\n\n\t\t// keypress and input/keyup\n\t\taddEvent(control_input,'keypress',\t(e) => self.onKeyPress(e as KeyboardEvent) );\n\t\taddEvent(control_input,'input',\t\t(e) => self.onInput(e as KeyboardEvent) );\n\t\taddEvent(focus_node,'blur', \t\t(e) => self.onBlur(e as FocusEvent) );\n\t\taddEvent(focus_node,'focus',\t\t(e) => self.onFocus(e as MouseEvent) );\n\t\taddEvent(control_input,'paste',\t\t(e) => self.onPaste(e as MouseEvent) );\n\n\n\t\tconst doc_mousedown = (evt:Event) => {\n\n\t\t\t// blur if target is outside of this instance\n\t\t\t// dropdown is not always inside wrapper\n\t\t\tconst target = evt.composedPath()[0];\n\t\t\tif( !wrapper.contains(target as HTMLElement) && !dropdown.contains(target as HTMLElement) ){\n\t\t\t\tif (self.isFocused) {\n\t\t\t\t\tself.blur();\n\t\t\t\t}\n\t\t\t\tself.inputState();\n\t\t\t\treturn;\n\t\t\t}\n\n\n\t\t\t// retain focus by preventing native handling. if the\n\t\t\t// event target is the input it should not be modified.\n\t\t\t// otherwise, text selection within the input won't work.\n\t\t\t// Fixes bug #212 which is no covered by tests\n\t\t\tif( target == control_input && self.isOpen ){\n\t\t\t\tevt.stopPropagation();\n\n\t\t\t// clicking anywhere in the control should not blur the control_input (which would close the dropdown)\n\t\t\t}else{\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t}\n\n\t\t};\n\n\t\tconst win_scroll = () => {\n\t\t\tif (self.isOpen) {\n\t\t\t\tself.positionDropdown();\n\t\t\t}\n\t\t};\n\n\n\t\taddEvent(document,'mousedown', doc_mousedown);\n\t\taddEvent(window,'scroll', win_scroll, passive_event);\n\t\taddEvent(window,'resize', win_scroll, passive_event);\n\n\t\tthis._destroy = () => {\n\t\t\tdocument.removeEventListener('mousedown',doc_mousedown);\n\t\t\twindow.removeEventListener('scroll',win_scroll);\n\t\t\twindow.removeEventListener('resize',win_scroll);\n\t\t\tif( label ) label.removeEventListener('click',label_click);\n\t\t};\n\n\t\t// store original html and tab index so that they can be\n\t\t// restored when the destroy() method is called.\n\t\tthis.revertSettings = {\n\t\t\tinnerHTML : input.innerHTML,\n\t\t\ttabIndex : input.tabIndex\n\t\t};\n\n\n\t\tinput.tabIndex = -1;\n\t\tinput.insertAdjacentElement('afterend', self.wrapper);\n\n\t\tself.sync(false);\n\t\tsettings.items = [];\n\t\tdelete settings.optgroups;\n\t\tdelete settings.options;\n\n\t\taddEvent(input,'invalid', () => {\n\t\t\tif( self.isValid ){\n\t\t\t\tself.isValid = false;\n\t\t\t\tself.isInvalid = true;\n\t\t\t\tself.refreshState();\n\t\t\t}\n\t\t});\n\n\t\tself.updateOriginalInput();\n\t\tself.refreshItems();\n\t\tself.close(false);\n\t\tself.inputState();\n\t\tself.isSetup = true;\n\n\t\tif( input.disabled ){\n\t\t\tself.disable();\n\t\t}else{\n\t\t\tself.enable(); //sets tabIndex\n\t\t}\n\n\t\tself.on('change', this.onChange);\n\n\t\taddClasses(input,'tomselected','ts-hidden-accessible');\n\t\tself.trigger('initialize');\n\n\t\t// preload options\n\t\tif (settings.preload === true) {\n\t\t\tself.preload();\n\t\t}\n\n\t}\n\n\n\t/**\n\t * Register options and optgroups\n\t *\n\t */\n\tsetupOptions(options:TomOption[] = [], optgroups:TomOption[] = []){\n\n\t\t// build options table\n\t\tthis.addOptions(options);\n\n\n\t\t// build optgroup table\n\t\titerate( optgroups, (optgroup:TomOption) => {\n\t\t\tthis.registerOptionGroup(optgroup);\n\t\t});\n\t}\n\n\t/**\n\t * Sets up default rendering functions.\n\t */\n\tsetupTemplates() {\n\t\tvar self = this;\n\t\tvar field_label = self.settings.labelField;\n\t\tvar field_optgroup = self.settings.optgroupLabelField;\n\n\t\tvar templates = {\n\t\t\t'optgroup': (data:TomOption) => {\n\t\t\t\tlet optgroup = document.createElement('div');\n\t\t\t\toptgroup.className = 'optgroup';\n\t\t\t\toptgroup.appendChild(data.options);\n\t\t\t\treturn optgroup;\n\n\t\t\t},\n\t\t\t'optgroup_header': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div class=\"optgroup-header\">' + escape(data[field_optgroup]) + '</div>';\n\t\t\t},\n\t\t\t'option': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div>' + escape(data[field_label]) + '</div>';\n\t\t\t},\n\t\t\t'item': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div>' + escape(data[field_label]) + '</div>';\n\t\t\t},\n\t\t\t'option_create': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div class=\"create\">Add <strong>' + escape(data.input) + '</strong>&hellip;</div>';\n\t\t\t},\n\t\t\t'no_results':() => {\n\t\t\t\treturn '<div class=\"no-results\">No results found</div>';\n\t\t\t},\n\t\t\t'loading':() => {\n\t\t\t\treturn '<div class=\"spinner\"></div>';\n\t\t\t},\n\t\t\t'not_loading':() => {},\n\t\t\t'dropdown':() => {\n\t\t\t\treturn '<div></div>';\n\t\t\t}\n\t\t};\n\n\n\t\tself.settings.render = Object.assign({}, templates, self.settings.render);\n\t}\n\n\t/**\n\t * Maps fired events to callbacks provided\n\t * in the settings used when creating the control.\n\t */\n\tsetupCallbacks() {\n\t\tvar key, fn;\n\t\tvar callbacks:{[key:string]:string} = {\n\t\t\t'initialize'      : 'onInitialize',\n\t\t\t'change'          : 'onChange',\n\t\t\t'item_add'        : 'onItemAdd',\n\t\t\t'item_remove'     : 'onItemRemove',\n\t\t\t'item_select'     : 'onItemSelect',\n\t\t\t'clear'           : 'onClear',\n\t\t\t'option_add'      : 'onOptionAdd',\n\t\t\t'option_remove'   : 'onOptionRemove',\n\t\t\t'option_clear'    : 'onOptionClear',\n\t\t\t'optgroup_add'    : 'onOptionGroupAdd',\n\t\t\t'optgroup_remove' : 'onOptionGroupRemove',\n\t\t\t'optgroup_clear'  : 'onOptionGroupClear',\n\t\t\t'dropdown_open'   : 'onDropdownOpen',\n\t\t\t'dropdown_close'  : 'onDropdownClose',\n\t\t\t'type'            : 'onType',\n\t\t\t'load'            : 'onLoad',\n\t\t\t'focus'           : 'onFocus',\n\t\t\t'blur'            : 'onBlur'\n\t\t};\n\n\t\tfor (key in callbacks) {\n\n\t\t\tfn = this.settings[callbacks[key] as (keyof TomSettings)];\n\t\t\tif (fn) this.on(key, fn);\n\n\t\t}\n\t}\n\n\t/**\n\t * Sync the Tom Select instance with the original input or select\n\t *\n\t */\n\tsync(get_settings:boolean=true):void{\n\t\tconst self\t\t= this;\n\t\tconst settings\t= get_settings ? getSettings( self.input, {delimiter:self.settings.delimiter} as RecursivePartial<TomSettings> ) : self.settings;\n\n\t\tself.setupOptions(settings.options,settings.optgroups);\n\n\t\tself.setValue(settings.items||[],true); // silent prevents recursion\n\n\t\tself.lastQuery = null; // so updated options will be displayed in dropdown\n\t}\n\n\t/**\n\t * Triggered when the main control element\n\t * has a click event.\n\t *\n\t */\n\tonClick():void {\n\t\tvar self = this;\n\n\t\tif( self.activeItems.length > 0 ){\n\t\t\tself.clearActiveItems();\n\t\t\tself.focus();\n\t\t\treturn;\n\t\t}\n\n\t\tif( self.isFocused && self.isOpen ){\n\t\t\tself.blur();\n\t\t} else {\n\t\t\tself.focus();\n\t\t}\n\t}\n\n\t/**\n\t * @deprecated v1.7\n\t *\n\t */\n\tonMouseDown():void {}\n\n\t/**\n\t * Triggered when the value of the control has been changed.\n\t * This should propagate the event to the original DOM\n\t * input / select element.\n\t */\n\tonChange() {\n\t\ttriggerEvent(this.input, 'input');\n\t\ttriggerEvent(this.input, 'change');\n\t}\n\n\t/**\n\t * Triggered on <input> paste.\n\t *\n\t */\n\tonPaste(e:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tif( self.isInputHidden || self.isLocked ){\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\n\t\t// If a regex or string is included, this will split the pasted\n\t\t// input and create Items for each separate value\n\t\tif( !self.settings.splitOn ){\n\t\t\treturn;\n\t\t}\n\n\t\t// Wait for pasted text to be recognized in value\n\t\tsetTimeout(() => {\n\t\t\tvar pastedText = self.inputValue();\n\t\t\tif( !pastedText.match(self.settings.splitOn)){\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tvar splitInput = pastedText.trim().split(self.settings.splitOn);\n\t\t\titerate( splitInput, (piece:string) => {\n\n\t\t\t\tconst hash = hash_key(piece);\n\t\t\t\tif( hash ){\n\t\t\t\t\tif( this.options[piece] ){\n\t\t\t\t\t\tself.addItem(piece);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tself.createItem(piece);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}, 0);\n\n\t}\n\n\t/**\n\t * Triggered on <input> keypress.\n\t *\n\t */\n\tonKeyPress(e:KeyboardEvent):void {\n\t\tvar self = this;\n\t\tif(self.isLocked){\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\t\tvar character = String.fromCharCode(e.keyCode || e.which);\n\t\tif (self.settings.create && self.settings.mode === 'multi' && character === self.settings.delimiter) {\n\t\t\tself.createItem();\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\t}\n\n\t/**\n\t * Triggered on <input> keydown.\n\t *\n\t */\n\tonKeyDown(e:KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tself.ignoreHover = true;\n\n\t\tif (self.isLocked) {\n\t\t\tif (e.keyCode !== constants.KEY_TAB) {\n\t\t\t\tpreventDefault(e);\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tswitch (e.keyCode) {\n\n\t\t\t// ctrl+A: select all\n\t\t\tcase constants.KEY_A:\n\t\t\t\tif( isKeyDown(constants.KEY_SHORTCUT,e) ){\n\t\t\t\t\tif( self.control_input.value == '' ){\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t\tself.selectAll();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\t// esc: close dropdown\n\t\t\tcase constants.KEY_ESC:\n\t\t\t\tif (self.isOpen) {\n\t\t\t\t\tpreventDefault(e,true);\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\t\t\t\tself.clearActiveItems();\n\t\t\t\treturn;\n\n\t\t\t// down: open dropdown or move selection down\n\t\t\tcase constants.KEY_DOWN:\n\t\t\t\tif (!self.isOpen && self.hasOptions) {\n\t\t\t\t\tself.open();\n\t\t\t\t} else if (self.activeOption) {\n\t\t\t\t\tlet next = self.getAdjacent(self.activeOption, 1);\n\t\t\t\t\tif (next) self.setActiveOption(next);\n\t\t\t\t}\n\t\t\t\tpreventDefault(e);\n\t\t\t\treturn;\n\n\t\t\t// up: move selection up\n\t\t\tcase constants.KEY_UP:\n\t\t\t\tif (self.activeOption) {\n\t\t\t\t\tlet prev = self.getAdjacent(self.activeOption, -1);\n\t\t\t\t\tif (prev) self.setActiveOption(prev);\n\t\t\t\t}\n\t\t\t\tpreventDefault(e);\n\t\t\t\treturn;\n\n\t\t\t// return: select active option\n\t\t\tcase constants.KEY_RETURN:\n\t\t\t\tif( self.canSelect(self.activeOption) ){\n\t\t\t\t\tself.onOptionSelect(e,self.activeOption!);\n\t\t\t\t\tpreventDefault(e);\n\n\t\t\t\t// if the option_create=null, the dropdown might be closed\n\t\t\t\t}else if (self.settings.create && self.createItem()) {\n\t\t\t\t\tpreventDefault(e);\n\n\t\t\t\t// don't submit form when searching for a value\n\t\t\t\t}else if( document.activeElement == self.control_input && self.isOpen ){\n\t\t\t\t\tpreventDefault(e);\n\t\t\t\t}\n\n\t\t\t\treturn;\n\n\t\t\t// left: modifiy item selection to the left\n\t\t\tcase constants.KEY_LEFT:\n\t\t\t\tself.advanceSelection(-1, e);\n\t\t\t\treturn;\n\n\t\t\t// right: modifiy item selection to the right\n\t\t\tcase constants.KEY_RIGHT:\n\t\t\t\tself.advanceSelection(1, e);\n\t\t\t\treturn;\n\n\t\t\t// tab: select active option and/or create item\n\t\t\tcase constants.KEY_TAB:\n\n\t\t\t\tif( self.settings.selectOnTab ){\n\t\t\t\t\tif( self.canSelect(self.activeOption) ){\n\t\t\t\t\t\tself.onOptionSelect(e,self.activeOption!);\n\n\t\t\t\t\t\t// prevent default [tab] behaviour of jump to the next field\n\t\t\t\t\t\t// if select isFull, then the dropdown won't be open and [tab] will work normally\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t\tif (self.settings.create && self.createItem()) {\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn;\n\n\t\t\t// delete|backspace: delete items\n\t\t\tcase constants.KEY_BACKSPACE:\n\t\t\tcase constants.KEY_DELETE:\n\t\t\t\tself.deleteSelection(e);\n\t\t\t\treturn;\n\t\t}\n\n\t\t// don't enter text in the control_input when active items are selected\n\t\tif( self.isInputHidden && !isKeyDown(constants.KEY_SHORTCUT,e) ){\n\t\t\tpreventDefault(e);\n\t\t}\n\t}\n\n\t/**\n\t * Triggered on <input> keyup.\n\t *\n\t */\n\tonInput(e:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tif( self.isLocked ){\n\t\t\treturn;\n\t\t}\n\n\t\tvar value = self.inputValue();\n\t\tif (self.lastValue !== value) {\n\t\t\tself.lastValue = value;\n\n\t\t\tif( self.settings.shouldLoad.call(self,value) ){\n\t\t\t\tself.load(value);\n\t\t\t}\n\n\t\t\tself.refreshOptions();\n\t\t\tself.trigger('type', value);\n\t\t}\n\t}\n\n\t/**\n\t * Triggered when the user rolls over\n\t * an option in the autocomplete dropdown menu.\n\t *\n\t */\n\tonOptionHover( evt:MouseEvent|KeyboardEvent, option:HTMLElement ):void{\n\t\tif( this.ignoreHover ) return;\n\t\tthis.setActiveOption(option, false);\n\t}\n\n\t/**\n\t * Triggered on <input> focus.\n\t *\n\t */\n\tonFocus(e?:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\t\tvar wasFocused = self.isFocused;\n\n\t\tif (self.isDisabled) {\n\t\t\tself.blur();\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\n\t\tif (self.ignoreFocus) return;\n\t\tself.isFocused = true;\n\t\tif( self.settings.preload === 'focus' ) self.preload();\n\n\t\tif (!wasFocused) self.trigger('focus');\n\n\t\tif (!self.activeItems.length) {\n\t\t\tself.showInput();\n\t\t\tself.refreshOptions(!!self.settings.openOnFocus);\n\t\t}\n\n\t\tself.refreshState();\n\t}\n\n\t/**\n\t * Triggered on <input> blur.\n\t *\n\t */\n\tonBlur(e?:FocusEvent):void {\n\n\t\tif( document.hasFocus() === false ) return;\n\n\t\tvar self = this;\n\t\tif (!self.isFocused) return;\n\t\tself.isFocused = false;\n\t\tself.ignoreFocus = false;\n\n\t\tvar deactivate = () => {\n\t\t\tself.close();\n\t\t\tself.setActiveItem();\n\t\t\tself.setCaret(self.items.length);\n\t\t\tself.trigger('blur');\n\t\t};\n\n\t\tif (self.settings.create && self.settings.createOnBlur) {\n\t\t\tself.createItem(null, deactivate);\n\t\t} else {\n\t\t\tdeactivate();\n\t\t}\n\t}\n\n\n\t/**\n\t * Triggered when the user clicks on an option\n\t * in the autocomplete dropdown menu.\n\t *\n\t */\n\tonOptionSelect( evt:MouseEvent|KeyboardEvent, option:HTMLElement ){\n\t\tvar value, self = this;\n\n\n\t\t// should not be possible to trigger a option under a disabled optgroup\n\t\tif( option.parentElement && option.parentElement.matches('[data-disabled]') ){\n\t\t\treturn;\n\t\t}\n\n\n\t\tif( option.classList.contains('create') ){\n\t\t\tself.createItem(null, () => {\n\t\t\t\tif (self.settings.closeAfterSelect) {\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tvalue = option.dataset.value;\n\t\t\tif (typeof value !== 'undefined') {\n\t\t\t\tself.lastQuery = null;\n\t\t\t\tself.addItem(value);\n\t\t\t\tif (self.settings.closeAfterSelect) {\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\n\t\t\t\tif( !self.settings.hideSelected && evt.type && /click/.test(evt.type) ){\n\t\t\t\t\tself.setActiveOption(option);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Return true if the given option can be selected\n\t *\n\t */\n\tcanSelect(option:HTMLElement|null):boolean{\n\n\t\tif( this.isOpen && option && this.dropdown_content.contains(option) ) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Triggered when the user clicks on an item\n\t * that has been selected.\n\t *\n\t */\n\tonItemSelect( evt?:MouseEvent, item?:TomItem ):boolean{\n\t\tvar self = this;\n\n\t\tif( !self.isLocked && self.settings.mode === 'multi' ){\n\t\t\tpreventDefault(evt);\n\t\t\tself.setActiveItem(item, evt);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Determines whether or not to invoke\n\t * the user-provided option provider / loader\n\t *\n\t * Note, there is a subtle difference between\n\t * this.canLoad() and this.settings.shouldLoad();\n\t *\n\t *\t- settings.shouldLoad() is a user-input validator.\n\t *\tWhen false is returned, the not_loading template\n\t *\twill be added to the dropdown\n\t *\n\t *\t- canLoad() is lower level validator that checks\n\t * \tthe Tom Select instance. There is no inherent user\n\t *\tfeedback when canLoad returns false\n\t *\n\t */\n\tcanLoad(value:string):boolean{\n\n\t\tif( !this.settings.load ) return false;\n\t\tif( this.loadedSearches.hasOwnProperty(value) ) return false;\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Invokes the user-provided option provider / loader.\n\t *\n\t */\n\tload(value:string):void {\n\t\tconst self = this;\n\n\t\tif( !self.canLoad(value) ) return;\n\n\t\taddClasses(self.wrapper,self.settings.loadingClass);\n\t\tself.loading++;\n\n\t\tconst callback = self.loadCallback.bind(self);\n\t\tself.settings.load.call(self, value, callback);\n\t}\n\n\t/**\n\t * Invoked by the user-provided option provider\n\t *\n\t */\n\tloadCallback( options:TomOption[], optgroups:TomOption[] ):void{\n\t\tconst self = this;\n\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\tself.lastQuery = null;\n\n\t\tself.clearActiveOption(); // when new results load, focus should be on first option\n\t\tself.setupOptions(options,optgroups);\n\n\t\tself.refreshOptions(self.isFocused && !self.isInputHidden);\n\n\t\tif (!self.loading) {\n\t\t\tremoveClasses(self.wrapper,self.settings.loadingClass);\n\t\t}\n\n\t\tself.trigger('load', options, optgroups);\n\t}\n\n\tpreload():void{\n\t\tvar classList = this.wrapper.classList;\n\t\tif( classList.contains('preloaded') ) return;\n\t\tclassList.add('preloaded');\n\t\tthis.load('');\n\t}\n\n\n\t/**\n\t * Sets the input field of the control to the specified value.\n\t *\n\t */\n\tsetTextboxValue(value:string = '') {\n\t\tvar input = this.control_input;\n\t\tvar changed = input.value !== value;\n\t\tif (changed) {\n\t\t\tinput.value = value;\n\t\t\ttriggerEvent(input,'update');\n\t\t\tthis.lastValue = value;\n\t\t}\n\t}\n\n\t/**\n\t * Returns the value of the control. If multiple items\n\t * can be selected (e.g. <select multiple>), this returns\n\t * an array. If only one item can be selected, this\n\t * returns a string.\n\t *\n\t */\n\tgetValue():string|string[] {\n\n\t\tif( this.is_select_tag && this.input.hasAttribute('multiple')) {\n\t\t\treturn this.items;\n\t\t}\n\n\t\treturn this.items.join(this.settings.delimiter);\n\t}\n\n\t/**\n\t * Resets the selected items to the given value.\n\t *\n\t */\n\tsetValue( value:string|string[], silent?:boolean ):void{\n\t\tvar events = silent ? [] : ['change'];\n\n\t\tdebounce_events(this, events,() => {\n\t\t\tthis.clear(silent);\n\t\t\tthis.addItems(value, silent);\n\t\t});\n\t}\n\n\n\t/**\n\t * Resets the number of max items to the given value\n\t *\n\t */\n\tsetMaxItems(value:null|number){\n\t\tif(value === 0) value = null; //reset to unlimited items.\n\t\tthis.settings.maxItems = value;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Sets the selected item.\n\t *\n\t */\n\tsetActiveItem( item?:TomItem, e?:MouseEvent|KeyboardEvent ){\n\t\tvar self = this;\n\t\tvar eventName;\n\t\tvar i, begin, end, swap;\n\t\tvar last;\n\n\t\tif (self.settings.mode === 'single') return;\n\n\t\t// clear the active selection\n\t\tif( !item ){\n\t\t\tself.clearActiveItems();\n\t\t\tif (self.isFocused) {\n\t\t\t\tself.showInput();\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\t// modify selection\n\t\teventName = e && e.type.toLowerCase();\n\n\t\tif (eventName === 'click' && isKeyDown('shiftKey',e) && self.activeItems.length) {\n\t\t\tlast\t= self.getLastActive();\n\t\t\tbegin\t= Array.prototype.indexOf.call(self.control.children, last);\n\t\t\tend\t\t= Array.prototype.indexOf.call(self.control.children, item);\n\n\t\t\tif (begin > end) {\n\t\t\t\tswap  = begin;\n\t\t\t\tbegin = end;\n\t\t\t\tend   = swap;\n\t\t\t}\n\t\t\tfor (i = begin; i <= end; i++) {\n\t\t\t\titem = self.control.children[i] as TomItem;\n\t\t\t\tif (self.activeItems.indexOf(item) === -1) {\n\t\t\t\t\tself.setActiveItemClass(item);\n\t\t\t\t}\n\t\t\t}\n\t\t\tpreventDefault(e);\n\t\t} else if ((eventName === 'click' && isKeyDown(constants.KEY_SHORTCUT,e) ) || (eventName === 'keydown' && isKeyDown('shiftKey',e))) {\n\t\t\tif( item.classList.contains('active') ){\n\t\t\t\tself.removeActiveItem( item );\n\t\t\t} else {\n\t\t\t\tself.setActiveItemClass(item);\n\t\t\t}\n\t\t} else {\n\t\t\tself.clearActiveItems();\n\t\t\tself.setActiveItemClass(item);\n\t\t}\n\n\t\t// ensure control has focus\n\t\tself.hideInput();\n\t\tif (!self.isFocused) {\n\t\t\tself.focus();\n\t\t}\n\t}\n\n\t/**\n\t * Set the active and last-active classes\n\t *\n\t */\n\tsetActiveItemClass( item:TomItem ){\n\t\tconst self = this;\n\t\tconst last_active = self.control.querySelector('.last-active');\n\t\tif( last_active ) removeClasses(last_active as HTMLElement,'last-active');\n\n\t\taddClasses(item,'active last-active');\n\t\tself.trigger('item_select', item);\n\t\tif( self.activeItems.indexOf(item) == -1 ){\n\t\t\tself.activeItems.push( item );\n\t\t}\n\t}\n\n\t/**\n\t * Remove active item\n\t *\n\t */\n\tremoveActiveItem( item:TomItem ){\n\t\tvar idx = this.activeItems.indexOf(item);\n\t\tthis.activeItems.splice(idx, 1);\n\t\tremoveClasses(item,'active');\n\t}\n\n\t/**\n\t * Clears all the active items\n\t *\n\t */\n\tclearActiveItems(){\n\t\tremoveClasses(this.activeItems,'active');\n\t\tthis.activeItems = [];\n\t}\n\n\t/**\n\t * Sets the selected item in the dropdown menu\n\t * of available options.\n\t *\n\t */\n\tsetActiveOption( option:null|HTMLElement,scroll:boolean=true ):void{\n\n\t\tif( option === this.activeOption ){\n\t\t\treturn;\n\t\t}\n\n\t\tthis.clearActiveOption();\n\t\tif( !option ) return;\n\n\t\tthis.activeOption = option;\n\t\tsetAttr(this.focus_node,{'aria-activedescendant':option.getAttribute('id')});\n\t\tsetAttr(option,{'aria-selected':'true'});\n\t\taddClasses(option,'active');\n\t\tif( scroll ) this.scrollToOption(option);\n\t}\n\n\t/**\n\t * Sets the dropdown_content scrollTop to display the option\n\t *\n\t */\n\tscrollToOption( option:null|HTMLElement, behavior?:string ):void{\n\n\t\tif( !option ) return;\n\n\t\tconst content\t\t= this.dropdown_content;\n\t\tconst height_menu\t= content.clientHeight;\n\t\tconst scrollTop\t\t= content.scrollTop || 0;\n\t\tconst height_item\t= option.offsetHeight;\n\t\tconst y\t\t\t\t= option.getBoundingClientRect().top - content.getBoundingClientRect().top + scrollTop;\n\n\t\tif (y + height_item > height_menu + scrollTop) {\n\t\t\tthis.scroll(y - height_menu + height_item, behavior);\n\n\t\t} else if (y < scrollTop) {\n\t\t\tthis.scroll(y, behavior);\n\t\t}\n\t}\n\n\t/**\n\t * Scroll the dropdown to the given position\n\t *\n\t */\n\tscroll( scrollTop:number, behavior?:string ):void{\n\t\tconst content = this.dropdown_content;\n\t\tif( behavior ){\n\t\t\tcontent.style.scrollBehavior = behavior;\n\t\t}\n\t\tcontent.scrollTop = scrollTop;\n\t\tcontent.style.scrollBehavior = '';\n\t}\n\n\t/**\n\t * Clears the active option\n\t *\n\t */\n\tclearActiveOption(){\n\t\tif( this.activeOption ){\n\t\t\tremoveClasses(this.activeOption,'active');\n\t\t\tsetAttr(this.activeOption,{'aria-selected':null});\n\t\t}\n\t\tthis.activeOption = null;\n\t\tsetAttr(this.focus_node,{'aria-activedescendant':null});\n\t}\n\n\n\t/**\n\t * Selects all items (CTRL + A).\n\t */\n\tselectAll() {\n\t\tconst self = this;\n\n\t\tif (self.settings.mode === 'single') return;\n\n\t\tconst activeItems = self.controlChildren();\n\n\t\tif( !activeItems.length ) return;\n\n\t\tself.hideInput();\n\t\tself.close();\n\n\t\tself.activeItems = activeItems;\n\t\titerate( activeItems, (item:TomItem) => {\n\t\t\tself.setActiveItemClass(item);\n\t\t});\n\n\t}\n\n\t/**\n\t * Determines if the control_input should be in a hidden or visible state\n\t *\n\t */\n\tinputState(){\n\t\tvar self = this;\n\n\t\tif( !self.control.contains(self.control_input) ) return;\n\n\t\tsetAttr(self.control_input,{placeholder:self.settings.placeholder});\n\n\t\tif( self.activeItems.length > 0 || (!self.isFocused && self.settings.hidePlaceholder && self.items.length > 0) ){\n\t\t\tself.setTextboxValue();\n\t\t\tself.isInputHidden = true;\n\n\t\t}else{\n\n\t\t\tif( self.settings.hidePlaceholder && self.items.length > 0 ){\n\t\t\t\tsetAttr(self.control_input,{placeholder:''});\n\t\t\t}\n\t\t\tself.isInputHidden = false;\n\t\t}\n\n\t\tself.wrapper.classList.toggle('input-hidden', self.isInputHidden );\n\t}\n\n\t/**\n\t * Hides the input element out of view, while\n\t * retaining its focus.\n\t * @deprecated 1.3\n\t */\n\thideInput() {\n\t\tthis.inputState();\n\t}\n\n\t/**\n\t * Restores input visibility.\n\t * @deprecated 1.3\n\t */\n\tshowInput() {\n\t\tthis.inputState();\n\t}\n\n\t/**\n\t * Get the input value\n\t */\n\tinputValue(){\n\t\treturn this.control_input.value.trim();\n\t}\n\n\t/**\n\t * Gives the control focus.\n\t */\n\tfocus() {\n\t\tvar self = this;\n\t\tif (self.isDisabled) return;\n\n\t\tself.ignoreFocus = true;\n\n\t\tif( self.control_input.offsetWidth ){\n\t\t\tself.control_input.focus();\n\t\t}else{\n\t\t\tself.focus_node.focus();\n\t\t}\n\n\t\tsetTimeout(() => {\n\t\t\tself.ignoreFocus = false;\n\t\t\tself.onFocus();\n\t\t}, 0);\n\t}\n\n\t/**\n\t * Forces the control out of focus.\n\t *\n\t */\n\tblur():void {\n\t\tthis.focus_node.blur();\n\t\tthis.onBlur();\n\t}\n\n\t/**\n\t * Returns a function that scores an object\n\t * to show how good of a match it is to the\n\t * provided query.\n\t *\n\t * @return {function}\n\t */\n\tgetScoreFunction(query:string) {\n\t\treturn this.sifter.getScoreFunction(query, this.getSearchOptions());\n\t}\n\n\t/**\n\t * Returns search options for sifter (the system\n\t * for scoring and sorting results).\n\t *\n\t * @see https://github.com/orchidjs/sifter.js\n\t * @return {object}\n\t */\n\tgetSearchOptions() {\n\t\tvar settings = this.settings;\n\t\tvar sort = settings.sortField;\n\t\tif (typeof settings.sortField === 'string') {\n\t\t\tsort = [{field: settings.sortField}];\n\t\t}\n\n\t\treturn {\n\t\t\tfields      : settings.searchField,\n\t\t\tconjunction : settings.searchConjunction,\n\t\t\tsort        : sort,\n\t\t\tnesting     : settings.nesting\n\t\t};\n\t}\n\n\t/**\n\t * Searches through available options and returns\n\t * a sorted array of matches.\n\t *\n\t */\n\tsearch(query:string) : ReturnType<Sifter['search']>{\n\t\tvar result, calculateScore;\n\t\tvar self     = this;\n\t\tvar options  = this.getSearchOptions();\n\n\t\t// validate user-provided result scoring function\n\t\tif ( self.settings.score ){\n\t\t\tcalculateScore = self.settings.score.call(self,query);\n\t\t\tif (typeof calculateScore !== 'function') {\n\t\t\t\tthrow new Error('Tom Select \"score\" setting must be a function that returns a function');\n\t\t\t}\n\t\t}\n\n\t\t// perform search\n\t\tif (query !== self.lastQuery) {\n\t\t\tself.lastQuery\t\t\t= query;\n\t\t\tresult\t\t\t\t\t= self.sifter.search(query, Object.assign(options, {score: calculateScore}));\n\t\t\tself.currentResults\t\t= result;\n\t\t} else {\n\t\t\tresult\t\t\t\t\t= Object.assign( {}, self.currentResults);\n\t\t}\n\n\t\t// filter out selected items\n\t\tif( self.settings.hideSelected ){\n\t\t\tresult.items = result.items.filter((item) => {\n\t\t\t\tlet hashed = hash_key(item.id);\n\t\t\t\treturn !(hashed && self.items.indexOf(hashed) !== -1 );\n\t\t\t});\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t/**\n\t * Refreshes the list of available options shown\n\t * in the autocomplete dropdown menu.\n\t *\n\t */\n\trefreshOptions( triggerDropdown:boolean = true ){\n\t\tvar i, j, k, n, optgroup, optgroups, html:DocumentFragment, has_create_option, active_group;\n\t\tvar create;\n\t\tconst groups: {[key:string]:DocumentFragment} = {};\n\n\t\tconst groups_order:string[]\t= [];\n\t\tvar self\t\t\t\t\t= this;\n\t\tvar query\t\t\t\t\t= self.inputValue();\n\t\tconst same_query\t\t\t= query === self.lastQuery || (query == '' && self.lastQuery == null);\n\t\tvar results\t\t\t\t\t= self.search(query);\n\t\tvar active_option\t\t\t= null;\n\t\tvar show_dropdown\t\t\t= self.settings.shouldOpen || false;\n\t\tvar dropdown_content\t\t= self.dropdown_content;\n\n\n\t\tif( same_query ){\n\t\t\tactive_option\t\t\t= self.activeOption;\n\n\t\t\tif( active_option ){\n\t\t\t\tactive_group = active_option.closest('[data-group]') as HTMLElement;\n\t\t\t}\n\t\t}\n\n\t\t// build markup\n\t\tn = results.items.length;\n\t\tif (typeof self.settings.maxOptions === 'number') {\n\t\t\tn = Math.min(n, self.settings.maxOptions);\n\t\t}\n\n\t\tif( n > 0 ){\n\t\t\tshow_dropdown = true;\n\t\t}\n\n\t\t// render and group available options individually\n\t\tfor (i = 0; i < n; i++) {\n\n\t\t\t// get option dom element\n\t\t\tlet item\t\t\t= results.items[i];\n\t\t\tif( !item ) continue;\n\n\t\t\tlet opt_value\t\t= item.id;\n\t\t\tlet option\t\t\t= self.options[opt_value];\n\n\t\t\tif( option === undefined ) continue;\n\n\t\t\tlet opt_hash\t\t= get_hash(opt_value);\n\t\t\tlet option_el\t\t= self.getOption(opt_hash,true) as HTMLElement;\n\n\t\t\t// toggle 'selected' class\n\t\t\tif( !self.settings.hideSelected ){\n\t\t\t\toption_el.classList.toggle('selected', self.items.includes(opt_hash) );\n\t\t\t}\n\n\t\t\toptgroup    = option[self.settings.optgroupField] || '';\n\t\t\toptgroups   = Array.isArray(optgroup) ? optgroup : [optgroup];\n\n\t\t\tfor (j = 0, k = optgroups && optgroups.length; j < k; j++) {\n\t\t\t\toptgroup = optgroups[j];\n\t\t\t\tif (!self.optgroups.hasOwnProperty(optgroup)) {\n\t\t\t\t\toptgroup = '';\n\t\t\t\t}\n\n\t\t\t\tlet group_fragment = groups[optgroup];\n\t\t\t\tif( group_fragment === undefined ){\n\t\t\t\t\tgroup_fragment = document.createDocumentFragment();\n\t\t\t\t\tgroups_order.push(optgroup);\n\t\t\t\t}\n\n\t\t\t\t// nodes can only have one parent, so if the option is in mutple groups, we need a clone\n\t\t\t\tif( j > 0 ){\n\t\t\t\t\toption_el = option_el.cloneNode(true) as HTMLElement;\n\t\t\t\t\tsetAttr(option_el,{id: option.$id+'-clone-'+j,'aria-selected':null});\n\t\t\t\t\toption_el.classList.add('ts-cloned');\n\t\t\t\t\tremoveClasses(option_el,'active');\n\n\n\t\t\t\t\t// make sure we keep the activeOption in the same group\n\t\t\t\t\tif( self.activeOption && self.activeOption.dataset.value == opt_value ){\n\t\t\t\t\t\tif( active_group && active_group.dataset.group === optgroup.toString() ){\n\t\t\t\t\t\t\tactive_option = option_el;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tgroup_fragment.appendChild(option_el);\n\t\t\t\tgroups[optgroup] = group_fragment;\n\t\t\t}\n\t\t}\n\n\t\t// sort optgroups\n\t\tif( self.settings.lockOptgroupOrder ){\n\t\t\tgroups_order.sort((a, b) => {\n\t\t\t\tconst grp_a\t\t= self.optgroups[a];\n\t\t\t\tconst grp_b\t\t= self.optgroups[b];\n\t\t\t\tconst a_order\t= grp_a && grp_a.$order || 0;\n\t\t\t\tconst b_order\t= grp_b && grp_b.$order || 0;\n\t\t\t\treturn a_order - b_order;\n\t\t\t});\n\t\t}\n\n\t\t// render optgroup headers & join groups\n\t\thtml = document.createDocumentFragment();\n\t\titerate( groups_order, (optgroup:string) => {\n\n\t\t\tlet group_fragment = groups[optgroup];\n\n\t\t\tif( !group_fragment || !group_fragment.children.length ) return;\n\n\t\t\tlet group_heading = self.optgroups[optgroup];\n\n\t\t\tif( group_heading !== undefined ){\n\n\t\t\t\tlet group_options = document.createDocumentFragment();\n\t\t\t\tlet header = self.render('optgroup_header', group_heading);\n\t\t\t\tappend( group_options, header );\n\t\t\t\tappend( group_options, group_fragment );\n\n\t\t\t\tlet group_html = self.render('optgroup', {group:group_heading,options:group_options} );\n\n\t\t\t\tappend( html, group_html );\n\n\t\t\t} else {\n\t\t\t\tappend( html, group_fragment );\n\t\t\t}\n\t\t});\n\n\t\tdropdown_content.innerHTML = '';\n\t\tappend( dropdown_content, html );\n\n\t\t// highlight matching terms inline\n\t\tif (self.settings.highlight) {\n\t\t\tremoveHighlight( dropdown_content );\n\t\t\tif (results.query.length && results.tokens.length) {\n\t\t\t\titerate( results.tokens, (tok) => {\n\t\t\t\t\thighlight( dropdown_content, tok.regex);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\t// helper method for adding templates to dropdown\n\t\tvar add_template = (template:TomTemplateNames) => {\n\t\t\tlet content = self.render(template,{input:query});\n\t\t\tif( content ){\n\t\t\t\tshow_dropdown = true;\n\t\t\t\tdropdown_content.insertBefore(content, dropdown_content.firstChild);\n\t\t\t}\n\t\t\treturn content;\n\t\t};\n\n\n\t\t// add loading message\n\t\tif( self.loading ){\n\t\t\tadd_template('loading');\n\n\t\t// invalid query\n\t\t}else if( !self.settings.shouldLoad.call(self,query) ){\n\t\t\tadd_template('not_loading');\n\n\t\t// add no_results message\n\t\t}else if( results.items.length === 0 ){\n\t\t\tadd_template('no_results');\n\n\t\t}\n\n\n\n\t\t// add create option\n\t\thas_create_option = self.canCreate(query);\n\t\tif (has_create_option) {\n\t\t\tcreate = add_template('option_create');\n\t\t}\n\n\n\t\t// activate\n\t\tself.hasOptions = results.items.length > 0 || has_create_option;\n\t\tif( show_dropdown ){\n\n\t\t\tif (results.items.length > 0) {\n\n\t\t\t\tif( !active_option && self.settings.mode === 'single' && self.items[0] != undefined ){\n\t\t\t\t\tactive_option = self.getOption(self.items[0]);\n\t\t\t\t}\n\n\t\t\t\tif( !dropdown_content.contains(active_option)  ){\n\n\t\t\t\t\tlet active_index = 0;\n\t\t\t\t\tif( create && !self.settings.addPrecedence ){\n\t\t\t\t\t\tactive_index = 1;\n\t\t\t\t\t}\n\t\t\t\t\tactive_option = self.selectable()[active_index] as HTMLElement;\n\t\t\t\t}\n\n\t\t\t}else if( create ){\n\t\t\t\tactive_option = create;\n\t\t\t}\n\n\t\t\tif( triggerDropdown && !self.isOpen ){\n\t\t\t\tself.open();\n\t\t\t\tself.scrollToOption(active_option,'auto');\n\t\t\t}\n\t\t\tself.setActiveOption(active_option);\n\n\t\t}else{\n\t\t\tself.clearActiveOption();\n\t\t\tif( triggerDropdown && self.isOpen ){\n\t\t\t\tself.close(false); // if create_option=null, we want the dropdown to close but not reset the textbox value\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Return list of selectable options\n\t *\n\t */\n\tselectable():NodeList{\n\t\treturn this.dropdown_content.querySelectorAll('[data-selectable]');\n\t}\n\n\n\n\t/**\n\t * Adds an available option. If it already exists,\n\t * nothing will happen. Note: this does not refresh\n\t * the options list dropdown (use `refreshOptions`\n\t * for that).\n\t *\n\t * Usage:\n\t *\n\t *   this.addOption(data)\n\t *\n\t */\n\taddOption( data:TomOption, user_created = false ):false|string {\n\t\tconst self = this;\n\n\t\t// @deprecated 1.7.7\n\t\t// use addOptions( array, user_created ) for adding multiple options\n\t\tif( Array.isArray(data) ){\n\t\t\tself.addOptions( data, user_created);\n\t\t\treturn false;\n\t\t}\n\n\t\tconst key = hash_key(data[self.settings.valueField]);\n\t\tif( key === null || self.options.hasOwnProperty(key) ){\n\t\t\treturn false;\n\t\t}\n\n\t\tdata.$order\t\t\t= data.$order || ++self.order;\n\t\tdata.$id\t\t\t= self.inputId + '-opt-' + data.$order;\n\t\tself.options[key]\t= data;\n\t\tself.lastQuery\t\t= null;\n\n\t\tif( user_created ){\n\t\t\tself.userOptions[key] = user_created;\n\t\t\tself.trigger('option_add', key, data);\n\t\t}\n\n\t\treturn key;\n\t}\n\n\t/**\n\t * Add multiple options\n\t *\n\t */\n\taddOptions( data:TomOption[], user_created = false ):void{\n\t\titerate( data, (dat:TomOption) => {\n\t\t\tthis.addOption(dat, user_created);\n\t\t});\n\t}\n\n\t/**\n\t * @deprecated 1.7.7\n\t */\n\tregisterOption( data:TomOption ):false|string {\n\t\treturn this.addOption(data);\n\t}\n\n\t/**\n\t * Registers an option group to the pool of option groups.\n\t *\n\t * @return {boolean|string}\n\t */\n\tregisterOptionGroup(data:TomOption) {\n\t\tvar key = hash_key(data[this.settings.optgroupValueField]);\n\n\t\tif ( key === null ) return false;\n\n\t\tdata.$order = data.$order || ++this.order;\n\t\tthis.optgroups[key] = data;\n\t\treturn key;\n\t}\n\n\t/**\n\t * Registers a new optgroup for options\n\t * to be bucketed into.\n\t *\n\t */\n\taddOptionGroup(id:string, data:TomOption) {\n\t\tvar hashed_id;\n\t\tdata[this.settings.optgroupValueField] = id;\n\n\t\tif( hashed_id = this.registerOptionGroup(data) ){\n\t\t\tthis.trigger('optgroup_add', hashed_id, data);\n\t\t}\n\t}\n\n\t/**\n\t * Removes an existing option group.\n\t *\n\t */\n\tremoveOptionGroup(id:string) {\n\t\tif (this.optgroups.hasOwnProperty(id)) {\n\t\t\tdelete this.optgroups[id];\n\t\t\tthis.clearCache();\n\t\t\tthis.trigger('optgroup_remove', id);\n\t\t}\n\t}\n\n\t/**\n\t * Clears all existing option groups.\n\t */\n\tclearOptionGroups() {\n\t\tthis.optgroups = {};\n\t\tthis.clearCache();\n\t\tthis.trigger('optgroup_clear');\n\t}\n\n\t/**\n\t * Updates an option available for selection. If\n\t * it is visible in the selected items or options\n\t * dropdown, it will be re-rendered automatically.\n\t *\n\t */\n\tupdateOption(value:string, data:TomOption) {\n\t\tconst self = this;\n\t\tvar item_new;\n\t\tvar index_item;\n\n\t\tconst value_old\t\t= hash_key(value);\n\t\tconst value_new\t\t= hash_key(data[self.settings.valueField]);\n\n\t\t// sanity checks\n\t\tif( value_old === null ) return;\n\n\t\tconst data_old\t\t= self.options[value_old];\n\n\t\tif( data_old == undefined ) return;\n\t\tif( typeof value_new !== 'string' ) throw new Error('Value must be set in option data');\n\n\n\t\tconst option\t\t= self.getOption(value_old);\n\t\tconst item\t\t\t= self.getItem(value_old);\n\n\n\t\tdata.$order = data.$order || data_old.$order;\n\t\tdelete self.options[value_old];\n\n\t\t// invalidate render cache\n\t\t// don't remove existing node yet, we'll remove it after replacing it\n\t\tself.uncacheValue(value_new);\n\n\t\tself.options[value_new] = data;\n\n\t\t// update the option if it's in the dropdown\n\t\tif( option ){\n\t\t\tif( self.dropdown_content.contains(option) ){\n\n\t\t\t\tconst option_new\t= self._render('option', data);\n\t\t\t\treplaceNode(option, option_new);\n\n\t\t\t\tif( self.activeOption === option ){\n\t\t\t\t\tself.setActiveOption(option_new);\n\t\t\t\t}\n\t\t\t}\n\t\t\toption.remove();\n\t\t}\n\n\t\t// update the item if we have one\n\t\tif( item ){\n\t\t\tindex_item = self.items.indexOf(value_old);\n\t\t\tif (index_item !== -1) {\n\t\t\t\tself.items.splice(index_item, 1, value_new);\n\t\t\t}\n\n\t\t\titem_new\t= self._render('item', data);\n\n\t\t\tif( item.classList.contains('active') ) addClasses(item_new,'active');\n\n\t\t\treplaceNode( item, item_new);\n\t\t}\n\n\t\t// invalidate last query because we might have updated the sortField\n\t\tself.lastQuery = null;\n\t}\n\n\t/**\n\t * Removes a single option.\n\t *\n\t */\n\tremoveOption(value:string, silent?:boolean):void {\n\t\tconst self = this;\n\t\tvalue = get_hash(value);\n\n\t\tself.uncacheValue(value);\n\n\t\tdelete self.userOptions[value];\n\t\tdelete self.options[value];\n\t\tself.lastQuery = null;\n\t\tself.trigger('option_remove', value);\n\t\tself.removeItem(value, silent);\n\t}\n\n\t/**\n\t * Clears all options.\n\t */\n\tclearOptions(filter?:TomClearFilter ) {\n\n\t\tconst boundFilter = (filter || this.clearFilter).bind(this);\n\n\t\tthis.loadedSearches\t\t= {};\n\t\tthis.userOptions\t\t= {};\n\t\tthis.clearCache();\n\n\t\tconst selected:TomOptions\t= {};\n\t\titerate(this.options,(option:TomOption,key:string)=>{\n\t\t\tif( boundFilter(option,key as string) ){\n\t\t\t\tselected[key] = option;\n\t\t\t}\n\t\t});\n\n\t\tthis.options = this.sifter.items = selected;\n\t\tthis.lastQuery = null;\n\t\tthis.trigger('option_clear');\n\t}\n\n\t/**\n\t * Used by clearOptions() to decide whether or not an option should be removed\n\t * Return true to keep an option, false to remove\n\t *\n\t */\n\tclearFilter(option:TomOption,value:string){\n\t\tif( this.items.indexOf(value) >= 0 ){\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Returns the dom element of the option\n\t * matching the given value.\n\t *\n\t */\n\tgetOption(value:undefined|null|boolean|string|number, create:boolean=false):null|HTMLElement {\n\n\t\tconst hashed = hash_key(value);\n\t\tif( hashed === null ) return null;\n\n\t\tconst option = this.options[hashed];\n\t\tif( option != undefined ){\n\n\t\t\tif( option.$div ){\n\t\t\t\treturn option.$div;\n\t\t\t}\n\n\t\t\tif( create ){\n\t\t\t\treturn this._render('option', option);\n\t\t\t}\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Returns the dom element of the next or previous dom element of the same type\n\t * Note: adjacent options may not be adjacent DOM elements (optgroups)\n\t *\n\t */\n\tgetAdjacent( option:null|HTMLElement, direction:number, type:string = 'option' ) : HTMLElement|null{\n\t\tvar self = this, all;\n\n\t\tif( !option ){\n\t\t\treturn null;\n\t\t}\n\n\t\tif( type == 'item' ){\n\t\t\tall\t\t\t= self.controlChildren();\n\t\t}else{\n\t\t\tall\t\t\t= self.dropdown_content.querySelectorAll('[data-selectable]');\n\t\t}\n\n\t\tfor( let i = 0; i < all.length; i++ ){\n\t\t\tif( all[i] != option ){\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif( direction > 0 ){\n\t\t\t\treturn all[i+1] as HTMLElement;\n\t\t\t}\n\n\t\t\treturn all[i-1] as HTMLElement;\n\t\t}\n\t\treturn null;\n\t}\n\n\n\t/**\n\t * Returns the dom element of the item\n\t * matching the given value.\n\t *\n\t */\n\tgetItem(item:string|TomItem|null):null|TomItem {\n\n\t\tif( typeof item == 'object' ){\n\t\t\treturn item;\n\t\t}\n\n\t\tvar value = hash_key(item);\n\t\treturn value !== null\n\t\t\t? this.control.querySelector(`[data-value=\"${addSlashes(value)}\"]`)\n\t\t\t: null;\n\t}\n\n\t/**\n\t * \"Selects\" multiple items at once. Adds them to the list\n\t * at the current caret position.\n\t *\n\t */\n\taddItems( values:string|string[], silent?:boolean ):void{\n\t\tvar self = this;\n\n\t\tvar items = Array.isArray(values) ? values : [values];\n\t\titems = items.filter(x => self.items.indexOf(x) === -1);\n\t\tconst last_item = items[items.length - 1];\n\t\titems.forEach(item => {\n\t\t\tself.isPending = (item !== last_item);\n\t\t\tself.addItem(item, silent);\n\t\t});\n\t}\n\n\t/**\n\t * \"Selects\" an item. Adds it to the list\n\t * at the current caret position.\n\t *\n\t */\n\taddItem( value:string, silent?:boolean ):void{\n\t\tvar events = silent ? [] : ['change','dropdown_close'];\n\n\t\tdebounce_events(this, events, () => {\n\t\t\tvar item, wasFull;\n\t\t\tconst self = this;\n\t\t \tconst inputMode = self.settings.mode;\n\t\t\tconst hashed = hash_key(value);\n\n\t\t\tif( hashed && self.items.indexOf(hashed) !== -1 ){\n\n\t\t\t\tif( inputMode === 'single' ){\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\n\t\t\t\tif( inputMode === 'single' || !self.settings.duplicates ){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (hashed === null || !self.options.hasOwnProperty(hashed)) return;\n\t\t\tif (inputMode === 'single') self.clear(silent);\n\t\t\tif (inputMode === 'multi' && self.isFull()) return;\n\n\t\t\titem = self._render('item', self.options[hashed]);\n\n\t\t\tif( self.control.contains(item) ){ // duplicates\n\t\t\t\titem = item.cloneNode(true) as HTMLElement;\n\t\t\t}\n\n\t\t\twasFull = self.isFull();\n\t\t\tself.items.splice(self.caretPos, 0, hashed);\n\t\t\tself.insertAtCaret(item);\n\n\t\t\tif (self.isSetup) {\n\n\t\t\t\t// update menu / remove the option (if this is not one item being added as part of series)\n\t\t\t\tif( !self.isPending && self.settings.hideSelected ){\n\t\t\t\t\tlet option = self.getOption(hashed);\n\t\t\t\t\tlet next = self.getAdjacent(option, 1);\n\t\t\t\t\tif( next ){\n\t\t\t\t\t\tself.setActiveOption(next);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// refreshOptions after setActiveOption(),\n\t\t\t\t// otherwise setActiveOption() will be called by refreshOptions() with the wrong value\n\t\t\t\tif( !self.isPending && !self.settings.closeAfterSelect ){\n\t\t\t\t\tself.refreshOptions(self.isFocused && inputMode !== 'single');\n\t\t\t\t}\n\n\t\t\t\t// hide the menu if the maximum number of items have been selected or no options are left\n\t\t\t\tif( self.settings.closeAfterSelect != false && self.isFull() ){\n\t\t\t\t\tself.close();\n\t\t\t\t} else if (!self.isPending) {\n\t\t\t\t\tself.positionDropdown();\n\t\t\t\t}\n\n\t\t\t\tself.trigger('item_add', hashed, item);\n\n\t\t\t\tif (!self.isPending) {\n\t\t\t\t\tself.updateOriginalInput({silent: silent});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!self.isPending || (!wasFull && self.isFull())) {\n\t\t\t\tself.inputState();\n\t\t\t\tself.refreshState();\n\t\t\t}\n\n\t\t});\n\t}\n\n\t/**\n\t * Removes the selected item matching\n\t * the provided value.\n\t *\n\t */\n\tremoveItem( item:string|TomItem|null=null, silent?:boolean ){\n\t\tconst self\t\t= this;\n\t\titem\t\t\t= self.getItem(item);\n\n\t\tif( !item ) return;\n\n\t\tvar i,idx;\n\t\tconst value\t= item.dataset.value;\n\t\ti = nodeIndex(item);\n\n\t\titem.remove();\n\t\tif( item.classList.contains('active') ){\n\t\t\tidx = self.activeItems.indexOf(item);\n\t\t\tself.activeItems.splice(idx, 1);\n\t\t\tremoveClasses(item,'active');\n\t\t}\n\n\t\tself.items.splice(i, 1);\n\t\tself.lastQuery = null;\n\t\tif (!self.settings.persist && self.userOptions.hasOwnProperty(value)) {\n\t\t\tself.removeOption(value, silent);\n\t\t}\n\n\t\tif (i < self.caretPos) {\n\t\t\tself.setCaret(self.caretPos - 1);\n\t\t}\n\n\t\tself.updateOriginalInput({silent: silent});\n\t\tself.refreshState();\n\t\tself.positionDropdown();\n\t\tself.trigger('item_remove', value, item);\n\n\t}\n\n\t/**\n\t * Invokes the `create` method provided in the\n\t * TomSelect options that should provide the data\n\t * for the new item, given the user input.\n\t *\n\t * Once this completes, it will be added\n\t * to the item list.\n\t *\n\t */\n\tcreateItem( input:null|string=null, callback:TomCreateCallback = ()=>{} ):boolean{\n\n\t\t// triggerDropdown parameter @deprecated 2.1.1\n\t\tif( arguments.length === 3 ){\n\t\t\tcallback = arguments[2];\n\t\t}\n\t\tif( typeof callback != 'function' ){\n\t\t\tcallback = () => {};\n\t\t}\n\n\t\tvar self  = this;\n\t\tvar caret = self.caretPos;\n\t\tvar output;\n\t\tinput = input || self.inputValue();\n\n\t\tif (!self.canCreate(input)) {\n\t\t\tcallback();\n\t\t\treturn false;\n\t\t}\n\n\t\tself.lock();\n\n\t\tvar created = false;\n\t\tvar create = (data?:boolean|TomOption) => {\n\t\t\tself.unlock();\n\n\t\t\tif (!data || typeof data !== 'object') return callback();\n\t\t\tvar value = hash_key(data[self.settings.valueField]);\n\t\t\tif( typeof value !== 'string' ){\n\t\t\t\treturn callback();\n\t\t\t}\n\n\t\t\tself.setTextboxValue();\n\t\t\tself.addOption(data,true);\n\t\t\tself.setCaret(caret);\n\t\t\tself.addItem(value);\n\t\t\tcallback(data);\n\t\t\tcreated = true;\n\t\t};\n\n\t\tif( typeof self.settings.create === 'function' ){\n\t\t\toutput = self.settings.create.call(this, input, create);\n\t\t}else{\n\t\t\toutput = {\n\t\t\t\t[self.settings.labelField]: input,\n\t\t\t\t[self.settings.valueField]: input,\n\t\t\t};\n\t\t}\n\n\t\tif( !created ){\n\t\t\tcreate(output);\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Re-renders the selected item lists.\n\t */\n\trefreshItems() {\n\t\tvar self = this;\n\t\tself.lastQuery = null;\n\n\t\tif (self.isSetup) {\n\t\t\tself.addItems(self.items);\n\t\t}\n\n\t\tself.updateOriginalInput();\n\t\tself.refreshState();\n\t}\n\n\t/**\n\t * Updates all state-dependent attributes\n\t * and CSS classes.\n\t */\n\trefreshState() {\n\t\tconst self     = this;\n\n\t\tself.refreshValidityState();\n\n\t\tconst isFull\t= self.isFull();\n\t\tconst isLocked\t= self.isLocked;\n\n\t\tself.wrapper.classList.toggle('rtl',self.rtl);\n\n\n\t\tconst wrap_classList = self.wrapper.classList;\n\n\t\twrap_classList.toggle('focus', self.isFocused)\n\t\twrap_classList.toggle('disabled', self.isDisabled)\n\t\twrap_classList.toggle('required', self.isRequired)\n\t\twrap_classList.toggle('invalid', !self.isValid)\n\t\twrap_classList.toggle('locked', isLocked)\n\t\twrap_classList.toggle('full', isFull)\n\t\twrap_classList.toggle('input-active', self.isFocused && !self.isInputHidden)\n\t\twrap_classList.toggle('dropdown-active', self.isOpen)\n\t\twrap_classList.toggle('has-options', isEmptyObject(self.options) )\n\t\twrap_classList.toggle('has-items', self.items.length > 0);\n\n\t}\n\n\n\t/**\n\t * Update the `required` attribute of both input and control input.\n\t *\n\t * The `required` property needs to be activated on the control input\n\t * for the error to be displayed at the right place. `required` also\n\t * needs to be temporarily deactivated on the input since the input is\n\t * hidden and can't show errors.\n\t */\n\trefreshValidityState() {\n\t\tvar self = this;\n\n\t\tif( !self.input.validity ){\n\t\t\treturn;\n\t\t}\n\n\t\tself.isValid = self.input.validity.valid;\n\t\tself.isInvalid = !self.isValid;\n\t}\n\n\t/**\n\t * Determines whether or not more items can be added\n\t * to the control without exceeding the user-defined maximum.\n\t *\n\t * @returns {boolean}\n\t */\n\tisFull() {\n\t\treturn this.settings.maxItems !== null && this.items.length >= this.settings.maxItems;\n\t}\n\n\t/**\n\t * Refreshes the original <select> or <input>\n\t * element to reflect the current state.\n\t *\n\t */\n\tupdateOriginalInput( opts:TomArgObject = {} ){\n\t\tconst self = this;\n\t\tvar option, label;\n\n\t\tconst empty_option = self.input.querySelector('option[value=\"\"]') as HTMLOptionElement;\n\n\t\tif( self.is_select_tag ){\n\n\t\t\tconst selected:HTMLOptionElement[]\t\t= [];\n\t\t\tconst has_selected:number\t\t\t\t= self.input.querySelectorAll('option:checked').length;\n\n\t\t\tfunction AddSelected(option_el:HTMLOptionElement|null, value:string, label:string):HTMLOptionElement{\n\n\t\t\t\tif( !option_el ){\n\t\t\t\t\toption_el = getDom('<option value=\"' + escape_html(value) + '\">' + escape_html(label) + '</option>') as HTMLOptionElement;\n\t\t\t\t}\n\n\t\t\t\t// don't move empty option from top of list\n\t\t\t\t// fixes bug in firefox https://bugzilla.mozilla.org/show_bug.cgi?id=1725293\n\t\t\t\tif( option_el != empty_option ){\n\t\t\t\t\tself.input.append(option_el);\n\t\t\t\t}\n\n\t\t\t\tselected.push(option_el);\n\n\t\t\t\t// marking empty option as selected can break validation\n\t\t\t\t// fixes https://github.com/orchidjs/tom-select/issues/303\n\t\t\t\tif( option_el != empty_option || has_selected > 0 ){\n\t\t\t\t\toption_el.selected = true;\n\t\t\t\t}\n\n\t\t\t\treturn option_el;\n\t\t\t}\n\n\t\t\t// unselect all selected options\n\t\t\tself.input.querySelectorAll('option:checked').forEach((option_el:Element) => {\n\t\t\t\t(<HTMLOptionElement>option_el).selected = false;\n\t\t\t});\n\n\n\t\t\t// nothing selected?\n\t\t\tif( self.items.length == 0 && self.settings.mode == 'single' ){\n\n\t\t\t\tAddSelected(empty_option, \"\", \"\");\n\n\t\t\t// order selected <option> tags for values in self.items\n\t\t\t}else{\n\n\t\t\t\tself.items.forEach((value)=>{\n\t\t\t\t\toption\t\t\t= self.options[value]!;\n\t\t\t\t\tlabel\t\t\t= option[self.settings.labelField] || '';\n\n\t\t\t\t\tif( selected.includes(option.$option) ){\n\t\t\t\t\t\tconst reuse_opt = self.input.querySelector(`option[value=\"${addSlashes(value)}\"]:not(:checked)`) as HTMLOptionElement;\n\t\t\t\t\t\tAddSelected(reuse_opt, value, label);\n\t\t\t\t\t}else{\n\t\t\t\t\t\toption.$option\t= AddSelected(option.$option, value, label);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t}\n\n\t\t} else {\n\t\t\tself.input.value = self.getValue() as string;\n\t\t}\n\n\t\tif (self.isSetup) {\n\t\t\tif (!opts.silent) {\n\t\t\t\tself.trigger('change', self.getValue() );\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Shows the autocomplete dropdown containing\n\t * the available options.\n\t */\n\topen() {\n\t\tvar self = this;\n\n\t\tif (self.isLocked || self.isOpen || (self.settings.mode === 'multi' && self.isFull())) return;\n\t\tself.isOpen = true;\n\t\tsetAttr(self.focus_node,{'aria-expanded': 'true'});\n\t\tself.refreshState();\n\t\tapplyCSS(self.dropdown,{visibility: 'hidden', display: 'block'});\n\t\tself.positionDropdown();\n\t\tapplyCSS(self.dropdown,{visibility: 'visible', display: 'block'});\n\t\tself.focus();\n\t\tself.trigger('dropdown_open', self.dropdown);\n\t}\n\n\t/**\n\t * Closes the autocomplete dropdown menu.\n\t */\n\tclose(setTextboxValue=true) {\n\t\tvar self = this;\n\t\tvar trigger = self.isOpen;\n\n\t\tif( setTextboxValue ){\n\n\t\t\t// before blur() to prevent form onchange event\n\t\t\tself.setTextboxValue();\n\n\t\t\tif (self.settings.mode === 'single' && self.items.length) {\n\t\t\t\tself.hideInput();\n\t\t\t}\n\t\t}\n\n\t\tself.isOpen = false;\n\t\tsetAttr(self.focus_node,{'aria-expanded': 'false'});\n\t\tapplyCSS(self.dropdown,{display: 'none'});\n\t\tif( self.settings.hideSelected ){\n\t\t\tself.clearActiveOption();\n\t\t}\n\t\tself.refreshState();\n\n\t\tif (trigger) self.trigger('dropdown_close', self.dropdown);\n\t}\n\n\t/**\n\t * Calculates and applies the appropriate\n\t * position of the dropdown if dropdownParent = 'body'.\n\t * Otherwise, position is determined by css\n\t */\n\tpositionDropdown(){\n\n\t\tif( this.settings.dropdownParent !== 'body' ){\n\t\t\treturn;\n\t\t}\n\n\t\tvar context\t\t\t= this.control;\n\t\tvar rect\t\t\t= context.getBoundingClientRect();\n\t\tvar top\t\t\t\t= context.offsetHeight + rect.top  + window.scrollY;\n\t\tvar left\t\t\t= rect.left + window.scrollX;\n\n\n\t\tapplyCSS(this.dropdown,{\n\t\t\twidth : rect.width + 'px',\n\t\t\ttop   : top + 'px',\n\t\t\tleft  : left + 'px'\n\t\t});\n\n\t}\n\n\t/**\n\t * Resets / clears all selected items\n\t * from the control.\n\t *\n\t */\n\tclear(silent?:boolean) {\n\t\tvar self = this;\n\n\t\tif (!self.items.length) return;\n\n\t\tvar items = self.controlChildren();\n\t\titerate(items,(item:TomItem)=>{\n\t\t\tself.removeItem(item,true);\n\t\t});\n\n\t\tself.showInput();\n\t\tif( !silent ) self.updateOriginalInput();\n\t\tself.trigger('clear');\n\t}\n\n\t/**\n\t * A helper method for inserting an element\n\t * at the current caret position.\n\t *\n\t */\n\tinsertAtCaret(el:HTMLElement) {\n\t\tconst self\t\t= this;\n\t\tconst caret\t\t= self.caretPos;\n\t\tconst target\t= self.control;\n\n\t\ttarget.insertBefore(el, target.children[caret] || null);\n\t\tself.setCaret(caret + 1);\n\t}\n\n\t/**\n\t * Removes the current selected item(s).\n\t *\n\t */\n\tdeleteSelection(e:KeyboardEvent):boolean {\n\t\tvar direction, selection, caret, tail;\n\t\tvar self = this;\n\n\t\tdirection = (e && e.keyCode === constants.KEY_BACKSPACE) ? -1 : 1;\n\t\tselection = getSelection(self.control_input);\n\n\n\t\t// determine items that will be removed\n\t\tconst rm_items:TomItem[]\t= [];\n\n\t\tif (self.activeItems.length) {\n\n\t\t\ttail = getTail(self.activeItems, direction);\n\t\t\tcaret = nodeIndex(tail);\n\n\t\t\tif (direction > 0) { caret++; }\n\n\t\t\titerate(self.activeItems, (item:TomItem) => rm_items.push(item) );\n\n\t\t} else if ((self.isFocused || self.settings.mode === 'single') && self.items.length) {\n\t\t\tconst items = self.controlChildren();\n\t\t\tlet rm_item;\n\t\t\tif( direction < 0 && selection.start === 0 && selection.length === 0 ){\n\t\t\t\trm_item = items[self.caretPos - 1];\n\n\t\t\t}else if( direction > 0 && selection.start === self.inputValue().length ){\n\t\t\t\trm_item = items[self.caretPos];\n\t\t\t}\n\n\t\t\tif( rm_item !== undefined ){\n\t\t\t\trm_items.push( rm_item );\n\t\t\t}\n\t\t}\n\n\t\tif( !self.shouldDelete(rm_items,e) ){\n\t\t\treturn false;\n\t\t}\n\n\t\tpreventDefault(e,true);\n\n\t\t// perform removal\n\t\tif (typeof caret !== 'undefined') {\n\t\t\tself.setCaret(caret);\n\t\t}\n\n\t\twhile( rm_items.length ){\n\t\t\tself.removeItem(rm_items.pop());\n\t\t}\n\n\t\tself.showInput();\n\t\tself.positionDropdown();\n\t\tself.refreshOptions(false);\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Return true if the items should be deleted\n\t */\n\tshouldDelete(items:TomItem[],evt:MouseEvent|KeyboardEvent){\n\n\t\tconst values = items.map(item => item.dataset.value);\n\n\t\t// allow the callback to abort\n\t\tif( !values.length || (typeof this.settings.onDelete === 'function' && this.settings.onDelete(values,evt) === false) ){\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Selects the previous / next item (depending on the `direction` argument).\n\t *\n\t * > 0 - right\n\t * < 0 - left\n\t *\n\t */\n\tadvanceSelection(direction:number, e?:MouseEvent|KeyboardEvent) {\n\t\tvar last_active, adjacent, self = this;\n\n\t\tif (self.rtl) direction *= -1;\n\t\tif( self.inputValue().length ) return;\n\n\n\t\t// add or remove to active items\n\t\tif( isKeyDown(constants.KEY_SHORTCUT,e) || isKeyDown('shiftKey',e) ){\n\n\t\t\tlast_active\t\t\t= self.getLastActive(direction);\n\t\t\tif( last_active ){\n\n\t\t\t\tif( !last_active.classList.contains('active') ){\n\t\t\t\t\tadjacent\t\t\t= last_active;\n\t\t\t\t}else{\n\t\t\t\t\tadjacent\t\t\t= self.getAdjacent(last_active,direction,'item');\n\t\t\t\t}\n\n\t\t\t// if no active item, get items adjacent to the control input\n\t\t\t}else if( direction > 0 ){\n\t\t\t\tadjacent\t\t\t= self.control_input.nextElementSibling;\n\t\t\t}else{\n\t\t\t\tadjacent\t\t\t= self.control_input.previousElementSibling;\n\t\t\t}\n\n\n\t\t\tif( adjacent ){\n\t\t\t\tif( adjacent.classList.contains('active') ){\n\t\t\t\t\tself.removeActiveItem(last_active);\n\t\t\t\t}\n\t\t\t\tself.setActiveItemClass(adjacent); // mark as last_active !! after removeActiveItem() on last_active\n\t\t\t}\n\n\t\t// move caret to the left or right\n\t\t}else{\n\t\t\tself.moveCaret(direction);\n\t\t}\n\t}\n\n\tmoveCaret(direction:number){}\n\n\t/**\n\t * Get the last active item\n\t *\n\t */\n\tgetLastActive(direction?:number){\n\n\t\tlet last_active = this.control.querySelector('.last-active');\n\t\tif( last_active ){\n\t\t\treturn last_active;\n\t\t}\n\n\n\t\tvar result = this.control.querySelectorAll('.active');\n\t\tif( result ){\n\t\t\treturn getTail(result,direction);\n\t\t}\n\t}\n\n\n\t/**\n\t * Moves the caret to the specified index.\n\t *\n\t * The input must be moved by leaving it in place and moving the\n\t * siblings, due to the fact that focus cannot be restored once lost\n\t * on mobile webkit devices\n\t *\n\t */\n\tsetCaret(new_pos:number) {\n\t\tthis.caretPos = this.items.length;\n\t}\n\n\t/**\n\t * Return list of item dom elements\n\t *\n\t */\n\tcontrolChildren():TomItem[]{\n\t\treturn Array.from( this.control.querySelectorAll('[data-ts-item]') ) as TomItem[];\n\t}\n\n\t/**\n\t * Disables user input on the control. Used while\n\t * items are being asynchronously created.\n\t */\n\tlock() {\n\t\tthis.isLocked = true;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Re-enables user input on the control.\n\t */\n\tunlock() {\n\t\tthis.isLocked = false;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Disables user input on the control completely.\n\t * While disabled, it cannot receive focus.\n\t */\n\tdisable() {\n\t\tvar self = this;\n\t\tself.input.disabled\t\t\t\t= true;\n\t\tself.control_input.disabled\t\t= true;\n\t\tself.focus_node.tabIndex\t\t= -1;\n\t\tself.isDisabled\t\t\t\t\t= true;\n\t\tthis.close();\n\t\tself.lock();\n\t}\n\n\t/**\n\t * Enables the control so that it can respond\n\t * to focus and user input.\n\t */\n\tenable() {\n\t\tvar self = this;\n\t\tself.input.disabled\t\t\t\t= false;\n\t\tself.control_input.disabled\t\t= false;\n\t\tself.focus_node.tabIndex\t\t= self.tabIndex;\n\t\tself.isDisabled\t\t\t\t\t= false;\n\t\tself.unlock();\n\t}\n\n\t/**\n\t * Completely destroys the control and\n\t * unbinds all event listeners so that it can\n\t * be garbage collected.\n\t */\n\tdestroy() {\n\t\tvar self = this;\n\t\tvar revertSettings = self.revertSettings;\n\n\t\tself.trigger('destroy');\n\t\tself.off();\n\t\tself.wrapper.remove();\n\t\tself.dropdown.remove();\n\n\t\tself.input.innerHTML = revertSettings.innerHTML;\n\t\tself.input.tabIndex = revertSettings.tabIndex;\n\n\t\tremoveClasses(self.input,'tomselected','ts-hidden-accessible');\n\n\t\tself._destroy();\n\n\t\tdelete self.input.tomselect;\n\t}\n\n\t/**\n\t * A helper method for rendering \"item\" and\n\t * \"option\" templates, given the data.\n\t *\n\t */\n\trender( templateName:TomTemplateNames, data?:any ):null|HTMLElement{\n\t\tvar id, html;\n\t\tconst self = this;\n\n\t\tif( typeof this.settings.render[templateName] !== 'function' ){\n\t\t\treturn null;\n\t\t}\n\n\t\t// render markup\n\t\thtml = self.settings.render[templateName].call(this, data, escape_html);\n\n\t\tif( !html ){\n\t\t\treturn null;\n\t\t}\n\n\t\thtml = getDom( html );\n\n\t\t// add mandatory attributes\n\t\tif (templateName === 'option' || templateName === 'option_create') {\n\n\t\t\tif( data[self.settings.disabledField] ){\n\t\t\t\tsetAttr(html,{'aria-disabled':'true'});\n\t\t\t}else{\n\t\t\t\tsetAttr(html,{'data-selectable': ''});\n\t\t\t}\n\n\t\t}else if (templateName === 'optgroup') {\n\t\t\tid = data.group[self.settings.optgroupValueField];\n\t\t\tsetAttr(html,{'data-group': id});\n\t\t\tif(data.group[self.settings.disabledField]) {\n\t\t\t\tsetAttr(html,{'data-disabled': ''});\n\t\t\t}\n\t\t}\n\n\t\tif (templateName === 'option' || templateName === 'item') {\n\t\t\tconst value\t= get_hash(data[self.settings.valueField]);\n\t\t\tsetAttr(html,{'data-value': value });\n\n\n\t\t\t// make sure we have some classes if a template is overwritten\n\t\t\tif( templateName === 'item' ){\n\t\t\t\taddClasses(html,self.settings.itemClass);\n\t\t\t\tsetAttr(html,{'data-ts-item':''});\n\t\t\t}else{\n\t\t\t\taddClasses(html,self.settings.optionClass);\n\t\t\t\tsetAttr(html,{\n\t\t\t\t\trole:'option',\n\t\t\t\t\tid:data.$id\n\t\t\t\t});\n\n\t\t\t\t// update cache\n\t\t\t\tdata.$div = html;\n\t\t\t\tself.options[value] = data;\n\t\t\t}\n\n\n\t\t}\n\n\t\treturn html;\n\n\t}\n\n\n\t/**\n\t * Type guarded rendering\n\t *\n\t */\n\t_render( templateName:TomTemplateNames, data?:any ):HTMLElement{\n\t\tconst html = this.render(templateName, data);\n\n\t\tif( html == null ){\n\t\t\tthrow 'HTMLElement expected';\n\t\t}\n\t\treturn html;\n\t}\n\n\n\t/**\n\t * Clears the render cache for a template. If\n\t * no template is given, clears all render\n\t * caches.\n\t *\n\t */\n\tclearCache():void{\n\n\t\titerate(this.options, (option:TomOption)=>{\n\t\t\tif( option.$div ){\n\t\t\t\toption.$div.remove();\n\t\t\t\tdelete option.$div;\n\t\t\t}\n\t\t});\n\n\t}\n\n\t/**\n\t * Removes a value from item and option caches\n\t *\n\t */\n\tuncacheValue(value:string){\n\n\t\tconst option_el\t\t\t= this.getOption(value);\n\t\tif( option_el ) option_el.remove();\n\n\t}\n\n\t/**\n\t * Determines whether or not to display the\n\t * create item prompt, given a user input.\n\t *\n\t */\n\tcanCreate( input:string ):boolean {\n\t\treturn this.settings.create && (input.length > 0) && (this.settings.createFilter as TomCreateFilter ).call(this, input);\n\t}\n\n\n\t/**\n\t * Wraps this.`method` so that `new_fn` can be invoked 'before', 'after', or 'instead' of the original method\n\t *\n\t * this.hook('instead','onKeyDown',function( arg1, arg2 ...){\n\t *\n\t * });\n\t */\n\thook( when:string, method:string, new_fn:any ){\n\t\tvar self = this;\n\t\tvar orig_method = self[method];\n\n\n\t\tself[method] = function(){\n\t\t\tvar result, result_new;\n\n\t\t\tif( when === 'after' ){\n\t\t\t\tresult = orig_method.apply(self, arguments);\n\t\t\t}\n\n\t\t\tresult_new = new_fn.apply(self, arguments );\n\n\t\t\tif( when === 'instead' ){\n\t\t\t\treturn result_new;\n\t\t\t}\n\n\t\t\tif( when === 'before' ){\n\t\t\t\tresult = orig_method.apply(self, arguments);\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\n\t}\n\n};\n", "/**\n * microplugin.js\n * Copyright (c) 2013 <PERSON> & <PERSON>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\n\ntype TSettings = {\n\t[key:string]:any\n}\n\ntype TPlugins = {\n\tnames: string[],\n\tsettings: TSettings,\n\trequested: {[key:string]:boolean},\n\tloaded: {[key:string]:any}\n};\n\nexport type TPluginItem = {name:string,options:{}};\nexport type TPluginHash = {[key:string]:{}};\n\n\n\n\nexport default function MicroPlugin(Interface: any ){\n\n\tInterface.plugins = {};\n\n\treturn class extends Interface{\n\n\t\tpublic plugins:TPlugins = {\n\t\t\tnames     : [],\n\t\t\tsettings  : {},\n\t\t\trequested : {},\n\t\t\tloaded    : {}\n\t\t};\n\n\t\t/**\n\t\t * Registers a plugin.\n\t\t *\n\t\t * @param {function} fn\n\t\t */\n\t\tstatic define(name:string, fn:(this:any,settings:TSettings)=>any){\n\t\t\tInterface.plugins[name] = {\n\t\t\t\t'name' : name,\n\t\t\t\t'fn'   : fn\n\t\t\t};\n\t\t}\n\n\n\t\t/**\n\t\t * Initializes the listed plugins (with options).\n\t\t * Acceptable formats:\n\t\t *\n\t\t * List (without options):\n\t\t *   ['a', 'b', 'c']\n\t\t *\n\t\t * List (with options):\n\t\t *   [{'name': 'a', options: {}}, {'name': 'b', options: {}}]\n\t\t *\n\t\t * Hash (with options):\n\t\t *   {'a': { ... }, 'b': { ... }, 'c': { ... }}\n\t\t *\n\t\t * @param {array|object} plugins\n\t\t */\n\t\tinitializePlugins(plugins:string[]|TPluginItem[]|TPluginHash) {\n\t\t\tvar key, name;\n\t\t\tconst self  = this;\n\t\t\tconst queue:string[] = [];\n\n\t\t\tif (Array.isArray(plugins)) {\n\t\t\t\tplugins.forEach((plugin:string|TPluginItem)=>{\n\t\t\t\t\tif (typeof plugin === 'string') {\n\t\t\t\t\t\tqueue.push(plugin);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.plugins.settings[plugin.name] = plugin.options;\n\t\t\t\t\t\tqueue.push(plugin.name);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else if (plugins) {\n\t\t\t\tfor (key in plugins) {\n\t\t\t\t\tif (plugins.hasOwnProperty(key)) {\n\t\t\t\t\t\tself.plugins.settings[key] = plugins[key];\n\t\t\t\t\t\tqueue.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twhile( name = queue.shift() ){\n\t\t\t\tself.require(name);\n\t\t\t}\n\t\t}\n\n\t\tloadPlugin(name:string) {\n\t\t\tvar self    = this;\n\t\t\tvar plugins = self.plugins;\n\t\t\tvar plugin  = Interface.plugins[name];\n\n\t\t\tif (!Interface.plugins.hasOwnProperty(name)) {\n\t\t\t\tthrow new Error('Unable to find \"' +  name + '\" plugin');\n\t\t\t}\n\n\t\t\tplugins.requested[name] = true;\n\t\t\tplugins.loaded[name] = plugin.fn.apply(self, [self.plugins.settings[name] || {}]);\n\t\t\tplugins.names.push(name);\n\t\t}\n\n\t\t/**\n\t\t * Initializes a plugin.\n\t\t *\n\t\t */\n\t\trequire(name:string) {\n\t\t\tvar self = this;\n\t\t\tvar plugins = self.plugins;\n\n\t\t\tif (!self.plugins.loaded.hasOwnProperty(name)) {\n\t\t\t\tif (plugins.requested[name]) {\n\t\t\t\t\tthrow new Error('Plugin has circular dependency (\"' + name + '\")');\n\t\t\t\t}\n\t\t\t\tself.loadPlugin(name);\n\t\t\t}\n\n\t\t\treturn plugins.loaded[name];\n\t\t}\n\n\t};\n\n}\n", "import Tom<PERSON>elect from './tom-select';\n\nimport caret_position from './plugins/caret_position/plugin';\nimport dropdown_input from './plugins/dropdown_input/plugin';\nimport no_backspace_delete from './plugins/no_backspace_delete/plugin';\nimport remove_button from './plugins/remove_button/plugin';\nimport restore_on_backspace from './plugins/restore_on_backspace/plugin';\n\nTomSelect.define('caret_position', caret_position);\nTomSelect.define('dropdown_input', dropdown_input);\nTomSelect.define('no_backspace_delete', no_backspace_delete);\nTomSelect.define('remove_button', remove_button);\nTomSelect.define('restore_on_backspace', restore_on_backspace);\n\nexport default TomSelect;\n", "/**\n * Plugin: \"dropdown_input\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\nimport { nodeIndex, removeClasses } from '../../vanilla';\n\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\n\t/**\n\t * Moves the caret to the specified index.\n\t *\n\t * The input must be moved by leaving it in place and moving the\n\t * siblings, due to the fact that focus cannot be restored once lost\n\t * on mobile webkit devices\n\t *\n\t */\n\tself.hook('instead','setCaret',(new_pos:number) => {\n\n\t\tif( self.settings.mode === 'single' || !self.control.contains(self.control_input) ) {\n\t\t\tnew_pos = self.items.length;\n\t\t} else {\n\t\t\tnew_pos = Math.max(0, Math.min(self.items.length, new_pos));\n\n\t\t\tif( new_pos != self.caretPos && !self.isPending ){\n\n\t\t\t\tself.controlChildren().forEach((child,j) => {\n\t\t\t\t\tif( j < new_pos ){\n\t\t\t\t\t\tself.control_input.insertAdjacentElement('beforebegin', child );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.control.appendChild( child );\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tself.caretPos = new_pos;\n\t});\n\n\tself.hook('instead','moveCaret',(direction:number) => {\n\n\t\tif( !self.isFocused ) return;\n\n\t\t// move caret before or after selected items\n\t\tconst last_active\t\t= self.getLastActive(direction);\n\t\tif( last_active ){\n\t\t\tconst idx = nodeIndex(last_active);\n\t\t\tself.setCaret(direction > 0 ? idx + 1: idx);\n\t\t\tself.setActiveItem();\n\t\t\tremoveClasses(last_active as HTMLElement,'last-active');\n\n\t\t// move caret left or right of current position\n\t\t}else{\n\t\t\tself.setCaret(self.caretPos + direction);\n\n\t\t}\n\n\t});\n\n};\n", "/**\n * Plugin: \"dropdown_input\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\nimport * as constants from '../../constants';\nimport { getDom, addClasses } from '../../vanilla';\nimport { addEvent, preventDefault } from '../../utils';\n\n\nexport default function(this:TomSelect) {\n\tconst self = this;\n\n\tself.settings.shouldOpen = true; // make sure the input is shown even if there are no options to display in the dropdown\n\n\tself.hook('before','setup',()=>{\n\t\tself.focus_node\t\t= self.control;\n\n\t\taddClasses( self.control_input, 'dropdown-input');\n\n\t \tconst div = getDom('<div class=\"dropdown-input-wrap\">');\n\t\tdiv.append(self.control_input);\n\t\tself.dropdown.insertBefore(div, self.dropdown.firstChild);\n\n\t\t// set a placeholder in the select control\n\t\tconst placeholder = getDom('<input class=\"items-placeholder\" tabindex=\"-1\" />') as HTMLInputElement;\n\t\tplaceholder.placeholder = self.settings.placeholder ||'';\n\t\tself.control.append(placeholder);\n\n\t});\n\n\n\tself.on('initialize',()=>{\n\n\t\t// set tabIndex on control to -1, otherwise [shift+tab] will put focus right back on control_input\n\t\tself.control_input.addEventListener('keydown',(evt:KeyboardEvent) =>{\n\t\t//addEvent(self.control_input,'keydown' as const,(evt:KeyboardEvent) =>{\n\t\t\tswitch( evt.keyCode ){\n\t\t\t\tcase constants.KEY_ESC:\n\t\t\t\t\tif (self.isOpen) {\n\t\t\t\t\t\tpreventDefault(evt,true);\n\t\t\t\t\t\tself.close();\n\t\t\t\t\t}\n\t\t\t\t\tself.clearActiveItems();\n\t\t\t\treturn;\n\t\t\t\tcase constants.KEY_TAB:\n\t\t\t\t\tself.focus_node.tabIndex = -1;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\treturn self.onKeyDown.call(self,evt);\n\t\t});\n\n\t\tself.on('blur',()=>{\n\t\t\tself.focus_node.tabIndex = self.isDisabled ? -1 : self.tabIndex;\n\t\t});\n\n\n\t\t// give the control_input focus when the dropdown is open\n\t\tself.on('dropdown_open',() =>{\n\t\t\tself.control_input.focus();\n\t\t});\n\n\t\t// prevent onBlur from closing when focus is on the control_input\n\t\tconst orig_onBlur = self.onBlur;\n\t\tself.hook('instead','onBlur',(evt?:FocusEvent)=>{\n\t\t\tif( evt && evt.relatedTarget == self.control_input ) return;\n\t\t\treturn orig_onBlur.call(self);\n\t\t});\n\n\t\taddEvent(self.control_input,'blur', () => self.onBlur() );\n\n\t\t// return focus to control to allow further keyboard input\n\t\tself.hook('before','close',() =>{\n\n\t\t\tif( !self.isOpen ) return;\n\t\t\tself.focus_node.focus({preventScroll: true});\n\t\t});\n\n\t});\n\n};\n", "/**\n * Plugin: \"input_autogrow\" (Tom <PERSON>)\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\tvar orig_deleteSelection = self.deleteSelection;\n\n\tthis.hook('instead','deleteSelection',(evt:KeyboardEvent) => {\n\n\t\tif( self.activeItems.length ){\n\t\t\treturn orig_deleteSelection.call(self, evt);\n\t\t}\n\n\t\treturn false;\n\t});\n\n};\n", "/**\n * Plugin: \"remove_button\" (Tom Select)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\nimport { getDom } from '../../vanilla';\nimport { escape_html, preventDefault, addEvent } from '../../utils';\nimport { TomOption, TomItem } from '../../types/index';\nimport { RBOptions } from './types';\n\nexport default function(this:TomSelect, userOptions:RBOptions) {\n\n\tconst options = Object.assign({\n\t\t\tlabel     : '&times;',\n\t\t\ttitle     : 'Remove',\n\t\t\tclassName : 'remove',\n\t\t\tappend    : true\n\t\t}, userOptions);\n\n\n\t//options.className = 'remove-single';\n\tvar self\t\t\t= this;\n\n\t// override the render method to add remove button to each item\n\tif( !options.append ){\n\t\treturn;\n\t}\n\n\tvar html = '<a href=\"javascript:void(0)\" class=\"' + options.className + '\" tabindex=\"-1\" title=\"' + escape_html(options.title) + '\">' + options.label + '</a>';\n\n\tself.hook('after','setupTemplates',() => {\n\n\t\tvar orig_render_item = self.settings.render.item;\n\n\t\tself.settings.render.item = (data:TomOption, escape:typeof escape_html) => {\n\n\t\t\tvar item = getDom(orig_render_item.call(self, data, escape)) as TomItem;\n\n\t\t\tvar close_button = getDom(html);\n\t\t\titem.appendChild(close_button);\n\n\t\t\taddEvent(close_button,'mousedown',(evt) => {\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t});\n\n\t\t\taddEvent(close_button,'click',(evt) => {\n\n\t\t\t\t// propagating will trigger the dropdown to show for single mode\n\t\t\t\tpreventDefault(evt,true);\n\n\t\t\t\tif( self.isLocked ) return;\n\t\t\t\tif( !self.shouldDelete([item],evt as MouseEvent) ) return;\n\n\t\t\t\tself.removeItem(item);\n\t\t\t\tself.refreshOptions(false);\n\t\t\t\tself.inputState();\n\t\t\t});\n\n\t\t\treturn item;\n\t\t};\n\n\t});\n\n\n};\n", "/**\n * Plugin: \"restore_on_backspace\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\nimport TomSelect from '../../tom-select';\nimport { TomOption } from '../../types/index';\n\ntype TPluginOptions = {\n\ttext?:(option:TomOption)=>string,\n};\n\nexport default function(this:TomSelect, userOptions:TPluginOptions) {\n\tconst self = this;\n\n\tconst options = Object.assign({\n\t\ttext: (option:TomOption) => {\n\t\t\treturn option[self.settings.labelField];\n\t\t}\n\t},userOptions);\n\n\tself.on('item_remove',function(value:string){\n\t\tif( !self.isFocused ){\n\t\t\treturn;\n\t\t}\n\n\t\tif( self.control_input.value.trim() === '' ){\n\t\t\tvar option = self.options[value];\n\t\t\tif( option ){\n\t\t\t\tself.setTextboxValue(options.text.call(self, option));\n\t\t\t}\n\t\t}\n\t});\n\n};\n"], "names": ["forEvents", "events", "callback", "split", "for<PERSON>ach", "event", "MicroEvent", "constructor", "this", "_events", "on", "fct", "event_array", "push", "off", "n", "arguments", "length", "undefined", "splice", "indexOf", "trigger", "args", "self", "apply", "arrayToPattern", "chars", "filter", "Boolean", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "sequencePattern", "array", "hasDuplicates", "pattern", "prev_char_count", "prev_pattern", "char", "i", "setToPattern", "toArray", "Set", "size", "escape_regex", "str", "replace", "reduce", "longest", "value", "Math", "max", "unicodeLength", "p", "Array", "from", "allSubstrings", "input", "result", "start", "substring", "subresult", "tmp", "slice", "char<PERSON>t", "unshift", "code_points", "unicode_map", "multi_char_reg", "latin_convert", "latin_condensed", "/", "0", "a", "aa", "ae", "ao", "au", "av", "ay", "b", "c", "d", "e", "f", "g", "h", "j", "k", "l", "m", "o", "oe", "oi", "oo", "ou", "q", "r", "s", "t", "th", "tz", "u", "v", "vy", "w", "y", "z", "hv", "latin", "unicode", "convert_pat", "RegExp", "Object", "keys", "normalize", "form", "<PERSON><PERSON><PERSON><PERSON>", "_asciifold", "toLowerCase", "generateSets", "unicode_sets", "addMatching", "folded", "to_add", "folded_set", "patt", "match", "add", "code_point_min", "code_point_max", "composed", "String", "fromCharCode", "code_point", "generator", "generateMap", "multi_char", "set", "sort", "multi_char_patt", "substringsToPattern", "min_replacement", "map", "sub_pat", "strings", "chars_replaced", "mapSequence", "sequencesToPattern", "sequences", "all", "sequence", "seq", "len", "substrs", "inSequences", "needle_seq", "end", "needle_parts", "parts", "part", "needle_part", "substr", "Sequence", "[object Object]", "min", "position", "last_piece", "clone", "JSON", "parse", "stringify", "last_part", "pop", "last_substr", "clone_last_len", "getPattern", "_code_points", "match_str", "overlapping", "added_types", "last", "has", "new_seq", "old_seq", "getAttr", "obj", "name", "getAttrNesting", "names", "shift", "scoreValue", "token", "weight", "score", "pos", "regex", "search", "string", "propToArray", "key", "isArray", "iterate", "object", "hasOwnProperty", "cmp", "Sifter", "items", "settings", "diacritics", "query", "respect_word_boundaries", "weights", "tokens", "words", "field_regex", "word", "field_match", "field", "options", "prepareSearch", "_getScoreFunction", "token_count", "fields", "field_count", "getAttrFn", "scoreObject", "data", "sum", "conjunction", "_getSortFunction", "implicit_score", "sort_flds", "sort_empty", "bind", "get_field", "id", "fld", "direction", "sort_fld", "optsUser", "assign", "trim", "tokenize", "total", "nesting", "fn_score", "item", "_", "fn_sort", "limit", "getDom", "j<PERSON>y", "HTMLElement", "isHtmlString", "tpl", "document", "createElement", "innerHTML", "content", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "arg", "triggerEvent", "dom_el", "event_name", "createEvent", "initEvent", "dispatchEvent", "applyCSS", "css", "style", "addClasses", "elmts", "classes", "norm_classes", "classesArray", "cast<PERSON><PERSON><PERSON><PERSON>", "el", "cls", "classList", "removeClasses", "remove", "_classes", "concat", "parentMatch", "target", "selector", "wrapper", "contains", "matches", "parentNode", "getTail", "list", "nodeIndex", "amongst", "nodeName", "previousElementSibling", "setAttr", "attrs", "val", "attr", "removeAttribute", "setAttribute", "replaceNode", "existing", "replacement", "<PERSON><PERSON><PERSON><PERSON>", "highlight", "element", "highlightText", "highlightRecursive", "node", "nodeType", "spannode", "className", "middlebit", "splitText", "index", "middle<PERSON>lone", "cloneNode", "append<PERSON><PERSON><PERSON>", "childNodes", "test", "tagName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "KEY_SHORTCUT", "navigator", "userAgent", "defaults", "optgroups", "plugins", "delimiter", "splitOn", "persist", "create", "createOnBlur", "createFilter", "openOnFocus", "shouldOpen", "maxOptions", "maxItems", "hideSelected", "duplicates", "addPrecedence", "selectOnTab", "preload", "allowEmptyOption", "loadThrottle", "loadingClass", "dataAttr", "optgroupField", "valueField", "labelField", "<PERSON><PERSON><PERSON>", "optgroupLabelField", "optgroupValueField", "lockOptgroupOrder", "sortField", "searchField", "searchConjunction", "mode", "wrapperClass", "controlClass", "dropdownClass", "dropdownContentClass", "itemClass", "optionClass", "dropdownParent", "controlInput", "copyClassesToDropdown", "placeholder", "hidePlaceholder", "shouldLoad", "render", "hash_key", "get_hash", "escape_html", "loadDebounce", "fn", "delay", "timeout", "loading", "clearTimeout", "setTimeout", "loadedSearches", "call", "debounce_events", "types", "type", "event_args", "preventDefault", "evt", "stop", "stopPropagation", "addEvent", "addEventListener", "isKeyDown", "key_name", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "metaKey", "getId", "existing_id", "getAttribute", "addSlashes", "append", "parent", "getSettings", "settings_user", "attr_data", "field_label", "field_value", "field_disabled", "field_optgroup", "field_optgroup_label", "field_optgroup_value", "tag_name", "option", "textContent", "optionsMap", "group_count", "readData", "addOption", "settings_element", "dataset", "json", "group", "arr", "option_data", "disabled", "$option", "selected", "hasAttribute", "children", "child", "optgroup", "optgroup_data", "data_raw", "opt", "values", "init_textbox", "instance_i", "TomSelect", "Interface", "super", "requested", "loaded", "initializePlugins", "queue", "plugin", "require", "loadPlugin", "Error", "MicroPlugin", "input_arg", "user_settings", "dir", "control_input", "dropdown", "control", "dropdown_content", "focus_node", "order", "tabIndex", "is_select_tag", "rtl", "inputId", "_destroy", "sifter", "isOpen", "isDisabled", "isRequired", "isInvalid", "<PERSON><PERSON><PERSON><PERSON>", "isLocked", "isFocused", "isInputHidden", "isSetup", "ignoreFocus", "ignoreHover", "hasOptions", "currentResults", "lastValue", "caretPos", "activeOption", "activeItems", "userOptions", "tomselect", "window", "getComputedStyle", "getPropertyValue", "required", "setupCallbacks", "setupTemplates", "_render", "inputMode", "setup", "passive_event", "passive", "listboxId", "role", "aria-haspopup", "aria-expanded", "control_id", "escape<PERSON><PERSON>y", "label", "label_click", "focus", "for", "label_id", "width", "classes_plugins", "multiple", "load", "target_match", "onOptionHover", "capture", "onOptionSelect", "onItemSelect", "onClick", "onKeyDown", "onKeyPress", "onInput", "onBlur", "onFocus", "onPaste", "doc_mousedown", "<PERSON><PERSON><PERSON>", "blur", "inputState", "win_scroll", "positionDropdown", "removeEventListener", "revertSettings", "insertAdjacentElement", "sync", "refreshState", "updateOriginalInput", "refreshItems", "close", "disable", "enable", "onChange", "setupOptions", "addOptions", "registerOptionGroup", "templates", "optgroup_header", "escape", "option_create", "no_results", "not_loading", "callbacks", "initialize", "change", "item_add", "item_remove", "item_select", "clear", "option_add", "option_remove", "option_clear", "optgroup_add", "optgroup_remove", "optgroup_clear", "dropdown_open", "dropdown_close", "get_settings", "setValue", "last<PERSON><PERSON>y", "clearActiveItems", "onMouseDown", "pastedText", "inputValue", "splitInput", "piece", "addItem", "createItem", "character", "keyCode", "which", "constants", "selectAll", "open", "next", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setActiveOption", "prev", "canSelect", "activeElement", "advanceSelection", "deleteSelection", "refreshOptions", "wasFocused", "showInput", "hasFocus", "deactivate", "setActiveItem", "setCaret", "parentElement", "closeAfterSelect", "canLoad", "loadCallback", "clearActiveOption", "setTextboxValue", "getValue", "silent", "addItems", "setMaxItems", "eventName", "begin", "swap", "getLastActive", "prototype", "setActiveItemClass", "removeActiveItem", "hideInput", "last_active", "idx", "scroll", "aria-activedescendant", "aria-selected", "scrollToOption", "behavior", "height_menu", "clientHeight", "scrollTop", "height_item", "offsetHeight", "getBoundingClientRect", "top", "scroll<PERSON>eh<PERSON>or", "controlChildren", "toggle", "offsetWidth", "getScoreFunction", "getSearchOptions", "calculateScore", "hashed", "triggerDropdown", "html", "has_create_option", "active_group", "groups", "groups_order", "same_query", "elements", "results", "active_option", "show_dropdown", "closest", "opt_value", "opt_hash", "option_el", "getOption", "includes", "group_fragment", "createDocumentFragment", "$id", "toString", "grp_a", "grp_b", "a_order", "$order", "group_heading", "group_options", "header", "group_html", "querySelectorAll", "tok", "add_template", "template", "insertBefore", "canCreate", "active_index", "selectable", "user_created", "dat", "registerOption", "addOptionGroup", "hashed_id", "removeOptionGroup", "clearCache", "clearOptionGroups", "updateOption", "item_new", "index_item", "value_old", "value_new", "data_old", "getItem", "uncacheValue", "option_new", "removeOption", "removeItem", "clearOptions", "boundFilter", "clearFilter", "$div", "last_item", "x", "isPending", "<PERSON><PERSON><PERSON>", "isFull", "insertAtCaret", "output", "caret", "lock", "created", "unlock", "refreshValidityState", "wrap_classList", "validity", "valid", "opts", "empty_option", "has_selected", "AddSelected", "visibility", "display", "context", "rect", "scrollY", "left", "scrollX", "selection", "tail", "selectionStart", "selectionEnd", "rm_items", "rm_item", "shouldDelete", "onDelete", "adjacent", "nextElement<PERSON><PERSON>ling", "moveCaret", "new_pos", "destroy", "templateName", "aria-disabled", "data-selectable", "data-disabled", "data-ts-item", "hook", "when", "method", "new_fn", "orig_method", "result_new", "define", "div", "orig_onBlur", "relatedTarget", "preventScroll", "orig_deleteSelection", "title", "orig_render_item", "close_button", "text"], "mappings": ";;;;;AAgBA,SAASA,EAAUC,EAAcC,GAChCD,EAAOE,MAAM,OAAOC,SAASC,IAC5BH,EAASG,MAII,MAAMC,EAIpBC,cAAaC,KAFNC,aAEM,EACPA,KAAAA,QAAU,GAGhBC,GAAGT,EAAeU,GACjBX,EAAUC,GAAQI,IACjB,MAAMO,EAAcJ,KAAKC,QAAQJ,IAAU;AAC3CO,EAAYC,KAAKF,GACjBH,KAAKC,QAAQJ,GAASO,KAIxBE,IAAIb,EAAeU,GAClB,IAAII,EAAIC,UAAUC;AACR,IAANF,EAKJf,EAAUC,GAAQI,IAEbU,GAAM,IAANA,EAEH,mBADOP,KAAKC,QAAQJ;AAIrB,MAAMO,EAAcJ,KAAKC,QAAQJ;KACba,IAAhBN,IAEJA,EAAYO,OAAOP,EAAYQ,QAAQT,GAAM,GAC7CH,KAAKC,QAAQJ,GAASO,MAfjBH,KAAAA,QAAU,GAmBjBY,QAAQpB,KAAkBqB,GACrBC,IAAAA,EAAOf;AAEXR,EAAUC,GAAQI,IACjB,MAAMO,EAAcW,EAAKd,QAAQJ;KACba,IAAhBN,GACJA,EAAYR,SAAQO,IACnBA,EAAIa,MAAMD,EAAMD,UC3DpB,MAAMG,EAAiBC,IACrBA,EAAQA,EAAMC,OAAOC,UAEXX,OAAS,EACVS,EAAM,IAAM,GAGW,GAAzBG,EAAeH,GAAc,IAAMA,EAAMI,KAAK,IAAM,IAAM,MAAQJ,EAAMI,KAAK,KAAO,IAOvFC,EAAkBC,IACtB,IAAKC,EAAcD,GACjB,OAAOA,EAAMF,KAAK;AAGpB,IAAII,EAAU,GACVC,EAAkB;AAEtB,MAAMC,EAAe,KACfD,EAAkB,IACpBD,GAAW,IAAMC,EAAkB;AAevC,OAXAH,EAAM5B,SAAQ,CAACiC,EAAMC,KACfD,IAASL,EAAMM,EAAI,IAKvBF,IACAF,GAAWG,EACXF,EAAkB,GANhBA,OAQJC,IACOF,GAUHK,EAAeb,IACnB,IAAIM,EAAQQ,EAAQd;AACpB,OAAOD,EAAeO,IAQlBC,EAAgBD,GACb,IAAIS,IAAIT,GAAOU,OAASV,EAAMf,OAQjC0B,EAAeC,IACXA,EAAM,IAAIC,QAAQ,qCAAsC,QAQ5DhB,EAAiBG,GACdA,EAAMc,QAAO,CAACC,EAASC,IAAUC,KAAKC,IAAIH,EAASI,EAAcH,KAAS,GAM7EG,EAAgBP,GACbJ,EAAQI,GAAK3B,OAOhBuB,EAAUY,GAAKC,MAAMC,KAAKF,GC7F1BG,EAAgBC,IACpB,GAAqB,IAAjBA,EAAMvC,OAAc,MAAO,CAAC,CAACuC;AAGjC,IAAIC,EAAS;AACb,MAAMC,EAAQF,EAAMG,UAAU;AAU9B,OATaJ,EAAcG,GACtBtD,SAAQ,SAAUwD,GACrB,IAAIC,EAAMD,EAAUE,MAAM;AAC1BD,EAAI,GAAKL,EAAMO,OAAO,GAAKF,EAAI,GAC/BJ,EAAO5C,KAAKgD,GACZA,EAAMD,EAAUE,MAAM,GACtBD,EAAIG,QAAQR,EAAMO,OAAO,IACzBN,EAAO5C,KAAKgD,MAEPJ,GCRHQ,EAAc,CAAC,CAAC,EAAG;AAIzB,IAAIC,EAGAC;AACJ,MAGMC,EAAgB,GAGhBC,EAAkB,CACtBC,IAAK,KACLC,EAAK,IACLC,EAAK,MACLC,GAAM,IACNC,GAAM,MACNC,GAAM,IACNC,GAAM,IACNC,GAAM,KACNC,GAAM,IACNC,EAAK,MACLC,EAAK,OACLC,EAAK,WACLC,EAAK,OACLC,EAAK,KACLC,EAAK,SACLC,EAAK,OACL/C,EAAK,KACLgD,EAAK,KACLC,EAAK,SACLC,EAAK,WACLC,EAAK,MACL1E,EAAK,UACL2E,EAAK,UACLC,GAAM,IACNC,GAAM,IACNC,GAAM,IACNC,GAAM,IACN1C,EAAK,SACL2C,EAAK,MACLC,EAAK,QACLC,EAAK,QACLC,EAAK,QACLC,GAAM,IACNC,GAAM,IACNC,EAAK,IACLC,EAAK,MACLC,GAAM,IACNC,EAAK,IACLC,EAAK,MACLC,EAAK,QACLC,GAAM;AAGR,IAAK,IAAIC,KAASvC,EAAiB,CACjC,IAAIwC,EAAUxC,EAAgBuC,IAAU;AAExC,IAAK,IAAItE,EAAI,EAAGA,EAAIuE,EAAQ5F,OAAQqB,IAAK,CACvC,IAAID,EAAOwE,EAAQlD,UAAUrB,EAAGA,EAAI;AACpC8B,EAAc/B,GAAQuE,GAI1B,MAAME,EAAc,IAAIC,OAAOC,OAAOC,KAAK7C,GAAetC,KAAK,KAAhCkF,YAAyD,MAkBlFE,EAAY,CAACtE,EAAKuE,EAAO,SAAWvE,EAAIsE,UAAUC,GASlDC,EAAYxE,GACTJ,EAAQI,GAAKE,QAKpB,CAACW,EAAQpB,IACAoB,EAAS4D,EAAWhF,IAC1B,IAOCgF,EAAazE,IACjBA,EAAMsE,EAAUtE,GAAK0E,cAAczE,QAAQiE,GAE3CzE,GACS+B,EAAc/B,IAAS,KAGzB6E,EAAUtE,EAAK;AA6CxB,MAAM2E,EAAetD,IAEnB,MAAMuD,EAAe,GAMfC,EAAc,CAACC,EAAQC,KAE3B,MAAMC,EAAaJ,EAAaE,IAAW,IAAIjF,IACzCoF,EAAO,IAAId,OAAO,IAAMxE,EAAaqF,GAAc,IAAK;AAE1DD,EAAOG,MAAMD,KAIjBD,EAAWG,IAAIpF,EAAagF,IAC5BH,EAAaE,GAAUE;AAGzB,IAAK,IAAI5E,KA1DX,UAAoBiB,GAClB,IAAK,MAAO+D,EAAgBC,KAAmBhE,EAC7C,IAAK,IAAI3B,EAAI0F,EAAgB1F,GAAK2F,EAAgB3F,IAAK,CACrD,IAAI4F,EAAWC,OAAOC,aAAa9F,GAC/BoF,EAASN,EAAUc;AAEnBR,GAAUQ,EAASZ,gBASnBI,EAAOzG,OAnIO,GAuIG,GAAjByG,EAAOzG,cAIL,CACJyG,OAAQA,EACRQ,SAAUA,EACVG,WAAY/F,MAgCAgG,CAAUrE,GAC1BwD,EAAYzE,EAAM0E,OAAQ1E,EAAM0E,QAChCD,EAAYzE,EAAM0E,OAAQ1E,EAAMkF;AAGlC,OAAOV,GAUHe,EAActE,IAElB,MAAMuD,EAAeD,EAAatD,GAG5BC,EAAc;AAGpB,IAAIsE,EAAa;AAEjB,IAAK,IAAId,KAAUF,EAAc,CAC/B,IAAIiB,EAAMjB,EAAaE;AAEnBe,IACFvE,EAAYwD,GAAUnF,EAAakG,IAGjCf,EAAOzG,OAAS,GAClBuH,EAAW3H,KAAK8B,EAAa+E,IAIjCc,EAAWE,MAAK,CAAClE,EAAGO,IAAMA,EAAE9D,OAASuD,EAAEvD;AACvC,MAAM0H,EAAkBlH,EAAe+G;AAEvC,OADArE,EAAiB,IAAI4C,OAAO,IAAM4B,EAAiB,KAC5CzE,GAuCH0E,EAAsB,CAAChG,EAAKiG,EAAkB,KAClDA,EAAkB5F,KAAKC,IAAI2F,EAAiBjG,EAAI3B,OAAS,GAClDQ,EAAe8B,EAAcX,GAAKkG,KAAIC,GAhC3B,EAACC,EAASH,EAAkB,KAC9C,IAAII,EAAiB;AASrB,OARAD,EAAUA,EAAQF,KAAIlG,IAChBsB,EAAYtB,KACdqG,GAAkBrG,EAAI3B,QAGjBiD,EAAYtB,IAAQA,KAGzBqG,GAAkBJ,EACb9G,EAAgBiH,GAGlB,IAmBEE,CAAYH,EAASF,OAW1BM,EAAqB,CAACC,EAAWC,GAAM,KAC3C,IAAIR,EAAkBO,EAAUnI,OAAS,EAAI,EAAI;AACjD,OAAOQ,EAAe2H,EAAUN,KAAIQ,IAClC,IAAIC,EAAM;AACV,MAAMC,EAAMH,EAAMC,EAASrI,SAAWqI,EAASrI,SAAW;AAE1D,IAAK,IAAIqE,EAAI,EAAGA,EAAIkE,EAAKlE,IACvBiE,EAAI1I,KAAK+H,EAAoBU,EAASG,QAAQnE,IAAM,GAAIuD;AAG1D,OAAO9G,EAAgBwH,QAUrBG,EAAc,CAACC,EAAYP,KAC/B,IAAK,MAAMG,KAAOH,EAAW,CAC3B,GAAIG,EAAI7F,OAASiG,EAAWjG,OAAS6F,EAAIK,KAAOD,EAAWC,IACzD;AAGF,GAAIL,EAAIE,QAAQ3H,KAAK,MAAQ6H,EAAWF,QAAQ3H,KAAK,IACnD;AAGF,IAAI+H,EAAeF,EAAWG;AAK9B,MAAMnI,EAASoI,IACb,IAAK,MAAMC,KAAeH,EAAc,CACtC,GAAIG,EAAYtG,QAAUqG,EAAKrG,OAASsG,EAAYC,SAAWF,EAAKE,OAClE,OAAO;AAGT,GAAmB,GAAfF,EAAK9I,QAAqC,GAAtB+I,EAAY/I,OAApC,CASA,GAAI8I,EAAKrG,MAAQsG,EAAYtG,OAASqG,EAAKH,IAAMI,EAAYtG,MAC3D,OAAO;AAGT,GAAIsG,EAAYtG,MAAQqG,EAAKrG,OAASsG,EAAYJ,IAAMG,EAAKrG,MAC3D,OAAO,GAIX,OAAO;AAKT,KAFe6F,EAAIO,MAAMnI,OAAOA,GAEnBV,OAAS,GAItB,OAAO,EAGT,OAAO;AAGT,MAAMiJ,EACJC,cAEE3J,KAAKsJ,MAAQ,GAGbtJ,KAAKiJ,QAAU,GACfjJ,KAAKkD,MAAQ,EACblD,KAAKoJ,IAAM,EAObO,IAAIJ,GACEA,IACFvJ,KAAKsJ,MAAMjJ,KAAKkJ,GAChBvJ,KAAKiJ,QAAQ5I,KAAKkJ,EAAKE,QACvBzJ,KAAKkD,MAAQT,KAAKmH,IAAIL,EAAKrG,MAAOlD,KAAKkD,OACvClD,KAAKoJ,IAAM3G,KAAKC,IAAI6G,EAAKH,IAAKpJ,KAAKoJ,MAIvCO,OACE,OAAO3J,KAAKsJ,MAAMtJ,KAAKsJ,MAAM7I,OAAS,GAGxCkJ,SACE,OAAO3J,KAAKsJ,MAAM7I,OAQpBkJ,MAAME,EAAUC,GACd,IAAIC,EAAQ,IAAIL,EACZJ,EAAQU,KAAKC,MAAMD,KAAKE,UAAUlK,KAAKsJ,QACvCa,EAAYb,EAAMc;AAEtB,IAAK,MAAMb,KAAQD,EACjBS,EAAMxC,IAAIgC;AAGZ,IAAIc,EAAcP,EAAWL,OAAOtG,UAAU,EAAG0G,EAAWM,EAAUjH,OAClEoH,EAAiBD,EAAY5J;AAOjC,OANAsJ,EAAMxC,IAAI,CACRrE,MAAOiH,EAAUjH,MACjBkG,IAAKe,EAAUjH,MAAQoH,EACvB7J,OAAQ6J,EACRb,OAAQY,IAEHN,GAqBX,MAAMQ,EAAanI,IA9VAoI,IAAAA;KACG9J,IAAhBgD,IACJA,EAAcqE,EAAYyC,GAAgB/G,IA8V1CrB,EAAMwE,EAAUxE;AAChB,IAAIV,EAAU,GACVkH,EAAY,CAAC,IAAIc;AAErB,IAAK,IAAI5H,EAAI,EAAGA,EAAIM,EAAI3B,OAAQqB,IAAK,CACnC,IACIwF,EADSlF,EAAIe,UAAUrB,GACRwF,MAAM3D;AACzB,MAAM9B,EAAOO,EAAIe,UAAUrB,EAAGA,EAAI,GAC5B2I,EAAYnD,EAAQA,EAAM,GAAK;AAGrC,IAAIoD,EAAc,GACdC,EAAc,IAAI1I;AAEtB,IAAK,MAAM6G,KAAYF,EAAW,CAChC,MAAMkB,EAAahB,EAAS8B;AAE5B,IAAKd,GAAmC,GAArBA,EAAWrJ,QAAeqJ,EAAWV,KAAOtH,EAE7D,GAAI2I,EAAW,CACb,MAAMzB,EAAMyB,EAAUhK;AACtBqI,EAASvB,IAAI,CACXrE,MAAOpB,EACPsH,IAAKtH,EAAIkH,EACTvI,OAAQuI,EACRS,OAAQgB,IAEVE,EAAYpD,IAAI,UAEhBuB,EAASvB,IAAI,CACXrE,MAAOpB,EACPsH,IAAKtH,EAAI,EACTrB,OAAQ,EACRgJ,OAAQ5H,IAEV8I,EAAYpD,IAAI;KAEb,GAAIkD,EAAW,CACpB,IAAIV,EAAQjB,EAASiB,MAAMjI,EAAGgI;AAC9B,MAAMd,EAAMyB,EAAUhK;AACtBsJ,EAAMxC,IAAI,CACRrE,MAAOpB,EACPsH,IAAKtH,EAAIkH,EACTvI,OAAQuI,EACRS,OAAQgB,IAEVC,EAAYrK,KAAK0J,QAIjBY,EAAYpD,IAAI,KAKpB,GAAImD,EAAYjK,OAAS,EAAzB,CAEEiK,EAAcA,EAAYxC,MAAK,CAAClE,EAAGO,IAC1BP,EAAEvD,SAAW8D,EAAE9D;AAGxB,IAAK,IAAIsJ,KAASW,EAEZxB,EAAYa,EAAOnB,IAIvBA,EAAUvI,KAAK0J,QAUnB,GAAIjI,EAAI,GAAyB,GAApB6I,EAAYzI,OAAcyI,EAAYE,IAAI,KAAM,CAC3DnJ,GAAWiH,EAAmBC,GAAW;AACzC,IAAIkC,EAAU,IAAIpB;AAClB,MAAMqB,EAAUnC,EAAU;AAEtBmC,GACFD,EAAQvD,IAAIwD,EAAQH,QAGtBhC,EAAY,CAACkC,IAKjB,OADApJ,GAAWiH,EAAmBC,GAAW,GAClClH,GC1gBHsJ,EAAU,CAACC,EAAKC,KACpB,GAAKD,EACL,OAAOA,EAAIC,IASPC,EAAiB,CAACF,EAAKC,KAC3B,GAAKD,EAAL,CAIA,IAHA,IAAI1B,EACA6B,EAAQF,EAAKvL,MAAM,MAEf4J,EAAO6B,EAAMC,WAAaJ,EAAMA,EAAI1B,MAE5C,OAAO0B,IAQHK,EAAa,CAAC9I,EAAO+I,EAAOC,KAChC,IAAIC,EAAOC;AACX,OAAKlJ,GACLA,GAAgB,GACG,MAAf+I,EAAMI,QAEG,KADbD,EAAMlJ,EAAMoJ,OAAOL,EAAMI,QADO,GAGhCF,EAAQF,EAAMM,OAAOpL,OAAS+B,EAAM/B,OACxB,IAARiL,IAAWD,GAAS,IACjBA,EAAQD,IAPI,GAcfM,EAAc,CAACb,EAAKc,KACxB,IAAIvJ,EAAQyI,EAAIc;AAChB,GAAoB,mBAATvJ,EAAqB,OAAOA;AAEnCA,IAAUK,MAAMmJ,QAAQxJ,KAC1ByI,EAAIc,GAAO,CAACvJ,KAcVyJ,EAAU,CAACC,EAAQxM,KACvB,GAAImD,MAAMmJ,QAAQE,GAChBA,EAAOtM,QAAQF;KAEf,IAAK,IAAIqM,KAAOG,EACVA,EAAOC,eAAeJ,IACxBrM,EAASwM,EAAOH,GAAMA,IAKxBK,EAAM,CAACpI,EAAGO,IACG,iBAANP,GAA+B,iBAANO,EAC3BP,EAAIO,EAAI,EAAIP,EAAIO,GAAK,EAAI,GAGlCP,EAAI4C,EAAU5C,EAAI,IAAI8C,gBACtBvC,EAAIqC,EAAUrC,EAAI,IAAIuC,eACJ,EACdvC,EAAIP,GAAW,EACZ;ACpET,MAAMqI,EASJ1C,YAAY2C,EAAOC,GACjBvM,KAAKsM,WAAQ,EACbtM,KAAKuM,cAAW,EAChBvM,KAAKsM,MAAQA,EACbtM,KAAKuM,SAAWA,GAAY,CAC1BC,YAAY,GAShB7C,SAAS8C,EAAOC,EAAyBC,GACvC,IAAKF,IAAUA,EAAMhM,OAAQ,MAAO;AACpC,MAAMmM,EAAS,GACTC,EAAQJ,EAAM9M,MAAM;AAC1B,IAAImN;AAgCJ,OA9BIH,IACFG,EAAc,IAAIvG,OAAO,KAAOC,OAAOC,KAAKkG,GAASrE,IAAInG,GAAcb,KAAK,KAAO,YAGrFuL,EAAMjN,SAAQmN,IACZ,IAAIC,EACAC,EAAQ,KACRtB,EAAQ;AAERmB,IAAgBE,EAAcD,EAAKzF,MAAMwF,MAC3CG,EAAQD,EAAY,GACpBD,EAAOC,EAAY,IAGjBD,EAAKtM,OAAS,IAEdkL,EADE3L,KAAKuM,SAASC,WACRjC,EAAWwC,IAAS,KAEpB5K,EAAa4K,GAGnBpB,GAASe,IAAyBf,EAAQ,MAAQA,IAGxDiB,EAAOvM,KAAK,CACVwL,OAAQkB,EACRpB,MAAOA,EAAQ,IAAIpF,OAAOoF,EAAO,MAAQ,KACzCsB,MAAOA,OAGJL,EAWTjD,iBAAiB8C,EAAOS,GACtB,IAAItB,EAAS5L,KAAKmN,cAAcV,EAAOS;AACvC,OAAOlN,KAAKoN,kBAAkBxB,GAQhCjC,kBAAkBiC,GAChB,MAAMgB,EAAShB,EAAOgB,OAChBS,EAAcT,EAAOnM;AAE3B,IAAK4M,EACH,OAAO,WACL,OAAO;AAIX,MAAMC,EAAS1B,EAAOsB,QAAQI,OACxBX,EAAUf,EAAOe,QACjBY,EAAcD,EAAO7M,OACrB+M,EAAY5B,EAAO4B;AAEzB,IAAKD,EACH,OAAO,WACL,OAAO;AAUX,MAAME,EACgB,IAAhBF,EACK,SAAUhC,EAAOmC,GACtB,MAAMT,EAAQK,EAAO,GAAGL;AACxB,OAAO3B,EAAWkC,EAAUE,EAAMT,GAAQ1B,EAAOoB,EAAQM,IAAU,IAIhE,SAAU1B,EAAOmC,GACtB,IAAIC,EAAM;AAEV,GAAIpC,EAAM0B,MAAO,CACf,MAAMzK,EAAQgL,EAAUE,EAAMnC,EAAM0B;CAE/B1B,EAAMI,OAASnJ,EAClBmL,GAAO,EAAIJ,EAEXI,GAAOrC,EAAW9I,EAAO+I,EAAO,QAGlCU,EAAQU,GAAS,CAACnB,EAAQyB,KACxBU,GAAOrC,EAAWkC,EAAUE,EAAMT,GAAQ1B,EAAOC;AAIrD,OAAOmC,EAAMJ;AAIjB,OAAoB,IAAhBF,EACK,SAAUK,GACf,OAAOD,EAAYb,EAAO,GAAIc,IAIC,QAA/B9B,EAAOsB,QAAQU,YACV,SAAUF,GACf,IAAIjC,EACAkC,EAAM;AAEV,IAAK,IAAIpC,KAASqB,EAAQ,CAExB,IADAnB,EAAQgC,EAAYlC,EAAOmC,KACd,EAAG,OAAO;AACvBC,GAAOlC,EAGT,OAAOkC,EAAMN,GAGR,SAAUK,GACf,IAAIC,EAAM;AAIV,OAHA1B,EAAQW,GAAQrB,IACdoC,GAAOF,EAAYlC,EAAOmC,MAErBC,EAAMN,GAYnB1D,gBAAgB8C,EAAOS,GACrB,IAAItB,EAAS5L,KAAKmN,cAAcV,EAAOS;AACvC,OAAOlN,KAAK6N,iBAAiBjC,GAG/BjC,iBAAiBiC,GACf,IAAIkC,EACAC,EAAY;AAChB,MAAMhN,EAAOf,KACPkN,EAAUtB,EAAOsB,QACjBhF,GAAQ0D,EAAOa,OAASS,EAAQc,WAAad,EAAQc,WAAad,EAAQhF;AAEhF,GAAmB,mBAARA,EACT,OAAOA,EAAK+F,KAAKjO;AASnB,MAAMkO,EAAY,SAAmBhD,EAAMjI,GACzC,MAAa,WAATiI,EAA0BjI,EAAOwI,MAC9BG,EAAO4B,UAAUzM,EAAKuL,MAAMrJ,EAAOkL,IAAKjD;AAIjD,GAAIhD,EACF,IAAK,IAAIzC,KAAKyC,GACR0D,EAAOa,OAAqB,WAAZhH,EAAEwH,QACpBc,EAAU1N,KAAKoF;AAOrB,GAAImG,EAAOa,MAAO,CAChBqB,GAAiB;AAEjB,IAAK,IAAIM,KAAOL,EACd,GAAkB,WAAdK,EAAInB,MAAoB,CAC1Ba,GAAiB;AACjB,MAIAA,GACFC,EAAUvK,QAAQ,CAChByJ,MAAO,SACPoB,UAAW,cAKfN,EAAYA,EAAU5M,QAAOiN,GAAqB,WAAdA,EAAInB;AAM1C,OAFwBc,EAAUtN,OAM3B,SAAUuD,EAAGO,GAClB,IAAItB,EAAQgK;AAEZ,IAAK,IAAIqB,KAAYP,EAAW,CAI9B,GAHAd,EAAQqB,EAASrB,MAEjBhK,GADwC,SAAvBqL,EAASD,WAAwB,EAAI,GAChCjC,EAAI8B,EAAUjB,EAAOjJ,GAAIkK,EAAUjB,EAAO1I,IACpD,OAAOtB,EAGrB,OAAO,GAbA,KAuBX0G,cAAc8C,EAAO8B,GACnB,MAAM5B,EAAU;AAChB,IAAIO,EAAU1G,OAAOgI,OAAO,GAAID;AAIhC,GAHAzC,EAAYoB,EAAS,QACrBpB,EAAYoB,EAAS,cAEjBA,EAAQI,OAAQ,CAClBxB,EAAYoB,EAAS;AACrB,MAAMI,EAAS;AACfJ,EAAQI,OAAO1N,SAAQqN,IACD,iBAATA,IACTA,EAAQ,CACNA,MAAOA,EACPzB,OAAQ,IAIZ8B,EAAOjN,KAAK4M,GACZN,EAAQM,EAAMA,OAAS,WAAYA,EAAQA,EAAMzB,OAAS,KAE5D0B,EAAQI,OAASA,EAGnB,MAAO,CACLJ,QAASA,EACTT,MAAOA,EAAM3F,cAAc2H,OAC3B7B,OAAQ5M,KAAK0O,SAASjC,EAAOS,EAAQR,wBAAyBC,GAC9DgC,MAAO,EACPrC,MAAO,GACPK,QAASA,EACTa,UAAWN,EAAQ0B,QAAUzD,EAAiBH,GAQlDrB,OAAO8C,EAAOS,GACZ,IACIzB,EACAG,EAFA7K,EAAOf;AAGX4L,EAAS5L,KAAKmN,cAAcV,EAAOS,GACnCA,EAAUtB,EAAOsB,QACjBT,EAAQb,EAAOa;AAEf,MAAMoC,EAAW3B,EAAQzB,OAAS1K,EAAKqM,kBAAkBxB;AAGrDa,EAAMhM,OACRwL,EAAQlL,EAAKuL,OAAO,CAACwC,EAAMX,KACzB1C,EAAQoD,EAASC,KAEM,IAAnB5B,EAAQ/L,QAAoBsK,EAAQ,IACtCG,EAAOU,MAAMjM,KAAK,CAChBoL,MAASA,EACT0C,GAAMA,OAKZlC,EAAQlL,EAAKuL,OAAO,CAACyC,EAAGZ,KACtBvC,EAAOU,MAAMjM,KAAK,CAChBoL,MAAS,EACT0C,GAAMA;AAKZ,MAAMa,EAAUjO,EAAK8M,iBAAiBjC;AAUtC,OARIoD,GAASpD,EAAOU,MAAMpE,KAAK8G,GAE/BpD,EAAO+C,MAAQ/C,EAAOU,MAAM7L,OAEC,iBAAlByM,EAAQ+B,QACjBrD,EAAOU,MAAQV,EAAOU,MAAMhJ,MAAM,EAAG4J,EAAQ+B,QAGxCrD,GC5RJ,MAAMK,EAAU,CAACC,EAA8BxM,KAErD,GAAKmD,MAAMmJ,QAAQE,GAClBA,EAAOtM,QAAQF;KAIf,IAAK,IAAIqM,KAAOG,EACXA,EAAOC,eAAeJ,IACzBrM,EAASwM,EAAOH,GAAMA,IC5EbmD,EAAWzC,IAEnBA,GAAAA,EAAM0C,OACF1C,OAAAA,EAAM;AAGVA,GAAAA,aAAiB2C,YACpB,OAAO3C;AAGR,GAAI4C,EAAa5C,GAAQ,CACxB,IAAI6C,EAAMC,SAASC,cAAc;AAEjC,OADAF,EAAIG,UAAYhD,EAAMgC,OACfa,EAAII,QAAQC,WAGpB,OAAOJ,SAASK,cAAcnD,IAGlB4C,EAAgBQ,GACT,iBAARA,GAAoBA,EAAIjP,QAAQ,MAAQ,EAcvCkP,EAAe,CAAEC,EAAoBC,KACjD,IAAInQ,EAAQ0P,SAASU,YAAY;AACjCpQ,EAAMqQ,UAAUF,GAAY,GAAM,GAClCD,EAAOI,cAActQ,IAOTuQ,EAAW,CAAEL,EAAoBM,KAC7C7J,OAAOgI,OAAOuB,EAAOO,MAAOD,IAQhBE,EAAa,CAAEC,KAAoCC,KAE/D,IAAIC,EAAgBC,EAAaF,IACjCD,EAAWI,EAAYJ,IAEjBlI,KAAKuI,IACVH,EAAapI,KAAKwI,IACjBD,EAAGE,UAAUxJ,IAAKuJ,UASPE,EAAgB,CAAER,KAAoCC,KAElE,IAAIC,EAAgBC,EAAaF,IAClCD,EAAWI,EAAYJ,IAEjBlI,KAAKuI,IACVH,EAAapI,KAAIwI,IACfD,EAAGE,UAAUE,OAAQH,UAUZH,EAAgB7P,IACxB2P,IAAAA,EAAmB;AAUvB,OATAxE,EAASnL,GAAOoQ,IACS,iBAAbA,IACVA,EAAWA,EAASzC,OAAO9O,MAAM,sBAE9BkD,MAAMmJ,QAAQkF,KACjBT,EAAUA,EAAQU,OAAOD,OAIpBT,EAAQtP,OAAOC,UAQVwP,EAAef,IACtBhN,MAAMmJ,QAAQ6D,KACjBA,EAAM,CAACA,IAEFA,GASKuB,EAAc,CAAEC,EAAyBC,EAAiBC,KAElEA,IAAAA,GAAYA,EAAQC,SAASH,GAIjC,KAAOA,GAAUA,EAAOI,SAAS,CAEhC,GAAIJ,EAAOI,QAAQH,GAClB,OAAOD;AAGRA,EAASA,EAAOK,aAYLC,EAAU,CAAEC,EAA0BvD,EAAiB,IAE/DA,EAAY,EACRuD,EAAKA,EAAKnR,OAAO,GAGlBmR,EAAK,GAgBAC,EAAY,CAAEhB,EAAiBiB,KAC3C,IAAKjB,EAAI,OAAQ;AAEjBiB,EAAUA,GAAWjB,EAAGkB;AAGxB,IADIjQ,IAAAA,EAAI,EACD+O,EAAKA,EAAGmB,wBAEVnB,EAAGY,QAAQK,IACdhQ;AAGF,OAAOA,GAQKmQ,EAAU,CAACpB,EAAWqB,KAClCjG,EAASiG,GAAM,CAACC,EAAIC,KACR,MAAPD,EACHtB,EAAGwB,gBAAgBD,GAEnBvB,EAAGyB,aAAaF,EAAgB,GAAGD,OASzBI,EAAc,CAAEC,EAAeC,KACvCD,EAASd,YAAac,EAASd,WAAWgB,aAAaD,EAAaD,ICrM5DG,EAAY,CAACC,EAAqBjH,KAE9C,GAAc,OAAVA,EAAiB;AAGrB,GAAqB,iBAAVA,EAAoB,CAE9B,IAAKA,EAAMlL,OAAS;AACpBkL,EAAQ,IAAIpF,OAAOoF,EAAO,KAMrBkH,MA8BAC,EAAuBC,GAEN,IAAlBA,EAAKC,SAhCcD,CAAAA,IAEnBzL,IAAAA,EAAQyL,EAAKrF,KAAKpG,MAAMqE;AACxBrE,GAAAA,GAASyL,EAAKrF,KAAKjN,OAAS,EAAG,CAClC,IAAIwS,EAAY1D,SAASC,cAAc;AACvCyD,EAASC,UAAY;AACjBC,IAAAA,EAAaJ,EAAKK,UAAU9L,EAAM+L;AAEtCF,EAAUC,UAAU9L,EAAM,GAAI7G;AAC9B,IAAI6S,EAAeH,EAAUI,WAAU;AAIvC,OAFAN,EAASO,YAAYF,GACrBf,EAAYY,EAAWF,GAChB,EAGR,OAAO,GAiBCJ,CAAcE,IAZKA,CAAAA,IACL,IAAlBA,EAAKC,WAAkBD,EAAKU,YAAe,kBAAkBC,KAAKX,EAAKY,UAAiC,cAAnBZ,EAAKG,WAA8C,SAAjBH,EAAKY,SAC/H9Q,MAAMC,KAAKiQ,EAAKU,YAAY7T,SAAQgT,IACnCE,EAAmBF,OAYrBgB,CAAkBb,GAEX;AAGRD,EAAoBF,ICtDRiB,EADqC,oBAAdC,WAAoC,MAAMJ,KAAKI,UAAUC,WACvD,UAAY;ACXnC,IAAAC,EAAA,CACd9G,QAAS,GACT+G,UAAW,GAEXC,QAAS,GACTC,UAAW,IACXC,QAAS,KACTC,SAAS,EACT7H,YAAY,EACZ8H,OAAQ,KACRC,cAAc,EACdC,aAAc,KACd7B,WAAW,EACX8B,aAAa,EACbC,WAAY,KACZC,WAAY,GACZC,SAAU,KACVC,aAAc,KACdC,YAAY,EACZC,eAAe,EACfC,aAAa,EACbC,QAAS,KACTC,kBAAkB,EAGlBC,aAAc,IACdC,aAAc,UAEdC,SAAU,KACVC,cAAe,WACfC,WAAY,QACZC,WAAY,OACZC,cAAe,WACfC,mBAAoB,QACpBC,mBAAoB,QACpBC,mBAAmB,EAEnBC,UAAW,SACXC,YAAa,CAAC,QACdC,kBAAmB,MAEnBC,KAAM,KACNC,aAAc,aACdC,aAAc,aACdC,cAAe,cACfC,qBAAsB,sBACtBC,UAAW,OACXC,YAAa,SAEbC,eAAgB,KAChBC,aAAc,oDAEdC,uBAAuB,EAEvBC,YAAa,KACbC,gBAAiB,KAEjBC,WAAY,SAASnK,GACpB,OAAOA,EAAMhM,OAAS,GAuBvBoW,OAAQ;AC/DF,MAAMC,EAAYtU,GACpB,MAAOA,EAAgD,KACpDuU,EAASvU,GAGJuU,EAAYvU,GACH,kBAAVA,EAA4BA,EAAQ,IAAM,IAC9CA,EAAQ,GAOHwU,EAAe5U,IACnBA,EAAM,IACZC,QAAQ,KAAM,SACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,UAQJ4U,GAAe,CAACC,EAAmDC,KAC/E,IAAIC;AACJ,OAAO,SAAyB5U,EAAa9C,GACxCqB,IAAAA,EAAOf;AAEPoX,IACHrW,EAAKsW,QAAU5U,KAAKC,IAAI3B,EAAKsW,QAAU,EAAG,GAC1CC,aAAaF,IAEdA,EAAUG,YAAW,WACpBH,EAAU,KACVrW,EAAKyW,eAAehV,IAAS,EAC7B0U,EAAGO,KAAK1W,EAAMyB,EAAO9C,KAEnByX,KAUQO,GAAkB,CAAE3W,EAAgB4W,EAAgBT,KAChE,IAAIU,EACA/W,EAAUE,EAAKF,QACfgX,EAAoC;AAiBnCD,IAAAA,KAdL7W,EAAKF,QAAU,WACd,IAAI+W,EAAOpX,UAAU;AACjBmX,IAAyB,IAAzBA,EAAM/W,QAAQgX,GAGjB,OAAO/W,EAAQG,MAAMD,EAAMP;AAF3BqX,EAAWD,GAAQpX,WAOrB0W,EAAGlW,MAAMD,EAAM,IACfA,EAAKF,QAAUA,EAGF8W,GACRC,KAAQC,GACXhX,EAAQG,MAAMD,EAAM8W,EAAWD,KAyBrBE,GAAiB,CAACC,EAAYC,GAAa,KACnDD,IACHA,EAAID,iBACAE,GACHD,EAAIE,oBAUMC,GAAW,CAAC7G,EAAoBuG,EAAalY,EAA6CwN,KACtGmE,EAAO8G,iBAAiBP,EAAKlY,EAASwN,IAU1BkL,GAAY,CAAEC,EAA2CN,MAEhEA,MAIAA,EAAIM,IAMK,KAFDN,EAAIO,OAAO,EAAE,IAAMP,EAAIQ,QAAQ,EAAE,IAAMR,EAAIS,SAAS,EAAE,IAAMT,EAAIU,QAAQ,EAAE,IAe3EC,GAAQ,CAAC7H,EAAW1C,KAChC,MAAMwK,EAAc9H,EAAG+H,aAAa;AACpC,OAAID,IAIJ9H,EAAGyB,aAAa,KAAKnE,GACdA,IAOK0K,GAAczW,GACnBA,EAAIC,QAAQ,UAAW,QAMlByW,GAAS,CAAEC,EAAiChG,KACpDA,GAAOgG,EAAOD,OAAO/F;ACtLX,SAASiG,GAAahW,EAAgBiW,GAChD1M,IAAAA,EAAuB/F,OAAOgI,OAAO,GAAIwF,EAAUiF,GAEnDC,EAAe3M,EAAS8I,SACxB8D,EAAiB5M,EAASiJ,WAC1B4D,EAAiB7M,EAASgJ,WAC1B8D,EAAmB9M,EAASkJ,cAC5B6D,EAAmB/M,EAAS+I,cAC5BiE,EAAuBhN,EAASmJ,mBAChC8D,EAAuBjN,EAASoJ,mBAEhC8D,EAAczW,EAAM2Q,QAAQ7M,cAC5B4P,EAAiB1T,EAAM4V,aAAa,gBAAkB5V,EAAM4V,aAAa;AAE7E,IAAKlC,IAAgBnK,EAAS2I,iBAAkB,CAC/C,IAAIwE,EAAU1W,EAAM4M,cAAc;AAC9B8J,IACHhD,EAAcgD,EAAOC,aAKvB,IAoBKhG,EACAzG,EACA0M,EACAC,EAEAC,EAYAC,EArCDC,EAMA,CACHtD,YAAcA,EACdxJ,QAAW,GACX+G,UAAY,GACZ3H,MAAS,GACTsI,SAAW;AAmILpO,MANU,WAAbiT,GAnHCvM,EAAU8M,EAAiB9M,QAC3B0M,EAAgC,GAChCC,EAAc,EAEdC,EAAYjJ,IAEf,IAAInD,EAAOlH,OAAOgI,OAAO,GAAGqC,EAAGoJ,SAC3BC,EAAOhB,GAAaxL,EAAKwL;AAM7B,MAJoB,iBAATgB,GAAqBA,EAAKzZ,SACpCiN,EAAOlH,OAAOgI,OAAOd,EAAK1D,KAAKC,MAAMiQ,KAG/BxM,GAGJqM,EAAY,CAACL,EAA0BS,KAE1C,IAAI3X,EAAQsU,EAAS4C,EAAOlX;AACvBA,GAAS,MAATA,IACCA,GAAU+J,EAAS2I,kBAApB,CAML,GAAI0E,EAAWzN,eAAe3J,IAC7B,GAAI2X,EAAO,CACNC,IAAAA,EAAMR,EAAWpX,GAAO8W;AACvBc,EAEOvX,MAAMmJ,QAAQoO,GAGzBA,EAAI/Z,KAAK8Z,GAFTP,EAAWpX,GAAO8W,GAAkB,CAACc,EAAKD,GAF1CP,EAAWpX,GAAO8W,GAAkBa,OAQlC,CAEJ,IAAIE,EAA0BP,EAASJ;AACvCW,EAAYlB,GAAkBkB,EAAYlB,IAAgBO,EAAOC,YACjEU,EAAYjB,GAAkBiB,EAAYjB,IAAgB5W,EAC1D6X,EAAYhB,GAAkBgB,EAAYhB,IAAmBK,EAAOY,SACpED,EAAYf,GAAkBe,EAAYf,IAAmBa,EAC7DE,EAAYE,QAAYb,EAExBE,EAAWpX,GAAS6X,EACpBnN,EAAQ7M,KAAKga,GAGVX,EAAOc,UACVR,EAAiB1N,MAAMjM,KAAKmC,KAqB9BwX,EAAiBpF,SAAW5R,EAAMyX,aAAa,YAAc,KAAO,EAEpExO,EAAQjJ,EAAM0X,UAAUC,IAnBPC,IAAAA,EACZzM,EAAW0M;AAoBC,cADhBlH,EAAUgH,EAAMhH,QAAQ7M,iBAjBxB+T,EAAsBf,EAHNc,EAsBND,IAlBIpB,GAAyBsB,EAActB,IAAyBqB,EAAShC,aAAa,UAAY,GAChHiC,EAAcrB,GAAyBqB,EAAcrB,IAAyBK,IAC9EgB,EAAcxB,GAAoBwB,EAAcxB,IAAmBuB,EAASN,SAC5EN,EAAiB/F,UAAU5T,KAAKwa,GAEhC1M,EAAK0M,EAAcrB,GAEnBvN,EAAQ2O,EAASF,UAAWhB,IAC3BK,EAAUL,EAA6BvL,OAWjB,WAAZwF,GACVoG,EAAUY,OAWM,MAClB,MAAMG,EAAW9X,EAAM4V,aAAaM;AAEhC,GAAC4B,EAaJd,EAAiB9M,QAAUlD,KAAKC,MAAM6Q,GACtC7O,EAAS+N,EAAiB9M,SAAU6N,IACnCf,EAAiB1N,MAAMjM,KAAK0a,EAAI3B;IAfnB,CACV5W,IAAAA,EAAQQ,EAAMR,MAAMiM,QAAU;AAC9B,IAAClC,EAAS2I,mBAAqB1S,EAAM/B,OAAQ;AAC3Cua,MAAAA,EAASxY,EAAM7C,MAAM4M,EAAS4H;AAEpClI,EAAS+O,GAASxY,IACXkX,MAAAA,EAAmB;AACzBA,EAAOP,GAAe3W,EACtBkX,EAAON,GAAe5W,EACtBwX,EAAiB9M,QAAQ7M,KAAKqZ,MAE/BM,EAAiB1N,MAAQ0O,IAa1BC,GAGMzU,OAAOgI,OAAQ,GAAIwF,EAAUgG,EAAkBf,GCnIvD,IAAIiC,GAAa;AAEF,MAAMC,WCTN,SAAqBC,GAI5B,OAFPA,EAAUlH,QAAU,GAEb,cAAckH,EAASzR,eAAA7I,GAAAua,SAAAva,GAAAd,KAEtBkU,QAAmB,CACzB9I,MAAY,GACZmB,SAAY,GACZ+O,UAAY,GACZC,OAAY,IAQA5R,cAACuB,EAAagM,GAC1BkE,EAAUlH,QAAQhJ,GAAQ,CACzBA,KAASA,EACAgM,GAAAA,GAoBXsE,kBAAkBtH,GACbnI,IAAAA,EAAKb;AACHnK,MAAAA,EAAQf,KACRyb,EAAiB;AAEvB,GAAI5Y,MAAMmJ,QAAQkI,GACjBA,EAAQtU,SAAS8b,IACM,iBAAXA,EACVD,EAAMpb,KAAKqb,IAEX3a,EAAKmT,QAAQ3H,SAASmP,EAAOxQ,MAAQwQ,EAAOxO,QAC5CuO,EAAMpb,KAAKqb,EAAOxQ;KAGd,GAAIgJ,EACLnI,IAAAA,KAAOmI,EACPA,EAAQ/H,eAAeJ,KAC1BhL,EAAKmT,QAAQ3H,SAASR,GAAOmI,EAAQnI,GACrC0P,EAAMpb,KAAK0L;AAKd,KAAOb,EAAOuQ,EAAMpQ,SACnBtK,EAAK4a,QAAQzQ,GAIf0Q,WAAW1Q,GACNnK,IAAAA,EAAUf,KACVkU,EAAUnT,EAAKmT,QACfwH,EAAUN,EAAUlH,QAAQhJ;AAE5B,IAACkQ,EAAUlH,QAAQ/H,eAAejB,GACrC,MAAM,IAAI2Q,MAAM,mBAAsB3Q,EAAO;AAG9CgJ,EAAQoH,UAAUpQ,IAAQ,EAC1BgJ,EAAQqH,OAAOrQ,GAAQwQ,EAAOxE,GAAGlW,MAAMD,EAAM,CAACA,EAAKmT,QAAQ3H,SAASrB,IAAS,KAC7EgJ,EAAQ9I,MAAM/K,KAAK6K,GAOpByQ,QAAQzQ,GACHnK,IAAAA,EAAOf,KACPkU,EAAUnT,EAAKmT;AAEf,IAACnT,EAAKmT,QAAQqH,OAAOpP,eAAejB,GAAO,CAC9C,GAAIgJ,EAAQoH,UAAUpQ,GACrB,MAAM,IAAI2Q,MAAM,oCAAsC3Q,EAAO;AAE9DnK,EAAK6a,WAAW1Q,GAGjB,OAAOgJ,EAAQqH,OAAOrQ,KDzFc4Q,CAAYhc,IAiDlDC,YAAagc,EAA4BC,GAKxC,IAAIC;AAJJZ,QADqFrb,KA/C/Ekc,mBA+C+E,EAAAlc,KA9C/EuR,aA8C+E,EAAAvR,KA7C/Emc,cA6C+E,EAAAnc,KA5C/Eoc,aA4C+E,EAAApc,KA3C/Eqc,sBA2C+E,EAAArc,KA1C/Esc,gBA0C+E,EAxC/EC,KAAAA,MAAqB,EAwC0Dvc,KAvC/EuM,cAuC+E,EAAAvM,KAtC/EgD,WAsC+E,EAAAhD,KArC/Ewc,cAqC+E,EAAAxc,KApC/Eyc,mBAoC+E,EAAAzc,KAnC/E0c,SAmC+E,EAAA1c,KAlC9E2c,aAkC8E,EAAA3c,KAhC9E4c,cAgC8E,EAAA5c,KA/B/E6c,YA+B+E,EA5B/EC,KAAAA,QAAuB,EACvBC,KAAAA,YAA0B,EA2BqD/c,KA1B/Egd,gBA0B+E,EAzB/EC,KAAAA,WAAyB,EACzBC,KAAAA,SAAwB,EACxBC,KAAAA,UAAyB,EACzBC,KAAAA,WAAyB,EACzBC,KAAAA,eAA4B,EAC5BC,KAAAA,SAAwB,EACxBC,KAAAA,aAA2B,EAC3BC,KAAAA,aAA2B,EAC3BC,KAAAA,YAA0B,EAiBqDzd,KAhB/E0d,oBAgB+E,EAf/EC,KAAAA,UAAwB,GACxBC,KAAAA,SAAwB,EACxBvG,KAAAA,QAAuB,EACvBG,KAAAA,eAAgD,GAEhDqG,KAAAA,aAAqC,KACrCC,KAAAA,YAA6B,GAE7B7J,KAAAA,UAA4B,GAC5B/G,KAAAA,QAA2B,GAC3B6Q,KAAAA,YAA0C,GAC1CzR,KAAAA,MAAuB,GAO7B4O;AAGA,IAAIlY,EAAWkM,EAAQ6M;AAEnB/Y,GAAAA,EAAMgb,UACT,MAAM,IAAInC,MAAM;AAIjB7Y,EAAMgb,UAAche,KAKpBic,GADqBgC,OAAOC,kBAAoBD,OAAOC,iBAAiBlb,EAAO,OACtDmb,iBAAiB;AAG1C,MAAM5R,EAAayM,GAAahW,EAAOgZ;AAClCzP,KAAAA,SAAaA,EACbvJ,KAAAA,MAAWA,EAChBhD,KAAKwc,SAAaxZ,EAAMwZ,UAAY,EAC/BC,KAAAA,cAAiD,WAAhCzZ,EAAM2Q,QAAQ7M,cACpC9G,KAAK0c,IAAS,OAAOhJ,KAAKuI,GACrBU,KAAAA,QAAYjE,GAAM1V,EAAO,aAAakY,IAC3Clb,KAAKgd,WAAeha,EAAMob,SAI1Bpe,KAAK6c,OAAS,IAAIxQ,EAAOrM,KAAKkN,QAAS,CAACV,WAAYD,EAASC,aAG7DD,EAASyJ,KAAOzJ,EAASyJ,OAA+B,IAAtBzJ,EAASqI,SAAiB,SAAW,SAClC,kBAA1BrI,EAASsI,eACnBtI,EAASsI,aAAiC,UAAlBtI,EAASyJ,MAGM,kBAA7BzJ,EAASoK,kBACnBpK,EAASoK,gBAAoC,UAAlBpK,EAASyJ;AAIrC,IAAI7U,EAASoL,EAASiI;AACA,mBAAXrT,IAEY,iBAAXA,IACVA,EAAS,IAAIoF,OAAOpF,IAGjBA,aAAkBoF,OACrBgG,EAASiI,aAAgBxR,GAAW7B,EAAkBuS,KAAK1Q,GAE3DuJ,EAASiI,aAAgBhS,GACjBxC,KAAKuM,SAASuI,aAAe9U,KAAKkN,QAAQ1K,IAMpDxC,KAAKwb,kBAAkBjP,EAAS2H,SAChClU,KAAKqe,iBACAC,KAAAA;AAIL,MAAM/M,EAAYrC,EAAO,SACnBkN,EAAYlN,EAAO,SACnBiN,EAAanc,KAAKue,QAAQ,YAC1BlC,EAAmBnN,EAAQ,sCAE3BuB,EAAYzQ,KAAKgD,MAAM4V,aAAa,UAAY,GAChD4F,EAAcjS,EAASyJ;AAE7B,IAAIkG;AAuBJ,GApBA3L,EAAYgB,EAAShF,EAAS0J,aAAcxF,EAAS+N,GAGrDjO,EAAW6L,EAAQ7P,EAAS2J,cAC5B4C,GAAQvH,EAAS6K,GAGjB7L,EAAW4L,EAAU5P,EAAS4J,cAAeqI,GACzCjS,EAASkK,uBACZlG,EAAY4L,EAAU1L,GAIvBF,EAAW8L,EAAkB9P,EAAS6J,sBACtC0C,GAAQqD,EAAUE,GAElBnN,EAAQ3C,EAASgK,gBAAkBhF,GAAUiC,YAAa2I,GAItD9M,EAAa9C,EAASiK,cAAe,CACxC0F,EAAiBhN,EAAO3C,EAASiK;AAIjCvK,EADY,CAAC,cAAc,iBAAiB,iBAC7BmG,IACVpP,EAAM4V,aAAaxG,IACtBH,EAAQiK,EAAc,CAACvS,CAACyI,GAAMpP,EAAM4V,aAAaxG,QAInD8J,EAAcM,UAAY,EAC1BJ,EAAQ5I,YAAa0I,GACrBlc,KAAKsc,WAAcJ,OAGV3P,EAASiK,cAClB0F,EAAiBhN,EAAQ3C,EAASiK,cAC7B8F,KAAAA,WAAcJ,IAGnBA,EAAiBhN,EAAO,YACnBoN,KAAAA,WAAcF;AAGf7K,KAAAA,QAAYA,EACZ4K,KAAAA,SAAaA,EACbE,KAAAA,iBAAmBA,EACnBD,KAAAA,QAAaA,EACbF,KAAAA,cAAiBA,EAEtBlc,KAAKye,QAONA,QAEO1d,MAAAA,EAAOf,KACPuM,EAAcxL,EAAKwL,SACnB2P,EAAkBnb,EAAKmb,cACvBC,EAAcpb,EAAKob,SACnBE,EAAoBtb,EAAKsb,iBACzB9K,EAAaxQ,EAAKwQ,QAClB6K,EAAarb,EAAKqb,QAClBpZ,EAAYjC,EAAKiC,MACjBsZ,EAAevb,EAAKub,WACpBoC,EAAkB,CAAEC,SAAS,GAC7BC,EAAe7d,EAAK4b,QAAS;AAGnC1K,EAAQoK,EAAiB,CACxBlO,GAAIyQ,IAGL3M,EAAQqK,EAAW,CAClBuC,KAAK,WACLC,gBAAgB,UAChBC,gBAAgB,QACAH,gBAAAA;AAGXI,MAAAA,EAAatG,GAAM4D,EAAWvb,EAAK4b,QAAU,eAC7ClQ,EAAU,cN5NUA,CAAAA,GACpBA,EAAMpK,QAAQ,UAAW,QM2ND4c,CAAYle,EAAK4b,SAAS,KAClDuC,EAAU3P,SAASK,cAAcnD,GACjC0S,EAAcpe,EAAKqe,MAAMnR,KAAKlN;AACpC,GAAIme,EAAO,CACVhH,GAASgH,EAAM,QAASC,GACxBlN,EAAQiN,EAAM,CAACG,IAAIL;AACbM,MAAAA,EAAW5G,GAAMwG,EAAMne,EAAK4b,QAAQ;AAC1C1K,EAAQqK,EAAW,CAAmBgD,kBAAAA,IACtCrN,EAAQoK,EAAiB,CAAmBiD,kBAAAA,IAK7C,GAFA/N,EAAQjB,MAAMiP,MAAQvc,EAAMsN,MAAMiP,MAE9Bxe,EAAKmT,QAAQ9I,MAAM3K,OAAQ,CAC9B,MAAM+e,EAAkB,UAAYze,EAAKmT,QAAQ9I,MAAM9J,KAAK;AAC5DiP,EAAY,CAACgB,EAAQ4K,GAAWqD,IAGN,OAAtBjT,EAASqI,UAAqBrI,EAASqI,SAAW,IAAM7T,EAAK0b,eACjExK,EAAQjP,EAAM,CAACyc,SAAS,aAGrBlT,EAASmK,aACZzE,EAAQiK,EAAc,CAACxF,YAAYnK,EAASmK,eAIxCnK,EAAS6H,SAAW7H,EAAS4H,YACjC5H,EAAS6H,QAAU,IAAI7N,OAAO,OAASpE,EAAaoK,EAAS4H,WAAa,UAKvE5H,EAASmT,MAAQnT,EAAS4I,eAC7B5I,EAASmT,KAAOzI,GAAa1K,EAASmT,KAAKnT,EAAS4I,eAGrDpU,EAAKmb,cAActE,KAAO5U,EAAM4U,KAEhCM,GAASiE,EAAS,aAAa,KAC9Bpb,EAAKyc,aAAc,KAGpBtF,GAASiE,EAAS,cAAezX,IAE5Bib,IAAAA,EAAevO,EAAY1M,EAAE2M,OAAuB,oBAAqB8K;AACzEwD,GAAe5e,EAAK6e,cAAelb,EAAiBib,KAEtD,CAACE,SAAQ,IAGZ3H,GAASiE,EAAS,SAASpE,IACpB2B,MAAAA,EAAStI,EAAY2G,EAAI1G,OAAuB;AAClDqI,IACH3Y,EAAK+e,eAAgB/H,EAAmB2B,GACxC5B,GAAeC,GAAI,OAIrBG,GAASkE,EAAQ,SAAUrE,IAEtB4H,IAAAA,EAAevO,EAAa2G,EAAI1G,OAAuB,iBAAkB+K;AACzEuD,GAAgB5e,EAAKgf,aAAahI,EAAmB4H,GACxD7H,GAAeC,GAAI,GAKO,IAAvBmE,EAAc1Z,QAIlBzB,EAAKif,UACLlI,GAAeC,GAAI,OAKpBG,GAASoE,EAAW,WAAa5X,GAAM3D,EAAKkf,UAAUvb,KAGtDwT,GAASgE,EAAc,YAAaxX,GAAM3D,EAAKmf,WAAWxb,KAC1DwT,GAASgE,EAAc,SAAWxX,GAAM3D,EAAKof,QAAQzb,KACrDwT,GAASoE,EAAW,QAAW5X,GAAM3D,EAAKqf,OAAO1b,KACjDwT,GAASoE,EAAW,SAAW5X,GAAM3D,EAAKsf,QAAQ3b,KAClDwT,GAASgE,EAAc,SAAWxX,GAAM3D,EAAKuf,QAAQ5b;AAG/C6b,MAAAA,EAAiBxI,IAItB,MAAM1G,EAAS0G,EAAIyI,eAAe;AAClC,IAAKjP,EAAQC,SAASH,KAA2B8K,EAAS3K,SAASH,GAKlE,OAJItQ,EAAKqc,WACRrc,EAAK0f,YAEN1f,EAAK2f;AASFrP,GAAU6K,GAAiBnb,EAAK+b,OACnC/E,EAAIE,kBAIJH,GAAeC,GAAI,IAKf4I,EAAa,KACd5f,EAAK+b,QACR/b,EAAK6f;AAKP1I,GAAS3I,SAAS,YAAagR,GAC/BrI,GAAS+F,OAAO,SAAU0C,EAAYjC,GACtCxG,GAAS+F,OAAO,SAAU0C,EAAYjC,GAEjC9B,KAAAA,SAAW,KACfrN,SAASsR,oBAAoB,YAAYN,GACzCtC,OAAO4C,oBAAoB,SAASF,GACpC1C,OAAO4C,oBAAoB,SAASF,GAChCzB,GAAQA,EAAM2B,oBAAoB,QAAQ1B,IAK/Cnf,KAAK8gB,eAAiB,CACrBrR,UAAYzM,EAAMyM,UAClB+M,SAAWxZ,EAAMwZ,UAIlBxZ,EAAMwZ,UAAY,EAClBxZ,EAAM+d,sBAAsB,WAAYhgB,EAAKwQ,SAE7CxQ,EAAKigB,MAAK,GACVzU,EAASD,MAAQ,UACVC,EAAS0H,iBACT1H,EAASW,QAEhBgL,GAASlV,EAAM,WAAW,KACrBjC,EAAKmc,UACRnc,EAAKmc,SAAU,EACfnc,EAAKkc,WAAY,EACjBlc,EAAKkgB,mBAIPlgB,EAAKmgB,sBACLngB,EAAKogB,eACLpgB,EAAKqgB,OAAM,GACXrgB,EAAK2f,aACL3f,EAAKuc,SAAU,EAEXta,EAAMsX,SACTvZ,EAAKsgB,UAELtgB,EAAKugB,SAGNvgB,EAAKb,GAAG,SAAUF,KAAKuhB,UAEvBhR,EAAWvN,EAAM,cAAc,wBAC/BjC,EAAKF,QAAQ,eAGY,IAArB0L,EAAS0I,SACZlU,EAAKkU,UAUPuM,aAAatU,EAAsB,GAAI+G,EAAwB,IAG9DjU,KAAKyhB,WAAWvU,GAIhBjB,EAASgI,GAAY2G,IACf8G,KAAAA,oBAAoB9G,MAO3B0D,iBACKvd,IAAAA,EAAOf,KACPmZ,EAAcpY,EAAKwL,SAASiJ,WAC5B8D,EAAiBvY,EAAKwL,SAASmJ,mBAE/BiM,EAAY,CACf/G,SAAalN,IACZ,IAAIkN,EAAWrL,SAASC,cAAc;AAGtC,OAFAoL,EAAS1H,UAAY,WACrB0H,EAASpH,YAAY9F,EAAKR,SACnB0N,GAGRgH,gBAAmB,CAAClU,EAAgBmU,IAC5B,gCAAkCA,EAAOnU,EAAK4L,IAAmB,SAEzEI,OAAU,CAAChM,EAAgBmU,IACnB,QAAUA,EAAOnU,EAAKyL,IAAgB,SAE9CrK,KAAQ,CAACpB,EAAgBmU,IACjB,QAAUA,EAAOnU,EAAKyL,IAAgB,SAE9C2I,cAAiB,CAACpU,EAAgBmU,IAC1B,mCAAqCA,EAAOnU,EAAK1K,OAAS,0BAElE+e,WAAa,IACL,iDAER1K,QAAU,IACF,8BAEM2K,YAAA,OACd7F,SAAW,IACH;AAKTpb,EAAKwL,SAASsK,OAASrQ,OAAOgI,OAAO,GAAImT,EAAW5gB,EAAKwL,SAASsK,QAOnEwH,iBACKtS,IAAAA,EAAKmL,EACL+K,EAAkC,CACrCC,WAAoB,eACpBC,OAAoB,WACpBC,SAAoB,YACpBC,YAAoB,eACpBC,YAAoB,eACpBC,MAAoB,UACpBC,WAAoB,cACpBC,cAAoB,iBACpBC,aAAoB,gBACpBC,aAAoB,mBACpBC,gBAAoB,sBACpBC,eAAoB,qBACpBC,cAAoB,iBACpBC,eAAoB,kBACpBnL,KAAoB,SACpB8H,KAAoB,SACpBN,MAAoB,UACAqB,KAAA;AAGhB1U,IAAAA,KAAOkW,GAEX/K,EAAKlX,KAAKuM,SAAS0V,EAAUlW,MACrB/L,KAAKE,GAAG6L,EAAKmL,GASvB8J,KAAKgC,GAAqB,GACnBjiB,MAAAA,EAAQf,KACRuM,EAAWyW,EAAehK,GAAajY,EAAKiC,MAAO,CAACmR,UAAUpT,EAAKwL,SAAS4H,YAAgDpT,EAAKwL;AAEvIxL,EAAKygB,aAAajV,EAASW,QAAQX,EAAS0H,WAE5ClT,EAAKkiB,SAAS1W,EAASD,OAAO,IAAG,GAEjCvL,EAAKmiB,UAAY,KAQlBlD,UACKjf,IAAAA,EAAOf;AAEX,GAAIe,EAAK+c,YAAYrd,OAAS,EAG7B,OAFAM,EAAKoiB,wBACLpiB,EAAKqe;AAIFre,EAAKqc,WAAarc,EAAK+b,OAC1B/b,EAAK0f,OAEL1f,EAAKqe,QAQPgE,eAOA7B,WACCzR,EAAa9P,KAAKgD,MAAO,SACzB8M,EAAa9P,KAAKgD,MAAO,UAO1Bsd,QAAQ5b,GACH3D,IAAAA,EAAOf;AAEPe,EAAKsc,eAAiBtc,EAAKoc,SAC9BrF,GAAepT,GAMX3D,EAAKwL,SAAS6H,SAKnBmD,YAAW,KACV,IAAI8L,EAAatiB,EAAKuiB;AAClB,GAACD,EAAW/b,MAAMvG,EAAKwL,SAAS6H,SAAhC,CAIJ,IAAImP,EAAaF,EAAW5U,OAAO9O,MAAMoB,EAAKwL,SAAS6H;AACvDnI,EAASsX,GAAaC,IAER1M,EAAS0M,KAEjBxjB,KAAKkN,QAAQsW,GAChBziB,EAAK0iB,QAAQD,GAEbziB,EAAK2iB,WAAWF,UAIjB,GAQJtD,WAAWxb,GACN3D,IAAAA,EAAOf;AACRe,IAAAA,EAAKoc,SAALpc,CAIH,IAAI4iB,EAAYhc,OAAOC,aAAalD,EAAEkf,SAAWlf,EAAEmf;AAC/C9iB,OAAAA,EAAKwL,SAAS+H,QAAiC,UAAvBvT,EAAKwL,SAASyJ,MAAoB2N,IAAc5iB,EAAKwL,SAAS4H,WACzFpT,EAAK2iB,kBACL5L,GAAepT,SAFZ3D,EAJH+W,GAAepT,GAejBub,UAAUvb,GACL3D,IAAAA,EAAOf;AAIPe,GAFJA,EAAKyc,aAAc,EAEfzc,EAAKoc,SJpoBc,IIqoBlBzY,EAAEkf,SACL9L,GAAepT;IAFb3D,CAOI2D,OAAAA,EAAEkf,SAGJE,KJvpBgB,GIwpBhB1L,GAAAA,GAAU0L,EAAuBpf,IACJ,IAA5B3D,EAAKmb,cAAc1Z,MAGtB,OAFAsV,GAAepT,QACf3D,EAAKgjB;AAIP;AAGID,KJhqBiB,GIsqBrB,OALI/iB,EAAK+b,SACRhF,GAAepT,GAAE,GACjB3D,EAAKqgB,cAENrgB,EAAKoiB;AAIDW,KJrqBkB,GIsqBlB,IAAC/iB,EAAK+b,QAAU/b,EAAK0c,WACxB1c,EAAKijB;KACC,GAAIjjB,EAAK8c,aAAc,CACzBoG,IAAAA,EAAOljB,EAAKmjB,YAAYnjB,EAAK8c,aAAc;AAC3CoG,GAAMljB,EAAKojB,gBAAgBF,GAGhC,YADAnM,GAAepT;AAIXof,KJlrBiB,GImrBjB/iB,GAAAA,EAAK8c,aAAc,CACtB,IAAIuG,EAAOrjB,EAAKmjB,YAAYnjB,EAAK8c,cAAe;AAC5CuG,GAAMrjB,EAAKojB,gBAAgBC,GAGhC,YADAtM,GAAepT;AAIXof,KJ9rBoB,GI4sBxB,YAbI/iB,EAAKsjB,UAAUtjB,EAAK8c,eACvB9c,EAAK+e,eAAepb,EAAE3D,EAAK8c,cAC3B/F,GAAepT,KAGN3D,EAAKwL,SAAS+H,QAAUvT,EAAK2iB,cAI7BnU,SAAS+U,eAAiBvjB,EAAKmb,eAAiBnb,EAAK+b,SAH9DhF,GAAepT;AAUZof,KJ7sBkB,GI+sBtB,YADA/iB,EAAKwjB,kBAAkB,EAAG7f;AAItBof,KJhtBmB,GIktBvB,YADA/iB,EAAKwjB,iBAAiB,EAAG7f;AAIrBof,KJjtBiB,EI+tBrB,YAZI/iB,EAAKwL,SAASyI,cACbjU,EAAKsjB,UAAUtjB,EAAK8c,gBACvB9c,EAAK+e,eAAepb,EAAE3D,EAAK8c,cAI3B/F,GAAepT,IAEZ3D,EAAKwL,SAAS+H,QAAUvT,EAAK2iB,cAChC5L,GAAepT;AAMbof,KJpuBsB,EIquBtBA,KJpuBoB,GIsuBxB,YADA/iB,EAAKyjB,gBAAgB9f,GAKnB3D,EAAKsc,gBAAkBjF,GAAU0L,EAAuBpf,IAC3DoT,GAAepT,IAQjByb,QAAQzb,GACH3D,IAAAA,EAAOf;AAEPe,IAAAA,EAAKoc,SAALpc,CAIJ,IAAIyB,EAAQzB,EAAKuiB;AACbviB,EAAK4c,YAAcnb,IACtBzB,EAAK4c,UAAYnb,EAEbzB,EAAKwL,SAASqK,WAAWa,KAAK1W,EAAKyB,IACtCzB,EAAK2e,KAAKld,GAGXzB,EAAK0jB,iBACL1jB,EAAKF,QAAQ,OAAQ2B,KASvBod,cAAe7H,EAA8B2B,GACxC1Z,KAAKwd,aACTxd,KAAKmkB,gBAAgBzK,GAAQ,GAO9B2G,QAAQ3b,GACH3D,IAAAA,EAAOf,KACP0kB,EAAa3jB,EAAKqc;AAElBrc,GAAAA,EAAKgc,WAGR,OAFAhc,EAAK0f,YACL3I,GAAepT;AAIZ3D,EAAKwc,cACTxc,EAAKqc,WAAY,EACa,UAA1Brc,EAAKwL,SAAS0I,SAAsBlU,EAAKkU,UAExCyP,GAAY3jB,EAAKF,QAAQ,SAEzBE,EAAK+c,YAAYrd,SACrBM,EAAK4jB,YACL5jB,EAAK0jB,iBAAiB1jB,EAAKwL,SAASkI,cAGrC1T,EAAKkgB,gBAONb,OAAO1b,GAEN,IAA4B,IAAxB6K,SAASqV,WAAb,CAEI7jB,IAAAA,EAAOf;AACX,GAAKe,EAAKqc,UAAV,CACArc,EAAKqc,WAAY,EACjBrc,EAAKwc,aAAc;AAEfsH,IAAAA,EAAa,KAChB9jB,EAAKqgB,QACLrgB,EAAK+jB,gBACL/jB,EAAKgkB,SAAShkB,EAAKuL,MAAM7L,QACzBM,EAAKF,QAAQ;AAGVE,EAAKwL,SAAS+H,QAAUvT,EAAKwL,SAASgI,aACzCxT,EAAK2iB,WAAW,KAAMmB,GAEtBA,MAUF/E,eAAgB/H,EAA8B2B,GAC7C,IAAIlX,EAAOzB,EAAOf;AAId0Z,EAAOsL,eAAiBtL,EAAOsL,cAAcvT,QAAQ,qBAKrDiI,EAAO3I,UAAUS,SAAS,UAC7BzQ,EAAK2iB,WAAW,MAAM,KACjB3iB,EAAKwL,SAAS0Y,kBACjBlkB,EAAKqgB,gBAKc,KADrB5e,EAAQkX,EAAOO,QAAQzX,SAEtBzB,EAAKmiB,UAAY,KACjBniB,EAAK0iB,QAAQjhB,GACTzB,EAAKwL,SAAS0Y,kBACjBlkB,EAAKqgB,SAGDrgB,EAAKwL,SAASsI,cAAgBkD,EAAIH,MAAQ,QAAQlE,KAAKqE,EAAIH,OAC/D7W,EAAKojB,gBAAgBzK,KAUzB2K,UAAU3K,GAET,SAAI1Z,KAAK8c,QAAUpD,GAAU1Z,KAAKqc,iBAAiB7K,SAASkI,IAW7DqG,aAAchI,EAAiBjJ,GAC1B/N,IAAAA,EAAOf;AAEX,OAAKe,EAAKoc,UAAmC,UAAvBpc,EAAKwL,SAASyJ,OACnC8B,GAAeC,GACfhX,EAAK+jB,cAAchW,EAAMiJ,IAClB,GAqBTmN,QAAQ1iB,GAEP,QAAKxC,KAAKuM,SAASmT,OACf1f,KAAKwX,eAAerL,eAAe3J,GASxCkd,KAAKld,GACEzB,MAAAA,EAAOf;AAEb,IAAKe,EAAKmkB,QAAQ1iB,GAAS;AAE3B+N,EAAWxP,EAAKwQ,QAAQxQ,EAAKwL,SAAS6I,cACtCrU,EAAKsW;AAEC3X,MAAAA,EAAWqB,EAAKokB,aAAalX,KAAKlN;AACxCA,EAAKwL,SAASmT,KAAKjI,KAAK1W,EAAMyB,EAAO9C,GAOtCylB,aAAcjY,EAAqB+G,GAC5BlT,MAAAA,EAAOf;AACbe,EAAKsW,QAAU5U,KAAKC,IAAI3B,EAAKsW,QAAU,EAAG,GAC1CtW,EAAKmiB,UAAY,KAEjBniB,EAAKqkB,oBACLrkB,EAAKygB,aAAatU,EAAQ+G,GAE1BlT,EAAK0jB,eAAe1jB,EAAKqc,YAAcrc,EAAKsc,eAEvCtc,EAAKsW,SACTrG,EAAcjQ,EAAKwQ,QAAQxQ,EAAKwL,SAAS6I,cAG1CrU,EAAKF,QAAQ,OAAQqM,EAAS+G,GAG/BgB,UACC,IAAIlE,EAAY/Q,KAAKuR,QAAQR;AACzBA,EAAUS,SAAS,eACvBT,EAAUxJ,IAAI,aACTmY,KAAAA,KAAK,KAQX2F,gBAAgB7iB,EAAe,IAC1BQ,IAAAA,EAAQhD,KAAKkc;AACHlZ,EAAMR,QAAUA,IAE7BQ,EAAMR,MAAQA,EACdsN,EAAa9M,EAAM,UACd2a,KAAAA,UAAYnb,GAWnB8iB,WAEK,OAAAtlB,KAAKyc,eAAiBzc,KAAKgD,MAAMyX,aAAa,YAC1Cza,KAAKsM,MAGNtM,KAAKsM,MAAMhL,KAAKtB,KAAKuM,SAAS4H,WAOtC8O,SAAUzgB,EAAuB+iB,GAGhC7N,GAAgB1X,KAFHulB,EAAS,GAAK,CAAC,WAEC,KACvBhD,KAAAA,MAAMgD,GACXvlB,KAAKwlB,SAAShjB,EAAO+iB,MASvBE,YAAYjjB,GACE,IAAVA,IAAaA,EAAQ,MACxBxC,KAAKuM,SAASqI,SAAWpS,EACzBxC,KAAKihB,eAON6D,cAAehW,EAAepK,GACzB3D,IACA2kB,EACA5jB,EAAG6jB,EAAOvc,EAAKwc,EACfhb,EAHA7J,EAAOf;AAKPe,GAAuB,WAAvBA,EAAKwL,SAASyJ,KAAdjV,CAGA,IAAC+N,EAKJ,OAJA/N,EAAKoiB,wBACDpiB,EAAKqc,WACRrc,EAAK4jB;AAQP,GAAkB,WAFlBe,EAAYhhB,GAAKA,EAAEkT,KAAK9Q,gBAEKsR,GAAU,WAAW1T,IAAM3D,EAAK+c,YAAYrd,OAAQ,CAU3EqB,IATL8I,EAAO7J,EAAK8kB,iBACZF,EAAQ9iB,MAAMijB,UAAUllB,QAAQ6W,KAAK1W,EAAKqb,QAAQ1B,SAAU9P,KAC5DxB,EAAOvG,MAAMijB,UAAUllB,QAAQ6W,KAAK1W,EAAKqb,QAAQ1B,SAAU5L,MAG1D8W,EAAQD,EACRA,EAAQvc,EACRA,EAAQwc,GAEJ9jB,EAAI6jB,EAAO7jB,GAAKsH,EAAKtH,IACzBgN,EAAO/N,EAAKqb,QAAQ1B,SAAS5Y,IACW,IAApCf,EAAK+c,YAAYld,QAAQkO,IAC5B/N,EAAKglB,mBAAmBjX;AAG1BgJ,GAAepT,OACU,UAAdghB,GAAyBtN,GAAU0L,EAAuBpf,IAAuB,YAAdghB,GAA2BtN,GAAU,WAAW1T,GAC1HoK,EAAKiC,UAAUS,SAAS,UAC3BzQ,EAAKilB,iBAAkBlX,GAEvB/N,EAAKglB,mBAAmBjX,IAGzB/N,EAAKoiB,mBACLpiB,EAAKglB,mBAAmBjX;AAIzB/N,EAAKklB,YACAllB,EAAKqc,WACTrc,EAAKqe,SAQP2G,mBAAoBjX,GACb/N,MAAAA,EAAOf,KACPkmB,EAAcnlB,EAAKqb,QAAQxM,cAAc;AAC3CsW,GAAclV,EAAckV,EAA2B,eAE3D3V,EAAWzB,EAAK,sBAChB/N,EAAKF,QAAQ,cAAeiO,IACW,GAAnC/N,EAAK+c,YAAYld,QAAQkO,IAC5B/N,EAAK+c,YAAYzd,KAAMyO,GAQzBkX,iBAAkBlX,GACbqX,IAAAA,EAAMnmB,KAAK8d,YAAYld,QAAQkO;AACnC9O,KAAK8d,YAAYnd,OAAOwlB,EAAK,GAC7BnV,EAAclC,EAAK,UAOpBqU,mBACCnS,EAAchR,KAAK8d,YAAY,UAC1BA,KAAAA,YAAc,GAQpBqG,gBAAiBzK,EAAwB0M,GAAe,GAEnD1M,IAAW1Z,KAAK6d,eAIpB7d,KAAKolB,oBACA1L,IAEAmE,KAAAA,aAAenE,EACpBzH,EAAQjS,KAAKsc,WAAW,CAAC+J,wBAAwB3M,EAAOd,aAAa,QACrE3G,EAAQyH,EAAO,CAAiB4M,gBAAA,SAChC/V,EAAWmJ,EAAO,UACd0M,GAASpmB,KAAKumB,eAAe7M,KAOlC6M,eAAgB7M,EAAyB8M,GAEpC,IAAC9M,EAAS;AAERhK,MAAAA,EAAW1P,KAAKqc,iBAChBoK,EAAc/W,EAAQgX,aACtBC,EAAajX,EAAQiX,WAAa,EAClCC,EAAclN,EAAOmN,aACrB5gB,EAAOyT,EAAOoN,wBAAwBC,IAAMrX,EAAQoX,wBAAwBC,IAAMJ;AAEpF1gB,EAAI2gB,EAAcH,EAAcE,EAC9BP,KAAAA,OAAOngB,EAAIwgB,EAAcG,EAAaJ,GAEjCvgB,EAAI0gB,GACd3mB,KAAKomB,OAAOngB,EAAGugB,GAQjBJ,OAAQO,EAAkBH,GACnB9W,MAAAA,EAAU1P,KAAKqc;AACjBmK,IACH9W,EAAQY,MAAM0W,eAAiBR,GAEhC9W,EAAQiX,UAAYA,EACpBjX,EAAQY,MAAM0W,eAAiB,GAOhC5B,oBACKplB,KAAK6d,eACR7M,EAAchR,KAAK6d,aAAa,UAChC5L,EAAQjS,KAAK6d,aAAa,CAAiByI,gBAAA,QAEvCzI,KAAAA,aAAe,KACpB5L,EAAQjS,KAAKsc,WAAW,CAAyB+J,wBAAA,OAOlDtC,YACOhjB,MAAAA,EAAOf;AAEb,GAA2B,WAAvBe,EAAKwL,SAASyJ,KAAmB;AAErC,MAAM8H,EAAc/c,EAAKkmB;AAEpBnJ,EAAYrd,SAEjBM,EAAKklB,YACLllB,EAAKqgB,QAELrgB,EAAK+c,YAAcA,EACnB7R,EAAS6R,GAAchP,IACtB/N,EAAKglB,mBAAmBjX,OAS1B4R,aACK3f,IAAAA,EAAOf;AAENe,EAAKqb,QAAQ5K,SAASzQ,EAAKmb,iBAEhCjK,EAAQlR,EAAKmb,cAAc,CAACxF,YAAY3V,EAAKwL,SAASmK,cAElD3V,EAAK+c,YAAYrd,OAAS,IAAOM,EAAKqc,WAAarc,EAAKwL,SAASoK,iBAAmB5V,EAAKuL,MAAM7L,OAAS,GAC3GM,EAAKskB,kBACLtkB,EAAKsc,eAAgB,IAIjBtc,EAAKwL,SAASoK,iBAAmB5V,EAAKuL,MAAM7L,OAAS,GACxDwR,EAAQlR,EAAKmb,cAAc,CAACxF,YAAY,KAEzC3V,EAAKsc,eAAgB,GAGtBtc,EAAKwQ,QAAQR,UAAUmW,OAAO,eAAgBnmB,EAAKsc,gBAQpD4I,YACCjmB,KAAK0gB,aAONiE,YACC3kB,KAAK0gB,aAMN4C,aACC,OAAOtjB,KAAKkc,cAAc1Z,MAAMiM,OAMjC2Q,QACKre,IAAAA,EAAOf;AACPe,EAAKgc,aAEThc,EAAKwc,aAAc,EAEfxc,EAAKmb,cAAciL,YACtBpmB,EAAKmb,cAAckD,QAEnBre,EAAKub,WAAW8C,QAGjB7H,YAAW,KACVxW,EAAKwc,aAAc,EACnBxc,EAAKsf,YACH,IAOJI,OACMnE,KAAAA,WAAWmE,OAChBzgB,KAAKogB,SAUNgH,iBAAiB3a,GACT,OAAAzM,KAAK6c,OAAOuK,iBAAiB3a,EAAOzM,KAAKqnB,oBAUjDA,mBACK9a,IAAAA,EAAWvM,KAAKuM,SAChBrE,EAAOqE,EAASsJ;AAKb,MAJ2B,iBAAvBtJ,EAASsJ,YACnB3N,EAAO,CAAC,CAAC+E,MAAOV,EAASsJ,aAGnB,CACNvI,OAAcf,EAASuJ,YACvBlI,YAAcrB,EAASwJ,kBACvB7N,KAAcA,EACd0G,QAAcrC,EAASqC,SASzBhD,OAAOa,GACFxJ,IAAAA,EAAQqkB,EACRvmB,EAAWf,KACXkN,EAAWlN,KAAKqnB;AAGpB,GAAKtmB,EAAKwL,SAASd,OAEY,mBAD9B6b,EAAiBvmB,EAAKwL,SAASd,MAAMgM,KAAK1W,EAAK0L,IAE9C,MAAM,IAAIoP,MAAM;AAqBlB,OAhBIpP,IAAU1L,EAAKmiB,WAClBniB,EAAKmiB,UAAczW,EACnBxJ,EAAalC,EAAK8b,OAAOjR,OAAOa,EAAOjG,OAAOgI,OAAOtB,EAAS,CAACzB,MAAO6b,KACtEvmB,EAAK2c,eAAkBza,GAEvBA,EAAauD,OAAOgI,OAAQ,GAAIzN,EAAK2c,gBAIlC3c,EAAKwL,SAASsI,eACjB5R,EAAOqJ,MAAQrJ,EAAOqJ,MAAMnL,QAAQ2N,IACnC,IAAIyY,EAASzQ,EAAShI,EAAKX;AAC3B,QAASoZ,IAA0C,IAAhCxmB,EAAKuL,MAAM1L,QAAQ2mB,QAIjCtkB,EAQRwhB,eAAgB+C,GAA0B,GACzC,IAAI1lB,EAAGgD,EAAGC,EAAGxE,EAAGqa,EAAU3G,EAAWwT,EAAuBC,EAAmBC,EAC3ErT;AACEsT,MAAAA,EAA0C,GAE1CC,EAAwB;AAC1B9mB,IAAAA,EAAWf,KACXyM,EAAY1L,EAAKuiB;AACrB,MAAMwE,EAAerb,IAAU1L,EAAKmiB,WAAuB,IAATzW,GAAiC,MAAlB1L,EAAKmiB;AACtE,ILlyCG6E,EKkyCCC,EAAcjnB,EAAK6K,OAAOa,GAC1Bwb,EAAkB,KAClBC,EAAkBnnB,EAAKwL,SAASmI,aAAc,EAC9C2H,EAAoBtb,EAAKsb;AAsBxBva,IAnBDgmB,IACHG,EAAkBlnB,EAAK8c,gBAGtB8J,EAAeM,EAAcE,QAAQ,iBAKvC5nB,EAAIynB,EAAQ1b,MAAM7L,OACsB,iBAA7BM,EAAKwL,SAASoI,aACxBpU,EAAIkC,KAAKmH,IAAIrJ,EAAGQ,EAAKwL,SAASoI,aAG3BpU,EAAI,IACP2nB,GAAgB,GAIZpmB,EAAI,EAAGA,EAAIvB,EAAGuB,IAAK,CAGvB,IAAIgN,EAASkZ,EAAQ1b,MAAMxK;AACvB,IAACgN,EAAO;AAEZ,IAAIsZ,EAAatZ,EAAKX,GAClBuL,EAAW3Y,EAAKmM,QAAQkb;AAExB1O,QAAWhZ,IAAXgZ,EAAuB;AAE3B,IAAI2O,EAAYtR,EAASqR,GACrBE,EAAavnB,EAAKwnB,UAAUF,GAAS;AAUzC,IAPKtnB,EAAKwL,SAASsI,cAClByT,EAAUvX,UAAUmW,OAAO,WAAYnmB,EAAKuL,MAAMkc,SAASH,IAG5DzN,EAAclB,EAAO3Y,EAAKwL,SAAS+I,gBAAkB,GAGhDxQ,EAAI,EAAGC,GAFZkP,EAAcpR,MAAMmJ,QAAQ4O,GAAYA,EAAW,CAACA,KAEvB3G,EAAUxT,OAAQqE,EAAIC,EAAGD,IAAK,CAC1D8V,EAAW3G,EAAUnP,GAChB/D,EAAKkT,UAAU9H,eAAeyO,KAClCA,EAAW;AAGZ,IAAI6N,EAAiBb,EAAOhN;KACLla,IAAnB+nB,IACHA,EAAiBlZ,SAASmZ,yBAC1Bb,EAAaxnB,KAAKua,IAIf9V,EAAI,IACPwjB,EAAYA,EAAU/U,WAAU,GAChCtB,EAAQqW,EAAU,CAACna,GAAIuL,EAAOiP,IAAI,UAAU7jB,EAAkBwhB,gBAAA,OAC9DgC,EAAUvX,UAAUxJ,IAAI,aACxByJ,EAAcsX,EAAU,UAIpBvnB,EAAK8c,cAAgB9c,EAAK8c,aAAa5D,QAAQzX,OAAS4lB,GACvDT,GAAgBA,EAAa1N,QAAQE,QAAUS,EAASgO,aAC3DX,EAAgBK,IAKnBG,EAAejV,YAAY8U,GAC3BV,EAAOhN,GAAY6N,GAKjB1nB,EAAKwL,SAASqJ,mBACjBiS,EAAa3f,MAAK,CAAClE,EAAGO,KACrB,MAAMskB,EAAS9nB,EAAKkT,UAAUjQ,GACxB8kB,EAAS/nB,EAAKkT,UAAU1P;AAGvBwkB,OAFSF,GAASA,EAAMG,QAAU,IACzBF,GAASA,EAAME,QAAU,MAM3CvB,EAAOlY,SAASmZ,yBAChBzc,EAAS4b,GAAejN,IAEvB,IAAI6N,EAAiBb,EAAOhN;AAExB,IAAC6N,IAAmBA,EAAe/N,SAASja,OAAS;AAEzD,IAAIwoB,EAAgBloB,EAAKkT,UAAU2G;AAE/BqO,QAAkBvoB,IAAlBuoB,EAA6B,CAEhC,IAAIC,EAAgB3Z,SAASmZ,yBACzBS,EAASpoB,EAAK8V,OAAO,kBAAmBoS;AAC5CnQ,GAAQoQ,EAAeC,GACvBrQ,GAAQoQ,EAAeT;AAEvB,IAAIW,EAAaroB,EAAK8V,OAAO,WAAY,CAACsD,MAAM8O,EAAc/b,QAAQgc;AAEtEpQ,GAAQ2O,EAAM2B,QAGdtQ,GAAQ2O,EAAMgB,MAIhBpM,EAAiB5M,UAAY,GAC7BqJ,GAAQuD,EAAkBoL,GAGtB1mB,EAAKwL,SAASoG,YL35CfoV,EK45Ce1L,EL55CDgN,iBAAiB,kBACnCxmB,MAAMijB,UAAUlmB,QAAQ6X,KAAKsQ,GAAU,SAASlX,GAC/C,IAAIkI,EAASlI,EAAGa;AAChBqH,EAAOrG,aAAa7B,EAAGlB,WAAoBkB,GAC3CkI,EAAOrS,eKy5CFshB,EAAQvb,MAAMhM,QAAUunB,EAAQpb,OAAOnM,QAC1CwL,EAAS+b,EAAQpb,QAAS0c,IACzB3W,EAAW0J,EAAkBiN,EAAI3d;AAMhC4d,IAAAA,EAAgBC,IACnB,IAAI9Z,EAAU3O,EAAK8V,OAAO2S,EAAS,CAACxmB,MAAMyJ;AAK1C,OAJIiD,IACHwY,GAAgB,EAChB7L,EAAiBoN,aAAa/Z,EAAS2M,EAAiB1M,aAElDD;AA6BR,GAxBI3O,EAAKsW,QACRkS,EAAa,WAGHxoB,EAAKwL,SAASqK,WAAWa,KAAK1W,EAAK0L,GAIX,IAAzBub,EAAQ1b,MAAM7L,QACvB8oB,EAAa,cAJbA,EAAa,gBAWd7B,EAAoB3mB,EAAK2oB,UAAUjd,MAElC6H,EAASiV,EAAa,kBAKvBxoB,EAAK0c,WAAauK,EAAQ1b,MAAM7L,OAAS,GAAKinB,EAC1CQ,EAAe,CAElB,GAAIF,EAAQ1b,MAAM7L,OAAS,GAM1B,GAJKwnB,GAAwC,WAAvBlnB,EAAKwL,SAASyJ,MAAsCtV,MAAjBK,EAAKuL,MAAM,KACnE2b,EAAgBlnB,EAAKwnB,UAAUxnB,EAAKuL,MAAM,MAGtC+P,EAAiB7K,SAASyW,GAAiB,CAE3C0B,IAAAA,EAAe;AACfrV,IAAWvT,EAAKwL,SAASwI,gBAC5B4U,EAAe,GAEhB1B,EAAgBlnB,EAAK6oB,aAAaD,SAG1BrV,IACT2T,EAAgB3T;AAGbkT,IAAoBzmB,EAAK+b,SAC5B/b,EAAKijB,OACLjjB,EAAKwlB,eAAe0B,EAAc,SAEnClnB,EAAKojB,gBAAgB8D,QAGrBlnB,EAAKqkB,oBACDoC,GAAmBzmB,EAAK+b,QAC3B/b,EAAKqgB,OAAM,GASdwI,aACC,OAAO5pB,KAAKqc,iBAAiBgN,iBAAiB,qBAgB/CtP,UAAWrM,EAAgBmc,GAAe,GACzC,MAAM9oB,EAAOf;AAIb,GAAI6C,MAAMmJ,QAAQ0B,GAEjB,OADA3M,EAAK0gB,WAAY/T,EAAMmc,IAChB;AAGR,MAAM9d,EAAM+K,EAASpJ,EAAK3M,EAAKwL,SAASgJ;AACxC,OAAY,OAARxJ,IAAgBhL,EAAKmM,QAAQf,eAAeJ,KAIhD2B,EAAKsb,OAAWtb,EAAKsb,UAAYjoB,EAAKwb,MACtC7O,EAAKib,IAAQ5nB,EAAK4b,QAAU,QAAUjP,EAAKsb,OAC3CjoB,EAAKmM,QAAQnB,GAAO2B,EACpB3M,EAAKmiB,UAAa,KAEd2G,IACH9oB,EAAKgd,YAAYhS,GAAO8d,EACxB9oB,EAAKF,QAAQ,aAAckL,EAAK2B,IAG1B3B,GAOR0V,WAAY/T,EAAkBmc,GAAe,GAC5C5d,EAASyB,GAAOoc,IACf9pB,KAAK+Z,UAAU+P,EAAKD,MAOtBE,eAAgBrc,GACf,OAAO1N,KAAK+Z,UAAUrM,GAQvBgU,oBAAoBhU,GACf3B,IAAAA,EAAM+K,EAASpJ,EAAK1N,KAAKuM,SAASoJ;AAEtC,OAAa,OAAR5J,IAEL2B,EAAKsb,OAAStb,EAAKsb,UAAYhpB,KAAKuc,MACpCvc,KAAKiU,UAAUlI,GAAO2B,EACf3B,GAQRie,eAAe7b,EAAWT,GACzB,IAAIuc;AACJvc,EAAK1N,KAAKuM,SAASoJ,oBAAsBxH,GAErC8b,EAAYjqB,KAAK0hB,oBAAoBhU,KACxC1N,KAAKa,QAAQ,eAAgBopB,EAAWvc,GAQ1Cwc,kBAAkB/b,GACbnO,KAAKiU,UAAU9H,eAAegC,YAC1BnO,KAAKiU,UAAU9F,GACtBnO,KAAKmqB,aACLnqB,KAAKa,QAAQ,kBAAmBsN,IAOlCic,oBACMnW,KAAAA,UAAY,GACjBjU,KAAKmqB,aACAtpB,KAAAA,QAAQ,kBASdwpB,aAAa7nB,EAAckL,GACpB3M,MAAAA,EAAOf;AACb,IAAIsqB,EACAC;AAEJ,MAAMC,EAAa1T,EAAStU,GACtBioB,EAAa3T,EAASpJ,EAAK3M,EAAKwL,SAASgJ;AAG3CiV,GAAc,OAAdA,EAAqB;AAEzB,MAAME,EAAY3pB,EAAKmM,QAAQsd;AAE3BE,GAAYhqB,MAAZgqB,EAAwB;AACxB,GAAqB,iBAAdD,EAAyB,MAAM,IAAI5O,MAAM;AAGpD,MAAMnC,EAAU3Y,EAAKwnB,UAAUiC,GACzB1b,EAAS/N,EAAK4pB,QAAQH;AAa5B,GAVA9c,EAAKsb,OAAStb,EAAKsb,QAAU0B,EAAS1B,cAC/BjoB,EAAKmM,QAAQsd,GAIpBzpB,EAAK6pB,aAAaH,GAElB1pB,EAAKmM,QAAQud,GAAa/c,EAGtBgM,EAAQ,CACP3Y,GAAAA,EAAKsb,iBAAiB7K,SAASkI,GAAS,CAErCmR,MAAAA,EAAa9pB,EAAKwd,QAAQ,SAAU7Q;AAC1C6E,EAAYmH,EAAQmR,GAEhB9pB,EAAK8c,eAAiBnE,GACzB3Y,EAAKojB,gBAAgB0G,GAGvBnR,EAAOzI,SAIJnC,KAEiB,KADpByb,EAAaxpB,EAAKuL,MAAM1L,QAAQ4pB,KAE/BzpB,EAAKuL,MAAM3L,OAAO4pB,EAAY,EAAGE,GAGlCH,EAAWvpB,EAAKwd,QAAQ,OAAQ7Q,GAE5BoB,EAAKiC,UAAUS,SAAS,WAAYjB,EAAW+Z,EAAS,UAE5D/X,EAAazD,EAAMwb,IAIpBvpB,EAAKmiB,UAAY,KAOlB4H,aAAatoB,EAAc+iB,GACpBxkB,MAAAA,EAAOf;AACbwC,EAAQuU,EAASvU,GAEjBzB,EAAK6pB,aAAapoB,UAEXzB,EAAKgd,YAAYvb,UACjBzB,EAAKmM,QAAQ1K,GACpBzB,EAAKmiB,UAAY,KACjBniB,EAAKF,QAAQ,gBAAiB2B,GAC9BzB,EAAKgqB,WAAWvoB,EAAO+iB,GAMxByF,aAAa7pB,GAEN8pB,MAAAA,GAAe9pB,GAAUnB,KAAKkrB,aAAajd,KAAKjO;AAEjDwX,KAAAA,eAAkB,GAClBuG,KAAAA,YAAe,GACpB/d,KAAKmqB;AAEC3P,MAAAA,EAAsB;AAC5BvO,EAAQjM,KAAKkN,SAAQ,CAACwM,EAAiB3N,KAClCkf,EAAYvR,EAAO3N,KACtByO,EAASzO,GAAO2N,MAIlB1Z,KAAKkN,QAAUlN,KAAK6c,OAAOvQ,MAAQkO,EAC9B0I,KAAAA,UAAY,KACZriB,KAAAA,QAAQ,gBAQdqqB,YAAYxR,EAAiBlX,GACxB,OAAAxC,KAAKsM,MAAM1L,QAAQ4B,IAAU,EAWlC+lB,UAAU/lB,EAA4C8R,GAAe,GAEpE,MAAMiT,EAASzQ,EAAStU;AACxB,GAAe,OAAX+kB,EAAkB,OAAO;AAE7B,MAAM7N,EAAS1Z,KAAKkN,QAAQqa;AACxB7N,GAAUhZ,MAAVgZ,EAAqB,CAEpBA,GAAAA,EAAOyR,KACHzR,OAAAA,EAAOyR;AAGf,GAAI7W,EACH,OAAOtU,KAAKue,QAAQ,SAAU7E,GAIhC,OAAO,KAQRwK,YAAaxK,EAAyBrL,EAAkBuJ,EAAc,UACjE7W,IAAa8H;AAEb,IAAC6Q,EACJ,OAAO;AAIP7Q,EADW,QAAR+O,EANO5X,KAOGinB,kBAPHjnB,KASGqc,iBAAiBgN,iBAAiB;AAGhD,IAAK,IAAIvnB,EAAI,EAAGA,EAAI+G,EAAIpI,OAAQqB,IAC/B,GAAI+G,EAAI/G,IAAM4X,EAIVrL,OAAAA,EAAY,EACRxF,EAAI/G,EAAE,GAGP+G,EAAI/G,EAAE;AAEd,OAAO,KASR6oB,QAAQ7b,GAEP,GAAmB,iBAARA,EACV,OAAOA;AAGR,IAAItM,EAAQsU,EAAShI;AACrB,OAAiB,OAAVtM,EACJxC,KAAKoc,QAAQxM,cAAe,gBAAeiJ,GAAWrW,QACtD,KAQJgjB,SAAUxK,EAAwBuK,GAC7BxkB,IAAAA,EAAOf,KAEPsM,EAAQzJ,MAAMmJ,QAAQgP,GAAUA,EAAS,CAACA;AAExCoQ,MAAAA,GADN9e,EAAQA,EAAMnL,QAAOkqB,IAAgC,IAA3BtqB,EAAKuL,MAAM1L,QAAQyqB,MACrB/e,EAAM7L,OAAS;AACvC6L,EAAM1M,SAAQkP,IACb/N,EAAKuqB,UAAaxc,IAASsc,EAC3BrqB,EAAK0iB,QAAQ3U,EAAMyW,MASrB9B,QAASjhB,EAAc+iB,GAGtB7N,GAAgB1X,KAFHulB,EAAS,GAAK,CAAC,SAAS,mBAEP,KACzBzW,IAAAA,EAAMyc;AACJxqB,MAAAA,EAAOf,KACNwe,EAAYzd,EAAKwL,SAASyJ,KAC3BuR,EAASzQ,EAAStU;AAExB,KAAI+kB,IAA0C,IAAhCxmB,EAAKuL,MAAM1L,QAAQ2mB,KAEd,WAAd/I,GACHzd,EAAKqgB,QAGY,WAAd5C,GAA2Bzd,EAAKwL,SAASuI,cAK/B,OAAXyS,GAAoBxmB,EAAKmM,QAAQf,eAAeob,KAClC,WAAd/I,GAAwBzd,EAAKwhB,MAAMgD,GACrB,UAAd/G,IAAyBzd,EAAKyqB,UAA9BhN,CAYAzd,GAVJ+N,EAAO/N,EAAKwd,QAAQ,OAAQxd,EAAKmM,QAAQqa,IAErCxmB,EAAKqb,QAAQ5K,SAAS1C,KACzBA,EAAOA,EAAKyE,WAAU,IAGvBgY,EAAUxqB,EAAKyqB,SACfzqB,EAAKuL,MAAM3L,OAAOI,EAAK6c,SAAU,EAAG2J,GACpCxmB,EAAK0qB,cAAc3c,GAEf/N,EAAKuc,QAAS,CAGb,IAACvc,EAAKuqB,WAAavqB,EAAKwL,SAASsI,aAAc,CAClD,IAAI6E,EAAS3Y,EAAKwnB,UAAUhB,GACxBtD,EAAOljB,EAAKmjB,YAAYxK,EAAQ;AAChCuK,GACHljB,EAAKojB,gBAAgBF,GAMlBljB,EAAKuqB,WAAcvqB,EAAKwL,SAAS0Y,kBACrClkB,EAAK0jB,eAAe1jB,EAAKqc,WAA2B,WAAdoB,GAID,GAAlCzd,EAAKwL,SAAS0Y,kBAA6BlkB,EAAKyqB,SACnDzqB,EAAKqgB,QACMrgB,EAAKuqB,WAChBvqB,EAAK6f,mBAGN7f,EAAKF,QAAQ,WAAY0mB,EAAQzY,GAE5B/N,EAAKuqB,WACTvqB,EAAKmgB,oBAAoB,CAACqE,OAAQA,MAI/BxkB,EAAKuqB,YAAeC,GAAWxqB,EAAKyqB,YACxCzqB,EAAK2f,aACL3f,EAAKkgB,oBAWR8J,WAAYjc,EAAyB,KAAMyW,GACpCxkB,MAAAA,EAAQf;AAGV,KAFJ8O,EAAS/N,EAAK4pB,QAAQ7b,IAEV;AAERhN,IAAAA,EAAEqkB;AACN,MAAM3jB,EAAQsM,EAAKmL,QAAQzX;AAC3BV,EAAI+P,EAAU/C,GAEdA,EAAKmC,SACDnC,EAAKiC,UAAUS,SAAS,YAC3B2U,EAAMplB,EAAK+c,YAAYld,QAAQkO,GAC/B/N,EAAK+c,YAAYnd,OAAOwlB,EAAK,GAC7BnV,EAAclC,EAAK,WAGpB/N,EAAKuL,MAAM3L,OAAOmB,EAAG,GACrBf,EAAKmiB,UAAY,MACZniB,EAAKwL,SAAS8H,SAAWtT,EAAKgd,YAAY5R,eAAe3J,IAC7DzB,EAAK+pB,aAAatoB,EAAO+iB,GAGtBzjB,EAAIf,EAAK6c,UACZ7c,EAAKgkB,SAAShkB,EAAK6c,SAAW,GAG/B7c,EAAKmgB,oBAAoB,CAACqE,OAAQA,IAClCxkB,EAAKkgB,eACLlgB,EAAK6f,mBACL7f,EAAKF,QAAQ,cAAe2B,EAAOsM,GAapC4U,WAAY1gB,EAAkB,KAAMtD,EAA6B,UAGvC,IAArBc,UAAUC,SACbf,EAAWc,UAAU,IAEC,mBAAZd,IACVA,EAAW;AAGRqB,IAEA2qB,EAFA3qB,EAAQf,KACR2rB,EAAQ5qB,EAAK6c;AAIjB,GAFA5a,EAAQA,GAASjC,EAAKuiB,cAEjBviB,EAAK2oB,UAAU1mB,GAEnB,OADAtD,KACO;AAGRqB,EAAK6qB;AAEDC,IAAAA,GAAU,EACVvX,EAAU5G,IAGT,GAFJ3M,EAAK+qB,UAEApe,GAAwB,iBAATA,EAAmB,OAAOhO;AAC9C,IAAI8C,EAAQsU,EAASpJ,EAAK3M,EAAKwL,SAASgJ;AACxC,GAAqB,iBAAV/S,EACV,OAAO9C;AAGRqB,EAAKskB,kBACLtkB,EAAKgZ,UAAUrM,GAAK,GACpB3M,EAAKgkB,SAAS4G,GACd5qB,EAAK0iB,QAAQjhB,GACb9C,EAASgO,GACTme,GAAU;AAgBX,OAZCH,EADmC,mBAAzB3qB,EAAKwL,SAAS+H,OACfvT,EAAKwL,SAAS+H,OAAOmD,KAAKzX,KAAMgD,EAAOsR,GAEvC,CACR3K,CAAC5I,EAAKwL,SAASiJ,YAAaxS,EAC5B2G,CAAC5I,EAAKwL,SAASgJ,YAAavS,GAIzB6oB,GACJvX,EAAOoX,IAGD,EAMRvK,eACKpgB,IAAAA,EAAOf;AACXe,EAAKmiB,UAAY,KAEbniB,EAAKuc,SACRvc,EAAKykB,SAASzkB,EAAKuL,OAGpBvL,EAAKmgB,sBACLngB,EAAKkgB,eAONA,eACOlgB,MAAAA,EAAWf;AAEjBe,EAAKgrB;AAEL,MAAMP,EAASzqB,EAAKyqB,SACdrO,EAAWpc,EAAKoc;AAEtBpc,EAAKwQ,QAAQR,UAAUmW,OAAO,MAAMnmB,EAAK2b;AAGzC,MAAMsP,EAAiBjrB,EAAKwQ,QAAQR;AN76DR9F,IAAAA;AM+6D5B+gB,EAAe9E,OAAO,QAASnmB,EAAKqc,WACpC4O,EAAe9E,OAAO,WAAYnmB,EAAKgc,YACvCiP,EAAe9E,OAAO,WAAYnmB,EAAKic,YACvCgP,EAAe9E,OAAO,WAAYnmB,EAAKmc,SACvC8O,EAAe9E,OAAO,SAAU/J,GAChC6O,EAAe9E,OAAO,OAAQsE,GAC9BQ,EAAe9E,OAAO,eAAgBnmB,EAAKqc,YAAcrc,EAAKsc,eAC9D2O,EAAe9E,OAAO,kBAAmBnmB,EAAK+b,QAC9CkP,EAAe9E,OAAO,eNv7DMjc,EMu7DuBlK,EAAKmM,QNt7DrB,IAA5B1G,OAAOC,KAAKwE,GAAKxK,SMu7DxBurB,EAAe9E,OAAO,YAAanmB,EAAKuL,MAAM7L,OAAS,GAaxDsrB,uBACKhrB,IAAAA,EAAOf;AAENe,EAAKiC,MAAMipB,WAIhBlrB,EAAKmc,QAAUnc,EAAKiC,MAAMipB,SAASC,MACnCnrB,EAAKkc,WAAalc,EAAKmc,SASxBsO,SACC,OAAkC,OAA3BxrB,KAAKuM,SAASqI,UAAqB5U,KAAKsM,MAAM7L,QAAUT,KAAKuM,SAASqI,SAQ9EsM,oBAAqBiL,EAAoB,IAClCprB,MAAAA,EAAOf;AACT0Z,IAAAA,EAAQwF;AAENkN,MAAAA,EAAerrB,EAAKiC,MAAM4M,cAAc;AAE1C7O,GAAAA,EAAK0b,cAAe,CAEjBjC,MAAAA,EAAgC,GAChC6R,EAAyBtrB,EAAKiC,MAAMqmB,iBAAiB,kBAAkB5oB;AAE7E,SAAS6rB,EAAYhE,EAAkC9lB,EAAc0c,GAoBpE,OAlBKoJ,IACJA,EAAYpZ,EAAO,kBAAoB8H,EAAYxU,GAAS,KAAOwU,EAAYkI,GAAS,cAKrFoJ,GAAa8D,GAChBrrB,EAAKiC,MAAM8V,OAAOwP,GAGnB9N,EAASna,KAAKioB,IAIVA,GAAa8D,GAAgBC,EAAe,KAC/C/D,EAAU9N,UAAW,GAGf8N,EAIRvnB,EAAKiC,MAAMqmB,iBAAiB,kBAAkBzpB,SAAS0oB,IAClCA,EAAW9N,UAAW,KAKlB,GAArBzZ,EAAKuL,MAAM7L,QAAqC,UAAtBM,EAAKwL,SAASyJ,KAE3CsW,EAAYF,EAAc,GAAI,IAK9BrrB,EAAKuL,MAAM1M,SAAS4C,IAIfgY,GAHJd,EAAW3Y,EAAKmM,QAAQ1K,GACxB0c,EAAUxF,EAAO3Y,EAAKwL,SAASiJ,aAAe,GAE1CgF,EAASgO,SAAS9O,EAAOa,SAAU,CAEtC+R,EADkBvrB,EAAKiC,MAAM4M,cAAe,iBAAgBiJ,GAAWrW,sBAChDA,EAAO0c,QAE9BxF,EAAOa,QAAU+R,EAAY5S,EAAOa,QAAS/X,EAAO0c,WAOvDne,EAAKiC,MAAMR,MAAQzB,EAAKukB;AAGrBvkB,EAAKuc,UACH6O,EAAK5G,QACTxkB,EAAKF,QAAQ,SAAUE,EAAKukB,aAS/BtB,OACKjjB,IAAAA,EAAOf;AAEPe,EAAKoc,UAAYpc,EAAK+b,QAAkC,UAAvB/b,EAAKwL,SAASyJ,MAAoBjV,EAAKyqB,WAC5EzqB,EAAK+b,QAAS,EACd7K,EAAQlR,EAAKub,WAAW,CAAkByC,gBAAA,SAC1Che,EAAKkgB,eACL7Q,EAASrP,EAAKob,SAAS,CAACoQ,WAAY,SAAUC,QAAS,UACvDzrB,EAAK6f,mBACLxQ,EAASrP,EAAKob,SAAS,CAACoQ,WAAY,UAAWC,QAAS,UACxDzrB,EAAKqe,QACLre,EAAKF,QAAQ,gBAAiBE,EAAKob,WAMpCiF,MAAMiE,GAAgB,GACjBtkB,IAAAA,EAAOf,KACPa,EAAUE,EAAK+b;AAEfuI,IAGHtkB,EAAKskB,kBAEsB,WAAvBtkB,EAAKwL,SAASyJ,MAAqBjV,EAAKuL,MAAM7L,QACjDM,EAAKklB,aAIPllB,EAAK+b,QAAS,EACd7K,EAAQlR,EAAKub,WAAW,CAAkByC,gBAAA,UAC1C3O,EAASrP,EAAKob,SAAS,CAACqQ,QAAS,SAC7BzrB,EAAKwL,SAASsI,cACjB9T,EAAKqkB,oBAENrkB,EAAKkgB,eAEDpgB,GAASE,EAAKF,QAAQ,iBAAkBE,EAAKob,UAQlDyE,mBAEC,GAAqC,SAAjC5gB,KAAKuM,SAASgK,eAAlB,CAIIkW,IAAAA,EAAYzsB,KAAKoc,QACjBsQ,EAASD,EAAQ3F,wBACjBC,EAAS0F,EAAQ5F,aAAe6F,EAAK3F,IAAO9I,OAAO0O,QACnDC,EAASF,EAAKE,KAAO3O,OAAO4O;AAGhCzc,EAASpQ,KAAKmc,SAAS,CACtBoD,MAAQmN,EAAKnN,MAAQ,KACrBwH,IAAQA,EAAM,KACd6F,KAAQA,EAAO,QAUjBrK,MAAMgD,GACDxkB,IAAAA,EAAOf;AAEX,GAAKe,EAAKuL,MAAM7L,OAAhB,CAEA,IAAI6L,EAAQvL,EAAKkmB;AACjBhb,EAAQK,GAAOwC,IACd/N,EAAKgqB,WAAWjc,GAAK,MAGtB/N,EAAK4jB,YACAY,GAASxkB,EAAKmgB,sBACnBngB,EAAKF,QAAQ,UAQd4qB,cAAc5a,GACP9P,MAAAA,EAAQf,KACR2rB,EAAS5qB,EAAK6c,SACdvM,EAAStQ,EAAKqb;AAEpB/K,EAAOoY,aAAa5Y,EAAIQ,EAAOqJ,SAASiR,IAAU,MAClD5qB,EAAKgkB,SAAS4G,EAAQ,GAOvBnH,gBAAgB9f,GACf,IAAI2J,EAAWye,EAAWnB,EAAOoB,EFjtEN/pB,EEktEvBjC,EAAOf;AAEXqO,EAAa3J,GJtzEe,IIszEVA,EAAEkf,SAAwC,EAAI,EAChEkJ,EFptEM,CACN5pB,OAF2BF,EEqtEFjC,EAAKmb,eFntEhB8Q,gBAAkB,EAChCvsB,QAAUuC,EAAMiqB,cAAc,IAAMjqB,EAAMgqB,gBAAgB;AEstEpDE,MAAAA,EAAqB;AAE3B,GAAInsB,EAAK+c,YAAYrd,OAEpBssB,EAAOpb,EAAQ5Q,EAAK+c,YAAazP,GACjCsd,EAAQ9Z,EAAUkb,GAEd1e,EAAY,GAAKsd,IAErB1f,EAAQlL,EAAK+c,aAAchP,GAAiBoe,EAAS7sB,KAAKyO;KAEpD,IAAK/N,EAAKqc,WAAoC,WAAvBrc,EAAKwL,SAASyJ,OAAsBjV,EAAKuL,MAAM7L,OAAQ,CACpF,MAAM6L,EAAQvL,EAAKkmB;AACnB,IAAIkG;AACA9e,EAAY,GAAyB,IAApBye,EAAU5pB,OAAoC,IAArB4pB,EAAUrsB,OACvD0sB,EAAU7gB,EAAMvL,EAAK6c,SAAW,GAEvBvP,EAAY,GAAKye,EAAU5pB,QAAUnC,EAAKuiB,aAAa7iB,SAChE0sB,EAAU7gB,EAAMvL,EAAK6c,gBAGNld,IAAZysB,GACHD,EAAS7sB,KAAM8sB,GAIb,IAACpsB,EAAKqsB,aAAaF,EAASxoB,GAC/B,OAAO;AAUDwoB,IAPPpV,GAAepT,GAAE,QAGI,IAAVinB,GACV5qB,EAAKgkB,SAAS4G,GAGRuB,EAASzsB,QACfM,EAAKgqB,WAAWmC,EAAS9iB;AAO1B,OAJArJ,EAAK4jB,YACL5jB,EAAK6f,mBACL7f,EAAK0jB,gBAAe,IAEb,EAMR2I,aAAa9gB,EAAgByL,GAE5B,MAAMiD,EAAS1O,EAAMhE,KAAIwG,GAAQA,EAAKmL,QAAQzX;AAG1C,SAACwY,EAAOva,QAA6C,mBAA3BT,KAAKuM,SAAS8gB,WAAkE,IAAvCrtB,KAAKuM,SAAS8gB,SAASrS,EAAOjD,IActGwM,iBAAiBlW,EAAkB3J,GAClC,IAAIwhB,EAAaoH,EAAUvsB,EAAOf;AAE9Be,EAAK2b,MAAKrO,IAAc,GACxBtN,EAAKuiB,aAAa7iB,SAIlB2X,GAAU0L,EAAuBpf,IAAM0T,GAAU,WAAW1T,IAQ7D4oB,GANFpH,EAAgBnlB,EAAK8kB,cAAcxX,IAG7B6X,EAAYnV,UAAUS,SAAS,UAGtBzQ,EAAKmjB,YAAYgC,EAAY7X,EAAU,QAFvC6X,EAML7X,EAAY,EACRtN,EAAKmb,cAAcqR,mBAEnBxsB,EAAKmb,cAAclK,0BAK5Bsb,EAASvc,UAAUS,SAAS,WAC/BzQ,EAAKilB,iBAAiBE,GAEvBnlB,EAAKglB,mBAAmBuH,IAKzBvsB,EAAKysB,UAAUnf,IAIjBmf,UAAUnf,IAMVwX,cAAcxX,GAET6X,IAAAA,EAAclmB,KAAKoc,QAAQxM,cAAc;AAC7C,GAAIsW,EACH,OAAOA;AAIJjjB,IAAAA,EAASjD,KAAKoc,QAAQiN,iBAAiB;AAC3C,OAAIpmB,EACI0O,EAAQ1O,EAAOoL,QADvB,EAcD0W,SAAS0I,GACRztB,KAAK4d,SAAW5d,KAAKsM,MAAM7L,OAO5BwmB,kBACQpkB,OAAAA,MAAMC,KAAM9C,KAAKoc,QAAQiN,iBAAiB,mBAOlDuC,OACMzO,KAAAA,UAAW,EAChBnd,KAAKihB,eAMN6K,SACM3O,KAAAA,UAAW,EAChBnd,KAAKihB,eAONI,UACKtgB,IAAAA,EAAOf;AACXe,EAAKiC,MAAMsX,UAAc,EACzBvZ,EAAKmb,cAAc5B,UAAY,EAC/BvZ,EAAKub,WAAWE,UAAa,EAC7Bzb,EAAKgc,YAAiB,EACtB/c,KAAKohB,QACLrgB,EAAK6qB,OAONtK,SACKvgB,IAAAA,EAAOf;AACXe,EAAKiC,MAAMsX,UAAc,EACzBvZ,EAAKmb,cAAc5B,UAAY,EAC/BvZ,EAAKub,WAAWE,SAAYzb,EAAKyb,SACjCzb,EAAKgc,YAAiB,EACtBhc,EAAK+qB,SAQN4B,UACK3sB,IAAAA,EAAOf,KACP8gB,EAAiB/f,EAAK+f;AAE1B/f,EAAKF,QAAQ,WACbE,EAAKT,MACLS,EAAKwQ,QAAQN,SACblQ,EAAKob,SAASlL,SAEdlQ,EAAKiC,MAAMyM,UAAYqR,EAAerR,UACtC1O,EAAKiC,MAAMwZ,SAAWsE,EAAetE,SAErCxL,EAAcjQ,EAAKiC,MAAM,cAAc,wBAEvCjC,EAAK6b,kBAEE7b,EAAKiC,MAAMgb,UAQnBnH,OAAQ8W,EAA+BjgB,GAClCS,IAAAA,EAAIsZ;AACF1mB,MAAAA,EAAOf;AAET,GAA8C,mBAAvCA,KAAKuM,SAASsK,OAAO8W,GAC/B,OAAO;AAMJ,KAFJlG,EAAO1mB,EAAKwL,SAASsK,OAAO8W,GAAclW,KAAKzX,KAAM0N,EAAMsJ,IAG1D,OAAO;AAsBR,GAnBAyQ,EAAOvY,EAAQuY,GAGM,WAAjBkG,GAA8C,kBAAjBA,EAE5BjgB,EAAK3M,EAAKwL,SAASkJ,eACtBxD,EAAQwV,EAAK,CAAiBmG,gBAAA,SAE9B3b,EAAQwV,EAAK,CAAoBoG,kBAAA,KAGR,aAAjBF,IACTxf,EAAKT,EAAKyM,MAAMpZ,EAAKwL,SAASoJ,oBAC9B1D,EAAQwV,EAAK,CAAetZ,aAAAA,IACzBT,EAAKyM,MAAMpZ,EAAKwL,SAASkJ,gBAC3BxD,EAAQwV,EAAK,CAAkBqG,gBAAA,MAIZ,WAAjBH,GAA8C,SAAjBA,EAAyB,CACzD,MAAMnrB,EAAQuU,EAASrJ,EAAK3M,EAAKwL,SAASgJ;AAC1CtD,EAAQwV,EAAK,CAAejlB,aAAAA,IAIP,SAAjBmrB,GACHpd,EAAWkX,EAAK1mB,EAAKwL,SAAS8J,WAC9BpE,EAAQwV,EAAK,CAAgBsG,eAAA,OAE7Bxd,EAAWkX,EAAK1mB,EAAKwL,SAAS+J,aAC9BrE,EAAQwV,EAAK,CACZ5I,KAAK,SACL1Q,GAAGT,EAAKib,MAITjb,EAAKyd,KAAO1D,EACZ1mB,EAAKmM,QAAQ1K,GAASkL,GAMxB,OAAO+Z,EASRlJ,QAASoP,EAA+BjgB,GACjC+Z,MAAAA,EAAOznB,KAAK6W,OAAO8W,EAAcjgB;AAEnC+Z,GAAQ,MAARA,EACH,KAAM;AAEP,OAAOA,EAUR0C,aAECle,EAAQjM,KAAKkN,SAAUwM,IAClBA,EAAOyR,OACVzR,EAAOyR,KAAKla,gBACLyI,EAAOyR,SAUjBP,aAAapoB,GAEZ,MAAM8lB,EAActoB,KAAKuoB,UAAU/lB;AAC/B8lB,GAAYA,EAAUrX,SAS3ByY,UAAW1mB,GACH,OAAAhD,KAAKuM,SAAS+H,QAAWtR,EAAMvC,OAAS,GAAOT,KAAKuM,SAASiI,aAAkCiD,KAAKzX,KAAMgD,GAWlHgrB,KAAMC,EAAaC,EAAeC,GAC7BptB,IAAAA,EAAOf,KACPouB,EAAcrtB,EAAKmtB;AAGvBntB,EAAKmtB,GAAU,WACVjrB,IAAAA,EAAQorB;AAQRJ,MANS,UAATA,IACHhrB,EAASmrB,EAAYptB,MAAMD,EAAMP,YAGlC6tB,EAAaF,EAAOntB,MAAMD,EAAMP,WAEnB,YAATytB,EACII,GAGK,WAATJ,IACHhrB,EAASmrB,EAAYptB,MAAMD,EAAMP,YAG3ByC,YE1qFVkY,GAAUmT,OAAO,kBCWF,WACVvtB,IAAAA,EAAOf;AAUXe,EAAKitB,KAAK,UAAU,YAAYP,IAEJ,WAAvB1sB,EAAKwL,SAASyJ,MAAsBjV,EAAKqb,QAAQ5K,SAASzQ,EAAKmb,gBAGlEuR,EAAUhrB,KAAKC,IAAI,EAAGD,KAAKmH,IAAI7I,EAAKuL,MAAM7L,OAAQgtB,MAEnC1sB,EAAK6c,UAAa7c,EAAKuqB,WAErCvqB,EAAKkmB,kBAAkBrnB,SAAQ,CAAC+a,EAAM7V,KACjCA,EAAI2oB,EACP1sB,EAAKmb,cAAc6E,sBAAsB,cAAepG,GAExD5Z,EAAKqb,QAAQ5I,YAAamH,MAV7B8S,EAAU1sB,EAAKuL,MAAM7L,OAgBtBM,EAAK6c,SAAW6P,KAGjB1sB,EAAKitB,KAAK,UAAU,aAAa3f,IAEhC,IAAKtN,EAAKqc,UAAY;AAGtB,MAAM8I,EAAenlB,EAAK8kB,cAAcxX;AACxC,GAAI6X,EAAa,CAChB,MAAMC,EAAMtU,EAAUqU;AACtBnlB,EAAKgkB,SAAS1W,EAAY,EAAI8X,EAAM,EAAGA,GACvCplB,EAAK+jB,gBACL9T,EAAckV,EAA2B,oBAIzCnlB,EAAKgkB,SAAShkB,EAAK6c,SAAWvP,SDzDjC8M,GAAUmT,OAAO,kBEYF,WACRvtB,MAAAA,EAAOf;AAEbe,EAAKwL,SAASmI,YAAa,EAE3B3T,EAAKitB,KAAK,SAAS,SAAQ,KAC1BjtB,EAAKub,WAAcvb,EAAKqb,QAExB7L,EAAYxP,EAAKmb,cAAe;AAE/B,MAAMqS,EAAMrf,EAAO;AACpBqf,EAAIzV,OAAO/X,EAAKmb,eAChBnb,EAAKob,SAASsN,aAAa8E,EAAKxtB,EAAKob,SAASxM;AAG9C,MAAM+G,EAAcxH,EAAO;AAC3BwH,EAAYA,YAAc3V,EAAKwL,SAASmK,aAAc,GACtD3V,EAAKqb,QAAQtD,OAAOpC,MAKrB3V,EAAKb,GAAG,cAAa,KAGpBa,EAAKmb,cAAc/D,iBAAiB,WAAWJ,IAEtCA,OAAAA,EAAI6L,SACNE,KR/CgB,GQqDrB,OALK/iB,EAAK+b,SACRhF,GAAeC,GAAI,GACnBhX,EAAKqgB,cAENrgB,EAAKoiB;AAEDW,KR/CgB,EQgDpB/iB,EAAKub,WAAWE,UAAY,EAGvBzb,OAAAA,EAAKkf,UAAUxI,KAAK1W,EAAKgX,MAGjChX,EAAKb,GAAG,QAAO,KACda,EAAKub,WAAWE,SAAWzb,EAAKgc,YAAc,EAAIhc,EAAKyb,YAKxDzb,EAAKb,GAAG,iBAAgB,KACvBa,EAAKmb,cAAckD;AAIpB,MAAMoP,EAAcztB,EAAKqf;AACzBrf,EAAKitB,KAAK,UAAU,UAAUjW,IACzBA,IAAAA,GAAOA,EAAI0W,eAAiB1tB,EAAKmb,cACrC,OAAOsS,EAAY/W,KAAK1W,MAGzBmX,GAASnX,EAAKmb,cAAc,QAAQ,IAAMnb,EAAKqf,WAG/Crf,EAAKitB,KAAK,SAAS,SAAQ,KAErBjtB,EAAK+b,QACV/b,EAAKub,WAAW8C,MAAM,CAACsP,eAAe,aF5EzCvT,GAAUmT,OAAO,uBGMF,WACVvtB,IAAAA,EAAOf,KACP2uB,EAAuB5tB,EAAKyjB;AAEhCxkB,KAAKguB,KAAK,UAAU,mBAAmBjW,KAElChX,EAAK+c,YAAYrd,QACbkuB,EAAqBlX,KAAK1W,EAAMgX,QHZ1CoD,GAAUmT,OAAO,iBIUF,SAAyBvQ,GAEvC,MAAM7Q,EAAU1G,OAAOgI,OAAO,CAC5B0Q,MAAY,UACZ0P,MAAY,SACZ1b,UAAY,SACZ4F,QAAY,GACViF;AAIJ,IAAIhd,EAASf;AAGb,GAAKkN,EAAQ4L,OAAb,CAII2O,IAAAA,EAAO,uCAAyCva,EAAQgG,UAAY,0BAA4B8D,EAAY9J,EAAQ0hB,OAAS,KAAO1hB,EAAQgS,MAAQ;AAExJne,EAAKitB,KAAK,QAAQ,kBAAiB,KAE9Ba,IAAAA,EAAmB9tB,EAAKwL,SAASsK,OAAO/H;AAE5C/N,EAAKwL,SAASsK,OAAO/H,KAAO,CAACpB,EAAgBmU,KAE5C,IAAI/S,EAAOI,EAAO2f,EAAiBpX,KAAK1W,EAAM2M,EAAMmU,IAEhDiN,EAAe5f,EAAOuY;AAoB1B,OAnBA3Y,EAAK0E,YAAYsb,GAEjB5W,GAAS4W,EAAa,aAAa/W,IAClCD,GAAeC,GAAI,MAGpBG,GAAS4W,EAAa,SAAS/W,IAG9BD,GAAeC,GAAI,GAEfhX,EAAKoc,UACJpc,EAAKqsB,aAAa,CAACte,GAAMiJ,KAE9BhX,EAAKgqB,WAAWjc,GAChB/N,EAAK0jB,gBAAe,GACpB1jB,EAAK2f,iBAGC5R,UJzDVqM,GAAUmT,OAAO,wBKSF,SAAyBvQ,GACjChd,MAAAA,EAAOf,KAEPkN,EAAU1G,OAAOgI,OAAO,CAC7BugB,KAAOrV,GACCA,EAAO3Y,EAAKwL,SAASiJ,aAE5BuI;AAEFhd,EAAKb,GAAG,eAAc,SAASsC,GAC9B,GAAKzB,EAAKqc,WAI8B,KAApCrc,EAAKmb,cAAc1Z,MAAMiM,OAAe,CAC3C,IAAIiL,EAAS3Y,EAAKmM,QAAQ1K;AACtBkX,GACH3Y,EAAKskB,gBAAgBnY,EAAQ6hB,KAAKtX,KAAK1W,EAAM2Y;"}