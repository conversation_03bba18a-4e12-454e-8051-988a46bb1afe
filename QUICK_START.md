# Быстрый старт - KPI Опросник

## Шаги для запуска:

### 1. Создание базы данных PostgreSQL

```sql
-- Подключитесь к PostgreSQL и выполните:
CREATE DATABASE kpi_survey;
```

### 2. Запуск скрипта базы данных

```bash
# В командной строке выполните:
psql -U postgres -d kpi_survey -f db.sql
```

Или скопируйте содержимое файла `db.sql` и выполните в pgAdmin или другом клиенте PostgreSQL.

### 3. Настройка подключения

Отредактируйте файл `config.py` и укажите ваши данные для подключения к PostgreSQL:

```python
DATABASE_NAME = 'kpi_survey'      # Имя вашей БД
DATABASE_HOST = 'localhost'      # Адрес сервера БД
DATABASE_PORT = '5432'           # Порт БД
DATABASE_USER = 'postgres'       # Имя пользователя БД
DATABASE_PASSWORD = 'ваш_пароль' # Пароль от БД
```

### 4. Установка зависимостей

```bash
pip install -r requirements.txt
```

### 5. Запуск приложения

```bash
python run.py
```

### 6. Открытие в браузере

Откройте браузер и перейдите по адресу: http://localhost:15007

## Готово!

Теперь вы можете:
- Вводить имя и фамилию участника
- Выбирать тип опроса (деканы, заведующие кафедрами, преподаватели)
- Заполнять соответствующие формы
- Просматривать результаты и статистику

## Возможные проблемы:

1. **Ошибка подключения к БД**: Проверьте настройки в `config.py`
2. **Порт занят**: Измените порт в `config.py` (параметр PORT)
3. **Модули не найдены**: Установите зависимости через `pip install -r requirements.txt`

## Поддержка

При возникновении проблем проверьте:
- Запущен ли сервер PostgreSQL
- Правильно ли указаны данные подключения в config.py
- Установлены ли все зависимости из requirements.txt
