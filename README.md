# KPI Опросник

Система оценки эффективности деятельности для университета TIIAME.

## Описание

Веб-приложение для проведения опросов KPI (Key Performance Indicators) среди:
- Деканов факультетов
- Заведующих кафедрами  
- Профессорско-преподавательского состава

## Функциональность

- Ввод имени и фамилии участника
- Выбор типа опроса
- Заполнение соответствующей формы опроса
- Автоматический подсчет баллов
- Сохранение результатов в базе данных
- Просмотр статистики

## Установка и настройка

### 1. Установка зависимостей

```bash
pip install flask psycopg2-binary tabulate
```

### 2. Настройка базы данных PostgreSQL

1. Создайте базу данных:
```sql
CREATE DATABASE kpi_survey;
```

2. Запустите скрипт создания таблиц:
```bash
psql -U postgres -d kpi_survey -f db.sql
```

### 3. Настройка подключения к БД

Отредактируйте файл `config.py` или установите переменные окружения:

```bash
export DB_NAME=kpi_survey
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=your_password
```

### 4. Запуск приложения

```bash
python run.py
```

Приложение будет доступно по адресу: http://localhost:15007

## Структура проекта

```
KPI/
├── db.sql              # Схема базы данных
├── config.py           # Конфигурация подключения к БД
├── connector.py        # Модуль для работы с БД
├── run.py              # Основное приложение Flask
├── templates/          # HTML шаблоны
│   ├── base.html       # Базовый шаблон
│   ├── dashboard.html  # Главная страница
│   ├── deans.html      # Опрос для деканов
│   ├── departments.html # Опрос для зав. кафедрами
│   ├── professors.html # Опрос для преподавателей
│   └── survey_result.html # Результаты опроса
└── static/             # Статические файлы (CSS, JS, изображения)
```

## Использование

1. Откройте главную страницу
2. Введите имя и фамилию
3. Выберите тип опроса
4. Заполните форму опроса
5. Нажмите "Завершить опрос"
6. Просмотрите результаты

## База данных

### Таблицы:

- `users` - Пользователи (участники опросов)
- `survey_types` - Типы опросов
- `surveys` - Заполненные опросы
- `survey_answers` - Ответы на вопросы

### Типы опросов:

1. **deans** - Факультет декани фаолияти самарадорлигини баҳолаш мезонлари (KPI)
2. **departments** - Кафедра мудири фаолияти самарадорлигини баҳолаш мезонлари (KPI)  
3. **professors** - Кафедра профессор-ўқитувчилари фаолиятини баҳолаш мезонлари (KPI)

## Конфигурация

### Переменные окружения:

- `DB_NAME` - Имя базы данных (по умолчанию: kpi_survey)
- `DB_HOST` - Хост БД (по умолчанию: localhost)
- `DB_PORT` - Порт БД (по умолчанию: 5432)
- `DB_USER` - Пользователь БД (по умолчанию: postgres)
- `DB_PASSWORD` - Пароль БД (по умолчанию: postgres)
- `SECRET_KEY` - Секретный ключ Flask
- `DEBUG` - Режим отладки (по умолчанию: True)
- `HOST` - Хост сервера (по умолчанию: 0.0.0.0)
- `PORT` - Порт сервера (по умолчанию: 15007)

## Разработка

Для разработки рекомендуется:

1. Включить режим отладки в `config.py`
2. Использовать виртуальное окружение Python
3. Регулярно создавать резервные копии БД

## Лицензия

Проект разработан для TIIAME (Ташкентский институт инженеров ирригации и механизации сельского хозяйства).
