{% extends 'base.html' %}

{% block title %}Административная панель - KPI{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Административная панель</h2>
                <div>
                    <a href="/" class="btn btn-outline-primary me-2">Главная</a>
                    <a href="/admin/logout" class="btn btn-outline-danger">Выйти</a>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Результаты опросов</h3>
                </div>
                <div class="card-body">
                    {% if surveys %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Участник</th>
                                    <th>Тип опроса</th>
                                    <th>Общий балл</th>
                                    <th>Дата прохождения</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for survey in surveys %}
                                <tr>
                                    <td>{{ survey.id }}</td>
                                    <td>
                                        {% if survey.user %}
                                            {{ survey.user.first_name }} {{ survey.user.last_name }}
                                        {% else %}
                                            Неизвестно
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if survey.survey_type %}
                                            <span class="badge bg-primary">{{ survey.survey_type.name }}</span>
                                        {% else %}
                                            Неизвестно
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ survey.total_score }}</span>
                                    </td>
                                    <td>
                                        {% if survey.completed_at %}
                                            {{ survey.completed_at.strftime('%d.%m.%Y %H:%M') }}
                                        {% else %}
                                            Неизвестно
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="/admin/survey_details/{{ survey.id }}" class="btn btn-sm btn-outline-primary">
                                            Подробно
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <div class="text-muted">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg mb-3" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"></path>
                                <path d="M12 9h.01"></path>
                                <path d="M11 12h1v4h1"></path>
                            </svg>
                            <h3>Опросы не найдены</h3>
                            <p>Пока никто не прошел опросы</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Статистика -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Всего опросов</div>
                                <div class="ms-auto lh-1">
                                    <div class="dropdown">
                                        <a class="dropdown-toggle text-muted" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Последние 7 дней</a>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3">{{ surveys|length }}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Уникальных участников</div>
                            </div>
                            <div class="h1 mb-3">{{ surveys|map(attribute='user_id')|unique|list|length }}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Средний балл</div>
                            </div>
                            <div class="h1 mb-3">
                                {% if surveys %}
                                    {{ "%.1f"|format((surveys|sum(attribute='total_score')) / (surveys|length)) }}
                                {% else %}
                                    0
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Сегодня</div>
                            </div>
                            <div class="h1 mb-3">
                                {% set today_surveys = surveys|selectattr('completed_at')|list %}
                                {{ today_surveys|length }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
