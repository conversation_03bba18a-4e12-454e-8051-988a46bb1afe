{"version": 3, "file": "input_autogrow.js", "sources": ["../../../src/utils.ts", "../../../src/plugins/input_autogrow/plugin.ts"], "sourcesContent": ["\nimport TomSelect from './tom-select';\nimport { TomLoadCallback } from './types/index';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n", "/**\n * Plugin: \"input_autogrow\" (Tom <PERSON>)\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\nimport { addEvent } from '../../utils';\n\nexport default function(this:TomSelect) {\n\tvar self\t\t\t\t\t= this;\n\n\tself.on('initialize',()=>{\n\n\n\t\tvar test_input\t\t\t\t= document.createElement('span');\n\t\tvar control\t\t\t\t\t= self.control_input;\n\t\ttest_input.style.cssText\t= 'position:absolute; top:-99999px; left:-99999px; width:auto; padding:0; white-space:pre; ';\n\n\t\tself.wrapper.appendChild(test_input);\n\n\n\t\tvar transfer_styles\t\t\t= [ 'letterSpacing', 'fontSize', 'fontFamily', 'fontWeight', 'textTransform' ];\n\n\t\tfor( const style_name of transfer_styles ){\n\t\t\t// @ts-ignore TS7015 https://stackoverflow.com/a/50506154/697576\n\t\t\ttest_input.style[style_name] = control.style[style_name];\n\t\t}\n\n\n\t\t/**\n\t\t * Set the control width\n\t\t *\n\t\t */\n\t\tvar resize = ()=>{\n\t\t\ttest_input.textContent\t= control.value;\n\t\t\tcontrol.style.width\t\t= test_input.clientWidth+'px';\n\t\t};\n\n\t\tresize();\n\t\tself.on('update item_add item_remove',resize);\n\t\taddEvent(control,'input', resize );\n\t\taddEvent(control,'keyup', resize );\n\t\taddEvent(control,'blur', resize );\n\t\taddEvent(control,'update', resize );\n\t});\n\n};\n"], "names": ["addEvent", "target", "type", "callback", "options", "addEventListener", "self", "on", "test_input", "document", "createElement", "control", "control_input", "style", "cssText", "wrapper", "append<PERSON><PERSON><PERSON>", "transfer_styles", "style_name", "resize", "textContent", "value", "width", "clientWidth"], "mappings": ";;;;;;;;;;;CAKA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CA6GA;CACA;CACA;CACA;;CACO,MAAMA,QAAQ,GAAG,CAACC,MAAD,EAAqBC,IAArB,EAAkCC,QAAlC,EAA+EC,OAA/E,KAAwG;CAC/HH,EAAAA,MAAM,CAACI,gBAAP,CAAwBH,IAAxB,EAA6BC,QAA7B,EAAsCC,OAAtC,CAAA,CAAA;CACA,CAFM;;CCnIP;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAKe,eAAyB,IAAA;CACvC,EAAIE,IAAAA,IAAI,GAAO,IAAf,CAAA;CAEAA,EAAAA,IAAI,CAACC,EAAL,CAAQ,YAAR,EAAqB,MAAI;CAGxB,IAAA,IAAIC,UAAU,GAAMC,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAApB,CAAA;CACA,IAAA,IAAIC,OAAO,GAAOL,IAAI,CAACM,aAAvB,CAAA;CACAJ,IAAAA,UAAU,CAACK,KAAX,CAAiBC,OAAjB,GAA2B,0FAA3B,CAAA;CAEAR,IAAAA,IAAI,CAACS,OAAL,CAAaC,WAAb,CAAyBR,UAAzB,CAAA,CAAA;CAGA,IAAA,IAAIS,eAAe,GAAK,CAAE,eAAF,EAAmB,UAAnB,EAA+B,YAA/B,EAA6C,YAA7C,EAA2D,eAA3D,CAAxB,CAAA;;CAEA,IAAA,KAAK,MAAMC,UAAX,IAAyBD,eAAzB,EAA0C;CACzC;CACAT,MAAAA,UAAU,CAACK,KAAX,CAAiBK,UAAjB,CAA+BP,GAAAA,OAAO,CAACE,KAAR,CAAcK,UAAd,CAA/B,CAAA;CACA,KAAA;CAGD;CACF;CACA;CACA;;;CACE,IAAIC,IAAAA,MAAM,GAAG,MAAI;CAChBX,MAAAA,UAAU,CAACY,WAAX,GAAyBT,OAAO,CAACU,KAAjC,CAAA;CACAV,MAAAA,OAAO,CAACE,KAAR,CAAcS,KAAd,GAAuBd,UAAU,CAACe,WAAX,GAAuB,IAA9C,CAAA;CACA,KAHD,CAAA;;CAKAJ,IAAAA,MAAM,EAAA,CAAA;CACNb,IAAAA,IAAI,CAACC,EAAL,CAAQ,6BAAR,EAAsCY,MAAtC,CAAA,CAAA;CACAnB,IAAAA,QAAQ,CAACW,OAAD,EAAS,OAAT,EAAkBQ,MAAlB,CAAR,CAAA;CACAnB,IAAAA,QAAQ,CAACW,OAAD,EAAS,OAAT,EAAkBQ,MAAlB,CAAR,CAAA;CACAnB,IAAAA,QAAQ,CAACW,OAAD,EAAS,MAAT,EAAiBQ,MAAjB,CAAR,CAAA;CACAnB,IAAAA,QAAQ,CAACW,OAAD,EAAS,QAAT,EAAmBQ,MAAnB,CAAR,CAAA;CACA,GAjCD,CAAA,CAAA;CAmCA;;;;;;;;"}