"use strict";var PipsMode,PipsType;function isValidFormatter(t){return isValidPartialFormatter(t)&&"function"==typeof t.from}function isValidPartialFormatter(t){return"object"==typeof t&&"function"==typeof t.to}function removeElement(t){t.parentElement.removeChild(t)}function isSet(t){return null!=t}function preventDefault(t){t.preventDefault()}function unique(t){return t.filter(function(t){return!this[t]&&(this[t]=!0)},{})}function closest(t,e){return Math.round(t/e)*e}function offset(t,e){var r=t.getBoundingClientRect(),n=t.ownerDocument,t=n.documentElement,n=getPageOffset(n);return/webkit.*Chrome.*Mobile/i.test(navigator.userAgent)&&(n.x=0),e?r.top+n.y-t.clientTop:r.left+n.x-t.clientLeft}function isNumeric(t){return"number"==typeof t&&!isNaN(t)&&isFinite(t)}function addClassFor(t,e,r){0<r&&(addClass(t,e),setTimeout(function(){removeClass(t,e)},r))}function limit(t){return Math.max(Math.min(t,100),0)}function asArray(t){return Array.isArray(t)?t:[t]}function countDecimals(t){t=(t=String(t)).split(".");return 1<t.length?t[1].length:0}function addClass(t,e){t.classList&&!/\s/.test(e)?t.classList.add(e):t.className+=" "+e}function removeClass(t,e){t.classList&&!/\s/.test(e)?t.classList.remove(e):t.className=t.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")}function hasClass(t,e){return t.classList?t.classList.contains(e):new RegExp("\\b"+e+"\\b").test(t.className)}function getPageOffset(t){var e=void 0!==window.pageXOffset,r="CSS1Compat"===(t.compatMode||"");return{x:e?window.pageXOffset:(r?t.documentElement:t.body).scrollLeft,y:e?window.pageYOffset:(r?t.documentElement:t.body).scrollTop}}function getActions(){return window.navigator.pointerEnabled?{start:"pointerdown",move:"pointermove",end:"pointerup"}:window.navigator.msPointerEnabled?{start:"MSPointerDown",move:"MSPointerMove",end:"MSPointerUp"}:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"}}function getSupportsPassive(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("test",null,e)}catch(t){}return t}function getSupportsTouchActionNone(){return window.CSS&&CSS.supports&&CSS.supports("touch-action","none")}function subRangeRatio(t,e){return 100/(e-t)}function fromPercentage(t,e,r){return 100*e/(t[r+1]-t[r])}function toPercentage(t,e){return fromPercentage(t,t[0]<0?e+Math.abs(t[0]):e-t[0],0)}function isPercentage(t,e){return e*(t[1]-t[0])/100+t[0]}function getJ(t,e){for(var r=1;t>=e[r];)r+=1;return r}function toStepping(t,e,r){if(r>=t.slice(-1)[0])return 100;var n=getJ(r,t),i=t[n-1],s=t[n],t=e[n-1],n=e[n];return t+toPercentage([i,s],r)/subRangeRatio(t,n)}function fromStepping(t,e,r){if(100<=r)return t.slice(-1)[0];var n=getJ(r,e),i=t[n-1],s=t[n],t=e[n-1];return isPercentage([i,s],(r-t)*subRangeRatio(t,e[n]))}function getStep(t,e,r,n){if(100===n)return n;var i=getJ(n,t),s=t[i-1],o=t[i];return r?(o-s)/2<n-s?o:s:e[i-1]?t[i-1]+closest(n-t[i-1],e[i-1]):n}!function(t){t.Range="range",t.Steps="steps",t.Positions="positions",t.Count="count",t.Values="values"}(PipsMode=PipsMode||{}),function(t){t[t.None=-1]="None",t[t.NoValue=0]="NoValue",t[t.LargeValue=1]="LargeValue",t[t.SmallValue=2]="SmallValue"}(PipsType=PipsType||{});var Spectrum=function(){function t(e,t,r){var n;this.xPct=[],this.xVal=[],this.xSteps=[],this.xNumSteps=[],this.xHighestCompleteStep=[],this.xSteps=[r||!1],this.xNumSteps=[!1],this.snap=t;var i=[];for(Object.keys(e).forEach(function(t){i.push([asArray(e[t]),t])}),i.sort(function(t,e){return t[0][0]-e[0][0]}),n=0;n<i.length;n++)this.handleEntryPoint(i[n][1],i[n][0]);for(this.xNumSteps=this.xSteps.slice(0),n=0;n<this.xNumSteps.length;n++)this.handleStepPoint(n,this.xNumSteps[n])}return t.prototype.getDistance=function(t){for(var e=[],r=0;r<this.xNumSteps.length-1;r++)e[r]=fromPercentage(this.xVal,t,r);return e},t.prototype.getAbsoluteDistance=function(t,e,r){var n=0;if(t<this.xPct[this.xPct.length-1])for(;t>this.xPct[n+1];)n++;else t===this.xPct[this.xPct.length-1]&&(n=this.xPct.length-2);r||t!==this.xPct[n+1]||n++;for(var i,s=1,o=(e=null===e?[]:e)[n],a=0,l=0,u=0,c=r?(t-this.xPct[n])/(this.xPct[n+1]-this.xPct[n]):(this.xPct[n+1]-t)/(this.xPct[n+1]-this.xPct[n]);0<o;)i=this.xPct[n+1+u]-this.xPct[n+u],100<e[n+u]*s+100-100*c?(a=i*c,s=(o-100*c)/e[n+u],c=1):(a=e[n+u]*i/100*s,s=0),r?(l-=a,1<=this.xPct.length+u&&u--):(l+=a,1<=this.xPct.length-u&&u++),o=e[n+u]*s;return t+l},t.prototype.toStepping=function(t){return t=toStepping(this.xVal,this.xPct,t)},t.prototype.fromStepping=function(t){return fromStepping(this.xVal,this.xPct,t)},t.prototype.getStep=function(t){return t=getStep(this.xPct,this.xSteps,this.snap,t)},t.prototype.getDefaultStep=function(t,e,r){var n=getJ(t,this.xPct);return(100===t||e&&t===this.xPct[n-1])&&(n=Math.max(n-1,1)),(this.xVal[n]-this.xVal[n-1])/r},t.prototype.getNearbySteps=function(t){t=getJ(t,this.xPct);return{stepBefore:{startValue:this.xVal[t-2],step:this.xNumSteps[t-2],highestStep:this.xHighestCompleteStep[t-2]},thisStep:{startValue:this.xVal[t-1],step:this.xNumSteps[t-1],highestStep:this.xHighestCompleteStep[t-1]},stepAfter:{startValue:this.xVal[t],step:this.xNumSteps[t],highestStep:this.xHighestCompleteStep[t]}}},t.prototype.countStepDecimals=function(){var t=this.xNumSteps.map(countDecimals);return Math.max.apply(null,t)},t.prototype.hasNoSize=function(){return this.xVal[0]===this.xVal[this.xVal.length-1]},t.prototype.convert=function(t){return this.getStep(this.toStepping(t))},t.prototype.handleEntryPoint=function(t,e){t="min"===t?0:"max"===t?100:parseFloat(t);if(!isNumeric(t)||!isNumeric(e[0]))throw new Error("noUiSlider: 'range' value isn't numeric.");this.xPct.push(t),this.xVal.push(e[0]);e=Number(e[1]);t?this.xSteps.push(!isNaN(e)&&e):isNaN(e)||(this.xSteps[0]=e),this.xHighestCompleteStep.push(0)},t.prototype.handleStepPoint=function(t,e){e&&(this.xVal[t]!==this.xVal[t+1]?(this.xSteps[t]=fromPercentage([this.xVal[t],this.xVal[t+1]],e,0)/subRangeRatio(this.xPct[t],this.xPct[t+1]),e=(this.xVal[t+1]-this.xVal[t])/this.xNumSteps[t],e=Math.ceil(Number(e.toFixed(3))-1),e=this.xVal[t]+this.xNumSteps[t]*e,this.xHighestCompleteStep[t]=e):this.xSteps[t]=this.xHighestCompleteStep[t]=this.xVal[t])},t}(),defaultFormatter={to:function(t){return void 0===t?"":t.toFixed(2)},from:Number},cssClasses={target:"target",base:"base",origin:"origin",handle:"handle",handleLower:"handle-lower",handleUpper:"handle-upper",touchArea:"touch-area",horizontal:"horizontal",vertical:"vertical",background:"background",connect:"connect",connects:"connects",ltr:"ltr",rtl:"rtl",textDirectionLtr:"txt-dir-ltr",textDirectionRtl:"txt-dir-rtl",draggable:"draggable",drag:"state-drag",tap:"state-tap",active:"active",tooltip:"tooltip",pips:"pips",pipsHorizontal:"pips-horizontal",pipsVertical:"pips-vertical",marker:"marker",markerHorizontal:"marker-horizontal",markerVertical:"marker-vertical",markerNormal:"marker-normal",markerLarge:"marker-large",markerSub:"marker-sub",value:"value",valueHorizontal:"value-horizontal",valueVertical:"value-vertical",valueNormal:"value-normal",valueLarge:"value-large",valueSub:"value-sub"},INTERNAL_EVENT_NS={tooltips:".__tooltips",aria:".__aria"};function testStep(t,e){if(!isNumeric(e))throw new Error("noUiSlider: 'step' is not numeric.");t.singleStep=e}function testKeyboardPageMultiplier(t,e){if(!isNumeric(e))throw new Error("noUiSlider: 'keyboardPageMultiplier' is not numeric.");t.keyboardPageMultiplier=e}function testKeyboardMultiplier(t,e){if(!isNumeric(e))throw new Error("noUiSlider: 'keyboardMultiplier' is not numeric.");t.keyboardMultiplier=e}function testKeyboardDefaultStep(t,e){if(!isNumeric(e))throw new Error("noUiSlider: 'keyboardDefaultStep' is not numeric.");t.keyboardDefaultStep=e}function testRange(t,e){if("object"!=typeof e||Array.isArray(e))throw new Error("noUiSlider: 'range' is not an object.");if(void 0===e.min||void 0===e.max)throw new Error("noUiSlider: Missing 'min' or 'max' in 'range'.");t.spectrum=new Spectrum(e,t.snap||!1,t.singleStep)}function testStart(t,e){if(e=asArray(e),!Array.isArray(e)||!e.length)throw new Error("noUiSlider: 'start' option is incorrect.");t.handles=e.length,t.start=e}function testSnap(t,e){if("boolean"!=typeof e)throw new Error("noUiSlider: 'snap' option must be a boolean.");t.snap=e}function testAnimate(t,e){if("boolean"!=typeof e)throw new Error("noUiSlider: 'animate' option must be a boolean.");t.animate=e}function testAnimationDuration(t,e){if("number"!=typeof e)throw new Error("noUiSlider: 'animationDuration' option must be a number.");t.animationDuration=e}function testConnect(t,e){var r,n=[!1];if("lower"===e?e=[!0,!1]:"upper"===e&&(e=[!1,!0]),!0===e||!1===e){for(r=1;r<t.handles;r++)n.push(e);n.push(!1)}else{if(!Array.isArray(e)||!e.length||e.length!==t.handles+1)throw new Error("noUiSlider: 'connect' option doesn't match handle count.");n=e}t.connect=n}function testOrientation(t,e){switch(e){case"horizontal":t.ort=0;break;case"vertical":t.ort=1;break;default:throw new Error("noUiSlider: 'orientation' option is invalid.")}}function testMargin(t,e){if(!isNumeric(e))throw new Error("noUiSlider: 'margin' option must be numeric.");0!==e&&(t.margin=t.spectrum.getDistance(e))}function testLimit(t,e){if(!isNumeric(e))throw new Error("noUiSlider: 'limit' option must be numeric.");if(t.limit=t.spectrum.getDistance(e),!t.limit||t.handles<2)throw new Error("noUiSlider: 'limit' option is only supported on linear sliders with 2 or more handles.")}function testPadding(t,e){var r;if(!isNumeric(e)&&!Array.isArray(e))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(Array.isArray(e)&&2!==e.length&&!isNumeric(e[0])&&!isNumeric(e[1]))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(0!==e){for(Array.isArray(e)||(e=[e,e]),t.padding=[t.spectrum.getDistance(e[0]),t.spectrum.getDistance(e[1])],r=0;r<t.spectrum.xNumSteps.length-1;r++)if(t.padding[0][r]<0||t.padding[1][r]<0)throw new Error("noUiSlider: 'padding' option must be a positive number(s).");var n=e[0]+e[1],e=t.spectrum.xVal[0];if(1<n/(t.spectrum.xVal[t.spectrum.xVal.length-1]-e))throw new Error("noUiSlider: 'padding' option must not exceed 100% of the range.")}}function testDirection(t,e){switch(e){case"ltr":t.dir=0;break;case"rtl":t.dir=1;break;default:throw new Error("noUiSlider: 'direction' option was not recognized.")}}function testBehaviour(t,e){if("string"!=typeof e)throw new Error("noUiSlider: 'behaviour' must be a string containing options.");var r=0<=e.indexOf("tap"),n=0<=e.indexOf("drag"),i=0<=e.indexOf("fixed"),s=0<=e.indexOf("snap"),o=0<=e.indexOf("hover"),a=0<=e.indexOf("unconstrained"),l=0<=e.indexOf("drag-all"),e=0<=e.indexOf("smooth-steps");if(i){if(2!==t.handles)throw new Error("noUiSlider: 'fixed' behaviour must be used with 2 handles");testMargin(t,t.start[1]-t.start[0])}if(a&&(t.margin||t.limit))throw new Error("noUiSlider: 'unconstrained' behaviour cannot be used with margin or limit");t.events={tap:r||s,drag:n,dragAll:l,smoothSteps:e,fixed:i,snap:s,hover:o,unconstrained:a}}function testTooltips(t,e){if(!1!==e)if(!0===e||isValidPartialFormatter(e)){t.tooltips=[];for(var r=0;r<t.handles;r++)t.tooltips.push(e)}else{if((e=asArray(e)).length!==t.handles)throw new Error("noUiSlider: must pass a formatter for all handles.");e.forEach(function(t){if("boolean"!=typeof t&&!isValidPartialFormatter(t))throw new Error("noUiSlider: 'tooltips' must be passed a formatter or 'false'.")}),t.tooltips=e}}function testHandleAttributes(t,e){if(e.length!==t.handles)throw new Error("noUiSlider: must pass a attributes for all handles.");t.handleAttributes=e}function testAriaFormat(t,e){if(!isValidPartialFormatter(e))throw new Error("noUiSlider: 'ariaFormat' requires 'to' method.");t.ariaFormat=e}function testFormat(t,e){if(!isValidFormatter(e))throw new Error("noUiSlider: 'format' requires 'to' and 'from' methods.");t.format=e}function testKeyboardSupport(t,e){if("boolean"!=typeof e)throw new Error("noUiSlider: 'keyboardSupport' option must be a boolean.");t.keyboardSupport=e}function testDocumentElement(t,e){t.documentElement=e}function testCssPrefix(t,e){if("string"!=typeof e&&!1!==e)throw new Error("noUiSlider: 'cssPrefix' must be a string or `false`.");t.cssPrefix=e}function testCssClasses(e,r){if("object"!=typeof r)throw new Error("noUiSlider: 'cssClasses' must be an object.");"string"==typeof e.cssPrefix?(e.cssClasses={},Object.keys(r).forEach(function(t){e.cssClasses[t]=e.cssPrefix+r[t]})):e.cssClasses=r}function testOptions(e){var r={margin:null,limit:null,padding:null,animate:!0,animationDuration:300,ariaFormat:defaultFormatter,format:defaultFormatter},n={step:{r:!1,t:testStep},keyboardPageMultiplier:{r:!1,t:testKeyboardPageMultiplier},keyboardMultiplier:{r:!1,t:testKeyboardMultiplier},keyboardDefaultStep:{r:!1,t:testKeyboardDefaultStep},start:{r:!0,t:testStart},connect:{r:!0,t:testConnect},direction:{r:!0,t:testDirection},snap:{r:!1,t:testSnap},animate:{r:!1,t:testAnimate},animationDuration:{r:!1,t:testAnimationDuration},range:{r:!0,t:testRange},orientation:{r:!1,t:testOrientation},margin:{r:!1,t:testMargin},limit:{r:!1,t:testLimit},padding:{r:!1,t:testPadding},behaviour:{r:!0,t:testBehaviour},ariaFormat:{r:!1,t:testAriaFormat},format:{r:!1,t:testFormat},tooltips:{r:!1,t:testTooltips},keyboardSupport:{r:!0,t:testKeyboardSupport},documentElement:{r:!1,t:testDocumentElement},cssPrefix:{r:!0,t:testCssPrefix},cssClasses:{r:!0,t:testCssClasses},handleAttributes:{r:!1,t:testHandleAttributes}},i={connect:!1,direction:"ltr",behaviour:"tap",orientation:"horizontal",keyboardSupport:!0,cssPrefix:"noUi-",cssClasses:cssClasses,keyboardPageMultiplier:5,keyboardMultiplier:1,keyboardDefaultStep:10};e.format&&!e.ariaFormat&&(e.ariaFormat=e.format),Object.keys(n).forEach(function(t){if(isSet(e[t])||void 0!==i[t])n[t].t(r,(isSet(e[t])?e:i)[t]);else if(n[t].r)throw new Error("noUiSlider: '"+t+"' is required.")}),r.pips=e.pips;var t=document.createElement("div"),s=void 0!==t.style.msTransform,t=void 0!==t.style.transform;r.transformRule=t?"transform":s?"msTransform":"webkitTransform";return r.style=[["left","top"],["right","bottom"]][r.dir][r.ort],r}function scope(t,d,s){var r,l,a,n,i,u,c=getActions(),p=getSupportsTouchActionNone()&&getSupportsPassive(),f=t,S=d.spectrum,h=[],m=[],g=[],o=0,v={},b=t.ownerDocument,x=d.documentElement||b.documentElement,y=b.body,E="rtl"===b.dir||1===d.ort?0:100;function N(t,e){var r=b.createElement("div");return e&&addClass(r,e),t.appendChild(r),r}function P(t,e){var r,t=N(t,d.cssClasses.origin),n=N(t,d.cssClasses.handle);return N(n,d.cssClasses.touchArea),n.setAttribute("data-handle",String(e)),d.keyboardSupport&&(n.setAttribute("tabindex","0"),n.addEventListener("keydown",function(t){return function(t,e){if(C()||A(e))return!1;var r=["Left","Right"],n=["Down","Up"],i=["PageDown","PageUp"],s=["Home","End"];d.dir&&!d.ort?r.reverse():d.ort&&!d.dir&&(n.reverse(),i.reverse());var o=t.key.replace("Arrow",""),a=o===i[0],l=o===i[1],i=o===n[0]||o===r[0]||a,n=o===n[1]||o===r[1]||l,r=o===s[0],s=o===s[1];if(!(i||n||r||s))return!0;if(t.preventDefault(),n||i){var u=i?0:1,u=nt(e)[u];if(null===u)return!1;!1===u&&(u=S.getDefaultStep(m[e],i,d.keyboardDefaultStep)),u*=l||a?d.keyboardPageMultiplier:d.keyboardMultiplier,u=Math.max(u,1e-7),u*=i?-1:1,u=h[e]+u}else u=s?d.spectrum.xVal[d.spectrum.xVal.length-1]:d.spectrum.xVal[0];return Q(e,S.toStepping(u),!0,!0),J("slide",e),J("update",e),J("change",e),J("set",e),!1}(t,e)})),void 0!==d.handleAttributes&&(r=d.handleAttributes[e],Object.keys(r).forEach(function(t){n.setAttribute(t,r[t])})),n.setAttribute("role","slider"),n.setAttribute("aria-orientation",d.ort?"vertical":"horizontal"),0===e?addClass(n,d.cssClasses.handleLower):e===d.handles-1&&addClass(n,d.cssClasses.handleUpper),t.handle=n,t}function w(t,e){return!!e&&N(t,d.cssClasses.connect)}function e(t,e){return!(!d.tooltips||!d.tooltips[e])&&N(t.firstChild,d.cssClasses.tooltip)}function C(){return f.hasAttribute("disabled")}function A(t){return l[t].hasAttribute("disabled")}function V(){i&&(q("update"+INTERNAL_EVENT_NS.tooltips),i.forEach(function(t){t&&removeElement(t)}),i=null)}function k(){V(),i=l.map(e),K("update"+INTERNAL_EVENT_NS.tooltips,function(t,e,r){i&&d.tooltips&&!1!==i[e]&&(t=t[e],!0!==d.tooltips[e]&&(t=d.tooltips[e].to(r[e])),i[e].innerHTML=t)})}function M(t,e){return t.map(function(t){return S.fromStepping(e?S.getStep(t):t)})}function D(f){var h=function(t){if(t.mode===PipsMode.Range||t.mode===PipsMode.Steps)return S.xVal;if(t.mode!==PipsMode.Count)return t.mode===PipsMode.Positions?M(t.values,t.stepped):t.mode===PipsMode.Values?t.stepped?t.values.map(function(t){return S.fromStepping(S.getStep(S.toStepping(t)))}):t.values:[];if(t.values<2)throw new Error("noUiSlider: 'values' (>= 2) required for mode 'count'.");for(var e=t.values-1,r=100/e,n=[];e--;)n[e]=e*r;return n.push(100),M(n,t.stepped)}(f),m={},t=S.xVal[0],e=S.xVal[S.xVal.length-1],g=!1,v=!1,b=0;return(h=unique(h.slice().sort(function(t,e){return t-e})))[0]!==t&&(h.unshift(t),g=!0),h[h.length-1]!==e&&(h.push(e),v=!0),h.forEach(function(t,e){var r,n,i,s,o,a,l,u,t=t,c=h[e+1],p=f.mode===PipsMode.Steps,d=(d=p?S.xNumSteps[e]:d)||c-t;for(void 0===c&&(c=t),d=Math.max(d,1e-7),r=t;r<=c;r=Number((r+d).toFixed(7))){for(a=(s=(i=S.toStepping(r))-b)/(f.density||1),u=s/(l=Math.round(a)),n=1;n<=l;n+=1)m[(o=b+n*u).toFixed(5)]=[S.fromStepping(o),0];a=-1<h.indexOf(r)?PipsType.LargeValue:p?PipsType.SmallValue:PipsType.NoValue,!e&&g&&r!==c&&(a=0),r===c&&v||(m[i.toFixed(5)]=[r,a]),b=i}}),m}function T(i,s,o){var t,a=b.createElement("div"),n=((t={})[PipsType.None]="",t[PipsType.NoValue]=d.cssClasses.valueNormal,t[PipsType.LargeValue]=d.cssClasses.valueLarge,t[PipsType.SmallValue]=d.cssClasses.valueSub,t),l=((t={})[PipsType.None]="",t[PipsType.NoValue]=d.cssClasses.markerNormal,t[PipsType.LargeValue]=d.cssClasses.markerLarge,t[PipsType.SmallValue]=d.cssClasses.markerSub,t),u=[d.cssClasses.valueHorizontal,d.cssClasses.valueVertical],c=[d.cssClasses.markerHorizontal,d.cssClasses.markerVertical];function p(t,e){var r=e===d.cssClasses.value;return e+" "+(r?u:c)[d.ort]+" "+(r?n:l)[t]}return addClass(a,d.cssClasses.pips),addClass(a,0===d.ort?d.cssClasses.pipsHorizontal:d.cssClasses.pipsVertical),Object.keys(i).forEach(function(t){var e,r,n;r=i[e=t][0],n=i[t][1],(n=s?s(r,n):n)!==PipsType.None&&((t=N(a,!1)).className=p(n,d.cssClasses.marker),t.style[d.style]=e+"%",n>PipsType.NoValue&&((t=N(a,!1)).className=p(n,d.cssClasses.value),t.setAttribute("data-value",String(r)),t.style[d.style]=e+"%",t.innerHTML=String(o.to(r))))}),a}function U(){n&&(removeElement(n),n=null)}function L(t){U();var e=D(t),r=t.filter,t=t.format||{to:function(t){return String(Math.round(t))}};return n=f.appendChild(T(e,r,t))}function O(){var t=r.getBoundingClientRect(),e="offset"+["Width","Height"][d.ort];return 0===d.ort?t.width||r[e]:t.height||r[e]}function F(e,r,n,i){function s(t){return!!(t=function(e,t,r){var n=0===e.type.indexOf("touch"),i=0===e.type.indexOf("mouse"),s=0===e.type.indexOf("pointer"),o=0,a=0;0===e.type.indexOf("MSPointer")&&(s=!0);if("mousedown"===e.type&&!e.buttons&&!e.touches)return!1;if(n){var l=function(t){t=t.target;return t===r||r.contains(t)||e.composed&&e.composedPath().shift()===r};if("touchstart"===e.type){n=Array.prototype.filter.call(e.touches,l);if(1<n.length)return!1;o=n[0].pageX,a=n[0].pageY}else{l=Array.prototype.find.call(e.changedTouches,l);if(!l)return!1;o=l.pageX,a=l.pageY}}t=t||getPageOffset(b),(i||s)&&(o=e.clientX+t.x,a=e.clientY+t.y);return e.pageOffset=t,e.points=[o,a],e.cursor=i||s,e}(t,i.pageOffset,i.target||r))&&(!(C()&&!i.doNotReject)&&(!(hasClass(f,d.cssClasses.tap)&&!i.doNotReject)&&(!(e===c.start&&void 0!==t.buttons&&1<t.buttons)&&((!i.hover||!t.buttons)&&(p||t.preventDefault(),t.calcPoint=t.points[d.ort],void n(t,i))))))}var o=[];return e.split(" ").forEach(function(t){r.addEventListener(t,s,!!p&&{passive:!0}),o.push([t,s])}),o}function R(t){t=limit(t=100*(t-offset(r,d.ort))/O());return d.dir?100-t:t}function _(t,e){"mouseout"===t.type&&"HTML"===t.target.nodeName&&null===t.relatedTarget&&H(t,e)}function z(t,e){if(-1===navigator.appVersion.indexOf("MSIE 9")&&0===t.buttons&&0!==e.buttonsProperty)return H(t,e);t=(d.dir?-1:1)*(t.calcPoint-e.startCalcPoint);W(0<t,100*t/e.baseSize,e.locations,e.handleNumbers,e.connect)}function H(t,e){e.handle&&(removeClass(e.handle,d.cssClasses.active),--o),e.listeners.forEach(function(t){x.removeEventListener(t[0],t[1])}),0===o&&(removeClass(f,d.cssClasses.drag),G(),t.cursor&&(y.style.cursor="",y.removeEventListener("selectstart",preventDefault))),d.events.smoothSteps&&(e.handleNumbers.forEach(function(t){Q(t,m[t],!0,!0,!1,!1)}),e.handleNumbers.forEach(function(t){J("update",t)})),e.handleNumbers.forEach(function(t){J("change",t),J("set",t),J("end",t)})}function j(t,e){var r,n,i,s;e.handleNumbers.some(A)||(1===e.handleNumbers.length&&(s=l[e.handleNumbers[0]].children[0],o+=1,addClass(s,d.cssClasses.active)),t.stopPropagation(),n=F(c.move,x,z,{target:t.target,handle:s,connect:e.connect,listeners:r=[],startCalcPoint:t.calcPoint,baseSize:O(),pageOffset:t.pageOffset,handleNumbers:e.handleNumbers,buttonsProperty:t.buttons,locations:m.slice()}),i=F(c.end,x,H,{target:t.target,handle:s,listeners:r,doNotReject:!0,handleNumbers:e.handleNumbers}),s=F("mouseout",x,_,{target:t.target,handle:s,listeners:r,doNotReject:!0,handleNumbers:e.handleNumbers}),r.push.apply(r,n.concat(i,s)),t.cursor&&(y.style.cursor=getComputedStyle(t.target).cursor,1<l.length&&addClass(f,d.cssClasses.drag),y.addEventListener("selectstart",preventDefault,!1)),e.handleNumbers.forEach(function(t){J("start",t)}))}function I(t){t.stopPropagation();var i,s,o,e=R(t.calcPoint),r=(i=e,o=!(s=100),l.forEach(function(t,e){var r,n;A(e)||(r=m[e],((n=Math.abs(r-i))<s||n<=s&&r<i||100===n&&100===s)&&(o=e,s=n))}),o);!1!==r&&(d.events.snap||addClassFor(f,d.cssClasses.tap,d.animationDuration),Q(r,e,!0,!0),G(),J("slide",r,!0),J("update",r,!0),d.events.snap?j(t,{handleNumbers:[r]}):(J("change",r,!0),J("set",r,!0)))}function B(t){var t=R(t.calcPoint),t=S.getStep(t),e=S.fromStepping(t);Object.keys(v).forEach(function(t){"hover"===t.split(".")[0]&&v[t].forEach(function(t){t.call(it,e)})})}function K(t,e){v[t]=v[t]||[],v[t].push(e),"update"===t.split(".")[0]&&l.forEach(function(t,e){J("update",e)})}function q(t){var n=t&&t.split(".")[0],i=n?t.substring(n.length):t;Object.keys(v).forEach(function(t){var e=t.split(".")[0],r=t.substring(e.length);n&&n!==e||i&&i!==r||((e=r)!==INTERNAL_EVENT_NS.aria&&e!==INTERNAL_EVENT_NS.tooltips||i===r)&&delete v[t]})}function J(r,n,i){Object.keys(v).forEach(function(t){var e=t.split(".")[0];r===e&&v[t].forEach(function(t){t.call(it,h.map(d.format.to),n,h.slice(),i||!1,m.slice(),it)})})}function X(t,e,r,n,i,s,o){var a;return 1<l.length&&!d.events.unconstrained&&(n&&0<e&&(a=S.getAbsoluteDistance(t[e-1],d.margin,!1),r=Math.max(r,a)),i&&e<l.length-1&&(a=S.getAbsoluteDistance(t[e+1],d.margin,!0),r=Math.min(r,a))),1<l.length&&d.limit&&(n&&0<e&&(a=S.getAbsoluteDistance(t[e-1],d.limit,!1),r=Math.min(r,a)),i&&e<l.length-1&&(a=S.getAbsoluteDistance(t[e+1],d.limit,!0),r=Math.max(r,a))),d.padding&&(0===e&&(a=S.getAbsoluteDistance(0,d.padding[0],!1),r=Math.max(r,a)),e===l.length-1&&(a=S.getAbsoluteDistance(100,d.padding[1],!0),r=Math.min(r,a))),!((r=limit(r=!o?S.getStep(r):r))===t[e]&&!s)&&r}function Y(t,e){var r=d.ort;return(r?e:t)+", "+(r?t:e)}function W(t,r,n,e,i){var s=n.slice(),o=e[0],a=d.events.smoothSteps,l=[!t,t],u=[t,!t];e=e.slice(),t&&e.reverse(),1<e.length?e.forEach(function(t,e){e=X(s,t,s[t]+r,l[e],u[e],!1,a);!1===e?r=0:(r=e-s[t],s[t]=e)}):l=u=[!0];var c=!1;e.forEach(function(t,e){c=Q(t,n[t]+r,l[e],u[e],!1,a)||c}),c&&(e.forEach(function(t){J("update",t),J("slide",t)}),null!=i&&J("drag",o))}function $(t,e){return d.dir?100-t-e:t}function G(){g.forEach(function(t){var e=50<m[t]?-1:1,e=3+(l.length+e*t);l[t].style.zIndex=String(e)})}function Q(t,e,r,n,i,s){return!1!==(e=i?e:X(m,t,e,r,n,!1,s))&&(e=e,m[t=t]=e,h[t]=S.fromStepping(e),e="translate("+Y($(e,0)-E+"%","0")+")",l[t].style[d.transformRule]=e,Z(t),Z(t+1),!0)}function Z(t){var e,r;a[t]&&(r=100,e="translate("+Y($(e=(e=0)!==t?m[t-1]:e,r=(r=t!==a.length-1?m[t]:r)-e)+"%","0")+")",r="scale("+Y(r/100,"1")+")",a[t].style[d.transformRule]=e+" "+r)}function tt(t,e){return null===t||!1===t||void 0===t?m[e]:("number"==typeof t&&(t=String(t)),!1===(t=!1!==(t=d.format.from(t))?S.toStepping(t):t)||isNaN(t)?m[e]:t)}function et(t,e,r){var n=asArray(t),t=void 0===m[0];e=void 0===e||e,d.animate&&!t&&addClassFor(f,d.cssClasses.tap,d.animationDuration),g.forEach(function(t){Q(t,tt(n[t],t),!0,!1,r)});var i,s=1===g.length?0:1;for(t&&S.hasNoSize()&&(r=!0,m[0]=0,1<g.length&&(i=100/(g.length-1),g.forEach(function(t){m[t]=t*i})));s<g.length;++s)g.forEach(function(t){Q(t,m[t],!0,!0,r)});G(),g.forEach(function(t){J("update",t),null!==n[t]&&e&&J("set",t)})}function rt(t){if(t=void 0===t?!1:t)return 1===h.length?h[0]:h.slice(0);t=h.map(d.format.to);return 1===t.length?t[0]:t}function nt(t){var e=m[t],r=S.getNearbySteps(e),n=h[t],i=r.thisStep.step,t=null;if(d.snap)return[n-r.stepBefore.startValue||null,r.stepAfter.startValue-n||null];!1!==i&&n+i>r.stepAfter.startValue&&(i=r.stepAfter.startValue-n),t=n>r.thisStep.startValue?r.thisStep.step:!1!==r.stepBefore.step&&n-r.stepBefore.highestStep,100===e?i=null:0===e&&(t=null);e=S.countStepDecimals();return null!==i&&!1!==i&&(i=Number(i.toFixed(e))),[t=null!==t&&!1!==t?Number(t.toFixed(e)):t,i]}addClass(t=f,d.cssClasses.target),0===d.dir?addClass(t,d.cssClasses.ltr):addClass(t,d.cssClasses.rtl),0===d.ort?addClass(t,d.cssClasses.horizontal):addClass(t,d.cssClasses.vertical),addClass(t,"rtl"===getComputedStyle(t).direction?d.cssClasses.textDirectionRtl:d.cssClasses.textDirectionLtr),r=N(t,d.cssClasses.base),function(t,e){var r=N(e,d.cssClasses.connects);l=[],(a=[]).push(w(r,t[0]));for(var n=0;n<d.handles;n++)l.push(P(e,n)),g[n]=n,a.push(w(r,t[n+1]))}(d.connect,r),(u=d.events).fixed||l.forEach(function(t,e){F(c.start,t.children[0],j,{handleNumbers:[e]})}),u.tap&&F(c.start,r,I,{}),u.hover&&F(c.move,r,B,{hover:!0}),u.drag&&a.forEach(function(e,t){var r,n,i,s,o;!1!==e&&0!==t&&t!==a.length-1&&(r=l[t-1],n=l[t],i=[e],s=[r,n],o=[t-1,t],addClass(e,d.cssClasses.draggable),u.fixed&&(i.push(r.children[0]),i.push(n.children[0])),u.dragAll&&(s=l,o=g),i.forEach(function(t){F(c.start,t,j,{handles:s,handleNumbers:o,connect:e})}))}),et(d.start),d.pips&&L(d.pips),d.tooltips&&k(),q("update"+INTERNAL_EVENT_NS.aria),K("update"+INTERNAL_EVENT_NS.aria,function(t,e,s,r,o){g.forEach(function(t){var e=l[t],r=X(m,t,0,!0,!0,!0),n=X(m,t,100,!0,!0,!0),i=o[t],t=String(d.ariaFormat.to(s[t])),r=S.fromStepping(r).toFixed(1),n=S.fromStepping(n).toFixed(1),i=S.fromStepping(i).toFixed(1);e.children[0].setAttribute("aria-valuemin",r),e.children[0].setAttribute("aria-valuemax",n),e.children[0].setAttribute("aria-valuenow",i),e.children[0].setAttribute("aria-valuetext",t)})});var it={destroy:function(){for(q(INTERNAL_EVENT_NS.aria),q(INTERNAL_EVENT_NS.tooltips),Object.keys(d.cssClasses).forEach(function(t){removeClass(f,d.cssClasses[t])});f.firstChild;)f.removeChild(f.firstChild);delete f.noUiSlider},steps:function(){return g.map(nt)},on:K,off:q,get:rt,set:et,setHandle:function(t,e,r,n){if(!(0<=(t=Number(t))&&t<g.length))throw new Error("noUiSlider: invalid handle number, got: "+t);Q(t,tt(e,t),!0,!0,n),J("update",t),r&&J("set",t)},reset:function(t){et(d.start,t)},disable:function(t){null!=t?(l[t].setAttribute("disabled",""),l[t].handle.removeAttribute("tabindex")):(f.setAttribute("disabled",""),l.forEach(function(t){t.handle.removeAttribute("tabindex")}))},enable:function(t){null!=t?(l[t].removeAttribute("disabled"),l[t].handle.setAttribute("tabindex","0")):(f.removeAttribute("disabled"),l.forEach(function(t){t.removeAttribute("disabled"),t.handle.setAttribute("tabindex","0")}))},__moveHandles:function(t,e,r){W(t,e,m,r)},options:s,updateOptions:function(e,t){var r=rt(),n=["margin","limit","padding","range","animate","snap","step","format","pips","tooltips"];n.forEach(function(t){void 0!==e[t]&&(s[t]=e[t])});var i=testOptions(s);n.forEach(function(t){void 0!==e[t]&&(d[t]=i[t])}),S=i.spectrum,d.margin=i.margin,d.limit=i.limit,d.padding=i.padding,d.pips?L(d.pips):U(),(d.tooltips?k:V)(),m=[],et(isSet(e.start)?e.start:r,t)},target:f,removePips:U,removeTooltips:V,getPositions:function(){return m.slice()},getTooltips:function(){return i},getOrigins:function(){return l},pips:L};return it}function initialize(t,e){if(!t||!t.nodeName)throw new Error("noUiSlider: create requires a single element, got: "+t);if(t.noUiSlider)throw new Error("noUiSlider: Slider was already initialized.");e=scope(t,testOptions(e),e);return t.noUiSlider=e}export default{__spectrum:Spectrum,cssClasses:cssClasses,create:initialize};export{PipsMode,PipsType,initialize as create,cssClasses};