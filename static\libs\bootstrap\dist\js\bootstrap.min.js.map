{"version": 3, "names": ["elementMap", "Map", "Data", "set", "element", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "keys", "remove", "delete", "TRANSITION_END", "parseSelector", "selector", "window", "CSS", "escape", "replace", "match", "id", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "document", "querySelector", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "getAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "<PERSON><PERSON><PERSON><PERSON>", "args", "defaultValue", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "call", "this", "handlers", "previousFunction", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "slice", "keyHandlers", "trigger", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "obj", "meta", "value", "_unused", "defineProperty", "configurable", "normalizeData", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "static", "getInstance", "VERSION", "getSelector", "hrefAttribute", "trim", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLID", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "endCallBack", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "eventName", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "completeCallBack", "_isAnimated", "SELECTOR_ACTIVE", "clearInterval", "carousel", "slideIndex", "carousels", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "complete", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "autoClose", "boundary", "display", "offset", "popperConfig", "reference", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "destroy", "update", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "createPopper", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "options", "enabled", "_selectMenuItem", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "dataApiKeydownHandler", "clearMenus", "EVENT_MOUSEDOWN", "className", "clickCallback", "rootElement", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "TAB_NAV_BACKWARD", "autofocus", "trapElement", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "elements", "shift<PERSON>ey", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "overflow", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "sel", "EVENT_HIDDEN", "EVENT_SHOW", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "handleUpdate", "scrollTop", "modalBody", "transitionComplete", "_triggerBackdropTransition", "event2", "_resetAdjustments", "isModalOverflowing", "scrollHeight", "clientHeight", "initialOverflowY", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "EVENT_HIDE_PREVENTED", "scroll", "<PERSON><PERSON><PERSON>", "blur", "completeCallback", "position", "uriAttributes", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "nodeValue", "attributeRegex", "some", "regex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "allowList", "content", "extraClass", "html", "sanitize", "sanitizeFn", "template", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "container", "customClass", "delay", "fallbackPlacements", "title", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "_newContent", "tip", "_setListeners", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "click", "_leave", "_enter", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "ownerDocument", "_getTipElement", "_isWithActiveTrigger", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "_initializeOnDelegatedTarget", "_getDelegateConfig", "attachment", "phase", "state", "triggers", "eventIn", "eventOut", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "Popover", "_getContent", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "rootMargin", "smoothScroll", "threshold", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "hash", "height", "offsetTop", "scrollTo", "top", "behavior", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "_activateParents", "listGroup", "item", "activeNodes", "node", "spy", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "SELECTOR_INNER_ELEM", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "preventScroll", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "isShown", "_onInteraction", "isInteracting"], "sources": ["../../js/src/dom/data.js", "../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/util/scrollbar.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`)\n  }\n\n  return selector\n}\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object))\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback(...args) : defaultValue\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getjQuery,\n  getNextActiveElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  parseSelector,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index.js'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = hydrateObj(new Event(event, { bubbles, cancelable: true }), args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport { isElement, toType } from './index.js'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data.js'\nimport EventHandler from './dom/event-handler.js'\nimport Config from './util/config.js'\nimport { executeAfterTransition, getElement } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.0-alpha2'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index.js'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return parseSelector(selector)\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isDisabled } from './index.js'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport { execute } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index.js'\nimport Swipe from './util/swipe.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport { execute, executeAfterTransition, getElement, reflow } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport { defineJQueryPlugin, isRTL, isVisible, reflow } from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    EventHandler.off(window, EVENT_KEY)\n    EventHandler.off(this._dialog, EVENT_KEY)\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n// js-docs-end allow-list\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport { defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop } from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    this._activeTrigger.click = !this._activeTrigger.click\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Tooltip from './tooltip.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin, getElement, isDisabled, isVisible } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(anchor.hash, this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(anchor.hash, anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = ':not(.dropdown-toggle)'\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n    const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n    const nextActiveElement = getNextActiveElement(this._getChildren().filter(element => !isDisabled(element)), event.target, isNext, true)\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin, reflow } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert.js'\nimport Button from './src/button.js'\nimport Carousel from './src/carousel.js'\nimport Collapse from './src/collapse.js'\nimport Dropdown from './src/dropdown.js'\nimport Modal from './src/modal.js'\nimport Offcanvas from './src/offcanvas.js'\nimport Popover from './src/popover.js'\nimport ScrollSpy from './src/scrollspy.js'\nimport Tab from './src/tab.js'\nimport Toast from './src/toast.js'\nimport Tooltip from './src/tooltip.js'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "mappings": ";;;;;ujBAWMA,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAIC,EAASC,EAAKC,GACXN,EAAWO,IAAIH,IAClBJ,EAAWG,IAAIC,EAAS,IAAIH,KAG9B,MAAMO,EAAcR,EAAWS,IAAIL,GAI9BI,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYL,IAAIE,EAAKC,GAJnBK,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKN,EAAYO,QAAQ,M,EAOhIN,IAAGA,CAACL,EAASC,IACPL,EAAWO,IAAIH,IACVJ,EAAWS,IAAIL,GAASK,IAAIJ,IAG9B,KAGTW,OAAOZ,EAASC,GACd,IAAKL,EAAWO,IAAIH,GAClB,OAGF,MAAMI,EAAcR,EAAWS,IAAIL,GAEnCI,EAAYS,OAAOZ,GAGM,IAArBG,EAAYE,MACdV,EAAWiB,OAAOb,EAEtB,GC5CIc,EAAiB,gBAOjBC,EAAgBC,IAChBA,GAAYC,OAAOC,KAAOD,OAAOC,IAAIC,SAEvCH,EAAWA,EAASI,QAAQ,iBAAiB,CAACC,EAAOC,IAAQ,IAAGJ,IAAIC,OAAOG,QAGtEN,GA+CHO,EAAuBvB,IAC3BA,EAAQwB,cAAc,IAAIC,MAAMX,GAAgB,EAG5CY,EAAYC,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOC,SAChBD,EAASA,EAAO,SAGgB,IAApBA,EAAOE,UAGjBC,EAAaH,GAEbD,EAAUC,GACLA,EAAOC,OAASD,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOI,OAAS,EACzCC,SAASC,cAAclB,EAAcY,IAGvC,KAGHO,EAAYlC,IAChB,IAAK0B,EAAU1B,IAAgD,IAApCA,EAAQmC,iBAAiBJ,OAClD,OAAO,EAGT,MAAMK,EAAgF,YAA7DC,iBAAiBrC,GAASsC,iBAAiB,cAE9DC,EAAgBvC,EAAQwC,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkBvC,EAAS,CAC7B,MAAMyC,EAAUzC,EAAQwC,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,CAEX,CAEA,OAAOL,CAAgB,EAGnBO,EAAa3C,IACZA,GAAWA,EAAQ6B,WAAae,KAAKC,gBAItC7C,EAAQ8C,UAAUC,SAAS,mBAIC,IAArB/C,EAAQgD,SACVhD,EAAQgD,SAGVhD,EAAQiD,aAAa,aAAoD,UAArCjD,EAAQkD,aAAa,aAG5DC,EAAiBnD,IACrB,IAAKgC,SAASoB,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBrD,EAAQsD,YAA4B,CAC7C,MAAMC,EAAOvD,EAAQsD,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,IAC7C,CAEA,OAAIvD,aAAmBwD,WACdxD,EAIJA,EAAQ0C,WAINS,EAAenD,EAAQ0C,YAHrB,IAGgC,EAGrCe,EAAOA,OAUPC,EAAS1D,IACbA,EAAQ2D,YAAY,EAGhBC,EAAYA,IACZ3C,OAAO4C,SAAW7B,SAAS8B,KAAKb,aAAa,qBACxChC,OAAO4C,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQA,IAAuC,QAAjChC,SAASoB,gBAAgBa,IAEvCC,EAAqBC,IAnBAC,QAoBN,KACjB,MAAMC,EAAIT,IAEV,GAAIS,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,gBAElB,GA/B0B,YAAxB1C,SAAS6C,YAENd,EAA0BhC,QAC7BC,SAAS8C,iBAAiB,oBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,GACF,IAIJL,EAA0BgB,KAAKX,IAE/BA,GAoBA,EAGEY,EAAUA,CAACC,EAAkBC,EAAO,GAAIC,EAAeF,IACxB,mBAArBA,EAAkCA,KAAoBC,GAAQC,EAGxEC,EAAyBA,CAAChB,EAAUiB,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAN,EAAQZ,GAIV,MACMmB,EA7LiCvF,KACvC,IAAKA,EACH,OAAO,EAIT,IAAIwF,mBAAEA,EAAkBC,gBAAEA,GAAoBxE,OAAOoB,iBAAiBrC,GAEtE,MAAM0F,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDL,EAAkBA,EAAgBK,MAAM,KAAK,GAxDf,KA0DtBH,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,CAOoG,EAyKpFM,CAAiCV,GADlC,EAGxB,IAAIW,GAAS,EAEb,MAAMC,EAAUA,EAAGC,aACbA,IAAWb,IAIfW,GAAS,EACTX,EAAkBc,oBAAoBrF,EAAgBmF,GACtDjB,EAAQZ,GAAS,EAGnBiB,EAAkBP,iBAAiBhE,EAAgBmF,GACnDG,YAAW,KACJJ,GACHzE,EAAqB8D,EACvB,GACCE,EAAiB,EAYhBc,EAAuBA,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKvE,OACxB,IAAI4E,EAAQL,EAAKM,QAAQL,GAIzB,OAAe,IAAXI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAK,EAE1BC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,KAAI,EC7QrDM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAa1H,EAAS2H,GAC7B,OAAQA,GAAQ,GAAEA,MAAQP,OAAiBpH,EAAQoH,UAAYA,GACjE,CAEA,SAASQ,EAAiB5H,GACxB,MAAM2H,EAAMD,EAAa1H,GAKzB,OAHAA,EAAQoH,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,EACvB,CAoCA,SAASE,EAAYC,EAAQC,EAAUC,EAAqB,MAC1D,OAAOC,OAAOC,OAAOJ,GAClBK,MAAKC,GAASA,EAAML,WAAaA,GAAYK,EAAMJ,qBAAuBA,GAC/E,CAEA,SAASK,EAAoBC,EAAmBrC,EAASsC,GACvD,MAAMC,EAAiC,iBAAZvC,EAErB8B,EAAWS,EAAcD,EAAsBtC,GAAWsC,EAChE,IAAIE,EAAYC,EAAaJ,GAM7B,OAJKd,EAAarH,IAAIsI,KACpBA,EAAYH,GAGP,CAACE,EAAaT,EAAUU,EACjC,CAEA,SAASE,EAAW3I,EAASsI,EAAmBrC,EAASsC,EAAoBK,GAC3E,GAAiC,iBAAtBN,IAAmCtI,EAC5C,OAGF,IAAKwI,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAIzF,GAAID,KAAqBjB,EAAc,CACrC,MAAMwB,EAAepE,GACZ,SAAU2D,GACf,IAAKA,EAAMU,eAAkBV,EAAMU,gBAAkBV,EAAMW,iBAAmBX,EAAMW,eAAehG,SAASqF,EAAMU,eAChH,OAAOrE,EAAGuE,KAAKC,KAAMb,E,EAK3BL,EAAWc,EAAad,EAC1B,CAEA,MAAMD,EAASF,EAAiB5H,GAC1BkJ,EAAWpB,EAAOW,KAAeX,EAAOW,GAAa,IACrDU,EAAmBtB,EAAYqB,EAAUnB,EAAUS,EAAcvC,EAAU,MAEjF,GAAIkD,EAGF,YAFAA,EAAiBP,OAASO,EAAiBP,QAAUA,GAKvD,MAAMjB,EAAMD,EAAaK,EAAUO,EAAkBlH,QAAQ4F,EAAgB,KACvEvC,EAAK+D,EAxEb,SAAoCxI,EAASgB,EAAUyD,GACrD,OAAO,SAASwB,EAAQmC,GACtB,MAAMgB,EAAcpJ,EAAQqJ,iBAAiBrI,GAE7C,IAAK,IAAIkF,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAW+C,KAAM/C,EAASA,EAAOxD,WACtE,IAAK,MAAM4G,KAAcF,EACvB,GAAIE,IAAepD,EAUnB,OANAqD,EAAWnB,EAAO,CAAEW,eAAgB7C,IAEhCD,EAAQ2C,QACVY,EAAaC,IAAIzJ,EAASoI,EAAMsB,KAAM1I,EAAUyD,GAG3CA,EAAGkF,MAAMzD,EAAQ,CAACkC,G,CAIjC,CAqDIwB,CAA2B5J,EAASiG,EAAS8B,GArFjD,SAA0B/H,EAASyE,GACjC,OAAO,SAASwB,EAAQmC,GAOtB,OANAmB,EAAWnB,EAAO,CAAEW,eAAgB/I,IAEhCiG,EAAQ2C,QACVY,EAAaC,IAAIzJ,EAASoI,EAAMsB,KAAMjF,GAGjCA,EAAGkF,MAAM3J,EAAS,CAACoI,G,CAE9B,CA4EIyB,CAAiB7J,EAAS+H,GAE5BtD,EAAGuD,mBAAqBQ,EAAcvC,EAAU,KAChDxB,EAAGsD,SAAWA,EACdtD,EAAGmE,OAASA,EACZnE,EAAG2C,SAAWO,EACduB,EAASvB,GAAOlD,EAEhBzE,EAAQ8E,iBAAiB2D,EAAWhE,EAAI+D,EAC1C,CAEA,SAASsB,EAAc9J,EAAS8H,EAAQW,EAAWxC,EAAS+B,GAC1D,MAAMvD,EAAKoD,EAAYC,EAAOW,GAAYxC,EAAS+B,GAE9CvD,IAILzE,EAAQmG,oBAAoBsC,EAAWhE,EAAIsF,QAAQ/B,WAC5CF,EAAOW,GAAWhE,EAAG2C,UAC9B,CAEA,SAAS4C,EAAyBhK,EAAS8H,EAAQW,EAAWwB,GAC5D,MAAMC,EAAoBpC,EAAOW,IAAc,GAE/C,IAAK,MAAO0B,EAAY/B,KAAUH,OAAOmC,QAAQF,GAC3CC,EAAWE,SAASJ,IACtBH,EAAc9J,EAAS8H,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAGtE,CAEA,SAASU,EAAaN,GAGpB,OADAA,EAAQA,EAAMhH,QAAQ6F,EAAgB,IAC/BI,EAAae,IAAUA,CAChC,CAEA,MAAMoB,EAAe,CACnBc,GAAGtK,EAASoI,EAAOnC,EAASsC,GAC1BI,EAAW3I,EAASoI,EAAOnC,EAASsC,GAAoB,E,EAG1DgC,IAAIvK,EAASoI,EAAOnC,EAASsC,GAC3BI,EAAW3I,EAASoI,EAAOnC,EAASsC,GAAoB,E,EAG1DkB,IAAIzJ,EAASsI,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmCtI,EAC5C,OAGF,MAAOwI,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GACrFiC,EAAc/B,IAAcH,EAC5BR,EAASF,EAAiB5H,GAC1BkK,EAAoBpC,EAAOW,IAAc,GACzCgC,EAAcnC,EAAkBoC,WAAW,KAEjD,QAAwB,IAAb3C,EAAX,CAUA,GAAI0C,EACF,IAAK,MAAME,KAAgB1C,OAAOtH,KAAKmH,GACrCkC,EAAyBhK,EAAS8H,EAAQ6C,EAAcrC,EAAkBsC,MAAM,IAIpF,IAAK,MAAOC,EAAazC,KAAUH,OAAOmC,QAAQF,GAAoB,CACpE,MAAMC,EAAaU,EAAYzJ,QAAQ8F,EAAe,IAEjDsD,IAAelC,EAAkB+B,SAASF,IAC7CL,EAAc9J,EAAS8H,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAEpE,CAdA,KARA,CAEE,IAAKC,OAAOtH,KAAKuJ,GAAmBnI,OAClC,OAGF+H,EAAc9J,EAAS8H,EAAQW,EAAWV,EAAUS,EAAcvC,EAAU,KAE9E,C,EAiBF6E,QAAQ9K,EAASoI,EAAOlD,GACtB,GAAqB,iBAAVkD,IAAuBpI,EAChC,OAAO,KAGT,MAAMqE,EAAIT,IAIV,IAAImH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALH9C,IADFM,EAAaN,IAQZ/D,IACjB0G,EAAc1G,EAAE5C,MAAM2G,EAAOlD,GAE7Bb,EAAErE,GAAS8K,QAAQC,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,MAAMC,EAAM/B,EAAW,IAAI9H,MAAM2G,EAAO,CAAE4C,UAASO,YAAY,IAASrG,GAcxE,OAZIgG,GACFI,EAAIE,iBAGFP,GACFjL,EAAQwB,cAAc8J,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYS,iBAGPF,CACT,GAGF,SAAS/B,EAAWkC,EAAKC,EAAO,IAC9B,IAAK,MAAOzL,EAAK0L,KAAU1D,OAAOmC,QAAQsB,GACxC,IACED,EAAIxL,GAAO0L,CAQb,CAPE,MAAAC,GACA3D,OAAO4D,eAAeJ,EAAKxL,EAAK,CAC9B6L,cAAc,EACdzL,IAAGA,IACMsL,GAGb,CAGF,OAAOF,CACT,CCnTA,SAASM,EAAcJ,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAUhG,OAAOgG,GAAOK,WAC1B,OAAOrG,OAAOgG,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOM,KAAKC,MAAMC,mBAAmBR,GAGvC,CAFE,MAAAC,GACA,OAAOD,CACT,CACF,CAEA,SAASS,EAAiBnM,GACxB,OAAOA,EAAImB,QAAQ,UAAUiL,GAAQ,IAAGA,EAAIC,iBAC9C,CAEA,MAAMC,EAAc,CAClBC,iBAAiBxM,EAASC,EAAK0L,GAC7B3L,EAAQyM,aAAc,WAAUL,EAAiBnM,KAAQ0L,E,EAG3De,oBAAoB1M,EAASC,GAC3BD,EAAQ2M,gBAAiB,WAAUP,EAAiBnM,K,EAGtD2M,kBAAkB5M,GAChB,IAAKA,EACH,MAAO,GAGT,MAAM6M,EAAa,GACbC,EAAS7E,OAAOtH,KAAKX,EAAQ+M,SAASC,QAAO/M,GAAOA,EAAIyK,WAAW,QAAUzK,EAAIyK,WAAW,cAElG,IAAK,MAAMzK,KAAO6M,EAAQ,CACxB,IAAIG,EAAUhN,EAAImB,QAAQ,MAAO,IACjC6L,EAAUA,EAAQC,OAAO,GAAGZ,cAAgBW,EAAQrC,MAAM,EAAGqC,EAAQlL,QACrE8K,EAAWI,GAAWlB,EAAc/L,EAAQ+M,QAAQ9M,GACtD,CAEA,OAAO4M,C,EAGTM,iBAAgBA,CAACnN,EAASC,IACjB8L,EAAc/L,EAAQkD,aAAc,WAAUkJ,EAAiBnM,QCpD1E,MAAMmN,EAEOC,qBACT,MAAO,EACT,CAEWC,yBACT,MAAO,EACT,CAEW/I,kBACT,MAAM,IAAIgJ,MAAM,sEAClB,CAEAC,WAAWC,GAIT,OAHAA,EAASxE,KAAKyE,gBAAgBD,GAC9BA,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAChB,OAAOA,CACT,CAEAC,gBAAgBD,EAAQzN,GACtB,MAAM6N,EAAanM,EAAU1B,GAAWuM,EAAYY,iBAAiBnN,EAAS,UAAY,GAE1F,MAAO,IACFiJ,KAAK6E,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9CnM,EAAU1B,GAAWuM,EAAYK,kBAAkB5M,GAAW,MAC5C,iBAAXyN,EAAsBA,EAAS,GAE9C,CAEAG,iBAAiBH,EAAQM,EAAc9E,KAAK6E,YAAYR,aACtD,IAAK,MAAOU,EAAUC,KAAkBhG,OAAOmC,QAAQ2D,GAAc,CACnE,MAAMpC,EAAQ8B,EAAOO,GACfE,EAAYxM,EAAUiK,GAAS,UH1BrChK,OADSA,EG2B+CgK,GHzBlD,GAAEhK,IAGLsG,OAAOkG,UAAUnC,SAAShD,KAAKrH,GAAQN,MAAM,eAAe,GAAGiL,cGwBlE,IAAK,IAAI8B,OAAOH,GAAeI,KAAKH,GAClC,MAAM,IAAII,UACP,GAAErF,KAAK6E,YAAYvJ,KAAKgK,0BAA0BP,qBAA4BE,yBAAiCD,MAGtH,CHlCWtM,KGmCb,ECvCF,MAAM6M,UAAsBpB,EAC1BU,YAAY9N,EAASyN,GACnBgB,SAEAzO,EAAU8B,EAAW9B,MAKrBiJ,KAAKyF,SAAW1O,EAChBiJ,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAE/B3N,EAAKC,IAAIkJ,KAAKyF,SAAUzF,KAAK6E,YAAYc,SAAU3F,MACrD,CAGA4F,UACE/O,EAAKc,OAAOqI,KAAKyF,SAAUzF,KAAK6E,YAAYc,UAC5CpF,EAAaC,IAAIR,KAAKyF,SAAUzF,KAAK6E,YAAYgB,WAEjD,IAAK,MAAMC,KAAgB9G,OAAO+G,oBAAoB/F,MACpDA,KAAK8F,GAAgB,IAEzB,CAEAE,eAAe7K,EAAUpE,EAASkP,GAAa,GAC7C9J,EAAuBhB,EAAUpE,EAASkP,EAC5C,CAEA1B,WAAWC,GAIT,OAHAA,EAASxE,KAAKyE,gBAAgBD,EAAQxE,KAAKyF,UAC3CjB,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAGA0B,mBAAmBnP,GACjB,OAAOF,EAAKO,IAAIyB,EAAW9B,GAAUiJ,KAAK2F,SAC5C,CAEAO,2BAA2BnP,EAASyN,EAAS,IAC3C,OAAOxE,KAAKmG,YAAYpP,IAAY,IAAIiJ,KAAKjJ,EAA2B,iBAAXyN,EAAsBA,EAAS,KAC9F,CAEW4B,qBACT,MApDY,cAqDd,CAEWT,sBACT,MAAQ,MAAK3F,KAAK1E,MACpB,CAEWuK,uBACT,MAAQ,IAAG7F,KAAK2F,UAClB,CAEAO,iBAAiB7K,GACf,MAAQ,GAAEA,IAAO2E,KAAK6F,WACxB,ECxEF,MAAMQ,EAActP,IAClB,IAAIgB,EAAWhB,EAAQkD,aAAa,kBAEpC,IAAKlC,GAAyB,MAAbA,EAAkB,CACjC,IAAIuO,EAAgBvP,EAAQkD,aAAa,QAMzC,IAAKqM,IAAmBA,EAAclF,SAAS,OAASkF,EAAc7E,WAAW,KAC/E,OAAO,KAIL6E,EAAclF,SAAS,OAASkF,EAAc7E,WAAW,OAC3D6E,EAAiB,IAAGA,EAAczJ,MAAM,KAAK,MAG/C9E,EAAWuO,GAAmC,MAAlBA,EAAwBA,EAAcC,OAAS,IAC7E,CAEA,OAAOzO,EAAcC,EAAS,EAG1ByO,EAAiB,CACrBtH,KAAIA,CAACnH,EAAUhB,EAAUgC,SAASoB,kBACzB,GAAGsM,UAAUC,QAAQxB,UAAU9E,iBAAiBL,KAAKhJ,EAASgB,IAGvE4O,QAAOA,CAAC5O,EAAUhB,EAAUgC,SAASoB,kBAC5BuM,QAAQxB,UAAUlM,cAAc+G,KAAKhJ,EAASgB,GAGvD6O,SAAQA,CAAC7P,EAASgB,IACT,GAAG0O,UAAU1P,EAAQ6P,UAAU7C,QAAO8C,GAASA,EAAMC,QAAQ/O,KAGtEgP,QAAQhQ,EAASgB,GACf,MAAMgP,EAAU,GAChB,IAAIC,EAAWjQ,EAAQ0C,WAAWF,QAAQxB,GAE1C,KAAOiP,GACLD,EAAQjL,KAAKkL,GACbA,EAAWA,EAASvN,WAAWF,QAAQxB,GAGzC,OAAOgP,C,EAGTE,KAAKlQ,EAASgB,GACZ,IAAImP,EAAWnQ,EAAQoQ,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQ/O,GACnB,MAAO,CAACmP,GAGVA,EAAWA,EAASC,sBACtB,CAEA,MAAO,E,EAGTC,KAAKrQ,EAASgB,GACZ,IAAIqP,EAAOrQ,EAAQsQ,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQ/O,GACf,MAAO,CAACqP,GAGVA,EAAOA,EAAKC,kBACd,CAEA,MAAO,E,EAGTC,kBAAkBvQ,GAChB,MAAMwQ,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,KAAIzP,GAAa,GAAEA,2BAAiC0P,KAAK,KAE3D,OAAOzH,KAAKd,KAAKqI,EAAYxQ,GAASgN,QAAO2D,IAAOhO,EAAWgO,IAAOzO,EAAUyO,I,EAGlFC,uBAAuB5Q,GACrB,MAAMgB,EAAWsO,EAAYtP,GAE7B,OAAIgB,GACKyO,EAAeG,QAAQ5O,GAAYA,EAGrC,I,EAGT6P,uBAAuB7Q,GACrB,MAAMgB,EAAWsO,EAAYtP,GAE7B,OAAOgB,EAAWyO,EAAeG,QAAQ5O,GAAY,I,EAGvD8P,gCAAgC9Q,GAC9B,MAAMgB,EAAWsO,EAAYtP,GAE7B,OAAOgB,EAAWyO,EAAetH,KAAKnH,GAAY,EACpD,GC/GI+P,EAAuBA,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUlC,YACvCxK,EAAO0M,EAAUzM,KAEvBiF,EAAac,GAAGtI,SAAUkP,EAAa,qBAAoB5M,OAAU,SAAU8D,GAK7E,GAJI,CAAC,IAAK,QAAQiC,SAASpB,KAAKkI,UAC9B/I,EAAMoD,iBAGJ7I,EAAWsG,MACb,OAGF,MAAM/C,EAASuJ,EAAeoB,uBAAuB5H,OAASA,KAAKzG,QAAS,IAAG8B,KAC9D0M,EAAUI,oBAAoBlL,GAGtC+K,IACX,GAAE,ECAJ,MAAMI,UAAc7C,EAEPjK,kBACT,MAhBS,OAiBX,CAGA+M,QAGE,GAFmB9H,EAAasB,QAAQ7B,KAAKyF,SAjB5B,kBAmBFxD,iBACb,OAGFjC,KAAKyF,SAAS5L,UAAUlC,OApBJ,QAsBpB,MAAMsO,EAAajG,KAAKyF,SAAS5L,UAAUC,SAvBvB,QAwBpBkG,KAAKgG,gBAAe,IAAMhG,KAAKsI,mBAAmBtI,KAAKyF,SAAUQ,EACnE,CAGAqC,kBACEtI,KAAKyF,SAAS9N,SACd4I,EAAasB,QAAQ7B,KAAKyF,SA/BR,mBAgClBzF,KAAK4F,SACP,CAGAM,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoBnI,MAEvC,GAAsB,iBAAXwE,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxE,KANb,CAOF,GACF,EAOF8H,EAAqBM,EAAO,SAM5BnN,EAAmBmN,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAepD,EAERjK,kBACT,MAhBS,QAiBX,CAGAsN,SAEE5I,KAAKyF,SAASjC,aAAa,eAAgBxD,KAAKyF,SAAS5L,UAAU+O,OAjB7C,UAkBxB,CAGA1C,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAOG,EAAOR,oBAAoBnI,MAEzB,WAAXwE,GACFgE,EAAKhE,IAET,GACF,EAOFjE,EAAac,GAAGtI,SAlCc,2BAkCkB2P,GAAsBvJ,IACpEA,EAAMoD,iBAEN,MAAMsG,EAAS1J,EAAMlC,OAAO1D,QAAQmP,GACvBC,EAAOR,oBAAoBU,GAEnCD,QAAQ,IAOf3N,EAAmB0N,GCtDnB,MAYMvE,EAAU,CACd0E,YAAa,KACbC,aAAc,KACdC,cAAe,MAGX3E,EAAc,CAClByE,YAAa,kBACbC,aAAc,kBACdC,cAAe,mBAOjB,MAAMC,UAAc9E,EAClBU,YAAY9N,EAASyN,GACnBgB,QACAxF,KAAKyF,SAAW1O,EAEXA,GAAYkS,EAAMC,gBAIvBlJ,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAKmJ,QAAU,EACfnJ,KAAKoJ,sBAAwBtI,QAAQ9I,OAAOqR,cAC5CrJ,KAAKsJ,cACP,CAGWlF,qBACT,OAAOA,CACT,CAEWC,yBACT,OAAOA,CACT,CAEW/I,kBACT,MArDS,OAsDX,CAGAsK,UACErF,EAAaC,IAAIR,KAAKyF,SAzDR,YA0DhB,CAGA8D,OAAOpK,GACAa,KAAKoJ,sBAMNpJ,KAAKwJ,wBAAwBrK,KAC/Ba,KAAKmJ,QAAUhK,EAAMsK,SANrBzJ,KAAKmJ,QAAUhK,EAAMuK,QAAQ,GAAGD,OAQpC,CAEAE,KAAKxK,GACCa,KAAKwJ,wBAAwBrK,KAC/Ba,KAAKmJ,QAAUhK,EAAMsK,QAAUzJ,KAAKmJ,SAGtCnJ,KAAK4J,eACL7N,EAAQiE,KAAK0F,QAAQoD,YACvB,CAEAe,MAAM1K,GACJa,KAAKmJ,QAAUhK,EAAMuK,SAAWvK,EAAMuK,QAAQ5Q,OAAS,EACrD,EACAqG,EAAMuK,QAAQ,GAAGD,QAAUzJ,KAAKmJ,OACpC,CAEAS,eACE,MAAME,EAAYlM,KAAKmM,IAAI/J,KAAKmJ,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAY9J,KAAKmJ,QAEnCnJ,KAAKmJ,QAAU,EAEVa,GAILjO,EAAQiO,EAAY,EAAIhK,KAAK0F,QAAQsD,cAAgBhJ,KAAK0F,QAAQqD,aACpE,CAEAO,cACMtJ,KAAKoJ,uBACP7I,EAAac,GAAGrB,KAAKyF,SAxGA,wBAwG6BtG,GAASa,KAAKuJ,OAAOpK,KACvEoB,EAAac,GAAGrB,KAAKyF,SAxGF,sBAwG6BtG,GAASa,KAAK2J,KAAKxK,KAEnEa,KAAKyF,SAAS5L,UAAUoQ,IAvGG,mBAyG3B1J,EAAac,GAAGrB,KAAKyF,SAhHD,uBAgH6BtG,GAASa,KAAKuJ,OAAOpK,KACtEoB,EAAac,GAAGrB,KAAKyF,SAhHF,sBAgH6BtG,GAASa,KAAK6J,MAAM1K,KACpEoB,EAAac,GAAGrB,KAAKyF,SAhHH,qBAgH6BtG,GAASa,KAAK2J,KAAKxK,KAEtE,CAEAqK,wBAAwBrK,GACtB,OAAOa,KAAKoJ,wBAjHS,QAiHiBjK,EAAM+K,aAlHrB,UAkHyD/K,EAAM+K,YACxF,CAGAhE,qBACE,MAAO,iBAAkBnN,SAASoB,iBAAmBgQ,UAAUC,eAAiB,CAClF,ECrHF,MASMC,EAAa,OACbC,GAAa,OACbC,GAAiB,OACjBC,GAAkB,QAGlBC,GAAc,mBAQdC,GAAsB,WACtBC,GAAoB,SAepBC,GAAmB,CACvBC,UAAkBL,GAClBM,WAAmBP,IAGfnG,GAAU,CACd2G,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGF/G,GAAc,CAClB0G,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,KAAM,mBACNC,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiB9F,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKsL,UAAY,KACjBtL,KAAKuL,eAAiB,KACtBvL,KAAKwL,YAAa,EAClBxL,KAAKyL,aAAe,KACpBzL,KAAK0L,aAAe,KAEpB1L,KAAK2L,mBAAqBnF,EAAeG,QAzCjB,uBAyC8C3G,KAAKyF,UAC3EzF,KAAK4L,qBAED5L,KAAK0F,QAAQwF,OAASR,IACxB1K,KAAK6L,OAET,CAGWzH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MA9FS,UA+FX,CAGA8L,OACEpH,KAAK8L,OAAOzB,EACd,CAEA0B,mBAIOhT,SAASiT,QAAU/S,EAAU+G,KAAKyF,WACrCzF,KAAKoH,MAET,CAEAH,OACEjH,KAAK8L,OAAOxB,GACd,CAEAW,QACMjL,KAAKwL,YACPlT,EAAqB0H,KAAKyF,UAG5BzF,KAAKiM,gBACP,CAEAJ,QACE7L,KAAKiM,iBACLjM,KAAKkM,kBAELlM,KAAKsL,UAAYa,aAAY,IAAMnM,KAAK+L,mBAAmB/L,KAAK0F,QAAQqF,SAC1E,CAEAqB,oBACOpM,KAAK0F,QAAQwF,OAIdlL,KAAKwL,WACPjL,EAAae,IAAItB,KAAKyF,SAAUgF,IAAY,IAAMzK,KAAK6L,UAIzD7L,KAAK6L,QACP,CAEAQ,GAAG3O,GACD,MAAM4O,EAAQtM,KAAKuM,YACnB,GAAI7O,EAAQ4O,EAAMxT,OAAS,GAAK4E,EAAQ,EACtC,OAGF,GAAIsC,KAAKwL,WAEP,YADAjL,EAAae,IAAItB,KAAKyF,SAAUgF,IAAY,IAAMzK,KAAKqM,GAAG3O,KAI5D,MAAM8O,EAAcxM,KAAKyM,cAAczM,KAAK0M,cAC5C,GAAIF,IAAgB9O,EAClB,OAGF,MAAMiP,EAAQjP,EAAQ8O,EAAcnC,EAAaC,GAEjDtK,KAAK8L,OAAOa,EAAOL,EAAM5O,GAC3B,CAEAkI,UACM5F,KAAK0L,cACP1L,KAAK0L,aAAa9F,UAGpBJ,MAAMI,SACR,CAGAlB,kBAAkBF,GAEhB,OADAA,EAAOoI,gBAAkBpI,EAAOuG,SACzBvG,CACT,CAEAoH,qBACM5L,KAAK0F,QAAQsF,UACfzK,EAAac,GAAGrB,KAAKyF,SApKJ,uBAoK6BtG,GAASa,KAAK6M,SAAS1N,KAG5C,UAAvBa,KAAK0F,QAAQuF,QACf1K,EAAac,GAAGrB,KAAKyF,SAvKD,0BAuK6B,IAAMzF,KAAKiL,UAC5D1K,EAAac,GAAGrB,KAAKyF,SAvKD,0BAuK6B,IAAMzF,KAAKoM,uBAG1DpM,KAAK0F,QAAQyF,OAASlC,EAAMC,eAC9BlJ,KAAK8M,yBAET,CAEAA,0BACE,IAAK,MAAMC,KAAOvG,EAAetH,KAhKX,qBAgKmCc,KAAKyF,UAC5DlF,EAAac,GAAG0L,EAhLI,yBAgLmB5N,GAASA,EAAMoD,mBAGxD,MAqBMyK,EAAc,CAClBjE,aAAcA,IAAM/I,KAAK8L,OAAO9L,KAAKiN,kBAAkB1C,KACvDvB,cAAeA,IAAMhJ,KAAK8L,OAAO9L,KAAKiN,kBAAkBzC,KACxD1B,YAxBkBoE,KACS,UAAvBlN,KAAK0F,QAAQuF,QAYjBjL,KAAKiL,QACDjL,KAAKyL,cACP0B,aAAanN,KAAKyL,cAGpBzL,KAAKyL,aAAetO,YAAW,IAAM6C,KAAKoM,qBAjNjB,IAiN+DpM,KAAK0F,QAAQqF,UAAS,GAShH/K,KAAK0L,aAAe,IAAIzC,EAAMjJ,KAAKyF,SAAUuH,EAC/C,CAEAH,SAAS1N,GACP,GAAI,kBAAkBiG,KAAKjG,EAAMlC,OAAOiL,SACtC,OAGF,MAAM8B,EAAYY,GAAiBzL,EAAMnI,KACrCgT,IACF7K,EAAMoD,iBACNvC,KAAK8L,OAAO9L,KAAKiN,kBAAkBjD,IAEvC,CAEAyC,cAAc1V,GACZ,OAAOiJ,KAAKuM,YAAY5O,QAAQ5G,EAClC,CAEAqW,2BAA2B1P,GACzB,IAAKsC,KAAK2L,mBACR,OAGF,MAAM0B,EAAkB7G,EAAeG,QA1NnB,UA0N4C3G,KAAK2L,oBAErE0B,EAAgBxT,UAAUlC,OAAOgT,IACjC0C,EAAgB3J,gBAAgB,gBAEhC,MAAM4J,EAAqB9G,EAAeG,QAAS,sBAAqBjJ,MAAWsC,KAAK2L,oBAEpF2B,IACFA,EAAmBzT,UAAUoQ,IAAIU,IACjC2C,EAAmB9J,aAAa,eAAgB,QAEpD,CAEA0I,kBACE,MAAMnV,EAAUiJ,KAAKuL,gBAAkBvL,KAAK0M,aAE5C,IAAK3V,EACH,OAGF,MAAMwW,EAAkB7Q,OAAO8Q,SAASzW,EAAQkD,aAAa,oBAAqB,IAElF+F,KAAK0F,QAAQqF,SAAWwC,GAAmBvN,KAAK0F,QAAQkH,eAC1D,CAEAd,OAAOa,EAAO5V,EAAU,MACtB,GAAIiJ,KAAKwL,WACP,OAGF,MAAMlO,EAAgB0C,KAAK0M,aACrBe,EAASd,IAAUtC,EACnBqD,EAAc3W,GAAWqG,EAAqB4C,KAAKuM,YAAajP,EAAemQ,EAAQzN,KAAK0F,QAAQ0F,MAE1G,GAAIsC,IAAgBpQ,EAClB,OAGF,MAAMqQ,EAAmB3N,KAAKyM,cAAciB,GAEtCE,EAAeC,GACZtN,EAAasB,QAAQ7B,KAAKyF,SAAUoI,EAAW,CACpDhO,cAAe6N,EACf1D,UAAWhK,KAAK8N,kBAAkBnB,GAClClV,KAAMuI,KAAKyM,cAAcnP,GACzB+O,GAAIsB,IAMR,GAFmBC,EA5RF,qBA8RF3L,iBACb,OAGF,IAAK3E,IAAkBoQ,EAGrB,OAGF,MAAMK,EAAYjN,QAAQd,KAAKsL,WAC/BtL,KAAKiL,QAELjL,KAAKwL,YAAa,EAElBxL,KAAKoN,2BAA2BO,GAChC3N,KAAKuL,eAAiBmC,EAEtB,MAAMM,EAAuBP,EAnSR,sBADF,oBAqSbQ,EAAiBR,EAnSH,qBACA,qBAoSpBC,EAAY7T,UAAUoQ,IAAIgE,GAE1BxT,EAAOiT,GAEPpQ,EAAczD,UAAUoQ,IAAI+D,GAC5BN,EAAY7T,UAAUoQ,IAAI+D,GAa1BhO,KAAKgG,gBAXoBkI,KACvBR,EAAY7T,UAAUlC,OAAOqW,EAAsBC,GACnDP,EAAY7T,UAAUoQ,IAAIU,IAE1BrN,EAAczD,UAAUlC,OAAOgT,GAAmBsD,EAAgBD,GAElEhO,KAAKwL,YAAa,EAElBoC,EAAanD,GAAW,GAGYnN,EAAe0C,KAAKmO,eAEtDJ,GACF/N,KAAK6L,OAET,CAEAsC,cACE,OAAOnO,KAAKyF,SAAS5L,UAAUC,SAlUV,QAmUvB,CAEA4S,aACE,OAAOlG,EAAeG,QA9TGyH,wBA8T2BpO,KAAKyF,SAC3D,CAEA8G,YACE,OAAO/F,EAAetH,KAnUJ,iBAmUwBc,KAAKyF,SACjD,CAEAwG,iBACMjM,KAAKsL,YACP+C,cAAcrO,KAAKsL,WACnBtL,KAAKsL,UAAY,KAErB,CAEA2B,kBAAkBjD,GAChB,OAAIjP,IACKiP,IAAcO,GAAiBD,GAAaD,EAG9CL,IAAcO,GAAiBF,EAAaC,EACrD,CAEAwD,kBAAkBnB,GAChB,OAAI5R,IACK4R,IAAUrC,GAAaC,GAAiBC,GAG1CmC,IAAUrC,GAAaE,GAAkBD,EAClD,CAGArE,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAO6C,GAASlD,oBAAoBnI,KAAMwE,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IACP,OAVEgE,EAAK6D,GAAG7H,EAWZ,GACF,EAOFjE,EAAac,GAAGtI,SAjYc,6BAeF,uCAkXyC,SAAUoG,GAC7E,MAAMlC,EAASuJ,EAAeoB,uBAAuB5H,MAErD,IAAK/C,IAAWA,EAAOpD,UAAUC,SAAS4Q,IACxC,OAGFvL,EAAMoD,iBAEN,MAAM+L,EAAWjD,GAASlD,oBAAoBlL,GACxCsR,EAAavO,KAAK/F,aAAa,oBAErC,OAAIsU,GACFD,EAASjC,GAAGkC,QACZD,EAASlC,qBAIyC,SAAhD9I,EAAYY,iBAAiBlE,KAAM,UACrCsO,EAASlH,YACTkH,EAASlC,sBAIXkC,EAASrH,YACTqH,EAASlC,oBACX,IAEA7L,EAAac,GAAGrJ,OA9Za,6BA8ZgB,KAC3C,MAAMwW,EAAYhI,EAAetH,KA9YR,6BAgZzB,IAAK,MAAMoP,KAAYE,EACrBnD,GAASlD,oBAAoBmG,EAC/B,IAOFrT,EAAmBoQ,ICncnB,MAWMoD,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aASxBjG,GAAuB,8BAEvBtE,GAAU,CACdwK,OAAQ,KACRhG,QAAQ,GAGJvE,GAAc,CAClBuK,OAAQ,iBACRhG,OAAQ,WAOV,MAAMiG,WAAiBtJ,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAK8O,kBAAmB,EACxB9O,KAAK+O,cAAgB,GAErB,MAAMC,EAAaxI,EAAetH,KAAKwJ,IAEvC,IAAK,MAAMuG,KAAQD,EAAY,CAC7B,MAAMjX,EAAWyO,EAAemB,uBAAuBsH,GACjDC,EAAgB1I,EAAetH,KAAKnH,GACvCgM,QAAOoL,GAAgBA,IAAiBnP,KAAKyF,WAE/B,OAAb1N,GAAqBmX,EAAcpW,QACrCkH,KAAK+O,cAAcjT,KAAKmT,EAE5B,CAEAjP,KAAKoP,sBAEApP,KAAK0F,QAAQkJ,QAChB5O,KAAKqP,0BAA0BrP,KAAK+O,cAAe/O,KAAKsP,YAGtDtP,KAAK0F,QAAQkD,QACf5I,KAAK4I,QAET,CAGWxE,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MA9ES,UA+EX,CAGAsN,SACM5I,KAAKsP,WACPtP,KAAKuP,OAELvP,KAAKwP,MAET,CAEAA,OACE,GAAIxP,KAAK8O,kBAAoB9O,KAAKsP,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANIzP,KAAK0F,QAAQkJ,SACfa,EAAiBzP,KAAK0P,uBA9EH,wCA+EhB3L,QAAOhN,GAAWA,IAAYiJ,KAAKyF,WACnC+B,KAAIzQ,GAAW8X,GAAS1G,oBAAoBpR,EAAS,CAAE6R,QAAQ,OAGhE6G,EAAe3W,QAAU2W,EAAe,GAAGX,iBAC7C,OAIF,GADmBvO,EAAasB,QAAQ7B,KAAKyF,SAvG7B,oBAwGDxD,iBACb,OAGF,IAAK,MAAM0N,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAY5P,KAAK6P,gBAEvB7P,KAAKyF,SAAS5L,UAAUlC,OAAO+W,IAC/B1O,KAAKyF,SAAS5L,UAAUoQ,IAAI0E,IAE5B3O,KAAKyF,SAASqK,MAAMF,GAAa,EAEjC5P,KAAKqP,0BAA0BrP,KAAK+O,eAAe,GACnD/O,KAAK8O,kBAAmB,EAExB,MAYMiB,EAAc,SADSH,EAAU,GAAGtK,cAAgBsK,EAAUjO,MAAM,KAG1E3B,KAAKgG,gBAdYgK,KACfhQ,KAAK8O,kBAAmB,EAExB9O,KAAKyF,SAAS5L,UAAUlC,OAAOgX,IAC/B3O,KAAKyF,SAAS5L,UAAUoQ,IAAIyE,GAAqBD,IAEjDzO,KAAKyF,SAASqK,MAAMF,GAAa,GAEjCrP,EAAasB,QAAQ7B,KAAKyF,SAjIX,oBAiIiC,GAMpBzF,KAAKyF,UAAU,GAC7CzF,KAAKyF,SAASqK,MAAMF,GAAc,GAAE5P,KAAKyF,SAASsK,MACpD,CAEAR,OACE,GAAIvP,KAAK8O,mBAAqB9O,KAAKsP,WACjC,OAIF,GADmB/O,EAAasB,QAAQ7B,KAAKyF,SA/I7B,oBAgJDxD,iBACb,OAGF,MAAM2N,EAAY5P,KAAK6P,gBAEvB7P,KAAKyF,SAASqK,MAAMF,GAAc,GAAE5P,KAAKyF,SAASwK,wBAAwBL,OAE1EnV,EAAOuF,KAAKyF,UAEZzF,KAAKyF,SAAS5L,UAAUoQ,IAAI0E,IAC5B3O,KAAKyF,SAAS5L,UAAUlC,OAAO+W,GAAqBD,IAEpD,IAAK,MAAM5M,KAAW7B,KAAK+O,cAAe,CACxC,MAAMhY,EAAUyP,EAAeoB,uBAAuB/F,GAElD9K,IAAYiJ,KAAKsP,SAASvY,IAC5BiJ,KAAKqP,0BAA0B,CAACxN,IAAU,EAE9C,CAEA7B,KAAK8O,kBAAmB,EASxB9O,KAAKyF,SAASqK,MAAMF,GAAa,GAEjC5P,KAAKgG,gBATYgK,KACfhQ,KAAK8O,kBAAmB,EACxB9O,KAAKyF,SAAS5L,UAAUlC,OAAOgX,IAC/B3O,KAAKyF,SAAS5L,UAAUoQ,IAAIyE,IAC5BnO,EAAasB,QAAQ7B,KAAKyF,SA1KV,qBA0KiC,GAKrBzF,KAAKyF,UAAU,EAC/C,CAEA6J,SAASvY,EAAUiJ,KAAKyF,UACtB,OAAO1O,EAAQ8C,UAAUC,SAAS2U,GACpC,CAGA/J,kBAAkBF,GAGhB,OAFAA,EAAOoE,OAAS9H,QAAQ0D,EAAOoE,QAC/BpE,EAAOoK,OAAS/V,EAAW2L,EAAOoK,QAC3BpK,CACT,CAEAqL,gBACE,OAAO7P,KAAKyF,SAAS5L,UAAUC,SAtLL,uBAEhB,QACC,QAoLb,CAEAsV,sBACE,IAAKpP,KAAK0F,QAAQkJ,OAChB,OAGF,MAAMhI,EAAW5G,KAAK0P,uBAAuBhH,IAE7C,IAAK,MAAM3R,KAAW6P,EAAU,CAC9B,MAAMsJ,EAAW1J,EAAeoB,uBAAuB7Q,GAEnDmZ,GACFlQ,KAAKqP,0BAA0B,CAACtY,GAAUiJ,KAAKsP,SAASY,GAE5D,CACF,CAEAR,uBAAuB3X,GACrB,MAAM6O,EAAWJ,EAAetH,KA3MA,6BA2MiCc,KAAK0F,QAAQkJ,QAE9E,OAAOpI,EAAetH,KAAKnH,EAAUiI,KAAK0F,QAAQkJ,QAAQ7K,QAAOhN,IAAY6P,EAASxF,SAASrK,IACjG,CAEAsY,0BAA0Bc,EAAcC,GACtC,GAAKD,EAAarX,OAIlB,IAAK,MAAM/B,KAAWoZ,EACpBpZ,EAAQ8C,UAAU+O,OAvNK,aAuNyBwH,GAChDrZ,EAAQyM,aAAa,gBAAiB4M,EAE1C,CAGAlK,uBAAuB1B,GACrB,MAAMkB,EAAU,GAKhB,MAJsB,iBAAXlB,GAAuB,YAAYY,KAAKZ,KACjDkB,EAAQkD,QAAS,GAGZ5I,KAAKuI,MAAK,WACf,MAAMC,EAAOqG,GAAS1G,oBAAoBnI,KAAM0F,GAEhD,GAAsB,iBAAXlB,EAAqB,CAC9B,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IACP,CACF,GACF,EAOFjE,EAAac,GAAGtI,SA1Pc,6BA0PkB2P,IAAsB,SAAUvJ,IAEjD,MAAzBA,EAAMlC,OAAOiL,SAAoB/I,EAAMW,gBAAmD,MAAjCX,EAAMW,eAAeoI,UAChF/I,EAAMoD,iBAGR,IAAK,MAAMxL,KAAWyP,EAAeqB,gCAAgC7H,MACnE6O,GAAS1G,oBAAoBpR,EAAS,CAAE6R,QAAQ,IAASA,QAE7D,IAMA3N,EAAmB4T,IC1QnB,MAAMvT,GAAO,WAOP+U,GAAe,UACfC,GAAiB,YAOjBC,GAAwB,6BACxBC,GAA0B,+BAG1B/B,GAAkB,OAOlB/F,GAAuB,4DACvB+H,GAA8B,GAAE/H,UAChCgI,GAAgB,iBAKhBC,GAAgB5V,IAAU,UAAY,YACtC6V,GAAmB7V,IAAU,YAAc,UAC3C8V,GAAmB9V,IAAU,aAAe,eAC5C+V,GAAsB/V,IAAU,eAAiB,aACjDgW,GAAkBhW,IAAU,aAAe,cAC3CiW,GAAiBjW,IAAU,cAAgB,aAI3CqJ,GAAU,CACd6M,WAAW,EACXC,SAAU,kBACVC,QAAS,UACTC,OAAQ,CAAC,EAAG,GACZC,aAAc,KACdC,UAAW,UAGPjN,GAAc,CAClB4M,UAAW,mBACXC,SAAU,mBACVC,QAAS,SACTC,OAAQ,0BACRC,aAAc,yBACdC,UAAW,2BAOb,MAAMC,WAAiBhM,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKwR,QAAU,KACfxR,KAAKyR,QAAUzR,KAAKyF,SAAShM,WAE7BuG,KAAK0R,MAAQlL,EAAeY,KAAKpH,KAAKyF,SAAUiL,IAAe,IAC7DlK,EAAeS,KAAKjH,KAAKyF,SAAUiL,IAAe,IAClDlK,EAAeG,QAAQ+J,GAAe1Q,KAAKyR,SAC7CzR,KAAK2R,UAAY3R,KAAK4R,eACxB,CAGWxN,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,OAAOA,EACT,CAGAsN,SACE,OAAO5I,KAAKsP,WAAatP,KAAKuP,OAASvP,KAAKwP,MAC9C,CAEAA,OACE,GAAI9V,EAAWsG,KAAKyF,WAAazF,KAAKsP,WACpC,OAGF,MAAMzP,EAAgB,CACpBA,cAAeG,KAAKyF,UAKtB,IAFkBlF,EAAasB,QAAQ7B,KAAKyF,SA3F5B,mBA2FkD5F,GAEpDoC,iBAAd,CAUA,GANAjC,KAAK6R,gBAMD,iBAAkB9Y,SAASoB,kBAAoB6F,KAAKyR,QAAQlY,QAtFxC,eAuFtB,IAAK,MAAMxC,IAAW,GAAG0P,UAAU1N,SAAS8B,KAAK+L,UAC/CrG,EAAac,GAAGtK,EAAS,YAAayD,GAI1CwF,KAAKyF,SAASqM,QACd9R,KAAKyF,SAASjC,aAAa,iBAAiB,GAE5CxD,KAAK0R,MAAM7X,UAAUoQ,IAAIwE,IACzBzO,KAAKyF,SAAS5L,UAAUoQ,IAAIwE,IAC5BlO,EAAasB,QAAQ7B,KAAKyF,SAjHT,oBAiHgC5F,EAnBjD,CAoBF,CAEA0P,OACE,GAAI7V,EAAWsG,KAAKyF,YAAczF,KAAKsP,WACrC,OAGF,MAAMzP,EAAgB,CACpBA,cAAeG,KAAKyF,UAGtBzF,KAAK+R,cAAclS,EACrB,CAEA+F,UACM5F,KAAKwR,SACPxR,KAAKwR,QAAQQ,UAGfxM,MAAMI,SACR,CAEAqM,SACEjS,KAAK2R,UAAY3R,KAAK4R,gBAClB5R,KAAKwR,SACPxR,KAAKwR,QAAQS,QAEjB,CAGAF,cAAclS,GAEZ,IADkBU,EAAasB,QAAQ7B,KAAKyF,SApJ5B,mBAoJkD5F,GACpDoC,iBAAd,CAMA,GAAI,iBAAkBlJ,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG0P,UAAU1N,SAAS8B,KAAK+L,UAC/CrG,EAAaC,IAAIzJ,EAAS,YAAayD,GAIvCwF,KAAKwR,SACPxR,KAAKwR,QAAQQ,UAGfhS,KAAK0R,MAAM7X,UAAUlC,OAAO8W,IAC5BzO,KAAKyF,SAAS5L,UAAUlC,OAAO8W,IAC/BzO,KAAKyF,SAASjC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBzD,KAAK0R,MAAO,UAC5CnR,EAAasB,QAAQ7B,KAAKyF,SAxKR,qBAwKgC5F,EAlBlD,CAmBF,CAEA0E,WAAWC,GAGT,GAAgC,iBAFhCA,EAASgB,MAAMjB,WAAWC,IAER8M,YAA2B7Y,EAAU+L,EAAO8M,YACV,mBAA3C9M,EAAO8M,UAAUrB,sBAGxB,MAAM,IAAI5K,UAAW,GAAE/J,GAAKgK,+GAG9B,OAAOd,CACT,CAEAqN,gBACE,QAAsB,IAAXK,EACT,MAAM,IAAI7M,UAAU,gEAGtB,IAAI8M,EAAmBnS,KAAKyF,SAEG,WAA3BzF,KAAK0F,QAAQ4L,UACfa,EAAmBnS,KAAKyR,QACfhZ,EAAUuH,KAAK0F,QAAQ4L,WAChCa,EAAmBtZ,EAAWmH,KAAK0F,QAAQ4L,WACA,iBAA3BtR,KAAK0F,QAAQ4L,YAC7Ba,EAAmBnS,KAAK0F,QAAQ4L,WAGlC,MAAMD,EAAerR,KAAKoS,mBAC1BpS,KAAKwR,QAAUU,EAAOG,aAAaF,EAAkBnS,KAAK0R,MAAOL,EACnE,CAEA/B,WACE,OAAOtP,KAAK0R,MAAM7X,UAAUC,SAAS2U,GACvC,CAEA6D,gBACE,MAAMC,EAAiBvS,KAAKyR,QAE5B,GAAIc,EAAe1Y,UAAUC,SAzMN,WA0MrB,OAAOiX,GAGT,GAAIwB,EAAe1Y,UAAUC,SA5MJ,aA6MvB,OAAOkX,GAGT,GAAIuB,EAAe1Y,UAAUC,SA/MA,iBAgN3B,MAhMsB,MAmMxB,GAAIyY,EAAe1Y,UAAUC,SAlNE,mBAmN7B,MAnMyB,SAuM3B,MAAM0Y,EAAkF,QAA1EpZ,iBAAiB4G,KAAK0R,OAAOrY,iBAAiB,iBAAiBkN,OAE7E,OAAIgM,EAAe1Y,UAAUC,SA7NP,UA8Nb0Y,EAAQ5B,GAAmBD,GAG7B6B,EAAQ1B,GAAsBD,EACvC,CAEAe,gBACE,OAAkD,OAA3C5R,KAAKyF,SAASlM,QA5ND,UA6NtB,CAEAkZ,aACE,MAAMrB,OAAEA,GAAWpR,KAAK0F,QAExB,MAAsB,iBAAX0L,EACFA,EAAOvU,MAAM,KAAK2K,KAAI9E,GAAShG,OAAO8Q,SAAS9K,EAAO,MAGzC,mBAAX0O,EACFsB,GAActB,EAAOsB,EAAY1S,KAAKyF,UAGxC2L,CACT,CAEAgB,mBACE,MAAMO,EAAwB,CAC5BC,UAAW5S,KAAKsS,gBAChBO,UAAW,CAAC,CACVxX,KAAM,kBACNyX,QAAS,CACP5B,SAAUlR,KAAK0F,QAAQwL,WAG3B,CACE7V,KAAM,SACNyX,QAAS,CACP1B,OAAQpR,KAAKyS,iBAcnB,OARIzS,KAAK2R,WAAsC,WAAzB3R,KAAK0F,QAAQyL,WACjC7N,EAAYC,iBAAiBvD,KAAK0R,MAAO,SAAU,UACnDiB,EAAsBE,UAAY,CAAC,CACjCxX,KAAM,cACN0X,SAAS,KAIN,IACFJ,KACA5W,EAAQiE,KAAK0F,QAAQ2L,aAAc,CAACsB,IAE3C,CAEAK,iBAAgBhc,IAAEA,EAAGiG,OAAEA,IACrB,MAAMqP,EAAQ9F,EAAetH,KA5QF,8DA4Q+Bc,KAAK0R,OAAO3N,QAAOhN,GAAWkC,EAAUlC,KAE7FuV,EAAMxT,QAMXsE,EAAqBkP,EAAOrP,EAAQjG,IAAQsZ,IAAiBhE,EAAMlL,SAASnE,IAAS6U,OACvF,CAGA5L,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAO+I,GAASpJ,oBAAoBnI,KAAMwE,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,CAEA0B,kBAAkB/G,GAChB,GA/TuB,IA+TnBA,EAAM0J,QAAiD,UAAf1J,EAAMsB,MAlUtC,QAkU0DtB,EAAMnI,IAC1E,OAGF,MAAMic,EAAczM,EAAetH,KAAKuR,IAExC,IAAK,MAAM7H,KAAUqK,EAAa,CAChC,MAAMC,EAAU3B,GAASpL,YAAYyC,GACrC,IAAKsK,IAAyC,IAA9BA,EAAQxN,QAAQuL,UAC9B,SAGF,MAAMkC,EAAehU,EAAMgU,eACrBC,EAAeD,EAAa/R,SAAS8R,EAAQxB,OACnD,GACEyB,EAAa/R,SAAS8R,EAAQzN,WACC,WAA9ByN,EAAQxN,QAAQuL,YAA2BmC,GACb,YAA9BF,EAAQxN,QAAQuL,WAA2BmC,EAE5C,SAIF,GAAIF,EAAQxB,MAAM5X,SAASqF,EAAMlC,UAA4B,UAAfkC,EAAMsB,MAzV1C,QAyV8DtB,EAAMnI,KAAoB,qCAAqCoO,KAAKjG,EAAMlC,OAAOiL,UACvJ,SAGF,MAAMrI,EAAgB,CAAEA,cAAeqT,EAAQzN,UAE5B,UAAftG,EAAMsB,OACRZ,EAAcoI,WAAa9I,GAG7B+T,EAAQnB,cAAclS,EACxB,CACF,CAEAqG,6BAA6B/G,GAI3B,MAAMkU,EAAU,kBAAkBjO,KAAKjG,EAAMlC,OAAOiL,SAC9CoL,EA7WS,WA6WOnU,EAAMnI,IACtBuc,EAAkB,CAAClD,GAAcC,IAAgBlP,SAASjC,EAAMnI,KAEtE,IAAKuc,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGFnU,EAAMoD,iBAGN,MAAMiR,EAAkBxT,KAAK8G,QAAQ4B,IACnC1I,KACCwG,EAAeS,KAAKjH,KAAM0I,IAAsB,IAC/ClC,EAAeY,KAAKpH,KAAM0I,IAAsB,IAChDlC,EAAeG,QAAQ+B,GAAsBvJ,EAAMW,eAAerG,YAEhExC,EAAWsa,GAASpJ,oBAAoBqL,GAE9C,GAAID,EAIF,OAHApU,EAAMsU,kBACNxc,EAASuY,YACTvY,EAAS+b,gBAAgB7T,GAIvBlI,EAASqY,aACXnQ,EAAMsU,kBACNxc,EAASsY,OACTiE,EAAgB1B,QAEpB,EAOFvR,EAAac,GAAGtI,SAAUyX,GAAwB9H,GAAsB6I,GAASmC,uBACjFnT,EAAac,GAAGtI,SAAUyX,GAAwBE,GAAea,GAASmC,uBAC1EnT,EAAac,GAAGtI,SAAUwX,GAAsBgB,GAASoC,YACzDpT,EAAac,GAAGtI,SA7Yc,6BA6YkBwY,GAASoC,YACzDpT,EAAac,GAAGtI,SAAUwX,GAAsB7H,IAAsB,SAAUvJ,GAC9EA,EAAMoD,iBACNgP,GAASpJ,oBAAoBnI,MAAM4I,QACrC,IAMA3N,EAAmBsW,ICrbnB,MAEM9C,GAAkB,OAClBmF,GAAmB,wBAEnBxP,GAAU,CACdyP,UAAW,iBACXC,cAAe,KACf7N,YAAY,EACZhN,WAAW,EACX8a,YAAa,QAGT1P,GAAc,CAClBwP,UAAW,SACXC,cAAe,kBACf7N,WAAY,UACZhN,UAAW,UACX8a,YAAa,oBAOf,MAAMC,WAAiB7P,EACrBU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAKiU,aAAc,EACnBjU,KAAKyF,SAAW,IAClB,CAGWrB,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MA3CS,UA4CX,CAGAkU,KAAKrU,GACH,IAAK6E,KAAK0F,QAAQzM,UAEhB,YADA8C,EAAQZ,GAIV6E,KAAKkU,UAEL,MAAMnd,EAAUiJ,KAAKmU,cACjBnU,KAAK0F,QAAQO,YACfxL,EAAO1D,GAGTA,EAAQ8C,UAAUoQ,IAAIwE,IAEtBzO,KAAKoU,mBAAkB,KACrBrY,EAAQZ,EAAS,GAErB,CAEAoU,KAAKpU,GACE6E,KAAK0F,QAAQzM,WAKlB+G,KAAKmU,cAActa,UAAUlC,OAAO8W,IAEpCzO,KAAKoU,mBAAkB,KACrBpU,KAAK4F,UACL7J,EAAQZ,EAAS,KARjBY,EAAQZ,EAUZ,CAEAyK,UACO5F,KAAKiU,cAIV1T,EAAaC,IAAIR,KAAKyF,SAAUmO,IAEhC5T,KAAKyF,SAAS9N,SACdqI,KAAKiU,aAAc,EACrB,CAGAE,cACE,IAAKnU,KAAKyF,SAAU,CAClB,MAAM4O,EAAWtb,SAASub,cAAc,OACxCD,EAASR,UAAY7T,KAAK0F,QAAQmO,UAC9B7T,KAAK0F,QAAQO,YACfoO,EAASxa,UAAUoQ,IAjGH,QAoGlBjK,KAAKyF,SAAW4O,CAClB,CAEA,OAAOrU,KAAKyF,QACd,CAEAf,kBAAkBF,GAGhB,OADAA,EAAOuP,YAAclb,EAAW2L,EAAOuP,aAChCvP,CACT,CAEA0P,UACE,GAAIlU,KAAKiU,YACP,OAGF,MAAMld,EAAUiJ,KAAKmU,cACrBnU,KAAK0F,QAAQqO,YAAYQ,OAAOxd,GAEhCwJ,EAAac,GAAGtK,EAAS6c,IAAiB,KACxC7X,EAAQiE,KAAK0F,QAAQoO,cAAc,IAGrC9T,KAAKiU,aAAc,CACrB,CAEAG,kBAAkBjZ,GAChBgB,EAAuBhB,EAAU6E,KAAKmU,cAAenU,KAAK0F,QAAQO,WACpE,EClIF,MAEMJ,GAAa,gBAMb2O,GAAmB,WAEnBpQ,GAAU,CACdqQ,WAAW,EACXC,YAAa,MAGTrQ,GAAc,CAClBoQ,UAAW,UACXC,YAAa,WAOf,MAAMC,WAAkBxQ,EACtBU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAK4U,WAAY,EACjB5U,KAAK6U,qBAAuB,IAC9B,CAGWzQ,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MA1CS,WA2CX,CAGAwZ,WACM9U,KAAK4U,YAIL5U,KAAK0F,QAAQ+O,WACfzU,KAAK0F,QAAQgP,YAAY5C,QAG3BvR,EAAaC,IAAIzH,SAAU8M,IAC3BtF,EAAac,GAAGtI,SArDG,wBAqDsBoG,GAASa,KAAK+U,eAAe5V,KACtEoB,EAAac,GAAGtI,SArDO,4BAqDsBoG,GAASa,KAAKgV,eAAe7V,KAE1Ea,KAAK4U,WAAY,EACnB,CAEAK,aACOjV,KAAK4U,YAIV5U,KAAK4U,WAAY,EACjBrU,EAAaC,IAAIzH,SAAU8M,IAC7B,CAGAkP,eAAe5V,GACb,MAAMuV,YAAEA,GAAgB1U,KAAK0F,QAE7B,GAAIvG,EAAMlC,SAAWlE,UAAYoG,EAAMlC,SAAWyX,GAAeA,EAAY5a,SAASqF,EAAMlC,QAC1F,OAGF,MAAMiY,EAAW1O,EAAec,kBAAkBoN,GAE1B,IAApBQ,EAASpc,OACX4b,EAAY5C,QACH9R,KAAK6U,uBAAyBL,GACvCU,EAASA,EAASpc,OAAS,GAAGgZ,QAE9BoD,EAAS,GAAGpD,OAEhB,CAEAkD,eAAe7V,GApFD,QAqFRA,EAAMnI,MAIVgJ,KAAK6U,qBAAuB1V,EAAMgW,SAAWX,GAxFzB,UAyFtB,EChGF,MAAMY,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJ3Q,cACE7E,KAAKyF,SAAW1M,SAAS8B,IAC3B,CAGA4a,WAEE,MAAMC,EAAgB3c,SAASoB,gBAAgBwb,YAC/C,OAAO/X,KAAKmM,IAAI/R,OAAO4d,WAAaF,EACtC,CAEAnG,OACE,MAAMsG,EAAQ7V,KAAKyV,WACnBzV,KAAK8V,mBAEL9V,KAAK+V,sBAAsB/V,KAAKyF,SAAU6P,IAAkBU,GAAmBA,EAAkBH,IAEjG7V,KAAK+V,sBAAsBX,GAAwBE,IAAkBU,GAAmBA,EAAkBH,IAC1G7V,KAAK+V,sBAAsBV,GAAyBE,IAAiBS,GAAmBA,EAAkBH,GAC5G,CAEAI,QACEjW,KAAKkW,wBAAwBlW,KAAKyF,SAAU,YAC5CzF,KAAKkW,wBAAwBlW,KAAKyF,SAAU6P,IAC5CtV,KAAKkW,wBAAwBd,GAAwBE,IACrDtV,KAAKkW,wBAAwBb,GAAyBE,GACxD,CAEAY,gBACE,OAAOnW,KAAKyV,WAAa,CAC3B,CAGAK,mBACE9V,KAAKoW,sBAAsBpW,KAAKyF,SAAU,YAC1CzF,KAAKyF,SAASqK,MAAMuG,SAAW,QACjC,CAEAN,sBAAsBhe,EAAUue,EAAenb,GAC7C,MAAMob,EAAiBvW,KAAKyV,WAW5BzV,KAAKwW,2BAA2Bze,GAVHhB,IAC3B,GAAIA,IAAYiJ,KAAKyF,UAAYzN,OAAO4d,WAAa7e,EAAQ4e,YAAcY,EACzE,OAGFvW,KAAKoW,sBAAsBrf,EAASuf,GACpC,MAAMN,EAAkBhe,OAAOoB,iBAAiBrC,GAASsC,iBAAiBid,GAC1Evf,EAAQ+Y,MAAM2G,YAAYH,EAAgB,GAAEnb,EAASuB,OAAOC,WAAWqZ,QAAsB,GAIjG,CAEAI,sBAAsBrf,EAASuf,GAC7B,MAAMI,EAAc3f,EAAQ+Y,MAAMzW,iBAAiBid,GAC/CI,GACFpT,EAAYC,iBAAiBxM,EAASuf,EAAeI,EAEzD,CAEAR,wBAAwBne,EAAUue,GAahCtW,KAAKwW,2BAA2Bze,GAZHhB,IAC3B,MAAM2L,EAAQY,EAAYY,iBAAiBnN,EAASuf,GAEtC,OAAV5T,GAKJY,EAAYG,oBAAoB1M,EAASuf,GACzCvf,EAAQ+Y,MAAM2G,YAAYH,EAAe5T,IALvC3L,EAAQ+Y,MAAM6G,eAAeL,EAKgB,GAInD,CAEAE,2BAA2Bze,EAAU6e,GACnC,GAAIne,EAAUV,GACZ6e,EAAS7e,QAIX,IAAK,MAAM8e,KAAOrQ,EAAetH,KAAKnH,EAAUiI,KAAKyF,UACnDmR,EAASC,EAEb,EC1FF,MAEMhR,GAAa,YAMbiR,GAAgB,kBAChBC,GAAc,gBAQdC,GAAkB,aAElBvI,GAAkB,OAClBwI,GAAoB,eAOpB7S,GAAU,CACdiQ,UAAU,EACVvC,OAAO,EACP9G,UAAU,GAGN3G,GAAc,CAClBgQ,SAAU,mBACVvC,MAAO,UACP9G,SAAU,WAOZ,MAAMkM,WAAc3R,EAClBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKmX,QAAU3Q,EAAeG,QAxBV,gBAwBmC3G,KAAKyF,UAC5DzF,KAAKoX,UAAYpX,KAAKqX,sBACtBrX,KAAKsX,WAAatX,KAAKuX,uBACvBvX,KAAKsP,UAAW,EAChBtP,KAAK8O,kBAAmB,EACxB9O,KAAKwX,WAAa,IAAIhC,GAEtBxV,KAAK4L,oBACP,CAGWxH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MAnES,OAoEX,CAGAsN,OAAO/I,GACL,OAAOG,KAAKsP,SAAWtP,KAAKuP,OAASvP,KAAKwP,KAAK3P,EACjD,CAEA2P,KAAK3P,GACCG,KAAKsP,UAAYtP,KAAK8O,kBAIRvO,EAAasB,QAAQ7B,KAAKyF,SAAUsR,GAAY,CAChElX,kBAGYoC,mBAIdjC,KAAKsP,UAAW,EAChBtP,KAAK8O,kBAAmB,EAExB9O,KAAKwX,WAAWjI,OAEhBxW,SAAS8B,KAAKhB,UAAUoQ,IAAI+M,IAE5BhX,KAAKyX,gBAELzX,KAAKoX,UAAU5H,MAAK,IAAMxP,KAAK0X,aAAa7X,KAC9C,CAEA0P,OACOvP,KAAKsP,WAAYtP,KAAK8O,mBAITvO,EAAasB,QAAQ7B,KAAKyF,SAnG5B,iBAqGFxD,mBAIdjC,KAAKsP,UAAW,EAChBtP,KAAK8O,kBAAmB,EACxB9O,KAAKsX,WAAWrC,aAEhBjV,KAAKyF,SAAS5L,UAAUlC,OAAO8W,IAE/BzO,KAAKgG,gBAAe,IAAMhG,KAAK2X,cAAc3X,KAAKyF,SAAUzF,KAAKmO,gBACnE,CAEAvI,UACErF,EAAaC,IAAIxI,OAAQ6N,IACzBtF,EAAaC,IAAIR,KAAKmX,QAAStR,IAE/B7F,KAAKoX,UAAUxR,UACf5F,KAAKsX,WAAWrC,aAEhBzP,MAAMI,SACR,CAEAgS,eACE5X,KAAKyX,eACP,CAGAJ,sBACE,OAAO,IAAIrD,GAAS,CAClB/a,UAAW6H,QAAQd,KAAK0F,QAAQ2O,UAChCpO,WAAYjG,KAAKmO,eAErB,CAEAoJ,uBACE,OAAO,IAAI5C,GAAU,CACnBD,YAAa1U,KAAKyF,UAEtB,CAEAiS,aAAa7X,GAEN9G,SAAS8B,KAAKf,SAASkG,KAAKyF,WAC/B1M,SAAS8B,KAAK0Z,OAAOvU,KAAKyF,UAG5BzF,KAAKyF,SAASqK,MAAMqB,QAAU,QAC9BnR,KAAKyF,SAAS/B,gBAAgB,eAC9B1D,KAAKyF,SAASjC,aAAa,cAAc,GACzCxD,KAAKyF,SAASjC,aAAa,OAAQ,UACnCxD,KAAKyF,SAASoS,UAAY,EAE1B,MAAMC,EAAYtR,EAAeG,QAxIT,cAwIsC3G,KAAKmX,SAC/DW,IACFA,EAAUD,UAAY,GAGxBpd,EAAOuF,KAAKyF,UAEZzF,KAAKyF,SAAS5L,UAAUoQ,IAAIwE,IAa5BzO,KAAKgG,gBAXsB+R,KACrB/X,KAAK0F,QAAQoM,OACf9R,KAAKsX,WAAWxC,WAGlB9U,KAAK8O,kBAAmB,EACxBvO,EAAasB,QAAQ7B,KAAKyF,SArKX,iBAqKkC,CAC/C5F,iBACA,GAGoCG,KAAKmX,QAASnX,KAAKmO,cAC7D,CAEAvC,qBACErL,EAAac,GAAGrB,KAAKyF,SA1KM,4BA0K2BtG,IApLvC,WAqLTA,EAAMnI,MAINgJ,KAAK0F,QAAQsF,SACfhL,KAAKuP,OAIPvP,KAAKgY,6BAA4B,IAGnCzX,EAAac,GAAGrJ,OA1LE,mBA0LoB,KAChCgI,KAAKsP,WAAatP,KAAK8O,kBACzB9O,KAAKyX,eACP,IAGFlX,EAAac,GAAGrB,KAAKyF,SA9LQ,8BA8L2BtG,IAEtDoB,EAAae,IAAItB,KAAKyF,SAjMC,0BAiM8BwS,IAC/CjY,KAAKyF,WAAatG,EAAMlC,QAAU+C,KAAKyF,WAAawS,EAAOhb,SAIjC,WAA1B+C,KAAK0F,QAAQ2O,SAKbrU,KAAK0F,QAAQ2O,UACfrU,KAAKuP,OALLvP,KAAKgY,6BAMP,GACA,GAEN,CAEAL,aACE3X,KAAKyF,SAASqK,MAAMqB,QAAU,OAC9BnR,KAAKyF,SAASjC,aAAa,eAAe,GAC1CxD,KAAKyF,SAAS/B,gBAAgB,cAC9B1D,KAAKyF,SAAS/B,gBAAgB,QAC9B1D,KAAK8O,kBAAmB,EAExB9O,KAAKoX,UAAU7H,MAAK,KAClBxW,SAAS8B,KAAKhB,UAAUlC,OAAOqf,IAC/BhX,KAAKkY,oBACLlY,KAAKwX,WAAWvB,QAChB1V,EAAasB,QAAQ7B,KAAKyF,SAAUqR,GAAa,GAErD,CAEA3I,cACE,OAAOnO,KAAKyF,SAAS5L,UAAUC,SA5NX,OA6NtB,CAEAke,6BAEE,GADkBzX,EAAasB,QAAQ7B,KAAKyF,SA3OlB,0BA4OZxD,iBACZ,OAGF,MAAMkW,EAAqBnY,KAAKyF,SAAS2S,aAAerf,SAASoB,gBAAgBke,aAC3EC,EAAmBtY,KAAKyF,SAASqK,MAAMyI,UAEpB,WAArBD,GAAiCtY,KAAKyF,SAAS5L,UAAUC,SAASmd,MAIjEkB,IACHnY,KAAKyF,SAASqK,MAAMyI,UAAY,UAGlCvY,KAAKyF,SAAS5L,UAAUoQ,IAAIgN,IAC5BjX,KAAKgG,gBAAe,KAClBhG,KAAKyF,SAAS5L,UAAUlC,OAAOsf,IAC/BjX,KAAKgG,gBAAe,KAClBhG,KAAKyF,SAASqK,MAAMyI,UAAYD,CAAgB,GAC/CtY,KAAKmX,QAAQ,GACfnX,KAAKmX,SAERnX,KAAKyF,SAASqM,QAChB,CAMA2F,gBACE,MAAMU,EAAqBnY,KAAKyF,SAAS2S,aAAerf,SAASoB,gBAAgBke,aAC3E9B,EAAiBvW,KAAKwX,WAAW/B,WACjC+C,EAAoBjC,EAAiB,EAE3C,GAAIiC,IAAsBL,EAAoB,CAC5C,MAAMpT,EAAWhK,IAAU,cAAgB,eAC3CiF,KAAKyF,SAASqK,MAAM/K,GAAa,GAAEwR,KACrC,CAEA,IAAKiC,GAAqBL,EAAoB,CAC5C,MAAMpT,EAAWhK,IAAU,eAAiB,cAC5CiF,KAAKyF,SAASqK,MAAM/K,GAAa,GAAEwR,KACrC,CACF,CAEA2B,oBACElY,KAAKyF,SAASqK,MAAM2I,YAAc,GAClCzY,KAAKyF,SAASqK,MAAM4I,aAAe,EACrC,CAGAxS,uBAAuB1B,EAAQ3E,GAC7B,OAAOG,KAAKuI,MAAK,WACf,MAAMC,EAAO0O,GAAM/O,oBAAoBnI,KAAMwE,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQ3E,EANb,CAOF,GACF,EAOFU,EAAac,GAAGtI,SA7Sc,0BAUD,4BAmSyC,SAAUoG,GAC9E,MAAMlC,EAASuJ,EAAeoB,uBAAuB5H,MAEjD,CAAC,IAAK,QAAQoB,SAASpB,KAAKkI,UAC9B/I,EAAMoD,iBAGRhC,EAAae,IAAIrE,EAAQ8Z,IAAY4B,IAC/BA,EAAU1W,kBAKd1B,EAAae,IAAIrE,EAAQ6Z,IAAc,KACjC7d,EAAU+G,OACZA,KAAK8R,OACP,GACA,IAIJ,MAAM8G,EAAcpS,EAAeG,QA3Tf,eA4ThBiS,GACF1B,GAAM/Q,YAAYyS,GAAarJ,OAGpB2H,GAAM/O,oBAAoBlL,GAElC2L,OAAO5I,KACd,IAEA8H,EAAqBoP,IAMrBjc,EAAmBic,IC7VnB,MAOMzI,GAAkB,OAClBoK,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAKhBC,GAAwB,6BACxBlC,GAAgB,sBAOhB1S,GAAU,CACdiQ,UAAU,EACVrJ,UAAU,EACViO,QAAQ,GAGJ5U,GAAc,CAClBgQ,SAAU,mBACVrJ,SAAU,UACViO,OAAQ,WAOV,MAAMC,WAAkB3T,EACtBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKsP,UAAW,EAChBtP,KAAKoX,UAAYpX,KAAKqX,sBACtBrX,KAAKsX,WAAatX,KAAKuX,uBACvBvX,KAAK4L,oBACP,CAGWxH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MA5DS,WA6DX,CAGAsN,OAAO/I,GACL,OAAOG,KAAKsP,SAAWtP,KAAKuP,OAASvP,KAAKwP,KAAK3P,EACjD,CAEA2P,KAAK3P,GACCG,KAAKsP,UAIS/O,EAAasB,QAAQ7B,KAAKyF,SA5D5B,oBA4DkD,CAAE5F,kBAEtDoC,mBAIdjC,KAAKsP,UAAW,EAChBtP,KAAKoX,UAAU5H,OAEVxP,KAAK0F,QAAQuT,SAChB,IAAIzD,IAAkBjG,OAGxBvP,KAAKyF,SAASjC,aAAa,cAAc,GACzCxD,KAAKyF,SAASjC,aAAa,OAAQ,UACnCxD,KAAKyF,SAAS5L,UAAUoQ,IAAI4O,IAY5B7Y,KAAKgG,gBAVoBkI,KAClBlO,KAAK0F,QAAQuT,SAAUjZ,KAAK0F,QAAQ2O,UACvCrU,KAAKsX,WAAWxC,WAGlB9U,KAAKyF,SAAS5L,UAAUoQ,IAAIwE,IAC5BzO,KAAKyF,SAAS5L,UAAUlC,OAAOkhB,IAC/BtY,EAAasB,QAAQ7B,KAAKyF,SAnFX,qBAmFkC,CAAE5F,iBAAgB,GAG/BG,KAAKyF,UAAU,GACvD,CAEA8J,OACOvP,KAAKsP,WAIQ/O,EAAasB,QAAQ7B,KAAKyF,SA7F5B,qBA+FFxD,mBAIdjC,KAAKsX,WAAWrC,aAChBjV,KAAKyF,SAAS0T,OACdnZ,KAAKsP,UAAW,EAChBtP,KAAKyF,SAAS5L,UAAUoQ,IAAI6O,IAC5B9Y,KAAKoX,UAAU7H,OAcfvP,KAAKgG,gBAZoBoT,KACvBpZ,KAAKyF,SAAS5L,UAAUlC,OAAO8W,GAAiBqK,IAChD9Y,KAAKyF,SAAS/B,gBAAgB,cAC9B1D,KAAKyF,SAAS/B,gBAAgB,QAEzB1D,KAAK0F,QAAQuT,SAChB,IAAIzD,IAAkBS,QAGxB1V,EAAasB,QAAQ7B,KAAKyF,SAAUqR,GAAa,GAGb9W,KAAKyF,UAAU,IACvD,CAEAG,UACE5F,KAAKoX,UAAUxR,UACf5F,KAAKsX,WAAWrC,aAChBzP,MAAMI,SACR,CAGAyR,sBACE,MAUMpe,EAAY6H,QAAQd,KAAK0F,QAAQ2O,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtB5a,YACAgN,YAAY,EACZ8N,YAAa/T,KAAKyF,SAAShM,WAC3Bqa,cAAe7a,EAjBK6a,KACU,WAA1B9T,KAAK0F,QAAQ2O,SAKjBrU,KAAKuP,OAJHhP,EAAasB,QAAQ7B,KAAKyF,SAAUuT,GAI3B,EAWgC,MAE/C,CAEAzB,uBACE,OAAO,IAAI5C,GAAU,CACnBD,YAAa1U,KAAKyF,UAEtB,CAEAmG,qBACErL,EAAac,GAAGrB,KAAKyF,SAvJM,gCAuJ2BtG,IAtKvC,WAuKTA,EAAMnI,MAINgJ,KAAK0F,QAAQsF,SACfhL,KAAKuP,OAIPhP,EAAasB,QAAQ7B,KAAKyF,SAAUuT,IAAqB,GAE7D,CAGA9S,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAO0Q,GAAU/Q,oBAAoBnI,KAAMwE,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxE,KANb,CAOF,GACF,EAOFO,EAAac,GAAGtI,SA5Lc,8BAGD,gCAyLyC,SAAUoG,GAC9E,MAAMlC,EAASuJ,EAAeoB,uBAAuB5H,MAMrD,GAJI,CAAC,IAAK,QAAQoB,SAASpB,KAAKkI,UAC9B/I,EAAMoD,iBAGJ7I,EAAWsG,MACb,OAGFO,EAAae,IAAIrE,EAAQ6Z,IAAc,KAEjC7d,EAAU+G,OACZA,KAAK8R,OACP,IAIF,MAAM8G,EAAcpS,EAAeG,QAAQoS,IACvCH,GAAeA,IAAgB3b,GACjCic,GAAU/S,YAAYyS,GAAarJ,OAGxB2J,GAAU/Q,oBAAoBlL,GACtC2L,OAAO5I,KACd,IAEAO,EAAac,GAAGrJ,OAvOa,8BAuOgB,KAC3C,IAAK,MAAMD,KAAYyO,EAAetH,KAAK6Z,IACzCG,GAAU/Q,oBAAoBpQ,GAAUyX,MAC1C,IAGFjP,EAAac,GAAGrJ,OA/NM,uBA+NgB,KACpC,IAAK,MAAMjB,KAAWyP,EAAetH,KAAK,gDACG,UAAvC9F,iBAAiBrC,GAASsiB,UAC5BH,GAAU/Q,oBAAoBpR,GAASwY,MAE3C,IAGFzH,EAAqBoR,IAMrBje,EAAmBie,IChRnB,MAAMI,GAAgB,IAAI9a,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAQI+a,GAAmB,iEAOnBC,GAAmB,qIAEnBC,GAAmBA,CAACC,EAAWC,KACnC,MAAMC,EAAgBF,EAAUG,SAASxW,cAEzC,OAAIsW,EAAqBvY,SAASwY,IAC5BN,GAAcpiB,IAAI0iB,IACb9Y,QAAQyY,GAAiBnU,KAAKsU,EAAUI,YAAcN,GAAiBpU,KAAKsU,EAAUI,YAO1FH,EAAqB5V,QAAOgW,GAAkBA,aAA0B5U,SAC5E6U,MAAKC,GAASA,EAAM7U,KAAKwU,IAAe,EAMhCM,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAJP,kBAK7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHnO,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDoO,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IChEA1X,GAAU,CACd2X,UAAW7B,GACX8B,QAAS,GACTC,WAAY,GACZC,MAAM,EACNC,UAAU,EACVC,WAAY,KACZC,SAAU,eAGNhY,GAAc,CAClB0X,UAAW,SACXC,QAAS,SACTC,WAAY,oBACZC,KAAM,UACNC,SAAU,UACVC,WAAY,kBACZC,SAAU,UAGNC,GAAqB,CACzBC,MAAO,iCACPxkB,SAAU,oBAOZ,MAAMykB,WAAwBrY,EAC5BU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,EACjC,CAGWJ,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MA/CS,iBAgDX,CAGAmhB,aACE,OAAOzd,OAAOC,OAAOe,KAAK0F,QAAQsW,SAC/BxU,KAAIhD,GAAUxE,KAAK0c,yBAAyBlY,KAC5CT,OAAOjD,QACZ,CAEA6b,aACE,OAAO3c,KAAKyc,aAAa3jB,OAAS,CACpC,CAEA8jB,cAAcZ,GAGZ,OAFAhc,KAAK6c,cAAcb,GACnBhc,KAAK0F,QAAQsW,QAAU,IAAKhc,KAAK0F,QAAQsW,WAAYA,GAC9Chc,IACT,CAEA8c,SACE,MAAMC,EAAkBhkB,SAASub,cAAc,OAC/CyI,EAAgBC,UAAYhd,KAAKid,eAAejd,KAAK0F,QAAQ2W,UAE7D,IAAK,MAAOtkB,EAAUmlB,KAASle,OAAOmC,QAAQnB,KAAK0F,QAAQsW,SACzDhc,KAAKmd,YAAYJ,EAAiBG,EAAMnlB,GAG1C,MAAMskB,EAAWU,EAAgBnW,SAAS,GACpCqV,EAAajc,KAAK0c,yBAAyB1c,KAAK0F,QAAQuW,YAM9D,OAJIA,GACFI,EAASxiB,UAAUoQ,OAAOgS,EAAWpf,MAAM,MAGtCwf,CACT,CAGA1X,iBAAiBH,GACfgB,MAAMb,iBAAiBH,GACvBxE,KAAK6c,cAAcrY,EAAOwX,QAC5B,CAEAa,cAAcO,GACZ,IAAK,MAAOrlB,EAAUikB,KAAYhd,OAAOmC,QAAQic,GAC/C5X,MAAMb,iBAAiB,CAAE5M,WAAUwkB,MAAOP,GAAWM,GAEzD,CAEAa,YAAYd,EAAUL,EAASjkB,GAC7B,MAAMslB,EAAkB7W,EAAeG,QAAQ5O,EAAUskB,GAEpDgB,KAILrB,EAAUhc,KAAK0c,yBAAyBV,IAOpCvjB,EAAUujB,GACZhc,KAAKsd,sBAAsBzkB,EAAWmjB,GAAUqB,GAI9Crd,KAAK0F,QAAQwW,KACfmB,EAAgBL,UAAYhd,KAAKid,eAAejB,GAIlDqB,EAAgBE,YAAcvB,EAd5BqB,EAAgB1lB,SAepB,CAEAslB,eAAeG,GACb,OAAOpd,KAAK0F,QAAQyW,SDvDjB,SAAsBqB,EAAYzB,EAAW0B,GAClD,IAAKD,EAAW1kB,OACd,OAAO0kB,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAI1lB,OAAO2lB,WACKC,gBAAgBJ,EAAY,aACxDtI,EAAW,GAAGzO,UAAUiX,EAAgB7iB,KAAKuF,iBAAiB,MAEpE,IAAK,MAAMrJ,KAAWme,EAAU,CAC9B,MAAM2I,EAAc9mB,EAAQ8iB,SAASxW,cAErC,IAAKrE,OAAOtH,KAAKqkB,GAAW3a,SAASyc,GAAc,CACjD9mB,EAAQY,SAER,QACF,CAEA,MAAMmmB,EAAgB,GAAGrX,UAAU1P,EAAQ6M,YACrCma,EAAoB,GAAGtX,OAAOsV,EAAU,MAAQ,GAAIA,EAAU8B,IAAgB,IAEpF,IAAK,MAAMnE,KAAaoE,EACjBrE,GAAiBC,EAAWqE,IAC/BhnB,EAAQ2M,gBAAgBgW,EAAUG,SAGxC,CAEA,OAAO6D,EAAgB7iB,KAAKmiB,SAC9B,CCsBmCgB,CAAaZ,EAAKpd,KAAK0F,QAAQqW,UAAW/b,KAAK0F,QAAQ0W,YAAcgB,CACtG,CAEAV,yBAAyBU,GACvB,OAAOrhB,EAAQqhB,EAAK,CAACpd,MACvB,CAEAsd,sBAAsBvmB,EAASsmB,GAC7B,GAAIrd,KAAK0F,QAAQwW,KAGf,OAFAmB,EAAgBL,UAAY,QAC5BK,EAAgB9I,OAAOxd,GAIzBsmB,EAAgBE,YAAcxmB,EAAQwmB,WACxC,ECzIF,MACMU,GAAwB,IAAIzf,IAAI,CAAC,WAAY,YAAa,eAE1D0f,GAAkB,OAElBzP,GAAkB,OAGlB0P,GAAkB,SAElBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAehBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO3jB,IAAU,OAAS,QAC1B4jB,OAAQ,SACRC,KAAM7jB,IAAU,QAAU,QAGtBqJ,GAAU,CACd2X,UAAW7B,GACX2E,WAAW,EACX3N,SAAU,kBACV4N,WAAW,EACXC,YAAa,GACbC,MAAO,EACPC,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C/C,MAAM,EACN9K,OAAQ,CAAC,EAAG,GACZwB,UAAW,MACXvB,aAAc,KACd8K,UAAU,EACVC,WAAY,KACZrkB,UAAU,EACVskB,SAAU,+GAIV6C,MAAO,GACPrd,QAAS,eAGLwC,GAAc,CAClB0X,UAAW,SACX8C,UAAW,UACX3N,SAAU,mBACV4N,UAAW,2BACXC,YAAa,oBACbC,MAAO,kBACPC,mBAAoB,QACpB/C,KAAM,UACN9K,OAAQ,0BACRwB,UAAW,oBACXvB,aAAc,yBACd8K,SAAU,UACVC,WAAY,kBACZrkB,SAAU,mBACVskB,SAAU,SACV6C,MAAO,4BACPrd,QAAS,UAOX,MAAMsd,WAAgB5Z,EACpBV,YAAY9N,EAASyN,GACnB,QAAsB,IAAX0N,EACT,MAAM,IAAI7M,UAAU,+DAGtBG,MAAMzO,EAASyN,GAGfxE,KAAKof,YAAa,EAClBpf,KAAKqf,SAAW,EAChBrf,KAAKsf,WAAa,KAClBtf,KAAKuf,eAAiB,GACtBvf,KAAKwR,QAAU,KACfxR,KAAKwf,iBAAmB,KACxBxf,KAAKyf,YAAc,KAGnBzf,KAAK0f,IAAM,KAEX1f,KAAK2f,gBAEA3f,KAAK0F,QAAQ3N,UAChBiI,KAAK4f,WAET,CAGWxb,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MAxHS,SAyHX,CAGAukB,SACE7f,KAAKof,YAAa,CACpB,CAEAU,UACE9f,KAAKof,YAAa,CACpB,CAEAW,gBACE/f,KAAKof,YAAcpf,KAAKof,UAC1B,CAEAxW,SACO5I,KAAKof,aAIVpf,KAAKuf,eAAeS,OAAShgB,KAAKuf,eAAeS,MAC7ChgB,KAAKsP,WACPtP,KAAKigB,SAIPjgB,KAAKkgB,SACP,CAEAta,UACEuH,aAAanN,KAAKqf,UAElB9e,EAAaC,IAAIR,KAAKyF,SAASlM,QAAQ4kB,IAAiBC,GAAkBpe,KAAKmgB,mBAE3EngB,KAAKyF,SAASxL,aAAa,2BAC7B+F,KAAKyF,SAASjC,aAAa,QAASxD,KAAKyF,SAASxL,aAAa,2BAGjE+F,KAAKogB,iBACL5a,MAAMI,SACR,CAEA4J,OACE,GAAoC,SAAhCxP,KAAKyF,SAASqK,MAAMqB,QACtB,MAAM,IAAI7M,MAAM,uCAGlB,IAAMtE,KAAKqgB,mBAAoBrgB,KAAKof,WAClC,OAGF,MAAMzG,EAAYpY,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYgJ,UAzJxD,SA2JTyS,GADapmB,EAAe8F,KAAKyF,WACLzF,KAAKyF,SAAS8a,cAAcpmB,iBAAiBL,SAASkG,KAAKyF,UAE7F,GAAIkT,EAAU1W,mBAAqBqe,EACjC,OAIFtgB,KAAKogB,iBAEL,MAAMV,EAAM1f,KAAKwgB,iBAEjBxgB,KAAKyF,SAASjC,aAAa,mBAAoBkc,EAAIzlB,aAAa,OAEhE,MAAM6kB,UAAEA,GAAc9e,KAAK0F,QAe3B,GAbK1F,KAAKyF,SAAS8a,cAAcpmB,gBAAgBL,SAASkG,KAAK0f,OAC7DZ,EAAUvK,OAAOmL,GACjBnf,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYgJ,UA1KpC,cA6KnB7N,KAAKwR,QAAUxR,KAAK6R,cAAc6N,GAElCA,EAAI7lB,UAAUoQ,IAAIwE,IAMd,iBAAkB1V,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG0P,UAAU1N,SAAS8B,KAAK+L,UAC/CrG,EAAac,GAAGtK,EAAS,YAAayD,GAc1CwF,KAAKgG,gBAVYgK,KACfzP,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYgJ,UA7LvC,WA+LU,IAApB7N,KAAKsf,YACPtf,KAAKigB,SAGPjgB,KAAKsf,YAAa,CAAK,GAGKtf,KAAK0f,IAAK1f,KAAKmO,cAC/C,CAEAoB,OACE,GAAKvP,KAAKsP,aAIQ/O,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYgJ,UAjNxD,SAkND5L,iBAAd,CASA,GALYjC,KAAKwgB,iBACb3mB,UAAUlC,OAAO8W,IAIjB,iBAAkB1V,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG0P,UAAU1N,SAAS8B,KAAK+L,UAC/CrG,EAAaC,IAAIzJ,EAAS,YAAayD,GAI3CwF,KAAKuf,eAA4B,OAAI,EACrCvf,KAAKuf,eAA4B,OAAI,EACrCvf,KAAKuf,eAA4B,OAAI,EACrCvf,KAAKsf,WAAa,KAelBtf,KAAKgG,gBAbYgK,KACXhQ,KAAKygB,yBAIJzgB,KAAKsf,YACRtf,KAAKogB,iBAGPpgB,KAAKyF,SAAS/B,gBAAgB,oBAC9BnD,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYgJ,UA/OtC,WA+O8D,GAGjD7N,KAAK0f,IAAK1f,KAAKmO,cA/B7C,CAgCF,CAEA8D,SACMjS,KAAKwR,SACPxR,KAAKwR,QAAQS,QAEjB,CAGAoO,iBACE,OAAOvf,QAAQd,KAAK0gB,YACtB,CAEAF,iBAKE,OAJKxgB,KAAK0f,MACR1f,KAAK0f,IAAM1f,KAAK2gB,kBAAkB3gB,KAAKyf,aAAezf,KAAK4gB,2BAGtD5gB,KAAK0f,GACd,CAEAiB,kBAAkB3E,GAChB,MAAM0D,EAAM1f,KAAK6gB,oBAAoB7E,GAASc,SAG9C,IAAK4C,EACH,OAAO,KAGTA,EAAI7lB,UAAUlC,OAAOumB,GAAiBzP,IAEtCiR,EAAI7lB,UAAUoQ,IAAK,MAAKjK,KAAK6E,YAAYvJ,aAEzC,MAAMwlB,EpBnRKC,KACb,GACEA,GAAUnjB,KAAKojB,MAjCH,IAiCSpjB,KAAKqjB,gBACnBloB,SAASmoB,eAAeH,IAEjC,OAAOA,CAAM,EoB8QGI,CAAOnhB,KAAK6E,YAAYvJ,MAAMyH,WAQ5C,OANA2c,EAAIlc,aAAa,KAAMsd,GAEnB9gB,KAAKmO,eACPuR,EAAI7lB,UAAUoQ,IAAIiU,IAGbwB,CACT,CAEA0B,WAAWpF,GACThc,KAAKyf,YAAczD,EACfhc,KAAKsP,aACPtP,KAAKogB,iBACLpgB,KAAKwP,OAET,CAEAqR,oBAAoB7E,GAalB,OAZIhc,KAAKwf,iBACPxf,KAAKwf,iBAAiB5C,cAAcZ,GAEpChc,KAAKwf,iBAAmB,IAAIhD,GAAgB,IACvCxc,KAAK0F,QAGRsW,UACAC,WAAYjc,KAAK0c,yBAAyB1c,KAAK0F,QAAQqZ,eAIpD/e,KAAKwf,gBACd,CAEAoB,yBACE,MAAO,CACL,iBAA0B5gB,KAAK0gB,YAEnC,CAEAA,YACE,OAAO1gB,KAAK0c,yBAAyB1c,KAAK0F,QAAQwZ,QAAUlf,KAAKyF,SAASxL,aAAa,yBACzF,CAGAonB,6BAA6BliB,GAC3B,OAAOa,KAAK6E,YAAYsD,oBAAoBhJ,EAAMW,eAAgBE,KAAKshB,qBACzE,CAEAnT,cACE,OAAOnO,KAAK0F,QAAQmZ,WAAc7e,KAAK0f,KAAO1f,KAAK0f,IAAI7lB,UAAUC,SAASokB,GAC5E,CAEA5O,WACE,OAAOtP,KAAK0f,KAAO1f,KAAK0f,IAAI7lB,UAAUC,SAAS2U,GACjD,CAEAoD,cAAc6N,GACZ,MAAM9M,EAAY7W,EAAQiE,KAAK0F,QAAQkN,UAAW,CAAC5S,KAAM0f,EAAK1f,KAAKyF,WAC7D8b,EAAahD,GAAc3L,EAAUtN,eAC3C,OAAO4M,EAAOG,aAAarS,KAAKyF,SAAUia,EAAK1f,KAAKoS,iBAAiBmP,GACvE,CAEA9O,aACE,MAAMrB,OAAEA,GAAWpR,KAAK0F,QAExB,MAAsB,iBAAX0L,EACFA,EAAOvU,MAAM,KAAK2K,KAAI9E,GAAShG,OAAO8Q,SAAS9K,EAAO,MAGzC,mBAAX0O,EACFsB,GAActB,EAAOsB,EAAY1S,KAAKyF,UAGxC2L,CACT,CAEAsL,yBAAyBU,GACvB,OAAOrhB,EAAQqhB,EAAK,CAACpd,KAAKyF,UAC5B,CAEA2M,iBAAiBmP,GACf,MAAM5O,EAAwB,CAC5BC,UAAW2O,EACX1O,UAAW,CACT,CACExX,KAAM,OACNyX,QAAS,CACPmM,mBAAoBjf,KAAK0F,QAAQuZ,qBAGrC,CACE5jB,KAAM,SACNyX,QAAS,CACP1B,OAAQpR,KAAKyS,eAGjB,CACEpX,KAAM,kBACNyX,QAAS,CACP5B,SAAUlR,KAAK0F,QAAQwL,WAG3B,CACE7V,KAAM,QACNyX,QAAS,CACP/b,QAAU,IAAGiJ,KAAK6E,YAAYvJ,eAGlC,CACED,KAAM,kBACN0X,SAAS,EACTyO,MAAO,aACPhmB,GAAIgN,IAGFxI,KAAKwgB,iBAAiBhd,aAAa,wBAAyBgF,EAAKiZ,MAAM7O,UAAU,KAMzF,MAAO,IACFD,KACA5W,EAAQiE,KAAK0F,QAAQ2L,aAAc,CAACsB,IAE3C,CAEAgN,gBACE,MAAM+B,EAAW1hB,KAAK0F,QAAQ7D,QAAQhF,MAAM,KAE5C,IAAK,MAAMgF,KAAW6f,EACpB,GAAgB,UAAZ7f,EACFtB,EAAac,GAAGrB,KAAKyF,SAAUzF,KAAK6E,YAAYgJ,UAtZpC,SAsZ4D7N,KAAK0F,QAAQ3N,UAAUoH,IAC7Ea,KAAKqhB,6BAA6BliB,GAC1CyJ,QAAQ,SAEb,GAjaU,WAiaN/G,EAA4B,CACrC,MAAM8f,EAAU9f,IAAYwc,GAC1Bre,KAAK6E,YAAYgJ,UAzZF,cA0Zf7N,KAAK6E,YAAYgJ,UA5ZL,WA6ZR+T,EAAW/f,IAAYwc,GAC3Bre,KAAK6E,YAAYgJ,UA3ZF,cA4Zf7N,KAAK6E,YAAYgJ,UA9ZJ,YAgaftN,EAAac,GAAGrB,KAAKyF,SAAUkc,EAAS3hB,KAAK0F,QAAQ3N,UAAUoH,IAC7D,MAAM+T,EAAUlT,KAAKqhB,6BAA6BliB,GAClD+T,EAAQqM,eAA8B,YAAfpgB,EAAMsB,KAAqB6d,GAAgBD,KAAiB,EACnFnL,EAAQgN,QAAQ,IAElB3f,EAAac,GAAGrB,KAAKyF,SAAUmc,EAAU5hB,KAAK0F,QAAQ3N,UAAUoH,IAC9D,MAAM+T,EAAUlT,KAAKqhB,6BAA6BliB,GAClD+T,EAAQqM,eAA8B,aAAfpgB,EAAMsB,KAAsB6d,GAAgBD,IACjEnL,EAAQzN,SAAS3L,SAASqF,EAAMU,eAElCqT,EAAQ+M,QAAQ,GAEpB,CAGFjgB,KAAKmgB,kBAAoB,KACnBngB,KAAKyF,UACPzF,KAAKuP,MACP,EAGFhP,EAAac,GAAGrB,KAAKyF,SAASlM,QAAQ4kB,IAAiBC,GAAkBpe,KAAKmgB,kBAChF,CAEAP,YACE,MAAMV,EAAQlf,KAAKyF,SAASxL,aAAa,SAEpCilB,IAIAlf,KAAKyF,SAASxL,aAAa,eAAkB+F,KAAKyF,SAAS8X,YAAYhX,QAC1EvG,KAAKyF,SAASjC,aAAa,aAAc0b,GAG3Clf,KAAKyF,SAASjC,aAAa,yBAA0B0b,GACrDlf,KAAKyF,SAAS/B,gBAAgB,SAChC,CAEAwc,SACMlgB,KAAKsP,YAActP,KAAKsf,WAC1Btf,KAAKsf,YAAa,GAIpBtf,KAAKsf,YAAa,EAElBtf,KAAK6hB,aAAY,KACX7hB,KAAKsf,YACPtf,KAAKwP,MACP,GACCxP,KAAK0F,QAAQsZ,MAAMxP,MACxB,CAEAyQ,SACMjgB,KAAKygB,yBAITzgB,KAAKsf,YAAa,EAElBtf,KAAK6hB,aAAY,KACV7hB,KAAKsf,YACRtf,KAAKuP,MACP,GACCvP,KAAK0F,QAAQsZ,MAAMzP,MACxB,CAEAsS,YAAY7kB,EAAS8kB,GACnB3U,aAAanN,KAAKqf,UAClBrf,KAAKqf,SAAWliB,WAAWH,EAAS8kB,EACtC,CAEArB,uBACE,OAAOzhB,OAAOC,OAAOe,KAAKuf,gBAAgBne,UAAS,EACrD,CAEAmD,WAAWC,GACT,MAAMud,EAAiBze,EAAYK,kBAAkB3D,KAAKyF,UAE1D,IAAK,MAAMuc,KAAiBhjB,OAAOtH,KAAKqqB,GAClC9D,GAAsB/mB,IAAI8qB,WACrBD,EAAeC,GAW1B,OAPAxd,EAAS,IACJud,KACmB,iBAAXvd,GAAuBA,EAASA,EAAS,IAEtDA,EAASxE,KAAKyE,gBAAgBD,GAC9BA,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAkBhB,OAjBAA,EAAOsa,WAAiC,IAArBta,EAAOsa,UAAsB/lB,SAAS8B,KAAOhC,EAAW2L,EAAOsa,WAEtD,iBAAjBta,EAAOwa,QAChBxa,EAAOwa,MAAQ,CACbxP,KAAMhL,EAAOwa,MACbzP,KAAM/K,EAAOwa,QAIW,iBAAjBxa,EAAO0a,QAChB1a,EAAO0a,MAAQ1a,EAAO0a,MAAMnc,YAGA,iBAAnByB,EAAOwX,UAChBxX,EAAOwX,QAAUxX,EAAOwX,QAAQjZ,YAG3ByB,CACT,CAEA8c,qBACE,MAAM9c,EAAS,GAEf,IAAK,MAAOxN,EAAK0L,KAAU1D,OAAOmC,QAAQnB,KAAK0F,SACzC1F,KAAK6E,YAAYT,QAAQpN,KAAS0L,IACpC8B,EAAOxN,GAAO0L,GAUlB,OANA8B,EAAOzM,UAAW,EAClByM,EAAO3C,QAAU,SAKV2C,CACT,CAEA4b,iBACMpgB,KAAKwR,UACPxR,KAAKwR,QAAQQ,UACbhS,KAAKwR,QAAU,MAGbxR,KAAK0f,MACP1f,KAAK0f,IAAI/nB,SACTqI,KAAK0f,IAAM,KAEf,CAGAxZ,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAO2W,GAAQhX,oBAAoBnI,KAAMwE,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFvJ,EAAmBkkB,ICtmBnB,MAKM/a,GAAU,IACX+a,GAAQ/a,QACX4X,QAAS,GACT5K,OAAQ,CAAC,EAAG,GACZwB,UAAW,QACXyJ,SAAU,8IAKVxa,QAAS,SAGLwC,GAAc,IACf8a,GAAQ9a,YACX2X,QAAS,kCAOX,MAAMiG,WAAgB9C,GAET/a,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MAtCS,SAuCX,CAGA+kB,iBACE,OAAOrgB,KAAK0gB,aAAe1gB,KAAKkiB,aAClC,CAGAtB,yBACE,MAAO,CACL,kBAAkB5gB,KAAK0gB,YACvB,gBAAoB1gB,KAAKkiB,cAE7B,CAEAA,cACE,OAAOliB,KAAK0c,yBAAyB1c,KAAK0F,QAAQsW,QACpD,CAGA9V,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAOyZ,GAAQ9Z,oBAAoBnI,KAAMwE,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFvJ,EAAmBgnB,IC9EnB,MAMME,GAAe,qBAIfxX,GAAoB,SAGpByX,GAAwB,SASxBhe,GAAU,CACdgN,OAAQ,KACRiR,WAAY,eACZC,cAAc,EACdrlB,OAAQ,KACRslB,UAAW,CAAC,GAAK,GAAK,IAGlBle,GAAc,CAClB+M,OAAQ,gBACRiR,WAAY,SACZC,aAAc,UACdrlB,OAAQ,UACRslB,UAAW,SAOb,MAAMC,WAAkBjd,EACtBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAGfxE,KAAKyiB,aAAe,IAAI7rB,IACxBoJ,KAAK0iB,oBAAsB,IAAI9rB,IAC/BoJ,KAAK2iB,aAA6D,YAA9CvpB,iBAAiB4G,KAAKyF,UAAU8S,UAA0B,KAAOvY,KAAKyF,SAC1FzF,KAAK4iB,cAAgB,KACrB5iB,KAAK6iB,UAAY,KACjB7iB,KAAK8iB,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnBhjB,KAAKijB,SACP,CAGW7e,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MArES,WAsEX,CAGA2nB,UACEjjB,KAAKkjB,mCACLljB,KAAKmjB,2BAEDnjB,KAAK6iB,UACP7iB,KAAK6iB,UAAUO,aAEfpjB,KAAK6iB,UAAY7iB,KAAKqjB,kBAGxB,IAAK,MAAMC,KAAWtjB,KAAK0iB,oBAAoBzjB,SAC7Ce,KAAK6iB,UAAUU,QAAQD,EAE3B,CAEA1d,UACE5F,KAAK6iB,UAAUO,aACf5d,MAAMI,SACR,CAGAlB,kBAAkBF,GAWhB,OATAA,EAAOvH,OAASpE,EAAW2L,EAAOvH,SAAWlE,SAAS8B,KAGtD2J,EAAO6d,WAAa7d,EAAO4M,OAAU,GAAE5M,EAAO4M,oBAAsB5M,EAAO6d,WAE3C,iBAArB7d,EAAO+d,YAChB/d,EAAO+d,UAAY/d,EAAO+d,UAAU1lB,MAAM,KAAK2K,KAAI9E,GAAShG,OAAOC,WAAW+F,MAGzE8B,CACT,CAEA2e,2BACOnjB,KAAK0F,QAAQ4c,eAKlB/hB,EAAaC,IAAIR,KAAK0F,QAAQzI,OAAQklB,IAEtC5hB,EAAac,GAAGrB,KAAK0F,QAAQzI,OAAQklB,GAAaC,IAAuBjjB,IACvE,MAAMqkB,EAAoBxjB,KAAK0iB,oBAAoBtrB,IAAI+H,EAAMlC,OAAOwmB,MACpE,GAAID,EAAmB,CACrBrkB,EAAMoD,iBACN,MAAMjI,EAAO0F,KAAK2iB,cAAgB3qB,OAC5B0rB,EAASF,EAAkBG,UAAY3jB,KAAKyF,SAASke,UAC3D,GAAIrpB,EAAKspB,SAEP,YADAtpB,EAAKspB,SAAS,CAAEC,IAAKH,EAAQI,SAAU,WAKzCxpB,EAAKud,UAAY6L,CACnB,KAEJ,CAEAL,kBACE,MAAMvQ,EAAU,CACdxY,KAAM0F,KAAK2iB,aACXJ,UAAWviB,KAAK0F,QAAQ6c,UACxBF,WAAYriB,KAAK0F,QAAQ2c,YAG3B,OAAO,IAAI0B,sBAAqB5iB,GAAWnB,KAAKgkB,kBAAkB7iB,IAAU2R,EAC9E,CAGAkR,kBAAkB7iB,GAChB,MAAM8iB,EAAgB1H,GAASvc,KAAKyiB,aAAarrB,IAAK,IAAGmlB,EAAMtf,OAAO5E,MAChEyc,EAAWyH,IACfvc,KAAK8iB,oBAAoBC,gBAAkBxG,EAAMtf,OAAO0mB,UACxD3jB,KAAKkkB,SAASD,EAAc1H,GAAO,EAG/ByG,GAAmBhjB,KAAK2iB,cAAgB5pB,SAASoB,iBAAiB0d,UAClEsM,EAAkBnB,GAAmBhjB,KAAK8iB,oBAAoBE,gBACpEhjB,KAAK8iB,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAMzG,KAASpb,EAAS,CAC3B,IAAKob,EAAM6H,eAAgB,CACzBpkB,KAAK4iB,cAAgB,KACrB5iB,KAAKqkB,kBAAkBJ,EAAc1H,IAErC,QACF,CAEA,MAAM+H,EAA2B/H,EAAMtf,OAAO0mB,WAAa3jB,KAAK8iB,oBAAoBC,gBAEpF,GAAIoB,GAAmBG,GAGrB,GAFAxP,EAASyH,IAEJyG,EACH,YAOCmB,GAAoBG,GACvBxP,EAASyH,EAEb,CACF,CAEA2G,mCACEljB,KAAKyiB,aAAe,IAAI7rB,IACxBoJ,KAAK0iB,oBAAsB,IAAI9rB,IAE/B,MAAM2tB,EAAc/d,EAAetH,KAAKkjB,GAAuBpiB,KAAK0F,QAAQzI,QAE5E,IAAK,MAAMunB,KAAUD,EAAa,CAEhC,IAAKC,EAAOf,MAAQ/pB,EAAW8qB,GAC7B,SAGF,MAAMhB,EAAoBhd,EAAeG,QAAQ6d,EAAOf,KAAMzjB,KAAKyF,UAG/DxM,EAAUuqB,KACZxjB,KAAKyiB,aAAa3rB,IAAI0tB,EAAOf,KAAMe,GACnCxkB,KAAK0iB,oBAAoB5rB,IAAI0tB,EAAOf,KAAMD,GAE9C,CACF,CAEAU,SAASjnB,GACH+C,KAAK4iB,gBAAkB3lB,IAI3B+C,KAAKqkB,kBAAkBrkB,KAAK0F,QAAQzI,QACpC+C,KAAK4iB,cAAgB3lB,EACrBA,EAAOpD,UAAUoQ,IAAIU,IACrB3K,KAAKykB,iBAAiBxnB,GAEtBsD,EAAasB,QAAQ7B,KAAKyF,SAjNN,wBAiNgC,CAAE5F,cAAe5C,IACvE,CAEAwnB,iBAAiBxnB,GAEf,GAAIA,EAAOpD,UAAUC,SAlNQ,iBAmN3B0M,EAAeG,QAxMY,mBAwMsB1J,EAAO1D,QAzMpC,cA0MjBM,UAAUoQ,IAAIU,SAInB,IAAK,MAAM+Z,KAAale,EAAeO,QAAQ9J,EAnNnB,qBAsN1B,IAAK,MAAM0nB,KAAQne,EAAeS,KAAKyd,EAlNhB,sDAmNrBC,EAAK9qB,UAAUoQ,IAAIU,GAGzB,CAEA0Z,kBAAkBzV,GAChBA,EAAO/U,UAAUlC,OAAOgT,IAExB,MAAMia,EAAcpe,EAAetH,KAAM,gBAAgD0P,GACzF,IAAK,MAAMiW,KAAQD,EACjBC,EAAKhrB,UAAUlC,OAAOgT,GAE1B,CAGAzE,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAOga,GAAUra,oBAAoBnI,KAAMwE,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFjE,EAAac,GAAGrJ,OAlQa,8BAkQgB,KAC3C,IAAK,MAAM8sB,KAAOte,EAAetH,KA9PT,0BA+PtBsjB,GAAUra,oBAAoB2c,EAChC,IAOF7pB,EAAmBunB,ICnRnB,MAYMuC,GAAiB,YACjBC,GAAkB,aAClB3U,GAAe,UACfC,GAAiB,YAEjB3F,GAAoB,SACpBuT,GAAkB,OAClBzP,GAAkB,OAUlB/F,GAAuB,2EACvBuc,GAAuB,gHAAqBvc,KAQlD,MAAMwc,WAAY3f,EAChBV,YAAY9N,GACVyO,MAAMzO,GACNiJ,KAAKyR,QAAUzR,KAAKyF,SAASlM,QAfN,uCAiBlByG,KAAKyR,UAOVzR,KAAKmlB,sBAAsBnlB,KAAKyR,QAASzR,KAAKolB,gBAE9C7kB,EAAac,GAAGrB,KAAKyF,SA3CF,kBA2C2BtG,GAASa,KAAK6M,SAAS1N,KACvE,CAGW7D,kBACT,MAzDS,KA0DX,CAGAkU,OACE,MAAM6V,EAAYrlB,KAAKyF,SACvB,GAAIzF,KAAKslB,cAAcD,GACrB,OAIF,MAAME,EAASvlB,KAAKwlB,iBAEdC,EAAYF,EAChBhlB,EAAasB,QAAQ0jB,EAnEP,cAmE2B,CAAE1lB,cAAewlB,IAC1D,KAEgB9kB,EAAasB,QAAQwjB,EApEvB,cAoE8C,CAAExlB,cAAe0lB,IAEjEtjB,kBAAqBwjB,GAAaA,EAAUxjB,mBAI1DjC,KAAK0lB,YAAYH,EAAQF,GACzBrlB,KAAK2lB,UAAUN,EAAWE,GAC5B,CAGAI,UAAU5uB,EAAS6uB,GACZ7uB,IAILA,EAAQ8C,UAAUoQ,IAAIU,IAEtB3K,KAAK2lB,UAAUnf,EAAeoB,uBAAuB7Q,IAgBrDiJ,KAAKgG,gBAdYgK,KACsB,QAAjCjZ,EAAQkD,aAAa,SAKzBlD,EAAQ2M,gBAAgB,YACxB3M,EAAQyM,aAAa,iBAAiB,GACtCxD,KAAK6lB,gBAAgB9uB,GAAS,GAC9BwJ,EAAasB,QAAQ9K,EAhGN,eAgG4B,CACzC8I,cAAe+lB,KARf7uB,EAAQ8C,UAAUoQ,IAAIwE,GAStB,GAG0B1X,EAASA,EAAQ8C,UAAUC,SAASokB,KACpE,CAEAwH,YAAY3uB,EAAS6uB,GACd7uB,IAILA,EAAQ8C,UAAUlC,OAAOgT,IACzB5T,EAAQoiB,OAERnZ,KAAK0lB,YAAYlf,EAAeoB,uBAAuB7Q,IAcvDiJ,KAAKgG,gBAZYgK,KACsB,QAAjCjZ,EAAQkD,aAAa,SAKzBlD,EAAQyM,aAAa,iBAAiB,GACtCzM,EAAQyM,aAAa,WAAY,MACjCxD,KAAK6lB,gBAAgB9uB,GAAS,GAC9BwJ,EAAasB,QAAQ9K,EA7HL,gBA6H4B,CAAE8I,cAAe+lB,KAP3D7uB,EAAQ8C,UAAUlC,OAAO8W,GAOgD,GAG/C1X,EAASA,EAAQ8C,UAAUC,SAASokB,KACpE,CAEArR,SAAS1N,GACP,IAAM,CAAC4lB,GAAgBC,GAAiB3U,GAAcC,IAAgBlP,SAASjC,EAAMnI,KACnF,OAGFmI,EAAMsU,kBACNtU,EAAMoD,iBACN,MAAMkL,EAAS,CAACuX,GAAiB1U,IAAgBlP,SAASjC,EAAMnI,KAC1D8uB,EAAoB1oB,EAAqB4C,KAAKolB,eAAerhB,QAAOhN,IAAY2C,EAAW3C,KAAWoI,EAAMlC,OAAQwQ,GAAQ,GAE9HqY,IACFA,EAAkBhU,MAAM,CAAEiU,eAAe,IACzCb,GAAI/c,oBAAoB2d,GAAmBtW,OAE/C,CAEA4V,eACE,OAAO5e,EAAetH,KAAK+lB,GAAqBjlB,KAAKyR,QACvD,CAEA+T,iBACE,OAAOxlB,KAAKolB,eAAelmB,MAAK2H,GAAS7G,KAAKslB,cAAcze,MAAW,IACzE,CAEAse,sBAAsBvW,EAAQhI,GAC5B5G,KAAKgmB,yBAAyBpX,EAAQ,OAAQ,WAE9C,IAAK,MAAM/H,KAASD,EAClB5G,KAAKimB,6BAA6Bpf,EAEtC,CAEAof,6BAA6Bpf,GAC3BA,EAAQ7G,KAAKkmB,iBAAiBrf,GAC9B,MAAMsf,EAAWnmB,KAAKslB,cAAcze,GAC9Buf,EAAYpmB,KAAKqmB,iBAAiBxf,GACxCA,EAAMrD,aAAa,gBAAiB2iB,GAEhCC,IAAcvf,GAChB7G,KAAKgmB,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACHtf,EAAMrD,aAAa,WAAY,MAGjCxD,KAAKgmB,yBAAyBnf,EAAO,OAAQ,OAG7C7G,KAAKsmB,mCAAmCzf,EAC1C,CAEAyf,mCAAmCzf,GACjC,MAAM5J,EAASuJ,EAAeoB,uBAAuBf,GAEhD5J,IAIL+C,KAAKgmB,yBAAyB/oB,EAAQ,OAAQ,YAE1C4J,EAAMxO,IACR2H,KAAKgmB,yBAAyB/oB,EAAQ,kBAAoB,GAAE4J,EAAMxO,MAEtE,CAEAwtB,gBAAgB9uB,EAASwvB,GACvB,MAAMH,EAAYpmB,KAAKqmB,iBAAiBtvB,GACxC,IAAKqvB,EAAUvsB,UAAUC,SAxLN,YAyLjB,OAGF,MAAM8O,EAASA,CAAC7Q,EAAU8b,KACxB,MAAM9c,EAAUyP,EAAeG,QAAQ5O,EAAUquB,GAC7CrvB,GACFA,EAAQ8C,UAAU+O,OAAOiL,EAAW0S,EACtC,EAGF3d,EAjM6B,mBAiMI+B,IACjC/B,EAjM2B,iBAiMI6F,IAC/B2X,EAAU5iB,aAAa,gBAAiB+iB,EAC1C,CAEAP,yBAAyBjvB,EAAS2iB,EAAWhX,GACtC3L,EAAQiD,aAAa0f,IACxB3iB,EAAQyM,aAAakW,EAAWhX,EAEpC,CAEA4iB,cAAcrW,GACZ,OAAOA,EAAKpV,UAAUC,SAAS6Q,GACjC,CAGAub,iBAAiBjX,GACf,OAAOA,EAAKnI,QAAQme,IAAuBhW,EAAOzI,EAAeG,QAAQse,GAAqBhW,EAChG,CAGAoX,iBAAiBpX,GACf,OAAOA,EAAK1V,QAlNO,gCAkNoB0V,CACzC,CAGA/I,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAO0c,GAAI/c,oBAAoBnI,MAErC,GAAsB,iBAAXwE,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFjE,EAAac,GAAGtI,SA9Pc,eA8PkB2P,IAAsB,SAAUvJ,GAC1E,CAAC,IAAK,QAAQiC,SAASpB,KAAKkI,UAC9B/I,EAAMoD,iBAGJ7I,EAAWsG,OAIfklB,GAAI/c,oBAAoBnI,MAAMwP,MAChC,IAKAjP,EAAac,GAAGrJ,OA3Qa,eA2QgB,KAC3C,IAAK,MAAMjB,KAAWyP,EAAetH,KAtPF,iGAuPjCgmB,GAAI/c,oBAAoBpR,EAC1B,IAMFkE,EAAmBiqB,IC9RnB,MAcMsB,GAAkB,OAClB/X,GAAkB,OAClBoK,GAAqB,UAErBxU,GAAc,CAClBwa,UAAW,UACX4H,SAAU,UACVzH,MAAO,UAGH5a,GAAU,CACdya,WAAW,EACX4H,UAAU,EACVzH,MAAO,KAOT,MAAM0H,WAAcnhB,EAClBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKqf,SAAW,KAChBrf,KAAK2mB,sBAAuB,EAC5B3mB,KAAK4mB,yBAA0B,EAC/B5mB,KAAK2f,eACP,CAGWvb,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEW/I,kBACT,MAtDS,OAuDX,CAGAkU,OACoBjP,EAAasB,QAAQ7B,KAAKyF,SAjD5B,iBAmDFxD,mBAIdjC,KAAK6mB,gBAED7mB,KAAK0F,QAAQmZ,WACf7e,KAAKyF,SAAS5L,UAAUoQ,IAvDN,QAiEpBjK,KAAKyF,SAAS5L,UAAUlC,OAAO6uB,IAC/B/rB,EAAOuF,KAAKyF,UACZzF,KAAKyF,SAAS5L,UAAUoQ,IAAIwE,GAAiBoK,IAE7C7Y,KAAKgG,gBAXYgK,KACfhQ,KAAKyF,SAAS5L,UAAUlC,OAAOkhB,IAC/BtY,EAAasB,QAAQ7B,KAAKyF,SA9DX,kBAgEfzF,KAAK8mB,oBAAoB,GAOG9mB,KAAKyF,SAAUzF,KAAK0F,QAAQmZ,WAC5D,CAEAtP,OACOvP,KAAK+mB,YAIQxmB,EAAasB,QAAQ7B,KAAKyF,SAlF5B,iBAoFFxD,mBAUdjC,KAAKyF,SAAS5L,UAAUoQ,IAAI4O,IAC5B7Y,KAAKgG,gBAPYgK,KACfhQ,KAAKyF,SAAS5L,UAAUoQ,IAAIuc,IAC5BxmB,KAAKyF,SAAS5L,UAAUlC,OAAOkhB,GAAoBpK,IACnDlO,EAAasB,QAAQ7B,KAAKyF,SA1FV,kBA0FiC,GAIrBzF,KAAKyF,SAAUzF,KAAK0F,QAAQmZ,YAC5D,CAEAjZ,UACE5F,KAAK6mB,gBAED7mB,KAAK+mB,WACP/mB,KAAKyF,SAAS5L,UAAUlC,OAAO8W,IAGjCjJ,MAAMI,SACR,CAEAmhB,UACE,OAAO/mB,KAAKyF,SAAS5L,UAAUC,SAAS2U,GAC1C,CAIAqY,qBACO9mB,KAAK0F,QAAQ+gB,WAIdzmB,KAAK2mB,sBAAwB3mB,KAAK4mB,0BAItC5mB,KAAKqf,SAAWliB,YAAW,KACzB6C,KAAKuP,MAAM,GACVvP,KAAK0F,QAAQsZ,QAClB,CAEAgI,eAAe7nB,EAAO8nB,GACpB,OAAQ9nB,EAAMsB,MACZ,IAAK,YACL,IAAK,WACHT,KAAK2mB,qBAAuBM,EAC5B,MAGF,IAAK,UACL,IAAK,WACHjnB,KAAK4mB,wBAA0BK,EASnC,GAAIA,EAEF,YADAjnB,KAAK6mB,gBAIP,MAAMnZ,EAAcvO,EAAMU,cACtBG,KAAKyF,WAAaiI,GAAe1N,KAAKyF,SAAS3L,SAAS4T,IAI5D1N,KAAK8mB,oBACP,CAEAnH,gBACEpf,EAAac,GAAGrB,KAAKyF,SArKA,sBAqK2BtG,GAASa,KAAKgnB,eAAe7nB,GAAO,KACpFoB,EAAac,GAAGrB,KAAKyF,SArKD,qBAqK2BtG,GAASa,KAAKgnB,eAAe7nB,GAAO,KACnFoB,EAAac,GAAGrB,KAAKyF,SArKF,oBAqK2BtG,GAASa,KAAKgnB,eAAe7nB,GAAO,KAClFoB,EAAac,GAAGrB,KAAKyF,SArKD,qBAqK2BtG,GAASa,KAAKgnB,eAAe7nB,GAAO,IACrF,CAEA0nB,gBACE1Z,aAAanN,KAAKqf,UAClBrf,KAAKqf,SAAW,IAClB,CAGAnZ,uBAAuB1B,GACrB,OAAOxE,KAAKuI,MAAK,WACf,MAAMC,EAAOke,GAAMve,oBAAoBnI,KAAMwE,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxE,KACf,CACF,GACF,E,OAOF8H,EAAqB4e,IAMrBzrB,EAAmByrB,IC1MJ,CACbte,QACAO,SACA0C,YACAwD,YACA0C,YACA2F,SACAgC,aACA+I,WACAO,aACA0C,OACAwB,SACAvH,W"}