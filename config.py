# Конфигурация базы данных для KPI опросника
import os

class DatabaseConfig:
    """Конфигурация подключения к базе данных PostgreSQL"""
    
    # Основные параметры подключения
    DATABASE_NAME = os.getenv('DB_NAME', 'kpi')
    DATABASE_HOST = os.getenv('DB_HOST', '*************')
    DATABASE_PORT = os.getenv('DB_PORT', '22432')
    DATABASE_USER = os.getenv('DB_USER', 'postgres')
    DATABASE_PASSWORD = os.getenv('DB_PASSWORD', 'Qwerdsfa3241')
    
    # Дополнительные параметры
    DATABASE_CHARSET = 'utf8'
    DATABASE_AUTOCOMMIT = True
    
    @classmethod
    def get_connection_params(cls):
        """Возвращает параметры подключения в виде словаря"""
        return {
            'database': cls.DATABASE_NAME,
            'host': cls.DATABASE_HOST,
            'port': cls.DATABASE_PORT,
            'user': cls.DATABASE_USER,
            'password': cls.DATABASE_PASSWORD
        }
    
    @classmethod
    def get_connection_string(cls):
        """Возвращает строку подключения PostgreSQL"""
        return f"postgresql://{cls.DATABASE_USER}:{cls.DATABASE_PASSWORD}@{cls.DATABASE_HOST}:{cls.DATABASE_PORT}/{cls.DATABASE_NAME}"

class AppConfig:
    """Общие настройки приложения"""
    
    # Flask настройки
    SECRET_KEY = os.getenv('SECRET_KEY', 'kpi_survey_secret_key_2024')
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
    
    # Настройки сервера
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', '15007'))
    
    # Настройки опросов
    SURVEY_TYPES = {
        'deans': {
            'name': 'Факультет декани фаолияти самарадорлигини баҳолаш мезонлари (KPI)',
            'template': 'deans.html',
            'description': 'Опрос для оценки эффективности деятельности деканов факультетов'
        },
        'departments': {
            'name': 'Кафедра мудири фаолияти самарадорлигини баҳолаш мезонлари (KPI)',
            'template': 'departments.html',
            'description': 'Опрос для оценки эффективности деятельности заведующих кафедрами'
        },
        'professors': {
            'name': 'Кафедра профессор-ўқитувчилари фаолиятини баҳолаш мезонлари (KPI)',
            'template': 'professors.html',
            'description': 'Опрос для оценки эффективности деятельности профессорско-преподавательского состава'
        }
    }

# Для обратной совместимости с существующим кодом
DB_CONFIG = DatabaseConfig.get_connection_params()
