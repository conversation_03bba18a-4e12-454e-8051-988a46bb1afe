/**
 * TinyMCE version 6.4.2 (2023-04-26)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.ModelManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=n=e,(r=String).prototype.isPrototypeOf(o)||(null===(s=n.constructor)||void 0===s?void 0:s.name)===r.name)?"string":t;var o,n,r,s})(t)===e,o=e=>t=>typeof t===e,n=e=>t=>e===t,r=t("string"),s=t("object"),l=t("array"),a=n(null),c=o("boolean"),i=n(void 0),m=e=>!(e=>null==e)(e),d=o("function"),u=o("number"),f=()=>{},g=e=>()=>e,h=e=>e,p=(e,t)=>e===t;function w(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const b=e=>t=>!e(t),v=e=>e(),y=g(!1),x=g(!0);class C{constructor(e,t){this.tag=e,this.value=t}static some(e){return new C(!0,e)}static none(){return C.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?C.some(e(this.value)):C.none()}bind(e){return this.tag?e(this.value):C.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:C.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return m(e)?C.some(e):C.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}C.singletonNone=new C(!1);const S=Array.prototype.slice,T=Array.prototype.indexOf,R=Array.prototype.push,D=(e,t)=>{return o=e,n=t,T.call(o,n)>-1;var o,n},O=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},k=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},E=(e,t)=>{const o=e.length,n=new Array(o);for(let r=0;r<o;r++){const o=e[r];n[r]=t(o,r)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},B=(e,t)=>{const o=[],n=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?o:n).push(s)}return{pass:o,fail:n}},z=(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];t(r,n)&&o.push(r)}return o},A=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),L=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),W=(e,t)=>((e,t,o)=>{for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t(r,n))return C.some(r);if(o(r,n))break}return C.none()})(e,t,y),M=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return C.some(o);return C.none()},_=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);R.apply(t,e[o])}return t},j=(e,t)=>_(E(e,t)),P=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},I=(e,t)=>{const o={};for(let n=0,r=e.length;n<r;n++){const r=e[n];o[String(r)]=t(r,n)}return o},F=(e,t)=>t>=0&&t<e.length?C.some(e[t]):C.none(),H=e=>F(e,0),q=e=>F(e,e.length-1),V=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return C.none()},$=Object.keys,U=Object.hasOwnProperty,G=(e,t)=>{const o=$(e);for(let n=0,r=o.length;n<r;n++){const r=o[n];t(e[r],r)}},K=(e,t)=>Y(e,((e,o)=>({k:o,v:t(e,o)}))),Y=(e,t)=>{const o={};return G(e,((e,n)=>{const r=t(e,n);o[r.k]=r.v})),o},J=(e,t)=>{const o=[];return G(e,((e,n)=>{o.push(t(e,n))})),o},Q=e=>J(e,h),X=(e,t)=>U.call(e,t),Z="undefined"!=typeof window?window:Function("return this;")(),ee=(e,t)=>((e,t)=>{let o=null!=t?t:Z;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),te=Object.getPrototypeOf,oe=e=>{const t=ee("ownerDocument.defaultView",e);return s(e)&&((e=>((e,t)=>{const o=((e,t)=>ee(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(te(e).constructor.name))},ne=e=>e.dom.nodeName.toLowerCase(),re=e=>e.dom.nodeType,se=e=>t=>re(t)===e,le=e=>8===re(e)||"#comment"===ne(e),ae=e=>ce(e)&&oe(e.dom),ce=se(1),ie=se(3),me=se(9),de=se(11),ue=e=>t=>ce(t)&&ne(t)===e,fe=(e,t,o)=>{if(!(r(o)||c(o)||u(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},ge=(e,t,o)=>{fe(e.dom,t,o)},he=(e,t)=>{const o=e.dom;G(t,((e,t)=>{fe(o,t,e)}))},pe=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},we=(e,t)=>C.from(pe(e,t)),be=(e,t)=>{e.dom.removeAttribute(t)},ve=e=>L(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),ye=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},xe={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return ye(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return ye(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return ye(o)},fromDom:ye,fromPoint:(e,t,o)=>C.from(e.dom.elementFromPoint(t,o)).map(ye)},Ce=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Se=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Te=(e,t)=>{const o=void 0===t?document:t.dom;return Se(o)?C.none():C.from(o.querySelector(e)).map(xe.fromDom)},Re=(e,t)=>e.dom===t.dom,De=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},Oe=Ce,ke=e=>xe.fromDom(e.dom.ownerDocument),Ee=e=>me(e)?e:ke(e),Ne=e=>C.from(e.dom.parentNode).map(xe.fromDom),Be=e=>C.from(e.dom.parentElement).map(xe.fromDom),ze=(e,t)=>{const o=d(t)?t:y;let n=e.dom;const r=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=xe.fromDom(e);if(r.push(t),!0===o(t))break;n=e}return r},Ae=e=>C.from(e.dom.previousSibling).map(xe.fromDom),Le=e=>C.from(e.dom.nextSibling).map(xe.fromDom),We=e=>E(e.dom.childNodes,xe.fromDom),Me=(e,t)=>{const o=e.dom.childNodes;return C.from(o[t]).map(xe.fromDom)},_e=(e,t)=>{Ne(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},je=(e,t)=>{Le(e).fold((()=>{Ne(e).each((e=>{Ie(e,t)}))}),(e=>{_e(e,t)}))},Pe=(e,t)=>{const o=(e=>Me(e,0))(e);o.fold((()=>{Ie(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Ie=(e,t)=>{e.dom.appendChild(t.dom)},Fe=(e,t)=>{_e(e,t),Ie(t,e)},He=(e,t)=>{N(t,((o,n)=>{const r=0===n?e:t[n-1];je(r,o)}))},qe=(e,t)=>{N(t,(t=>{Ie(e,t)}))},Ve=e=>{e.dom.textContent="",N(We(e),(e=>{$e(e)}))},$e=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Ue=e=>{const t=We(e);t.length>0&&He(e,t),$e(e)},Ge=(e,t)=>xe.fromDom(e.dom.cloneNode(t)),Ke=e=>Ge(e,!1),Ye=e=>Ge(e,!0),Je=(e,t)=>{const o=xe.fromTag(t),n=ve(e);return he(o,n),o},Qe=["tfoot","thead","tbody","colgroup"],Xe=(e,t,o)=>({element:e,rowspan:t,colspan:o}),Ze=(e,t,o)=>({element:e,cells:t,section:o}),et=(e,t,o)=>({element:e,isNew:t,isLocked:o}),tt=(e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}),ot=d(Element.prototype.attachShadow)&&d(Node.prototype.getRootNode),nt=g(ot),rt=ot?e=>xe.fromDom(e.dom.getRootNode()):Ee,st=e=>xe.fromDom(e.dom.host),lt=e=>{const t=ie(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=rt(e);return de(o=t)&&m(o.dom.host)?C.some(t):C.none();var o})(xe.fromDom(t)).fold((()=>o.body.contains(t)),(n=lt,r=st,e=>n(r(e))));var n,r},at=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return xe.fromDom(t)},ct=(e,t)=>{let o=[];return N(We(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(ct(e,t))})),o},it=(e,t,o)=>((e,o,n)=>z(ze(e,n),(e=>Ce(e,t))))(e,0,o),mt=(e,t)=>((e,o)=>z(We(e),(e=>Ce(e,t))))(e),dt=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Se(o)?[]:E(o.querySelectorAll(e),xe.fromDom)})(t,e);var ut=(e,t,o,n,r)=>e(o,n)?C.some(o):d(r)&&r(o)?C.none():t(o,n,r);const ft=(e,t,o)=>{let n=e.dom;const r=d(o)?o:y;for(;n.parentNode;){n=n.parentNode;const e=xe.fromDom(n);if(t(e))return C.some(e);if(r(e))break}return C.none()},gt=(e,t,o)=>ut(((e,t)=>t(e)),ft,e,t,o),ht=(e,t,o)=>ft(e,(e=>Ce(e,t)),o),pt=(e,t)=>((e,o)=>W(e.dom.childNodes,(e=>{return o=xe.fromDom(e),Ce(o,t);var o})).map(xe.fromDom))(e),wt=(e,t)=>Te(t,e),bt=(e,t,o)=>ut(((e,t)=>Ce(e,t)),ht,e,t,o),vt=(e,t,o=p)=>e.exists((e=>o(e,t))),yt=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},xt=(e,t)=>e?C.some(t):C.none(),Ct=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,St=(e,t,o=0,n)=>{const r=e.indexOf(t,o);return-1!==r&&(!!i(n)||r+t.length<=n)},Tt=(e,t)=>Ct(e,t,0),Rt=(e,t)=>Ct(e,t,e.length-t.length),Dt=(e=>t=>t.replace(e,""))(/^\s+|\s+$/g),Ot=e=>e.length>0,kt=e=>void 0!==e.style&&d(e.style.getPropertyValue),Et=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);kt(e)&&e.style.setProperty(t,o)},Nt=(e,t,o)=>{const n=e.dom;Et(n,t,o)},Bt=(e,t)=>{const o=e.dom;G(t,((e,t)=>{Et(o,t,e)}))},zt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||lt(e)?n:At(o,t)},At=(e,t)=>kt(e)?e.style.getPropertyValue(t):"",Lt=(e,t)=>{const o=e.dom,n=At(o,t);return C.from(n).filter((e=>e.length>0))},Wt=(e,t)=>{((e,t)=>{kt(e)&&e.style.removeProperty(t)})(e.dom,t),vt(we(e,"style").map(Dt),"")&&be(e,"style")},Mt=(e,t,o=0)=>we(e,t).map((e=>parseInt(e,10))).getOr(o),_t=(e,t)=>Mt(e,t,1),jt=e=>ue("col")(e)?Mt(e,"span",1)>1:_t(e,"colspan")>1,Pt=e=>_t(e,"rowspan")>1,It=(e,t)=>parseInt(zt(e,t),10),Ft=g(10),Ht=g(10),qt=(e,t)=>Vt(e,t,x),Vt=(e,t,o)=>j(We(e),(e=>Ce(e,t)?o(e)?[e]:[]:Vt(e,t,o))),$t=(e,t)=>((e,t,o=y)=>o(t)?C.none():D(e,ne(t))?C.some(t):ht(t,e.join(","),(e=>Ce(e,"table")||o(e))))(["td","th"],e,t),Ut=e=>qt(e,"th,td"),Gt=e=>Ce(e,"colgroup")?mt(e,"col"):j(Jt(e),(e=>mt(e,"col"))),Kt=(e,t)=>bt(e,"table",t),Yt=e=>qt(e,"tr"),Jt=e=>Kt(e).fold(g([]),(e=>mt(e,"colgroup"))),Qt=(e,t)=>E(e,(e=>{if("colgroup"===ne(e)){const t=E(Gt(e),(e=>{const t=Mt(e,"span",1);return Xe(e,1,t)}));return Ze(e,t,"colgroup")}{const o=E(Ut(e),(e=>{const t=Mt(e,"rowspan",1),o=Mt(e,"colspan",1);return Xe(e,t,o)}));return Ze(e,o,t(e))}})),Xt=e=>Ne(e).map((e=>{const t=ne(e);return(e=>D(Qe,e))(t)?t:"tbody"})).getOr("tbody"),Zt=e=>{const t=Yt(e),o=[...Jt(e),...t];return Qt(o,Xt)},eo=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},to=()=>oo(0,0),oo=(e,t)=>({major:e,minor:t}),no={nu:oo,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?to():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return oo(n(1),n(2))})(e,o)},unknown:to},ro=(e,t)=>{const o=String(t).toLowerCase();return W(e,(e=>e.search(o)))},so=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,lo=e=>t=>St(t,e),ao=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>St(e,"edge/")&&St(e,"chrome")&&St(e,"safari")&&St(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,so],search:e=>St(e,"chrome")&&!St(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>St(e,"msie")||St(e,"trident")},{name:"Opera",versionRegexes:[so,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:lo("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:lo("firefox")},{name:"Safari",versionRegexes:[so,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(St(e,"safari")||St(e,"mobile/"))&&St(e,"applewebkit")}],co=[{name:"Windows",search:lo("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>St(e,"iphone")||St(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:lo("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:lo("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:lo("linux"),versionRegexes:[]},{name:"Solaris",search:lo("sunos"),versionRegexes:[]},{name:"FreeBSD",search:lo("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:lo("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],io={browsers:g(ao),oses:g(co)},mo="Edge",uo="Chromium",fo="Opera",go="Firefox",ho="Safari",po=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(mo),isChromium:n(uo),isIE:n("IE"),isOpera:n(fo),isFirefox:n(go),isSafari:n(ho)}},wo=()=>po({current:void 0,version:no.unknown()}),bo=po,vo=(g(mo),g(uo),g("IE"),g(fo),g(go),g(ho),"Windows"),yo="Android",xo="Linux",Co="macOS",So="Solaris",To="FreeBSD",Ro="ChromeOS",Do=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(vo),isiOS:n("iOS"),isAndroid:n(yo),isMacOS:n(Co),isLinux:n(xo),isSolaris:n(So),isFreeBSD:n(To),isChromeOS:n(Ro)}},Oo=()=>Do({current:void 0,version:no.unknown()}),ko=Do,Eo=(g(vo),g("iOS"),g(yo),g(xo),g(Co),g(So),g(To),g(Ro),e=>window.matchMedia(e).matches);let No=eo((()=>((e,t,o)=>{const n=io.browsers(),r=io.oses(),s=t.bind((e=>((e,t)=>V(t.brands,(t=>{const o=t.brand.toLowerCase();return W(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:no.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>ro(e,t).map((e=>{const o=no.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(wo,bo),l=((e,t)=>ro(e,t).map((e=>{const o=no.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(r,e).fold(Oo,ko),a=((e,t,o,n)=>{const r=e.isiOS()&&!0===/ipad/i.test(o),s=e.isiOS()&&!r,l=e.isiOS()||e.isAndroid(),a=l||n("(pointer:coarse)"),c=r||!s&&l&&n("(min-device-width:768px)"),i=s||l&&!c,m=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),d=!i&&!c&&!m;return{isiPad:g(r),isiPhone:g(s),isTablet:g(c),isPhone:g(i),isTouch:g(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:g(m),isDesktop:g(d)}})(l,s,e,o);return{browser:s,os:l,deviceType:a}})(navigator.userAgent,C.from(navigator.userAgentData),Eo)));const Bo=()=>No(),zo=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=zt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>L(t,((t,o)=>{const n=zt(e,o),r=void 0===n?0:parseInt(n,10);return isNaN(r)?t:t+r}),0);return{set:(t,o)=>{if(!u(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;kt(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}},Ao=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?C.none():C.some(t)})(e).getOr(t))(zt(e,t),o),Lo=zo("width",(e=>e.dom.offsetWidth)),Wo=e=>Lo.get(e),Mo=e=>Lo.getOuter(e),_o=e=>((e,t)=>{const o=e.dom,n=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?n:((e,t,o,n)=>t-Ao(e,"padding-left",0)-Ao(e,"padding-right",0)-Ao(e,"border-left-width",0)-Ao(e,"border-right-width",0))(e,n)})(e,"content-box"),jo=(e,t,o)=>{const n=e.cells,r=n.slice(0,t),s=n.slice(t),l=r.concat(o).concat(s);return Fo(e,l)},Po=(e,t,o)=>jo(e,t,[o]),Io=(e,t,o)=>{e.cells[t]=o},Fo=(e,t)=>tt(e.element,t,e.section,e.isNew),Ho=(e,t)=>e.cells[t],qo=(e,t)=>Ho(e,t).element,Vo=e=>e.cells.length,$o=e=>{const t=B(e,(e=>"colgroup"===e.section));return{rows:t.fail,cols:t.pass}},Uo=(e,t,o)=>{const n=E(e.cells,o);return tt(t(e.element),n,e.section,!0)},Go="data-snooker-locked-cols",Ko=e=>we(e,Go).bind((e=>C.from(e.match(/\d+/g)))).map((e=>I(e,x))),Yo=e=>{const t=L($o(e).rows,((e,t)=>(N(t.cells,((t,o)=>{t.isLocked&&(e[o]=!0)})),e)),{}),o=J(t,((e,t)=>parseInt(t,10)));return((e,t)=>{const o=S.call(e,0);return o.sort(void 0),o})(o)},Jo=(e,t)=>e+","+t,Qo=(e,t)=>{const o=j(e.all,(e=>e.cells));return z(o,t)},Xo=e=>{const t={},o=[],n=H(e).map((e=>e.element)).bind(Kt).bind(Ko).getOr({});let r=0,s=0,l=0;const{pass:a,fail:c}=B(e,(e=>"colgroup"===e.section));N(c,(e=>{const a=[];N(e.cells,(e=>{let o=0;for(;void 0!==t[Jo(l,o)];)o++;const r=((e,t)=>X(e,t)&&void 0!==e[t]&&null!==e[t])(n,o.toString()),c=((e,t,o,n,r,s)=>({element:e,rowspan:t,colspan:o,row:n,column:r,isLocked:s}))(e.element,e.rowspan,e.colspan,l,o,r);for(let n=0;n<e.colspan;n++)for(let r=0;r<e.rowspan;r++){const e=o+n,a=Jo(l+r,e);t[a]=c,s=Math.max(s,e+1)}a.push(c)})),r++,o.push(Ze(e.element,a,e.section)),l++}));const{columns:i,colgroups:m}=q(a).map((e=>{const t=(e=>{const t={};let o=0;return N(e.cells,(e=>{const n=e.colspan;k(n,(r=>{const s=o+r;t[s]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,n,s)})),o+=n})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,Q(t));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),d=((e,t)=>({rows:e,columns:t}))(r,s);return{grid:d,access:t,all:o,columns:i,colgroups:m}},Zo=e=>{const t=Zt(e);return Xo(t)},en=Xo,tn=(e,t,o)=>C.from(e.access[Jo(t,o)]),on=(e,t,o)=>{const n=Qo(e,(e=>o(t,e.element)));return n.length>0?C.some(n[0]):C.none()},nn=Qo,rn=e=>j(e.all,(e=>e.cells)),sn=e=>Q(e.columns),ln=e=>$(e.columns).length>0,an=(e,t)=>C.from(e.columns[t]),cn=(e,t=x)=>{const o=e.grid,n=k(o.columns,h),r=k(o.rows,h);return E(n,(o=>mn((()=>j(r,(t=>tn(e,t,o).filter((e=>e.column===o)).toArray()))),(e=>1===e.colspan&&t(e.element)),(()=>tn(e,0,o)))))},mn=(e,t,o)=>{const n=e();return W(n,t).orThunk((()=>C.from(n[0]).orThunk(o))).map((e=>e.element))},dn=e=>{const t=e.grid,o=k(t.rows,h),n=k(t.columns,h);return E(o,(t=>mn((()=>j(n,(o=>tn(e,t,o).filter((e=>e.row===t)).fold(g([]),(e=>[e]))))),(e=>1===e.rowspan),(()=>tn(e,t,0)))))},un=(e,t)=>o=>"rtl"===fn(o)?t:e,fn=e=>"rtl"===zt(e,"direction")?"rtl":"ltr",gn=zo("height",(e=>{const t=e.dom;return lt(e)?t.getBoundingClientRect().height:t.offsetHeight})),hn=e=>gn.get(e),pn=e=>gn.getOuter(e),wn=(e,t)=>({left:e,top:t,translate:(o,n)=>wn(e+o,t+n)}),bn=wn,vn=(e,t)=>void 0!==e?e:void 0!==t?t:0,yn=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,r=t.documentElement;if(o===e.dom)return bn(o.offsetLeft,o.offsetTop);const s=vn(null==n?void 0:n.pageYOffset,r.scrollTop),l=vn(null==n?void 0:n.pageXOffset,r.scrollLeft),a=vn(r.clientTop,o.clientTop),c=vn(r.clientLeft,o.clientLeft);return xn(e).translate(l-c,s-a)},xn=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?bn(o.offsetLeft,o.offsetTop):lt(e)?(e=>{const t=e.getBoundingClientRect();return bn(t.left,t.top)})(t):bn(0,0)},Cn=(e,t)=>({row:e,y:t}),Sn=(e,t)=>({col:e,x:t}),Tn=e=>yn(e).left+Mo(e),Rn=e=>yn(e).left,Dn=(e,t)=>Sn(e,Rn(t)),On=(e,t)=>Sn(e,Tn(t)),kn=e=>yn(e).top,En=(e,t)=>Cn(e,kn(t)),Nn=(e,t)=>Cn(e,kn(t)+pn(t)),Bn=(e,t,o)=>{if(0===o.length)return[];const n=E(o.slice(1),((t,o)=>t.map((t=>e(o,t))))),r=o[o.length-1].map((e=>t(o.length-1,e)));return n.concat([r])},zn={delta:h,positions:e=>Bn(En,Nn,e),edge:kn},An=un({delta:h,edge:Rn,positions:e=>Bn(Dn,On,e)},{delta:e=>-e,edge:Tn,positions:e=>Bn(On,Dn,e)}),Ln={delta:(e,t)=>An(t).delta(e,t),positions:(e,t)=>An(t).positions(e,t),edge:e=>An(e).edge(e)},Wn={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},Mn=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),_n=/(\d+(\.\d+)?)%/,jn=/(\d+(\.\d+)?)px|em/,Pn=ue("col"),In=(e,t,o)=>{const n=Be(e).getOrThunk((()=>at(ke(e))));return t(e)/o(n)*100},Fn=(e,t)=>{Nt(e,"width",t+"px")},Hn=(e,t)=>{Nt(e,"width",t+"%")},qn=(e,t)=>{Nt(e,"height",t+"px")},Vn=e=>{const t=(e=>{return Ao(t=e,"height",t.dom.offsetHeight)+"px";var t})(e);return t?((e,t,o,n)=>{const r=parseFloat(e);return Rt(e,"%")&&"table"!==ne(t)?((e,t,o,n)=>{const r=Kt(e).map((e=>{const n=o(e);return Math.floor(t/100*n)})).getOr(t);return n(e,r),r})(t,r,o,n):r})(t,e,hn,qn):hn(e)},$n=(e,t)=>Lt(e,t).orThunk((()=>we(e,t).map((e=>e+"px")))),Un=e=>$n(e,"width"),Gn=e=>In(e,Wo,_o),Kn=e=>{return Pn(e)?Wo(e):Ao(t=e,"width",t.dom.offsetWidth);var t},Yn=e=>((e,t,o)=>o(e)/_t(e,"rowspan"))(e,0,Vn),Jn=(e,t,o)=>{Nt(e,"width",t+o)},Qn=e=>In(e,Wo,_o)+"%",Xn=g(_n),Zn=ue("col"),er=e=>Un(e).getOrThunk((()=>Kn(e)+"px")),tr=e=>{return(t=e,$n(t,"height")).getOrThunk((()=>Yn(e)+"px"));var t},or=(e,t,o,n,r,s)=>e.filter(n).fold((()=>s(((e,t)=>{if(t<0||t>=e.length-1)return C.none();const o=e[t].fold((()=>{const o=(e=>{const t=S.call(e,0);return t.reverse(),t})(e.slice(0,t));return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:0}))),n=e[t+1].fold((()=>{const o=e.slice(t+1);return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:1})));return o.bind((e=>n.map((t=>{const o=t.delta+e.delta;return Math.abs(t.value-e.value)/o}))))})(o,t))),(e=>r(e))),nr=(e,t,o,n)=>{const r=cn(e),s=ln(e)?(e=>E(sn(e),(e=>C.from(e.element))))(e):r,l=[C.some(Ln.edge(t))].concat(E(Ln.positions(r,t),(e=>e.map((e=>e.x))))),a=b(jt);return E(s,((e,t)=>or(e,t,l,a,(e=>{if((e=>{const t=Bo().browser,o=t.isChromium()||t.isFirefox();return!Zn(e)||o})(e))return o(e);{const e=null!=(s=r[t])?h(s):C.none();return or(e,t,l,a,(e=>n(C.some(Wo(e)))),n)}var s}),n)))},rr=e=>e.map((e=>e+"px")).getOr(""),sr=(e,t,o)=>nr(e,t,Kn,(e=>e.getOrThunk(o.minCellWidth))),lr=(e,t,o,n,r)=>{const s=dn(e),l=[C.some(o.edge(t))].concat(E(o.positions(s,t),(e=>e.map((e=>e.y)))));return E(s,((e,t)=>or(e,t,l,b(Pt),n,r)))},ar=(e,t)=>()=>lt(e)?t(e):parseFloat(Lt(e,"width").getOr("0")),cr=e=>{const t=ar(e,(e=>parseFloat(Qn(e)))),o=ar(e,Wo);return{width:t,pixelWidth:o,getWidths:(t,o)=>((e,t,o)=>nr(e,t,Gn,(e=>e.fold((()=>o.minCellWidth()),(e=>e/o.pixelWidth()*100)))))(t,e,o),getCellDelta:e=>e/o()*100,singleColumnWidth:(e,t)=>[100-e],minCellWidth:()=>Ft()/o()*100,setElementWidth:Hn,adjustTableWidth:o=>{const n=t();Hn(e,n+o/100*n)},isRelative:!0,label:"percent"}},ir=e=>{const t=ar(e,Wo);return{width:t,pixelWidth:t,getWidths:(t,o)=>sr(t,e,o),getCellDelta:h,singleColumnWidth:(e,t)=>[Math.max(Ft(),e+t)-e],minCellWidth:Ft,setElementWidth:Fn,adjustTableWidth:o=>{const n=t()+o;Fn(e,n)},isRelative:!1,label:"pixel"}},mr=e=>Un(e).fold((()=>(e=>{const t=ar(e,Wo),o=g(0);return{width:t,pixelWidth:t,getWidths:(t,o)=>sr(t,e,o),getCellDelta:o,singleColumnWidth:g([0]),minCellWidth:o,setElementWidth:f,adjustTableWidth:f,isRelative:!0,label:"none"}})(e)),(t=>((e,t)=>null!==Xn().exec(t)?cr(e):ir(e))(e,t))),dr=ir,ur=cr,fr=(e,t,o)=>{const n=e[o].element,r=xe.fromTag("td");Ie(r,xe.fromTag("br")),(t?Ie:Pe)(n,r)},gr=((e,t)=>{const o=t=>e(t)?C.from(t.dom.nodeValue):C.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(ie),hr=e=>gr.get(e),pr=e=>gr.getOption(e),wr=(e,t)=>gr.set(e,t),br=e=>"img"===ne(e)?1:pr(e).fold((()=>We(e).length),(e=>e.length)),vr=["img","br"],yr=e=>pr(e).filter((e=>0!==e.trim().length||e.indexOf("\xa0")>-1)).isSome()||D(vr,ne(e))||(e=>ae(e)&&"false"===pe(e,"contenteditable"))(e),xr=e=>((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const r=xe.fromDom(e.childNodes[n]);if(t(r))return C.some(r);const s=o(e.childNodes[n]);if(s.isSome())return s}return C.none()};return o(e.dom)})(e,yr),Cr=e=>Sr(e,yr),Sr=(e,t)=>{const o=e=>{const n=We(e);for(let e=n.length-1;e>=0;e--){const r=n[e];if(t(r))return C.some(r);const s=o(r);if(s.isSome())return s}return C.none()};return o(e)},Tr={scope:["row","col"]},Rr=e=>()=>{const t=xe.fromTag("td",e.dom);return Ie(t,xe.fromTag("br",e.dom)),t},Dr=e=>()=>xe.fromTag("col",e.dom),Or=e=>()=>xe.fromTag("colgroup",e.dom),kr=e=>()=>xe.fromTag("tr",e.dom),Er=(e,t,o)=>{const n=((e,t)=>{const o=Je(e,t),n=We(Ye(e));return qe(o,n),o})(e,t);return G(o,((e,t)=>{null===e?be(n,t):ge(n,t,e)})),n},Nr=e=>e,Br=(e,t,o)=>{const n=(e,t)=>{((e,t)=>{const o=e.dom,n=t.dom;kt(o)&&kt(n)&&(n.style.cssText=o.style.cssText)})(e.element,t),Wt(t,"height"),1!==e.colspan&&Wt(t,"width")};return{col:o=>{const r=xe.fromTag(ne(o.element),t.dom);return n(o,r),e(o.element,r),r},colgroup:Or(t),row:kr(t),cell:r=>{const s=xe.fromTag(ne(r.element),t.dom),l=o.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),a=l.length>0?((e,t,o)=>xr(e).map((n=>{const r=o.join(","),s=it(n,r,(t=>Re(t,e)));return A(s,((e,t)=>{const o=Ke(t);return Ie(e,o),o}),t)})).getOr(t))(r.element,s,l):s;return Ie(a,xe.fromTag("br")),n(r,s),((e,t)=>{G(Tr,((o,n)=>we(e,n).filter((e=>D(o,e))).each((e=>ge(t,n,e)))))})(r.element,s),e(r.element,s),s},replace:Er,colGap:Dr(t),gap:Rr(t)}},zr=e=>({col:Dr(e),colgroup:Or(e),row:kr(e),cell:Rr(e),replace:Nr,colGap:Dr(e),gap:Rr(e)}),Ar=e=>bt(e,"[contenteditable]"),Lr=(e,t=!1)=>lt(e)?e.dom.isContentEditable:Ar(e).fold(g(t),(e=>"true"===Wr(e))),Wr=e=>e.dom.contentEditable,Mr=e=>xe.fromDom(e.getBody()),_r=e=>t=>Re(t,Mr(e)),jr=e=>{be(e,"data-mce-style");const t=e=>be(e,"data-mce-style");N(Ut(e),t),N(Gt(e),t),N(Yt(e),t)},Pr=e=>xe.fromDom(e.selection.getStart()),Ir=e=>e.getBoundingClientRect().width,Fr=e=>e.getBoundingClientRect().height,Hr=e=>gt(e,ue("table")).exists(Lr),qr=(e,t)=>{const o=t.column,n=t.column+t.colspan-1,r=t.row,s=t.row+t.rowspan-1;return o<=e.finishCol&&n>=e.startCol&&r<=e.finishRow&&s>=e.startRow},Vr=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,$r=(e,t,o)=>{const n=on(e,t,Re),r=on(e,o,Re);return n.bind((e=>r.map((t=>{return o=e,n=t,{startRow:Math.min(o.row,n.row),startCol:Math.min(o.column,n.column),finishRow:Math.max(o.row+o.rowspan-1,n.row+n.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,n.column+n.colspan-1)};var o,n}))))},Ur=(e,t,o)=>$r(e,t,o).map((t=>{const o=nn(e,w(qr,t));return E(o,(e=>e.element))})),Gr=(e,t)=>on(e,t,((e,t)=>De(t,e))).map((e=>e.element)),Kr=(e,t,o)=>{const n=Jr(e);return Ur(n,t,o)},Yr=(e,t,o,n,r)=>{const s=Jr(e),l=Re(e,o)?C.some(t):Gr(s,t),a=Re(e,r)?C.some(n):Gr(s,n);return l.bind((e=>a.bind((t=>Ur(s,e,t)))))},Jr=Zo;var Qr=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Xr=()=>({up:g({selector:ht,closest:bt,predicate:ft,all:ze}),down:g({selector:dt,predicate:ct}),styles:g({get:zt,getRaw:Lt,set:Nt,remove:Wt}),attrs:g({get:pe,set:ge,remove:be,copyTo:(e,t)=>{const o=ve(e);he(t,o)}}),insert:g({before:_e,after:je,afterAll:He,append:Ie,appendAll:qe,prepend:Pe,wrap:Fe}),remove:g({unwrap:Ue,remove:$e}),create:g({nu:xe.fromTag,clone:e=>xe.fromDom(e.dom.cloneNode(!1)),text:xe.fromText}),query:g({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:Ae,nextSibling:Le}),property:g({children:We,name:ne,parent:Ne,document:e=>Ee(e).dom,isText:ie,isComment:le,isElement:ce,isSpecial:e=>{const t=ne(e);return D(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>ce(e)?we(e,"lang"):C.none(),getText:hr,setText:wr,isBoundary:e=>!!ce(e)&&("body"===ne(e)||D(Qr,ne(e))),isEmptyTag:e=>!!ce(e)&&D(["br","img","hr","input"],ne(e)),isNonEditable:e=>ce(e)&&"false"===pe(e,"contenteditable")}),eq:Re,is:Oe});const Zr=(e,t,o,n)=>{const r=t(e,o);return A(n,((o,n)=>{const r=t(e,n);return es(e,o,r)}),r)},es=(e,t,o)=>t.bind((t=>o.filter(w(e.eq,t)))),ts=Xr(),os=(e,t)=>((e,t,o)=>o.length>0?((e,t,o,n)=>n(e,t,o[0],o.slice(1)))(e,t,o,Zr):C.none())(ts,((t,o)=>e(o)),t),ns=e=>ht(e,"table"),rs=(e,t,o)=>{const n=e=>t=>void 0!==o&&o(t)||Re(t,e);return Re(e,t)?C.some({boxes:C.some([e]),start:e,finish:t}):ns(e).bind((r=>ns(t).bind((s=>{if(Re(r,s))return C.some({boxes:Kr(r,e,t),start:e,finish:t});if(De(r,s)){const o=it(t,"td,th",n(r)),l=o.length>0?o[o.length-1]:t;return C.some({boxes:Yr(r,e,r,t,s),start:e,finish:l})}if(De(s,r)){const o=it(e,"td,th",n(s)),l=o.length>0?o[o.length-1]:e;return C.some({boxes:Yr(s,e,r,t,s),start:e,finish:l})}return((e,t,o)=>((e,t,o,n=y)=>{const r=[t].concat(e.up().all(t)),s=[o].concat(e.up().all(o)),l=e=>M(e,n).fold((()=>e),(t=>e.slice(0,t+1))),a=l(r),c=l(s),i=W(a,(t=>O(c,((e,t)=>w(e.eq,t))(e,t))));return{firstpath:a,secondpath:c,shared:i}})(ts,e,t,void 0))(e,t).shared.bind((l=>bt(l,"table",o).bind((o=>{const l=it(t,"td,th",n(o)),a=l.length>0?l[l.length-1]:t,c=it(e,"td,th",n(o)),i=c.length>0?c[c.length-1]:e;return C.some({boxes:Yr(o,e,r,t,s),start:i,finish:a})}))))}))))},ss=(e,t)=>{const o=dt(e,t);return o.length>0?C.some(o):C.none()},ls=(e,t,o)=>wt(e,t).bind((t=>wt(e,o).bind((e=>os(ns,[t,e]).map((o=>({first:t,last:e,table:o}))))))),as=(e,t,o,n,r)=>((e,t)=>W(e,(e=>Ce(e,t))))(e,r).bind((e=>((e,t,o)=>Kt(e).bind((n=>((e,t,o,n)=>on(e,t,Re).bind((t=>{const r=o>0?t.row+t.rowspan-1:t.row,s=n>0?t.column+t.colspan-1:t.column;return tn(e,r+o,s+n).map((e=>e.element))})))(Jr(n),e,t,o))))(e,t,o).bind((e=>((e,t)=>ht(e,"table").bind((o=>wt(o,t).bind((t=>rs(t,e).bind((e=>e.boxes.map((t=>({boxes:t,start:e.start,finish:e.finish}))))))))))(e,n))))),cs=(e,t)=>ss(e,t),is=(e,t,o)=>ls(e,t,o).bind((t=>{const o=t=>Re(e,t),n="thead,tfoot,tbody,table",r=ht(t.first,n,o),s=ht(t.last,n,o);return r.bind((e=>s.bind((o=>Re(e,o)?((e,t,o)=>((e,t,o)=>$r(e,t,o).bind((t=>((e,t)=>{let o=!0;const n=w(Vr,t);for(let r=t.startRow;r<=t.finishRow;r++)for(let s=t.startCol;s<=t.finishCol;s++)o=o&&tn(e,r,s).exists(n);return o?C.some(t):C.none()})(e,t))))(Jr(e),t,o))(t.table,t.first,t.last):C.none()))))})),ms=h,ds=e=>{const t=(e,t)=>we(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&P(e,(e=>t(e,"rowspan")||t(e,"colspan")))?C.some(e):C.none()},us=(e,t,o)=>t.length<=1?C.none():is(e,o.firstSelectedSelector,o.lastSelectedSelector).map((e=>({bounds:e,cells:t}))),fs={selected:"data-mce-selected",selectedSelector:"td[data-mce-selected],th[data-mce-selected]",firstSelected:"data-mce-first-selected",firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:"data-mce-last-selected",lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"},gs=(e,t,o)=>({element:o,mergable:us(t,e,fs),unmergable:ds(e),selection:ms(e)}),hs=e=>(t,o)=>{const n=ne(t),r="col"===n||"colgroup"===n?Kt(s=t).bind((e=>cs(e,fs.firstSelectedSelector))).fold(g(s),(e=>e[0])):t;var s;return bt(r,e,o)},ps=hs("th,td,caption"),ws=hs("th,td"),bs=e=>{return t=e.model.table.getSelectedCells(),E(t,xe.fromDom);var t},vs=(e,t)=>{e.on("BeforeGetContent",(t=>{const o=o=>{t.preventDefault(),(e=>Kt(e[0]).map((e=>{const t=((e,t)=>{const o=e=>Ce(e.element,t),n=Ye(e),r=Zt(n),s=mr(e),l=en(r),a=((e,t)=>{const o=e.grid.columns;let n=e.grid.rows,r=o,s=0,l=0;const a=[],c=[];return G(e.access,(e=>{if(a.push(e),t(e)){c.push(e);const t=e.row,o=t+e.rowspan-1,a=e.column,i=a+e.colspan-1;t<n?n=t:o>s&&(s=o),a<r?r=a:i>l&&(l=i)}})),((e,t,o,n,r,s)=>({minRow:e,minCol:t,maxRow:o,maxCol:n,allCells:r,selectedCells:s}))(n,r,s,l,a,c)})(l,o),c="th:not("+t+"),td:not("+t+")",i=Vt(n,"th,td",(e=>Ce(e,c)));N(i,$e),((e,t,o,n)=>{const r=z(e,(e=>"colgroup"!==e.section)),s=t.grid.columns,l=t.grid.rows;for(let e=0;e<l;e++){let l=!1;for(let a=0;a<s;a++)e<o.minRow||e>o.maxRow||a<o.minCol||a>o.maxCol||(tn(t,e,a).filter(n).isNone()?fr(r,l,e):l=!0)}})(r,l,a,o);const m=((e,t,o,n)=>{if(0===n.minCol&&t.grid.columns===n.maxCol+1)return 0;const r=sr(t,e,o),s=L(r,((e,t)=>e+t),0),l=L(r.slice(n.minCol,n.maxCol+1),((e,t)=>e+t),0),a=l/s*o.pixelWidth()-o.pixelWidth();return o.getCellDelta(a)})(e,Zo(e),s,a);return((e,t,o,n)=>{G(o.columns,(e=>{(e.column<t.minCol||e.column>t.maxCol)&&$e(e.element)}));const r=z(qt(e,"tr"),(e=>0===e.dom.childElementCount));N(r,$e),t.minCol!==t.maxCol&&t.minRow!==t.maxRow||N(qt(e,"th,td"),(e=>{be(e,"rowspan"),be(e,"colspan")})),be(e,Go),be(e,"data-snooker-col-series"),mr(e).adjustTableWidth(n)})(n,a,l,m),n})(e,"[data-mce-selected]");return jr(t),[t]})))(o).each((o=>{t.content="text"===t.format?(e=>E(e,(e=>e.dom.innerText)).join(""))(o):((e,t)=>E(t,(t=>e.selection.serializer.serialize(t.dom,{}))).join(""))(e,o)}))};if(!0===t.selection){const t=(e=>z(bs(e),(e=>Ce(e,fs.selectedSelector))))(e);t.length>=1&&o(t)}})),e.on("BeforeSetContent",(o=>{if(!0===o.selection&&!0===o.paste){const n=bs(e);H(n).each((n=>{Kt(n).each((r=>{const s=z(((e,t)=>{const o=document.createElement("div");return o.innerHTML=e,We(xe.fromDom(o))})(o.content),(e=>"meta"!==ne(e))),l=ue("table");if(1===s.length&&l(s[0])){o.preventDefault();const l=xe.fromDom(e.getDoc()),a=zr(l),c=((e,t,o)=>({element:e,clipboard:t,generators:o}))(n,s[0],a);t.pasteCells(r,c).each((()=>{e.focus()}))}}))}))}}))},ys=(e,t)=>({element:e,offset:t}),xs=(e,t,o)=>e.property().isText(t)&&0===e.property().getText(t).trim().length||e.property().isComment(t)?o(t).bind((t=>xs(e,t,o).orThunk((()=>C.some(t))))):C.none(),Cs=(e,t)=>e.property().isText(t)?e.property().getText(t).length:e.property().children(t).length,Ss=(e,t)=>{const o=xs(e,t,e.query().prevSibling).getOr(t);if(e.property().isText(o))return ys(o,Cs(e,o));const n=e.property().children(o);return n.length>0?Ss(e,n[n.length-1]):ys(o,Cs(e,o))},Ts=Ss,Rs=Xr(),Ds=(e,t)=>{if(!jt(e)){const o=(e=>Un(e).bind((e=>{return t=e,o=["fixed","relative","empty"],C.from(Mn.exec(t)).bind((e=>{const t=Number(e[1]),n=e[2];return((e,t)=>O(t,(t=>O(Wn[t],(t=>e===t)))))(n,o)?C.some({value:t,unit:n}):C.none()}));var t,o})))(e);o.each((o=>{const n=o.value/2;Jn(e,n,o.unit),Jn(t,n,o.unit)}))}},Os=e=>E(e,g(0)),ks=(e,t,o,n,r)=>r(e.slice(0,t)).concat(n).concat(r(e.slice(o))),Es=e=>(t,o,n,r)=>{if(e(n)){const e=Math.max(r,t[o]-Math.abs(n)),s=Math.abs(e-t[o]);return n>=0?s:-s}return n},Ns=Es((e=>e<0)),Bs=Es(x),zs=()=>{const e=(e,t,o,n)=>{const r=(100+o)/100,s=Math.max(n,(e[t]+o)/r);return E(e,((e,o)=>(o===t?s:e/r)-e))},t=(t,o,n,r,s,l)=>l?e(t,o,r,s):((e,t,o,n,r)=>{const s=Ns(e,t,n,r);return ks(e,t,o+1,[s,0],Os)})(t,o,n,r,s);return{resizeTable:(e,t)=>e(t),clampTableDelta:Ns,calcLeftEdgeDeltas:t,calcMiddleDeltas:(e,o,n,r,s,l,a)=>t(e,n,r,s,l,a),calcRightEdgeDeltas:(t,o,n,r,s,l)=>{if(l)return e(t,n,r,s);{const e=Ns(t,n,r,s);return Os(t.slice(0,n)).concat([e])}},calcRedestributedWidths:(e,t,o,n)=>{if(n){const n=(t+o)/t,r=E(e,(e=>e/n));return{delta:100*n-100,newSizes:r}}return{delta:o,newSizes:e}}}},As=()=>{const e=(e,t,o,n,r)=>{const s=Bs(e,n>=0?o:t,n,r);return ks(e,t,o+1,[s,-s],Os)};return{resizeTable:(e,t,o)=>{o&&e(t)},clampTableDelta:(e,t,o,n,r)=>{if(r){if(o>=0)return o;{const t=L(e,((e,t)=>e+t-n),0);return Math.max(-t,o)}}return Ns(e,t,o,n)},calcLeftEdgeDeltas:e,calcMiddleDeltas:(t,o,n,r,s,l)=>e(t,n,r,s,l),calcRightEdgeDeltas:(e,t,o,n,r,s)=>{if(s)return Os(e);{const t=n/e.length;return E(e,g(t))}},calcRedestributedWidths:(e,t,o,n)=>({delta:0,newSizes:e})}},Ls=e=>Zo(e).grid,Ws=ue("th"),Ms=e=>P(e,(e=>Ws(e.element))),_s=(e,t)=>e&&t?"sectionCells":e?"section":"cells",js=e=>{const t="thead"===e.section,o=vt(Ps(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:_s(t,o)}:{type:"body"}},Ps=e=>{const t=z(e,(e=>Ws(e.element)));return 0===t.length?C.some("td"):t.length===e.length?C.some("th"):C.none()},Is=(e,t,o)=>et(o(e.element,t),!0,e.isLocked),Fs=(e,t)=>e.section!==t?tt(e.element,e.cells,t,e.isNew):e,Hs=()=>({transformRow:Fs,transformCell:(e,t,o)=>{const n=o(e.element,t),r="td"!==ne(n)?((e,t)=>{const o=Je(e,"td");je(e,o);const n=We(e);return qe(o,n),$e(e),o})(n):n;return et(r,e.isNew,e.isLocked)}}),qs=()=>({transformRow:Fs,transformCell:Is}),Vs=()=>({transformRow:(e,t)=>Fs(e,"thead"===t?"tbody":t),transformCell:Is}),$s=Hs,Us=qs,Gs=Vs,Ks=()=>({transformRow:h,transformCell:Is}),Ys=(e,t,o,n)=>{o===n?be(e,t):ge(e,t,o)},Js=(e,t,o)=>{q(mt(e,t)).fold((()=>Pe(e,o)),(e=>je(e,o)))},Qs=(e,t)=>{const o=[],n=[],r=e=>E(e,(e=>{e.isNew&&o.push(e.element);const t=e.element;return Ve(t),N(e.cells,(e=>{e.isNew&&n.push(e.element),Ys(e.element,"colspan",e.colspan,1),Ys(e.element,"rowspan",e.rowspan,1),Ie(t,e.element)})),t})),s=e=>j(e,(e=>E(e.cells,(e=>(Ys(e.element,"span",e.colspan,1),e.element))))),l=(t,o)=>{const n=((e,t)=>{const o=pt(e,t).getOrThunk((()=>{const o=xe.fromTag(t,ke(e).dom);return"thead"===t?Js(e,"caption,colgroup",o):"colgroup"===t?Js(e,"caption",o):Ie(e,o),o}));return Ve(o),o})(e,o),l=("colgroup"===o?s:r)(t);qe(n,l)},a=(t,o)=>{t.length>0?l(t,o):(t=>{pt(e,t).each($e)})(o)},c=[],i=[],m=[],d=[];return N(t,(e=>{switch(e.section){case"thead":c.push(e);break;case"tbody":i.push(e);break;case"tfoot":m.push(e);break;case"colgroup":d.push(e)}})),a(d,"colgroup"),a(c,"thead"),a(i,"tbody"),a(m,"tfoot"),{newRows:o,newCells:n}},Xs=(e,t)=>{if(0===e.length)return 0;const o=e[0];return M(e,(e=>!t(o.element,e.element))).getOr(e.length)},Zs=(e,t)=>{const o=E(e,(e=>E(e.cells,y)));return E(e,((n,r)=>{const s=j(n.cells,((n,s)=>{if(!1===o[r][s]){const m=((e,t,o,n)=>{const r=((e,t)=>e[t])(e,t),s="colgroup"===r.section,l=Xs(r.cells.slice(o),n),a=s?1:Xs(((e,t)=>E(e,(e=>Ho(e,t))))(e.slice(t),o),n);return{colspan:l,rowspan:a}})(e,r,s,t);return((e,t,n,r)=>{for(let s=e;s<e+n;s++)for(let e=t;e<t+r;e++)o[s][e]=!0})(r,s,m.rowspan,m.colspan),[(l=n.element,a=m.rowspan,c=m.colspan,i=n.isNew,{element:l,rowspan:a,colspan:c,isNew:i})]}return[];var l,a,c,i}));return((e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}))(n.element,s,n.section,n.isNew)}))},el=(e,t,o)=>{const n=[];N(e.colgroups,(r=>{const s=[];for(let n=0;n<e.grid.columns;n++){const r=an(e,n).map((e=>et(e.element,o,!1))).getOrThunk((()=>et(t.colGap(),!0,!1)));s.push(r)}n.push(tt(r.element,s,"colgroup",o))}));for(let r=0;r<e.grid.rows;r++){const s=[];for(let n=0;n<e.grid.columns;n++){const l=tn(e,r,n).map((e=>et(e.element,o,e.isLocked))).getOrThunk((()=>et(t.gap(),!0,!1)));s.push(l)}const l=e.all[r],a=tt(l.element,s,l.section,o);n.push(a)}return n},tl=e=>Zs(e,Re),ol=(e,t)=>V(e.all,(e=>W(e.cells,(e=>Re(t,e.element))))),nl=(e,t,o)=>{const n=E(t.selection,(t=>$t(t).bind((t=>ol(e,t))).filter(o))),r=yt(n);return xt(r.length>0,r)},rl=(e,t,o,n,r)=>(s,l,a,c)=>{const i=Zo(s),m=C.from(null==c?void 0:c.section).getOrThunk(Ks);return t(i,l).map((t=>{const o=((e,t)=>el(e,t,!1))(i,a),n=e(o,t,Re,r(a),m),s=Yo(n.grid);return{info:t,grid:tl(n.grid),cursor:n.cursor,lockedColumns:s}})).bind((e=>{const t=Qs(s,e.grid),r=C.from(null==c?void 0:c.sizing).getOrThunk((()=>mr(s))),l=C.from(null==c?void 0:c.resize).getOrThunk(As);return o(s,e.grid,e.info,{sizing:r,resize:l,section:m}),n(s),be(s,Go),e.lockedColumns.length>0&&ge(s,Go,e.lockedColumns.join(",")),C.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})}))},sl=(e,t)=>nl(e,t,x).map((e=>({cells:e,generators:t.generators,clipboard:t.clipboard}))),ll=(e,t)=>nl(e,t,x),al=(e,t)=>nl(e,t,(e=>!e.isLocked)),cl=(e,t)=>P(t,(t=>((e,t)=>ol(e,t).exists((e=>!e.isLocked)))(e,t))),il=(e,t,o,n)=>{const r=$o(e).rows;let s=!0;for(let e=0;e<r.length;e++)for(let l=0;l<Vo(r[0]);l++){const a=r[e],c=Ho(a,l),i=o(c.element,t);i&&!s?Io(a,l,et(n(),!0,c.isLocked)):i&&(s=!1)}return e},ml=e=>{const t=t=>t(e),o=g(e),n=()=>r,r={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:x,isError:y,map:t=>ul.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>C.some(e)};return r},dl=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:y,isError:x,map:t,mapError:t=>ul.error(t(e)),bind:t,exists:y,forall:x,getOr:h,or:h,getOrThunk:v,orThunk:v,getOrDie:(n=String(e),()=>{throw new Error(n)}),each:f,toOptional:C.none};var n;return o},ul={value:ml,error:dl,fromOption:(e,t)=>e.fold((()=>dl(t)),ml)},fl=(e,t)=>({rowDelta:0,colDelta:Vo(e[0])-Vo(t[0])}),gl=(e,t)=>({rowDelta:e.length-t.length,colDelta:0}),hl=(e,t,o,n)=>{const r="colgroup"===t.section?o.col:o.cell;return k(e,(e=>et(r(),!0,n(e))))},pl=(e,t,o,n)=>{const r=e[e.length-1];return e.concat(k(t,(()=>{const e="colgroup"===r.section?o.colgroup:o.row,t=Uo(r,e,h),s=hl(t.cells.length,t,o,(e=>X(n,e.toString())));return Fo(t,s)})))},wl=(e,t,o,n)=>E(e,(e=>{const r=hl(t,e,o,y);return jo(e,n,r)})),bl=(e,t,o)=>{const n=t.colDelta<0?wl:h,r=t.rowDelta<0?pl:h,s=Yo(e),l=Vo(e[0]),a=O(s,(e=>e===l-1)),c=n(e,Math.abs(t.colDelta),o,a?l-1:l),i=Yo(c);return r(c,Math.abs(t.rowDelta),o,I(i,x))},vl=(e,t,o,n)=>{const r=w(n,Ho(e[t],o).element),s=e[t];return e.length>1&&Vo(s)>1&&(o>0&&r(qo(s,o-1))||o<s.cells.length-1&&r(qo(s,o+1))||t>0&&r(qo(e[t-1],o))||t<e.length-1&&r(qo(e[t+1],o)))},yl=(e,t,o)=>z(o,(o=>o>=e.column&&o<=Vo(t[0])+e.column)),xl=(e,t,o,n,r)=>{((e,t,o,n)=>{t>0&&t<e[0].cells.length&&N(e,(e=>{const r=e.cells[t-1];let s=0;const l=n();for(;e.cells.length>t+s&&o(r.element,e.cells[t+s].element);)Io(e,t+s,et(l,!0,e.cells[t+s].isLocked)),s++}))})(t,e,r,n.cell);const s=gl(o,t),l=bl(o,s,n),a=gl(t,l),c=bl(t,a,n);return E(c,((t,o)=>jo(t,e,l[o].cells)))},Cl=(e,t,o,n,r)=>{((e,t,o,n)=>{const r=$o(e).rows;if(t>0&&t<r.length){const e=((e,t)=>L(e,((e,o)=>O(e,(e=>t(e.element,o.element)))?e:e.concat([o])),[]))(r[t-1].cells,o);N(e,(e=>{let s=C.none();for(let l=t;l<r.length;l++)for(let t=0;t<Vo(r[0]);t++){const a=r[l],c=Ho(a,t);o(c.element,e.element)&&(s.isNone()&&(s=C.some(n())),s.each((e=>{Io(a,t,et(e,!0,c.isLocked))})))}}))}})(t,e,r,n.cell);const s=Yo(t),l=fl(t,o),a={...l,colDelta:l.colDelta-s.length},c=bl(t,a,n),{cols:i,rows:m}=$o(c),d=Yo(c),u=fl(o,t),f={...u,colDelta:u.colDelta+d.length},g=(p=n,w=d,E(o,(e=>L(w,((t,o)=>{const n=hl(1,e,p,x)[0];return Po(t,o,n)}),e)))),h=bl(g,f,n);var p,w;return[...i,...m.slice(0,e),...h,...m.slice(e,m.length)]},Sl=(e,t,o,n,r)=>{const{rows:s,cols:l}=$o(e),a=s.slice(0,t),c=s.slice(t);return[...l,...a,((e,t,o,n)=>Uo(e,(e=>n(e,o)),t))(s[o],((e,o)=>t>0&&t<s.length&&n(qo(s[t-1],o),qo(s[t],o))?Ho(s[t],o):et(r(e.element,n),!0,e.isLocked)),n,r),...c]},Tl=(e,t,o,n,r)=>E(e,(e=>{const s=t>0&&t<Vo(e)&&n(qo(e,t-1),qo(e,t)),l=((e,t,o,n,r,s,l)=>{if("colgroup"!==o&&n)return Ho(e,t);{const t=Ho(e,r);return et(l(t.element,s),!0,!1)}})(e,t,e.section,s,o,n,r);return Po(e,t,l)})),Rl=(e,t,o,n)=>((e,t,o,n)=>void 0!==qo(e[t],o)&&t>0&&n(qo(e[t-1],o),qo(e[t],o)))(e,t,o,n)||((e,t,o)=>t>0&&o(qo(e,t-1),qo(e,t)))(e[t],o,n),Dl=(e,t,o,n)=>{const r=e=>(e=>"row"===e?Pt(t):jt(t))(e)?`${e}group`:e;return e?Ws(t)?r(o):null:n&&Ws(t)?r("row"===o?"col":"row"):null},Ol=(e,t,o)=>et(o(e.element,t),!0,e.isLocked),kl=(e,t,o,n,r,s,l)=>E(e,((e,a)=>((e,c)=>{const i=e.cells,m=E(i,((e,c)=>{if((e=>O(t,(t=>o(e.element,t.element))))(e)){const t=l(e,a,c)?r(e,o,n):e;return s(t,a,c).each((e=>{var o,n;o=t.element,n={scope:C.from(e)},G(n,((e,t)=>{e.fold((()=>{be(o,t)}),(e=>{fe(o.dom,t,e)}))}))})),t}return e}));return tt(e.element,m,e.section,e.isNew)})(e))),El=(e,t,o)=>j(e,((n,r)=>Rl(e,r,t,o)?[]:[Ho(n,t)])),Nl=(e,t,o,n,r)=>{const s=$o(e).rows,l=j(t,(e=>El(s,e,n))),a=E(s,(e=>Ms(e.cells))),c=((e,t)=>P(t,h)&&Ms(e)?x:(e,o,n)=>!("th"===ne(e.element)&&t[o]))(l,a),i=((e,t)=>(o,n)=>C.some(Dl(e,o.element,"row",t[n])))(o,a);return kl(e,l,n,r,Ol,i,c)},Bl=(e,t,o,n)=>{const r=$o(e).rows,s=E(t,(e=>Ho(r[e.row],e.column)));return kl(e,s,o,n,Ol,C.none,x)},zl=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,r)=>{const s=$(n);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],c=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(c))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==c.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+c.length+" ("+c+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const n=$(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!P(t,(e=>D(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},Al={...zl([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}])},Ll=(e,t,o)=>{let n=0;for(let r=e;r<t;r++)n+=void 0!==o[r]?o[r]:0;return n},Wl=(e,t)=>{const o=rn(e);return E(o,(e=>{const o=Ll(e.row,e.row+e.rowspan,t);return{element:e.element,height:o,rowspan:e.rowspan}}))},Ml=(e,t,o)=>{const n=((e,t)=>ln(e)?((e,t)=>{const o=sn(e);return E(o,((e,o)=>({element:e.element,width:t[o],colspan:e.colspan})))})(e,t):((e,t)=>{const o=rn(e);return E(o,(e=>{const o=Ll(e.column,e.column+e.colspan,t);return{element:e.element,width:o,colspan:e.colspan}}))})(e,t))(e,t);N(n,(e=>{o.setElementWidth(e.element,e.width)}))},_l=(e,t,o,n,r)=>{const s=Zo(e),l=r.getCellDelta(t),a=r.getWidths(s,r),c=o===s.grid.columns-1,i=n.clampTableDelta(a,o,l,r.minCellWidth(),c),m=((e,t,o,n,r)=>{const s=e.slice(0),l=((e,t)=>0===e.length?Al.none():1===e.length?Al.only(0):0===t?Al.left(0,1):t===e.length-1?Al.right(t-1,t):t>0&&t<e.length-1?Al.middle(t-1,t,t+1):Al.none())(e,t),a=g(E(s,g(0)));return l.fold(a,(e=>n.singleColumnWidth(s[e],o)),((e,t)=>r.calcLeftEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)),((e,t,l)=>r.calcMiddleDeltas(s,e,t,l,o,n.minCellWidth(),n.isRelative)),((e,t)=>r.calcRightEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)))})(a,o,i,r,n),d=E(m,((e,t)=>e+a[t]));Ml(s,d,r),n.resizeTable(r.adjustTableWidth,i,c)},jl=e=>L(e,((e,t)=>O(e,(e=>e.column===t.column))?e:e.concat([t])),[]).sort(((e,t)=>e.column-t.column)),Pl=ue("col"),Il=ue("colgroup"),Fl=e=>"tr"===ne(e)||Il(e),Hl=e=>({element:e,colspan:Mt(e,"colspan",1),rowspan:Mt(e,"rowspan",1)}),ql=e=>we(e,"scope").map((e=>e.substr(0,3))),Vl=(e,t=Hl)=>{const o=o=>{if(Fl(o))return Il((r={element:o}).element)?e.colgroup(r):e.row(r);{const r=o,s=(t=>Pl(t.element)?e.col(t):e.cell(t))(t(r));return n=C.some({item:r,replacement:s}),s}var r};let n=C.none();return{getOrInit:(e,t)=>n.fold((()=>o(e)),(n=>t(e,n.item)?n.replacement:o(e)))}},$l=e=>t=>{const o=[],n=n=>{const r="td"===e?{scope:null}:{},s=t.replace(n,e,r);return o.push({item:n,sub:s}),s};return{replaceOrInit:(e,t)=>{if(Fl(e)||Pl(e))return e;{const r=e;return((e,t)=>W(o,(o=>t(o.item,e))))(r,t).fold((()=>n(r)),(o=>t(e,o.item)?o.sub:n(r)))}}}},Ul=e=>({unmerge:t=>{const o=ql(t);return o.each((e=>ge(t,"scope",e))),()=>{const n=e.cell({element:t,colspan:1,rowspan:1});return Wt(n,"width"),Wt(t,"width"),o.each((e=>ge(n,"scope",e))),n}},merge:e=>(Wt(e[0],"width"),(()=>{const t=yt(E(e,ql));if(0===t.length)return C.none();{const e=t[0],o=["row","col"];return O(t,(t=>t!==e&&D(o,t)))?C.none():C.from(e)}})().fold((()=>be(e[0],"scope")),(t=>ge(e[0],"scope",t+"group"))),g(e[0]))}),Gl=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Kl=Xr(),Yl=e=>((e,t)=>{const o=e.property().name(t);return D(Gl,o)})(Kl,e),Jl=e=>((e,t)=>{const o=e.property().name(t);return D(["ol","ul"],o)})(Kl,e),Ql=e=>{const t=ue("br"),o=e=>Cr(e).bind((o=>{const n=Le(o).map((e=>!!Yl(e)||!!((e,t)=>D(["br","img","hr","input"],e.property().name(t)))(Kl,e)&&"img"!==ne(e))).getOr(!1);return Ne(o).map((r=>{return!0===n||("li"===ne(s=r)||ft(s,Jl).isSome())||t(o)||Yl(r)&&!Re(e,r)?[]:[xe.fromTag("br")];var s}))})).getOr([]),n=(()=>{const n=j(e,(e=>{const n=We(e);return(e=>P(e,(e=>t(e)||ie(e)&&0===hr(e).trim().length)))(n)?[]:n.concat(o(e))}));return 0===n.length?[xe.fromTag("br")]:n})();Ve(e[0]),qe(e[0],n)},Xl=e=>Lr(e,!0),Zl=e=>{0===Ut(e).length&&$e(e)},ea=(e,t)=>({grid:e,cursor:t}),ta=(e,t,o)=>{const n=((e,t,o)=>{var n,r;const s=$o(e).rows;return C.from(null===(r=null===(n=s[t])||void 0===n?void 0:n.cells[o])||void 0===r?void 0:r.element).filter(Xl).orThunk((()=>(e=>V(e,(e=>V(e.cells,(e=>{const t=e.element;return xt(Xl(t),t)})))))(s)))})(e,t,o);return ea(e,n)},oa=e=>L(e,((e,t)=>O(e,(e=>e.row===t.row))?e:e.concat([t])),[]).sort(((e,t)=>e.row-t.row)),na=(e,t)=>(o,n,r,s,l)=>{const a=oa(n),c=E(a,(e=>e.row)),i=((e,t,o,n,r,s,l)=>{const{cols:a,rows:c}=$o(e),i=c[t[0]],m=j(t,(e=>((e,t,o)=>{const n=e[t];return j(n.cells,((n,r)=>Rl(e,t,r,o)?[]:[n]))})(c,e,r))),d=E(i.cells,((e,t)=>Ms(El(c,t,r)))),u=[...c];N(t,(e=>{u[e]=l.transformRow(c[e],o)}));const f=[...a,...u],g=((e,t)=>P(t,h)&&Ms(e.cells)?x:(e,o,n)=>!("th"===ne(e.element)&&t[n]))(i,d),p=((e,t)=>(o,n,r)=>C.some(Dl(e,o.element,"col",t[r])))(n,d);return kl(f,m,r,s,l.transformCell,p,g)})(o,c,e,t,r,s.replaceOrInit,l);return ta(i,n[0].row,n[0].column)},ra=na("thead",!0),sa=na("tbody",!1),la=na("tfoot",!1),aa=(e,t,o)=>{const n=((e,t)=>Qt(e,(()=>t)))(e,o.section),r=en(n);return el(r,t,!0)},ca=(e,t,o,n)=>((e,t,o,n)=>{const r=en(t),s=n.getWidths(r,n);Ml(r,s,n)})(0,t,0,n.sizing),ia=(e,t,o,n)=>((e,t,o,n,r)=>{const s=en(t),l=n.getWidths(s,n),a=n.pixelWidth(),{newSizes:c,delta:i}=r.calcRedestributedWidths(l,a,o.pixelDelta,n.isRelative);Ml(s,c,n),n.adjustTableWidth(i)})(0,t,o,n.sizing,n.resize),ma=(e,t)=>O(t,(e=>0===e.column&&e.isLocked)),da=(e,t)=>O(t,(t=>t.column+t.colspan>=e.grid.columns&&t.isLocked)),ua=(e,t)=>{const o=cn(e),n=jl(t);return L(n,((e,t)=>e+o[t.column].map(Mo).getOr(0)),0)},fa=e=>(t,o)=>ll(t,o).filter((o=>!(e?ma:da)(t,o))).map((e=>({details:e,pixelDelta:ua(t,e)}))),ga=e=>(t,o)=>sl(t,o).filter((o=>!(e?ma:da)(t,o.cells))),ha=$l("th"),pa=$l("td"),wa=rl(((e,t,o,n)=>{const r=t[0].row,s=oa(t),l=A(s,((e,t)=>({grid:Sl(e.grid,r,t.row+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return ta(l,r,t[0].column)}),ll,f,f,Vl),ba=rl(((e,t,o,n)=>{const r=oa(t),s=r[r.length-1],l=s.row+s.rowspan,a=A(r,((e,t)=>Sl(e,l,t.row,o,n.getOrInit)),e);return ta(a,l,t[0].column)}),ll,f,f,Vl),va=rl(((e,t,o,n)=>{const r=t.details,s=jl(r),l=s[0].column,a=A(s,((e,t)=>({grid:Tl(e.grid,l,t.column+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return ta(a,r[0].row,l)}),fa(!0),ia,f,Vl),ya=rl(((e,t,o,n)=>{const r=t.details,s=r[r.length-1],l=s.column+s.colspan,a=jl(r),c=A(a,((e,t)=>Tl(e,l,t.column,o,n.getOrInit)),e);return ta(c,r[0].row,l)}),fa(!1),ia,f,Vl),xa=rl(((e,t,o,n)=>{const r=jl(t.details),s=((e,t)=>j(e,(e=>{const o=e.cells,n=A(t,((e,t)=>t>=0&&t<e.length?e.slice(0,t).concat(e.slice(t+1)):e),o);return n.length>0?[tt(e.element,n,e.section,e.isNew)]:[]})))(e,E(r,(e=>e.column))),l=s.length>0?s[0].cells.length-1:0;return ta(s,r[0].row,Math.min(r[0].column,l))}),((e,t)=>al(e,t).map((t=>({details:t,pixelDelta:-ua(e,t)})))),ia,Zl,Vl),Ca=rl(((e,t,o,n)=>{const r=oa(t),s=((e,t,o)=>{const{rows:n,cols:r}=$o(e);return[...r,...n.slice(0,t),...n.slice(o+1)]})(e,r[0].row,r[r.length-1].row),l=s.length>0?s.length-1:0;return ta(s,Math.min(t[0].row,l),t[0].column)}),ll,f,Zl,Vl),Sa=rl(((e,t,o,n)=>{const r=jl(t),s=E(r,(e=>e.column)),l=Nl(e,s,!0,o,n.replaceOrInit);return ta(l,t[0].row,t[0].column)}),al,f,f,ha),Ta=rl(((e,t,o,n)=>{const r=jl(t),s=E(r,(e=>e.column)),l=Nl(e,s,!1,o,n.replaceOrInit);return ta(l,t[0].row,t[0].column)}),al,f,f,pa),Ra=rl(ra,al,f,f,ha),Da=rl(sa,al,f,f,pa),Oa=rl(la,al,f,f,pa),ka=rl(((e,t,o,n)=>{const r=Bl(e,t,o,n.replaceOrInit);return ta(r,t[0].row,t[0].column)}),al,f,f,ha),Ea=rl(((e,t,o,n)=>{const r=Bl(e,t,o,n.replaceOrInit);return ta(r,t[0].row,t[0].column)}),al,f,f,pa),Na=rl(((e,t,o,n)=>{const r=t.cells;Ql(r);const s=((e,t,o,n)=>{const r=$o(e).rows;if(0===r.length)return e;for(let e=t.startRow;e<=t.finishRow;e++)for(let o=t.startCol;o<=t.finishCol;o++){const t=r[e],s=Ho(t,o).isLocked;Io(t,o,et(n(),!1,s))}return e})(e,t.bounds,0,n.merge(r));return ea(s,C.from(r[0]))}),((e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>cl(e,t.cells)))),ca,f,Ul),Ba=rl(((e,t,o,n)=>{const r=A(t,((e,t)=>il(e,t,o,n.unmerge(t))),e);return ea(r,C.from(t[0]))}),((e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>cl(e,t)))),ca,f,Ul),za=rl(((e,t,o,n)=>{const r=((e,t)=>{const o=Zo(e);return el(o,t,!0)})(t.clipboard,t.generators);var s,l;return((e,t,o,n,r)=>{const s=Yo(t),l=((e,t,o)=>{const n=Vo(t[0]),r=$o(t).cols.length+e.row,s=k(n-e.column,(t=>t+e.column));return{row:r,column:W(s,(e=>P(o,(t=>t!==e)))).getOr(n-1)}})(e,t,s),a=$o(o).rows,c=yl(l,a,s),i=((e,t,o)=>{if(e.row>=t.length||e.column>Vo(t[0]))return ul.error("invalid start address out of table bounds, row: "+e.row+", column: "+e.column);const n=t.slice(e.row),r=n[0].cells.slice(e.column),s=Vo(o[0]),l=o.length;return ul.value({rowDelta:n.length-l,colDelta:r.length-s})})(l,t,a);return i.map((e=>{const o={...e,colDelta:e.colDelta-c.length},s=bl(t,o,n),i=Yo(s),m=yl(l,a,i);return((e,t,o,n,r,s)=>{const l=e.row,a=e.column,c=l+o.length,i=a+Vo(o[0])+s.length,m=I(s,x);for(let e=l;e<c;e++){let s=0;for(let c=a;c<i;c++){if(m[c]){s++;continue}vl(t,e,c,r)&&il(t,qo(t[e],c),r,n.cell);const i=c-a-s,d=Ho(o[e-l],i),u=d.element,f=n.replace(u);Io(t[e],c,et(f,!0,d.isLocked))}}return t})(l,s,a,n,r,m)}))})((s=t.row,l=t.column,{row:s,column:l}),e,r,t.generators,o).fold((()=>ea(e,C.some(t.element))),(e=>ta(e,t.row,t.column)))}),((e,t)=>$t(t.element).bind((o=>ol(e,o).map((e=>({...e,generators:t.generators,clipboard:t.clipboard})))))),ca,f,Vl),Aa=rl(((e,t,o,n)=>{const r=$o(e).rows,s=t.cells[0].column,l=r[t.cells[0].row],a=aa(t.clipboard,t.generators,l),c=xl(s,e,a,t.generators,o);return ta(c,t.cells[0].row,t.cells[0].column)}),ga(!0),f,f,Vl),La=rl(((e,t,o,n)=>{const r=$o(e).rows,s=t.cells[t.cells.length-1].column+t.cells[t.cells.length-1].colspan,l=r[t.cells[0].row],a=aa(t.clipboard,t.generators,l),c=xl(s,e,a,t.generators,o);return ta(c,t.cells[0].row,t.cells[0].column)}),ga(!1),f,f,Vl),Wa=rl(((e,t,o,n)=>{const r=$o(e).rows,s=t.cells[0].row,l=r[s],a=aa(t.clipboard,t.generators,l),c=Cl(s,e,a,t.generators,o);return ta(c,t.cells[0].row,t.cells[0].column)}),sl,f,f,Vl),Ma=rl(((e,t,o,n)=>{const r=$o(e).rows,s=t.cells[t.cells.length-1].row+t.cells[t.cells.length-1].rowspan,l=r[t.cells[0].row],a=aa(t.clipboard,t.generators,l),c=Cl(s,e,a,t.generators,o);return ta(c,t.cells[0].row,t.cells[0].column)}),sl,f,f,Vl),_a=(e,t)=>{const o=Zo(e);return ll(o,t).bind((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=_(E(o.all,(e=>z(e.cells,(e=>e.column>=n&&e.column<r)))));return Ps(s)})).getOr("")},ja=(e,t)=>{const o=Zo(e);return ll(o,t).bind(Ps).getOr("")},Pa=(e,t)=>{const o=Zo(e);return ll(o,t).bind((e=>{const t=e[e.length-1],n=e[0].row,r=t.row+t.rowspan;return(e=>{const t=E(e,(e=>js(e).type)),o=D(t,"header"),n=D(t,"footer");if(o||n){const e=D(t,"body");return!o||e||n?o||e||!n?C.none():C.some("footer"):C.some("header")}return C.some("body")})(o.all.slice(n,r))})).getOr("")},Ia=(e,t)=>e.dispatch("NewRow",{node:t}),Fa=(e,t)=>e.dispatch("NewCell",{node:t}),Ha=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},qa={structure:!1,style:!0},Va={structure:!0,style:!1},$a={structure:!0,style:!0},Ua=e=>t=>t.options.get(e),Ga="100%",Ka=e=>{var t;const o=e.dom,n=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return _o(xe.fromDom(n))+"px"},Ya=e=>C.from(e.options.get("table_clone_elements")),Ja=Ua("table_header_type"),Qa=Ua("table_column_resizing"),Xa=e=>"preservetable"===Qa(e),Za=e=>"resizetable"===Qa(e),ec=Ua("table_sizing_mode"),tc=e=>"relative"===ec(e),oc=e=>"fixed"===ec(e),nc=e=>"responsive"===ec(e),rc=Ua("table_resize_bars"),sc=Ua("table_style_by_css"),lc=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>nc(e)||sc(e)?t:oc(e)?{...t,width:Ka(e)}:{...t,width:Ga})(e,o)},ac=Ua("table_use_colgroups"),cc=(e,t)=>tc(e)?ur(t):oc(e)?dr(t):mr(t),ic=(e,t,o)=>{const n=e=>"table"===ne(Mr(e)),r=Ya(e),s=Za(e)?f:Ds,l=t=>{switch(Ja(e)){case"section":return $s();case"sectionCells":return Us();case"cells":return Gs();default:return((e,t)=>{var o;switch((o=Zo(e),V(o.all,(e=>{const t=js(e);return"header"===t.type?C.from(t.subType):C.none()}))).getOr(t)){case"section":return Hs();case"sectionCells":return qs();case"cells":return Vs()}})(t,"section")}},a=(n,s,a,c)=>(i,m,d=!1)=>{jr(i);const u=xe.fromDom(e.getDoc()),f=Br(a,u,r),g={sizing:cc(e,i),resize:Za(e)?zs():As(),section:l(i)};return s(i)?n(i,m,f,g).bind((n=>{t.refresh(i.dom),N(n.newRows,(t=>{Ia(e,t.dom)})),N(n.newCells,(t=>{Fa(e,t.dom)}));const r=((t,n)=>n.cursor.fold((()=>{const n=Ut(t);return H(n).filter(lt).map((n=>{o.clearSelectedCells(t.dom);const r=e.dom.createRng();return r.selectNode(n.dom),e.selection.setRng(r),ge(n,"data-mce-selected","1"),r}))}),(n=>{const r=Ts(Rs,n),s=e.dom.createRng();return s.setStart(r.element.dom,r.offset),s.setEnd(r.element.dom,r.offset),e.selection.setRng(s),o.clearSelectedCells(t.dom),C.some(s)})))(i,n);return lt(i)&&(jr(i),d||Ha(e,i.dom,c)),r.map((e=>({rng:e,effect:c})))})):C.none()},c=a(Ca,(t=>!n(e)||Ls(t).rows>1),f,Va),i=a(xa,(t=>!n(e)||Ls(t).columns>1),f,Va);return{deleteRow:c,deleteColumn:i,insertRowsBefore:a(wa,x,f,Va),insertRowsAfter:a(ba,x,f,Va),insertColumnsBefore:a(va,x,s,Va),insertColumnsAfter:a(ya,x,s,Va),mergeCells:a(Na,x,f,Va),unmergeCells:a(Ba,x,f,Va),pasteColsBefore:a(Aa,x,f,Va),pasteColsAfter:a(La,x,f,Va),pasteRowsBefore:a(Wa,x,f,Va),pasteRowsAfter:a(Ma,x,f,Va),pasteCells:a(za,x,f,$a),makeCellsHeader:a(ka,x,f,Va),unmakeCellsHeader:a(Ea,x,f,Va),makeColumnsHeader:a(Sa,x,f,Va),unmakeColumnsHeader:a(Ta,x,f,Va),makeRowsHeader:a(Ra,x,f,Va),makeRowsBody:a(Da,x,f,Va),makeRowsFooter:a(Oa,x,f,Va),getTableRowType:Pa,getTableCellType:ja,getTableColType:_a}},mc=(e,t,o)=>{const n=Mt(e,t,1);1===o||n<=1?be(e,t):ge(e,t,Math.min(o,n))},dc=(e,t)=>o=>{const n=o.column+o.colspan-1,r=o.column;return n>=e&&r<t},uc=zl([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),fc=(e,t,o)=>{const n=o.substring(0,o.length-e.length),r=parseFloat(n);return n===r.toString()?t(r):uc.invalid(o)},gc={...uc,from:e=>Rt(e,"%")?fc("%",uc.percent,e):Rt(e,"px")?fc("px",uc.pixels,e):uc.invalid(e)},hc=(e,t,o)=>{const n=gc.from(o),r=P(e,(e=>"0px"===e))?((e,t)=>{const o=e.fold((()=>g("")),(e=>g(e/t+"px")),(()=>g(100/t+"%")));return k(t,o)})(n,e.length):((e,t,o)=>e.fold((()=>t),(e=>((e,t,o)=>{const n=o/t;return E(e,(e=>gc.from(e).fold((()=>e),(e=>e*n+"px"),(e=>e/100*o+"px"))))})(t,o,e)),(e=>((e,t)=>E(e,(e=>gc.from(e).fold((()=>e),(e=>e/t*100+"%"),(e=>e+"%")))))(t,o))))(n,e,t);return bc(r)},pc=(e,t)=>0===e.length?t:A(e,((e,t)=>gc.from(t).fold(g(0),h,h)+e),0),wc=(e,t)=>gc.from(e).fold(g(e),(e=>e+t+"px"),(e=>e+t+"%")),bc=e=>{if(0===e.length)return e;const t=A(e,((e,t)=>{const o=gc.from(t).fold((()=>({value:t,remainder:0})),(e=>((e,t)=>{const o=Math.floor(e);return{value:o+"px",remainder:e-o}})(e)),(e=>({value:e+"%",remainder:0})));return{output:[o.value].concat(e.output),remainder:e.remainder+o.remainder}}),{output:[],remainder:0}),o=t.output;return o.slice(0,o.length-1).concat([wc(o[o.length-1],Math.round(t.remainder))])},vc=gc.from,yc=e=>vc(e).fold(g("px"),g("px"),g("%")),xc=(e,t,o)=>{const n=Zo(e),r=n.all,s=rn(n),l=sn(n);t.each((t=>{const o=yc(t),r=Wo(e),a=((e,t)=>nr(e,t,er,rr))(n,e),c=hc(a,r,t);ln(n)?((e,t,o)=>{N(t,((t,n)=>{const r=pc([e[n]],Ft());Nt(t.element,"width",r+o)}))})(c,l,o):((e,t,o)=>{N(t,(t=>{const n=e.slice(t.column,t.colspan+t.column),r=pc(n,Ft());Nt(t.element,"width",r+o)}))})(c,s,o),Nt(e,"width",t)})),o.each((t=>{const o=yc(t),l=hn(e),a=((e,t,o)=>lr(e,t,o,tr,rr))(n,e,zn);((e,t,o,n)=>{N(o,(t=>{const o=e.slice(t.row,t.rowspan+t.row),r=pc(o,Ht());Nt(t.element,"height",r+n)})),N(t,((t,o)=>{Nt(t.element,"height",e[o])}))})(hc(a,l,t),r,s,o),Nt(e,"height",t)}))},Cc=e=>Un(e).exists((e=>_n.test(e))),Sc=e=>Un(e).exists((e=>jn.test(e))),Tc=e=>Un(e).isNone(),Rc=e=>{be(e,"width")},Dc=e=>{const t=Qn(e);xc(e,C.some(t),C.none()),Rc(e)},Oc=e=>{const t=(e=>Wo(e)+"px")(e);xc(e,C.some(t),C.none()),Rc(e)},kc=e=>{Wt(e,"width");const t=Gt(e),o=t.length>0?t:Ut(e);N(o,(e=>{Wt(e,"width"),Rc(e)})),Rc(e)},Ec={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},Nc=(e,t,o,n)=>k(e,(e=>((e,t,o,n)=>{const r=xe.fromTag("tr");for(let s=0;s<e;s++){const e=xe.fromTag(n<t||s<o?"th":"td");s<o&&ge(e,"scope","row"),n<t&&ge(e,"scope","col"),Ie(e,xe.fromTag("br")),Ie(r,e)}return r})(t,o,n,e))),Bc=(e,t)=>{e.selection.select(t.dom,!0),e.selection.collapse(!0)},zc=(e,t,o,n,s)=>{const l=(e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>nc(e)||!sc(e)?t:oc(e)?{...t,width:Ka(e)}:{...t,width:Ga})(e,o)})(e),a={styles:l,attributes:lc(e),colGroups:ac(e)};return e.undoManager.ignore((()=>{const r=((e,t,o,n,r,s=Ec)=>{const l=xe.fromTag("table"),a="cells"!==r;Bt(l,s.styles),he(l,s.attributes),s.colGroups&&Ie(l,(e=>{const t=xe.fromTag("colgroup");return k(e,(()=>Ie(t,xe.fromTag("col")))),t})(t));const c=Math.min(e,o);if(a&&o>0){const e=xe.fromTag("thead");Ie(l,e);const s=Nc(o,t,"sectionCells"===r?c:0,n);qe(e,s)}const i=xe.fromTag("tbody");Ie(l,i);const m=Nc(a?e-c:e,t,a?0:o,n);return qe(i,m),l})(o,t,s,n,Ja(e),a);ge(r,"data-mce-id","__mce");const l=(e=>{const t=xe.fromTag("div"),o=xe.fromDom(e.dom.cloneNode(!0));return Ie(t,o),(e=>e.dom.innerHTML)(t)})(r);e.insertContent(l),e.addVisual()})),wt(Mr(e),'table[data-mce-id="__mce"]').map((t=>(oc(e)?Oc(t):nc(e)?kc(t):(tc(e)||(e=>r(e)&&-1!==e.indexOf("%"))(l.width))&&Dc(t),jr(t),be(t,"data-mce-id"),((e,t)=>{N(dt(t,"tr"),(t=>{Ia(e,t.dom),N(dt(t,"th,td"),(t=>{Fa(e,t.dom)}))}))})(e,t),((e,t)=>{wt(t,"td,th").each(w(Bc,e))})(e,t),t.dom))).getOrNull()};var Ac=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const Lc="x-tinymce/dom-table-",Wc=Lc+"rows",Mc=Lc+"columns",_c=e=>{const t=Ac.FakeClipboardItem(e);Ac.write([t])},jc=e=>{var t;const o=null!==(t=Ac.read())&&void 0!==t?t:[];return V(o,(t=>C.from(t.getType(e))))},Pc=e=>{jc(e).isSome()&&Ac.clear()},Ic=e=>{e.fold(Hc,(e=>_c({[Wc]:e})))},Fc=()=>jc(Wc),Hc=()=>Pc(Wc),qc=e=>{e.fold($c,(e=>_c({[Mc]:e})))},Vc=()=>jc(Mc),$c=()=>Pc(Mc),Uc=e=>ps(Pr(e),_r(e)).filter(Hr),Gc=(e,t)=>{const o=_r(e),n=e=>Kt(e,o),l=t=>(e=>ws(Pr(e),_r(e)).filter(Hr))(e).bind((e=>n(e).map((o=>t(o,e))))),a=t=>{e.focus()},c=(t,o=!1)=>l(((n,r)=>{const s=gs(bs(e),n,r);t(n,s,o).each(a)})),i=()=>l(((t,o)=>((e,t,o)=>{const n=Zo(e);return ll(n,t).bind((e=>{const t=el(n,o,!1),r=$o(t).rows.slice(e[0].row,e[e.length-1].row+e[e.length-1].rowspan),s=j(r,(e=>{const t=z(e.cells,(e=>!e.isLocked));return t.length>0?[{...e,cells:t}]:[]})),l=tl(s);return xt(l.length>0,l)})).map((e=>E(e,(e=>{const t=Ke(e.element);return N(e.cells,(e=>{const o=Ye(e.element);Ys(o,"colspan",e.colspan,1),Ys(o,"rowspan",e.rowspan,1),Ie(t,o)})),t}))))})(t,gs(bs(e),t,o),Br(f,xe.fromDom(e.getDoc()),C.none())))),m=()=>l(((t,o)=>((e,t)=>{const o=Zo(e);return al(o,t).map((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=((e,t,o)=>{if(ln(e)){const n=z(sn(e),dc(t,o)),r=E(n,(e=>{const n=Ye(e.element);return mc(n,"span",o-t),n})),s=xe.fromTag("colgroup");return qe(s,r),[s]}return[]})(o,n,r),l=((e,t,o)=>E(e.all,(e=>{const n=z(e.cells,dc(t,o)),r=E(n,(e=>{const n=Ye(e.element);return mc(n,"colspan",o-t),n})),s=xe.fromTag("tr");return qe(s,r),s})))(o,n,r);return[...s,...l]}))})(t,gs(bs(e),t,o)))),d=(t,o)=>o().each((o=>{const n=E(o,(e=>Ye(e)));l(((o,r)=>{const s=zr(xe.fromDom(e.getDoc())),l=((e,t,o,n)=>({selection:ms(e),clipboard:o,generators:n}))(bs(e),0,n,s);t(o,l).each(a)}))})),g=e=>(t,o)=>((e,t)=>X(e,t)?C.from(e.type):C.none())(o,"type").each((t=>{c(e(t),o.no_events)}));G({mceTableSplitCells:()=>c(t.unmergeCells),mceTableMergeCells:()=>c(t.mergeCells),mceTableInsertRowBefore:()=>c(t.insertRowsBefore),mceTableInsertRowAfter:()=>c(t.insertRowsAfter),mceTableInsertColBefore:()=>c(t.insertColumnsBefore),mceTableInsertColAfter:()=>c(t.insertColumnsAfter),mceTableDeleteCol:()=>c(t.deleteColumn),mceTableDeleteRow:()=>c(t.deleteRow),mceTableCutCol:()=>m().each((e=>{qc(e),c(t.deleteColumn)})),mceTableCutRow:()=>i().each((e=>{Ic(e),c(t.deleteRow)})),mceTableCopyCol:()=>m().each((e=>qc(e))),mceTableCopyRow:()=>i().each((e=>Ic(e))),mceTablePasteColBefore:()=>d(t.pasteColsBefore,Vc),mceTablePasteColAfter:()=>d(t.pasteColsAfter,Vc),mceTablePasteRowBefore:()=>d(t.pasteRowsBefore,Fc),mceTablePasteRowAfter:()=>d(t.pasteRowsAfter,Fc),mceTableDelete:()=>Uc(e).each((t=>{Kt(t,o).filter(b(o)).each((t=>{const o=xe.fromText("");if(je(t,o),$e(t),e.dom.isEmpty(e.getBody()))e.setContent(""),e.selection.setCursorLocation();else{const t=e.dom.createRng();t.setStart(o.dom,0),t.setEnd(o.dom,0),e.selection.setRng(t),e.nodeChanged()}}))})),mceTableCellToggleClass:(t,o)=>{l((t=>{const n=bs(e),r=P(n,(t=>e.formatter.match("tablecellclass",{value:o},t.dom))),s=r?e.formatter.remove:e.formatter.apply;N(n,(e=>s("tablecellclass",{value:o},e.dom))),Ha(e,t.dom,qa)}))},mceTableToggleClass:(t,o)=>{l((t=>{e.formatter.toggle("tableclass",{value:o},t.dom),Ha(e,t.dom,qa)}))},mceTableToggleCaption:()=>{Uc(e).each((t=>{Kt(t,o).each((o=>{pt(o,"caption").fold((()=>{const t=xe.fromTag("caption");Ie(t,xe.fromText("Caption")),((e,t,o)=>{Me(e,0).fold((()=>{Ie(e,t)}),(e=>{_e(e,t)}))})(o,t),e.selection.setCursorLocation(t.dom,0)}),(n=>{ue("caption")(t)&&Te("td",o).each((t=>e.selection.setCursorLocation(t.dom,0))),$e(n)})),Ha(e,o.dom,Va)}))}))},mceTableSizingMode:(t,n)=>(t=>Uc(e).each((n=>{nc(e)||oc(e)||tc(e)||Kt(n,o).each((o=>{"relative"!==t||Cc(o)?"fixed"!==t||Sc(o)?"responsive"!==t||Tc(o)||kc(o):Oc(o):Dc(o),jr(o),Ha(e,o.dom,Va)}))})))(n),mceTableCellType:g((e=>"th"===e?t.makeCellsHeader:t.unmakeCellsHeader)),mceTableColType:g((e=>"th"===e?t.makeColumnsHeader:t.unmakeColumnsHeader)),mceTableRowType:g((e=>{switch(e){case"header":return t.makeRowsHeader;case"footer":return t.makeRowsFooter;default:return t.makeRowsBody}}))},((t,o)=>e.addCommand(o,t))),e.addCommand("mceInsertTable",((t,o)=>{((e,t,o,n={})=>{const r=e=>u(e)&&e>0;if(r(t)&&r(o)){const r=n.headerRows||0,s=n.headerColumns||0;return zc(e,o,t,s,r)}console.error("Invalid values for mceInsertTable - rows and columns values are required to insert a table.")})(e,o.rows,o.columns,o.options)})),e.addCommand("mceTableApplyCellStyle",((t,o)=>{const l=e=>"tablecell"+e.toLowerCase().replace("-","");if(!s(o))return;const a=z(bs(e),Hr);if(0===a.length)return;const c=((e,t)=>{const o={};return((e,t,o,n)=>{G(e,((e,r)=>{(t(e,r)?o:n)(e,r)}))})(e,t,(e=>(t,o)=>{e[o]=t})(o),f),o})(o,((t,o)=>e.formatter.has(l(o))&&r(t)));(e=>{for(const t in e)if(U.call(e,t))return!1;return!0})(c)||(G(c,((t,o)=>{const n=l(o);N(a,(o=>{""===t?e.formatter.remove(n,{value:null},o.dom,!0):e.formatter.apply(n,{value:t},o.dom)}))})),n(a[0]).each((t=>Ha(e,t.dom,qa))))}))},Kc=zl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Yc={before:Kc.before,on:Kc.on,after:Kc.after,cata:(e,t,o,n)=>e.fold(t,o,n),getStart:e=>e.fold(h,h,h)},Jc=(e,t)=>({selection:e,kill:t}),Qc=(e,t)=>{const o=e.document.createRange();return o.selectNode(t.dom),o},Xc=(e,t)=>{const o=e.document.createRange();return Zc(o,t),o},Zc=(e,t)=>e.selectNodeContents(t.dom),ei=(e,t,o)=>{const n=e.document.createRange();var r;return r=n,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},ti=(e,t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},oi=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),ni=zl([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),ri=(e,t,o)=>t(xe.fromDom(o.startContainer),o.startOffset,xe.fromDom(o.endContainer),o.endOffset),si=(e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:g(e),rtl:C.none}),relative:(t,o)=>({ltr:eo((()=>ei(e,t,o))),rtl:eo((()=>C.some(ei(e,o,t))))}),exact:(t,o,n,r)=>({ltr:eo((()=>ti(e,t,o,n,r))),rtl:eo((()=>C.some(ti(e,n,r,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>ni.rtl(xe.fromDom(e.endContainer),e.endOffset,xe.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>ri(0,ni.ltr,o))):ri(0,ni.ltr,o)})(0,o)},li=(e,t)=>si(e,t).match({ltr:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},rtl:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(n.dom,r),s.setEnd(t.dom,o),s}});ni.ltr,ni.rtl;const ai=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),ci=(e,t,o,n)=>({start:Yc.on(e,t),finish:Yc.on(o,n)}),ii=(e,t)=>{const o=li(e,t);return ai(xe.fromDom(o.startContainer),o.startOffset,xe.fromDom(o.endContainer),o.endOffset)},mi=ci,di=(e,t,o,n,r)=>Re(o,n)?C.none():rs(o,n,t).bind((t=>{const n=t.boxes.getOr([]);return n.length>1?(r(e,n,t.start,t.finish),C.some(Jc(C.some(mi(o,0,o,br(o))),!0))):C.none()})),ui=(e,t)=>({item:e,mode:t}),fi=(e,t,o,n=gi)=>e.property().parent(t).map((e=>ui(e,n))),gi=(e,t,o,n=hi)=>o.sibling(e,t).map((e=>ui(e,n))),hi=(e,t,o,n=hi)=>{const r=e.property().children(t);return o.first(r).map((e=>ui(e,n)))},pi=[{current:fi,next:gi,fallback:C.none()},{current:gi,next:hi,fallback:C.some(fi)},{current:hi,next:hi,fallback:C.some(gi)}],wi=(e,t,o,n,r=pi)=>W(r,(e=>e.current===o)).bind((o=>o.current(e,t,n,o.next).orThunk((()=>o.fallback.bind((o=>wi(e,t,o,n))))))),bi=(e,t,o,n,r,s)=>wi(e,t,n,r).bind((t=>s(t.item)?C.none():o(t.item)?C.some(t.item):bi(e,t.item,o,t.mode,r,s))),vi=e=>t=>0===e.property().children(t).length,yi=(e,t,o,n)=>bi(e,t,o,gi,{sibling:(e,t)=>e.query().prevSibling(t),first:e=>e.length>0?C.some(e[e.length-1]):C.none()},n),xi=(e,t,o,n)=>bi(e,t,o,gi,{sibling:(e,t)=>e.query().nextSibling(t),first:e=>e.length>0?C.some(e[0]):C.none()},n),Ci=Xr(),Si=(e,t)=>((e,t,o)=>yi(e,t,vi(e),o))(Ci,e,t),Ti=(e,t)=>((e,t,o)=>xi(e,t,vi(e),o))(Ci,e,t),Ri=zl([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),Di=e=>bt(e,"tr"),Oi={...Ri,verify:(e,t,o,n,r,s,l)=>bt(n,"td,th",l).bind((o=>bt(t,"td,th",l).map((t=>Re(o,t)?Re(n,o)&&br(o)===r?s(t):Ri.none("in same cell"):os(Di,[o,t]).fold((()=>((e,t,o)=>{const n=e.getRect(t),r=e.getRect(o);return r.right>n.left&&r.left<n.right})(e,t,o)?Ri.success():s(t)),(e=>s(t))))))).getOr(Ri.none("default")),cata:(e,t,o,n,r)=>e.fold(t,o,n,r)},ki=ue("br"),Ei=(e,t,o)=>t(e,o).bind((e=>ie(e)&&0===hr(e).trim().length?Ei(e,t,o):C.some(e))),Ni=(e,t,o,n)=>((e,t)=>Me(e,t).filter(ki).orThunk((()=>Me(e,t-1).filter(ki))))(t,o).bind((t=>n.traverse(t).fold((()=>Ei(t,n.gather,e).map(n.relative)),(e=>(e=>Ne(e).bind((t=>{const o=We(t);return((e,t)=>M(e,w(Re,t)))(o,e).map((n=>((e,t,o,n)=>({parent:e,children:t,element:o,index:n}))(t,o,e,n)))})))(e).map((e=>Yc.on(e.parent,e.index))))))),Bi=(e,t)=>({left:e.left,top:e.top+t,right:e.right,bottom:e.bottom+t}),zi=(e,t)=>({left:e.left,top:e.top-t,right:e.right,bottom:e.bottom-t}),Ai=(e,t,o)=>({left:e.left+t,top:e.top+o,right:e.right+t,bottom:e.bottom+o}),Li=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom}),Wi=(e,t)=>C.some(e.getRect(t)),Mi=(e,t,o)=>ce(t)?Wi(e,t).map(Li):ie(t)?((e,t,o)=>o>=0&&o<br(t)?e.getRangedRect(t,o,t,o+1):o>0?e.getRangedRect(t,o-1,t,o):C.none())(e,t,o).map(Li):C.none(),_i=(e,t)=>ce(t)?Wi(e,t).map(Li):ie(t)?e.getRangedRect(t,0,t,br(t)).map(Li):C.none(),ji=zl([{none:[]},{retry:["caret"]}]),Pi=(e,t,o)=>gt(t,Yl).fold(y,(t=>_i(e,t).exists((e=>((e,t)=>e.left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right)(o,e))))),Ii={point:e=>e.bottom,adjuster:(e,t,o,n,r)=>{const s=Bi(r,5);return Math.abs(o.bottom-n.bottom)<1||o.top>r.bottom?ji.retry(s):o.top===r.bottom?ji.retry(Bi(r,1)):Pi(e,t,r)?ji.retry(Ai(s,5,0)):ji.none()},move:Bi,gather:Ti},Fi=(e,t,o,n,r)=>0===r?C.some(n):((e,t,o)=>e.elementFromPoint(t,o).filter((e=>"table"===ne(e))).isSome())(e,n.left,t.point(n))?((e,t,o,n,r)=>Fi(e,t,o,t.move(n,5),r))(e,t,o,n,r-1):e.situsFromPoint(n.left,t.point(n)).bind((s=>s.start.fold(C.none,(s=>_i(e,s).bind((l=>t.adjuster(e,s,l,o,n).fold(C.none,(n=>Fi(e,t,o,n,r-1))))).orThunk((()=>C.some(n)))),C.none))),Hi=(e,t,o)=>{const n=e.move(o,5),r=Fi(t,e,o,n,100).getOr(n);return((e,t,o)=>e.point(t)>o.getInnerHeight()?C.some(e.point(t)-o.getInnerHeight()):e.point(t)<0?C.some(-e.point(t)):C.none())(e,r,t).fold((()=>t.situsFromPoint(r.left,e.point(r))),(o=>(t.scrollBy(0,o),t.situsFromPoint(r.left,e.point(r)-o))))},qi={tryUp:w(Hi,{point:e=>e.top,adjuster:(e,t,o,n,r)=>{const s=zi(r,5);return Math.abs(o.top-n.top)<1||o.bottom<r.top?ji.retry(s):o.bottom===r.top?ji.retry(zi(r,1)):Pi(e,t,r)?ji.retry(Ai(s,5,0)):ji.none()},move:zi,gather:Si}),tryDown:w(Hi,Ii),getJumpSize:g(5)},Vi=(e,t,o)=>e.getSelection().bind((n=>((e,t,o,n)=>{const r=ki(t)?((e,t,o)=>o.traverse(t).orThunk((()=>Ei(t,o.gather,e))).map(o.relative))(e,t,n):Ni(e,t,o,n);return r.map((e=>({start:e,finish:e})))})(t,n.finish,n.foffset,o).fold((()=>C.some(ys(n.finish,n.foffset))),(r=>{const s=e.fromSitus(r);return l=Oi.verify(e,n.finish,n.foffset,s.finish,s.foffset,o.failure,t),Oi.cata(l,(e=>C.none()),(()=>C.none()),(e=>C.some(ys(e,0))),(e=>C.some(ys(e,br(e)))));var l})))),$i=(e,t,o,n,r,s)=>0===s?C.none():Ki(e,t,o,n,r).bind((l=>{const a=e.fromSitus(l),c=Oi.verify(e,o,n,a.finish,a.foffset,r.failure,t);return Oi.cata(c,(()=>C.none()),(()=>C.some(l)),(l=>Re(o,l)&&0===n?Ui(e,o,n,zi,r):$i(e,t,l,0,r,s-1)),(l=>Re(o,l)&&n===br(l)?Ui(e,o,n,Bi,r):$i(e,t,l,br(l),r,s-1)))})),Ui=(e,t,o,n,r)=>Mi(e,t,o).bind((t=>Gi(e,r,n(t,qi.getJumpSize())))),Gi=(e,t,o)=>{const n=Bo().browser;return n.isChromium()||n.isSafari()||n.isFirefox()?t.retry(e,o):C.none()},Ki=(e,t,o,n,r)=>Mi(e,o,n).bind((t=>Gi(e,r,t))),Yi=(e,t,o,n,r)=>bt(n,"td,th",t).bind((n=>bt(n,"table",t).bind((s=>((e,t)=>ft(e,(e=>Ne(e).exists((e=>Re(e,t)))),void 0).isSome())(r,s)?((e,t,o)=>Vi(e,t,o).bind((n=>$i(e,t,n.element,n.offset,o,20).map(e.fromSitus))))(e,t,o).bind((e=>bt(e.finish,"td,th",t).map((t=>({start:n,finish:t,range:e}))))):C.none())))),Ji=(e,t,o,n,r,s)=>s(n,t).orThunk((()=>Yi(e,t,o,n,r).map((e=>{const t=e.range;return Jc(C.some(mi(t.start,t.soffset,t.finish,t.foffset)),!0)})))),Qi=(e,t)=>bt(e,"tr",t).bind((e=>bt(e,"table",t).bind((o=>{const n=dt(o,"tr");return Re(e,n[0])?((e,t,o)=>yi(Ci,e,(e=>Cr(e).isSome()),o))(o,0,t).map((e=>{const t=br(e);return Jc(C.some(mi(e,t,e,t)),!0)})):C.none()})))),Xi=(e,t)=>bt(e,"tr",t).bind((e=>bt(e,"table",t).bind((o=>{const n=dt(o,"tr");return Re(e,n[n.length-1])?((e,t,o)=>xi(Ci,e,(e=>xr(e).isSome()),o))(o,0,t).map((e=>Jc(C.some(mi(e,0,e,0)),!0))):C.none()})))),Zi=(e,t,o,n,r,s,l)=>Yi(e,o,n,r,s).bind((e=>di(t,o,e.start,e.finish,l))),em=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},tm=()=>{const e=(e=>{const t=em(C.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(C.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(C.some(e))}}})(f);return{...e,on:t=>e.get().each(t)}},om=(e,t)=>bt(e,"td,th",t),nm=e=>Be(e).exists(Lr),rm={traverse:Le,gather:Ti,relative:Yc.before,retry:qi.tryDown,failure:Oi.failedDown},sm={traverse:Ae,gather:Si,relative:Yc.before,retry:qi.tryUp,failure:Oi.failedUp},lm=e=>t=>t===e,am=lm(38),cm=lm(40),im=e=>e>=37&&e<=40,mm={isBackward:lm(37),isForward:lm(39)},dm={isBackward:lm(39),isForward:lm(37)},um=zl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),fm={domRange:um.domRange,relative:um.relative,exact:um.exact,exactFromRange:e=>um.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>xe.fromDom(e.startContainer),relative:(e,t)=>Yc.getStart(e),exact:(e,t,o,n)=>e}))(e);return xe.fromDom(Ee(t).dom.defaultView)},range:ai},gm=document.caretPositionFromPoint?(e,t,o)=>{var n,r;return C.from(null===(r=(n=e.dom).caretPositionFromPoint)||void 0===r?void 0:r.call(n,t,o)).bind((t=>{if(null===t.offsetNode)return C.none();const o=e.dom.createRange();return o.setStart(t.offsetNode,t.offset),o.collapse(),C.some(o)}))}:document.caretRangeFromPoint?(e,t,o)=>{var n,r;return C.from(null===(r=(n=e.dom).caretRangeFromPoint)||void 0===r?void 0:r.call(n,t,o))}:C.none,hm=(e,t)=>{const o=ne(e);return"input"===o?Yc.after(e):D(["br","img"],o)?0===t?Yc.before(e):Yc.after(e):Yc.on(e,t)},pm=e=>C.from(e.getSelection()),wm=(e,t)=>{pm(e).each((e=>{e.removeAllRanges(),e.addRange(t)}))},bm=(e,t,o,n,r)=>{const s=ti(e,t,o,n,r);wm(e,s)},vm=(e,t)=>si(e,t).match({ltr:(t,o,n,r)=>{bm(e,t,o,n,r)},rtl:(t,o,n,r)=>{pm(e).each((s=>{if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom,o,n.dom,r);else if(s.extend)try{((e,t,o,n,r,s)=>{t.collapse(o.dom,n),t.extend(r.dom,s)})(0,s,t,o,n,r)}catch(s){bm(e,n,r,t,o)}else bm(e,n,r,t,o)}))}}),ym=(e,t,o,n,r)=>{const s=((e,t,o,n)=>{const r=hm(e,t),s=hm(o,n);return fm.relative(r,s)})(t,o,n,r);vm(e,s)},xm=(e,t,o)=>{const n=((e,t)=>{const o=e.fold(Yc.before,hm,Yc.after),n=t.fold(Yc.before,hm,Yc.after);return fm.relative(o,n)})(t,o);vm(e,n)},Cm=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return C.some(ai(xe.fromDom(t.startContainer),t.startOffset,xe.fromDom(o.endContainer),o.endOffset))}return C.none()},Sm=e=>{if(null===e.anchorNode||null===e.focusNode)return Cm(e);{const t=xe.fromDom(e.anchorNode),o=xe.fromDom(e.focusNode);return((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=ke(e).dom.createRange();return r.setStart(e.dom,t),r.setEnd(o.dom,n),r})(e,t,o,n),s=Re(e,o)&&t===n;return r.collapsed&&!s})(t,e.anchorOffset,o,e.focusOffset)?C.some(ai(t,e.anchorOffset,o,e.focusOffset)):Cm(e)}},Tm=(e,t,o=!0)=>{const n=(o?Xc:Qc)(e,t);wm(e,n)},Rm=e=>(e=>pm(e).filter((e=>e.rangeCount>0)).bind(Sm))(e).map((e=>fm.exact(e.start,e.soffset,e.finish,e.foffset))),Dm=e=>({elementFromPoint:(t,o)=>xe.fromPoint(xe.fromDom(e.document),t,o),getRect:e=>e.dom.getBoundingClientRect(),getRangedRect:(t,o,n,r)=>{const s=fm.exact(t,o,n,r);return((e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?C.some(o).map(oi):C.none()})(li(e,t)))(e,s)},getSelection:()=>Rm(e).map((t=>ii(e,t))),fromSitus:t=>{const o=fm.relative(t.start,t.finish);return ii(e,o)},situsFromPoint:(t,o)=>((e,t,o)=>((e,t,o)=>{const n=xe.fromDom(e.document);return gm(n,t,o).map((e=>ai(xe.fromDom(e.startContainer),e.startOffset,xe.fromDom(e.endContainer),e.endOffset)))})(e,t,o))(e,t,o).map((e=>ci(e.start,e.soffset,e.finish,e.foffset))),clearSelection:()=>{(e=>{pm(e).each((e=>e.removeAllRanges()))})(e)},collapseSelection:(t=!1)=>{Rm(e).each((o=>o.fold((e=>e.collapse(t)),((o,n)=>{const r=t?o:n;xm(e,r,r)}),((o,n,r,s)=>{const l=t?o:r,a=t?n:s;ym(e,l,a,l,a)}))))},setSelection:t=>{ym(e,t.start,t.soffset,t.finish,t.foffset)},setRelativeSelection:(t,o)=>{xm(e,t,o)},selectNode:t=>{Tm(e,t,!1)},selectContents:t=>{Tm(e,t)},getInnerHeight:()=>e.innerHeight,getScrollY:()=>(e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return bn(o,n)})(xe.fromDom(e.document)).top,scrollBy:(t,o)=>{((e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollBy(e,t)})(t,o,xe.fromDom(e.document))}}),Om=(e,t)=>({rows:e,cols:t}),km=e=>gt(e,ae).exists(Lr),Em=(e,t)=>km(e)||km(t),Nm=e=>void 0!==e.dom.classList,Bm=(e,t)=>((e,t,o)=>{const n=((e,t)=>{const o=pe(e,t);return void 0===o||""===o?[]:o.split(" ")})(e,t).concat([o]);return ge(e,t,n.join(" ")),!0})(e,"class",t),zm=(e,t)=>{Nm(e)?e.dom.classList.add(t):Bm(e,t)},Am=(e,t)=>Nm(e)&&e.dom.classList.contains(t),Lm=()=>({tag:"none"}),Wm=e=>({tag:"multiple",elements:e}),Mm=e=>({tag:"single",element:e}),_m=e=>{const t=xe.fromDom((e=>{if(nt()&&m(e.target)){const t=xe.fromDom(e.target);if(ce(t)&&m(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return H(t)}}return C.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),r=(s=n,l=o,(...e)=>s(l.apply(null,e)));var s,l;return((e,t,o,n,r,s,l)=>({target:e,x:t,y:o,stop:n,prevent:r,kill:s,raw:l}))(t,e.clientX,e.clientY,o,n,r,e)},jm=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Pm=x,Im=(e,t,o)=>((e,t,o,n)=>((e,t,o,n,r)=>{const s=((e,t)=>o=>{e(o)&&t(_m(o))})(o,n);return e.dom.addEventListener(t,s,r),{unbind:w(jm,e,t,s,r)}})(e,t,o,n,!1))(e,t,Pm,o),Fm=_m,Hm=e=>!Am(xe.fromDom(e.target),"ephox-snooker-resizer-bar"),qm=(e,t)=>{const o=(r=fs.selectedSelector,{get:()=>cs(xe.fromDom(e.getBody()),r).fold((()=>ws(Pr(e),_r(e)).fold(Lm,Mm)),Wm)}),n=((e,t,o)=>{const n=t=>{be(t,e.selected),be(t,e.firstSelected),be(t,e.lastSelected)},r=t=>{ge(t,e.selected,"1")},s=e=>{l(e),o()},l=t=>{const o=dt(t,`${e.selectedSelector},${e.firstSelectedSelector},${e.lastSelectedSelector}`);N(o,n)};return{clearBeforeUpdate:l,clear:s,selectRange:(o,n,l,a)=>{s(o),N(n,r),ge(l,e.firstSelected,"1"),ge(a,e.lastSelected,"1"),t(n,l,a)},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}})(fs,((t,o,n)=>{Kt(o).each((r=>{const s=Ya(e),l=Br(f,xe.fromDom(e.getDoc()),s),a=((e,t,o)=>{const n=Zo(e);return ll(n,t).map((e=>{const t=el(n,o,!1),{rows:r}=$o(t),s=((e,t)=>{const o=e.slice(0,t[t.length-1].row+1),n=tl(o);return j(n,(e=>{const o=e.cells.slice(0,t[t.length-1].column+1);return E(o,(e=>e.element))}))})(r,e),l=((e,t)=>{const o=e.slice(t[0].row+t[0].rowspan-1,e.length),n=tl(o);return j(n,(e=>{const o=e.cells.slice(t[0].column+t[0].colspan-1,e.cells.length);return E(o,(e=>e.element))}))})(r,e);return{upOrLeftCells:s,downOrRightCells:l}}))})(r,{selection:bs(e)},l);((e,t,o,n,r)=>{e.dispatch("TableSelectionChange",{cells:t,start:o,finish:n,otherCells:r})})(e,t,o,n,a)}))}),(()=>(e=>{e.dispatch("TableSelectionClear")})(e)));var r;return e.on("init",(o=>{const r=e.getWin(),s=Mr(e),l=_r(e),a=((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=tm(),s=r.clear,l=s=>{r.on((r=>{n.clearBeforeUpdate(t),om(s.target,o).each((l=>{rs(r,l,o).each((o=>{const r=o.boxes.getOr([]);if(1===r.length){const o=r[0],l="false"===Wr(o),a=vt(Ar(s.target),o,Re);l&&a&&(n.selectRange(t,r,o,o),e.selectContents(o))}else r.length>1&&(n.selectRange(t,r,o.start,o.finish),e.selectContents(l))}))}))}))};return{clearstate:s,mousedown:e=>{n.clear(t),om(e.target,o).filter(nm).each(r.set)},mouseover:e=>{l(e)},mouseup:e=>{l(e),s()}}})(Dm(e),t,o,n);return{clearstate:r.clearstate,mousedown:r.mousedown,mouseover:r.mouseover,mouseup:r.mouseup}})(r,s,l,n),c=((e,t,o,n)=>{const r=Dm(e),s=()=>(n.clear(t),C.none());return{keydown:(e,l,a,c,i,m)=>{const d=e.raw,u=d.which,f=!0===d.shiftKey,g=ss(t,n.selectedSelector).fold((()=>(im(u)&&!f&&n.clearBeforeUpdate(t),im(u)&&f&&!Em(l,c)?C.none:cm(u)&&f?w(Zi,r,t,o,rm,c,l,n.selectRange):am(u)&&f?w(Zi,r,t,o,sm,c,l,n.selectRange):cm(u)?w(Ji,r,o,rm,c,l,Xi):am(u)?w(Ji,r,o,sm,c,l,Qi):C.none)),(e=>{const o=o=>()=>{const s=V(o,(o=>((e,t,o,n,r)=>as(n,e,t,r.firstSelectedSelector,r.lastSelectedSelector).map((e=>(r.clearBeforeUpdate(o),r.selectRange(o,e.boxes,e.start,e.finish),e.boxes))))(o.rows,o.cols,t,e,n)));return s.fold((()=>ls(t,n.firstSelectedSelector,n.lastSelectedSelector).map((e=>{const o=cm(u)||m.isForward(u)?Yc.after:Yc.before;return r.setRelativeSelection(Yc.on(e.first,0),o(e.table)),n.clear(t),Jc(C.none(),!0)}))),(e=>C.some(Jc(C.none(),!0))))};return im(u)&&f&&!Em(l,c)?C.none:cm(u)&&f?o([Om(1,0)]):am(u)&&f?o([Om(-1,0)]):m.isBackward(u)&&f?o([Om(0,-1),Om(-1,0)]):m.isForward(u)&&f?o([Om(0,1),Om(1,0)]):im(u)&&!f?s:C.none}));return g()},keyup:(e,r,s,l,a)=>ss(t,n.selectedSelector).fold((()=>{const c=e.raw,i=c.which;return!0===c.shiftKey&&im(i)&&Em(r,l)?((e,t,o,n,r,s,l)=>Re(o,r)&&n===s?C.none():bt(o,"td,th",t).bind((o=>bt(r,"td,th",t).bind((n=>di(e,t,o,n,l))))))(t,o,r,s,l,a,n.selectRange):C.none()}),C.none)}})(r,s,l,n),i=((e,t,o,n)=>{const r=Dm(e);return(e,s)=>{n.clearBeforeUpdate(t),rs(e,s,o).each((e=>{const o=e.boxes.getOr([]);n.selectRange(t,o,e.start,e.finish),r.selectContents(s),r.collapseSelection()}))}})(r,s,l,n);e.on("TableSelectorChange",(e=>i(e.start,e.finish)));const m=(t,o)=>{(e=>!0===e.raw.shiftKey)(t)&&(o.kill&&t.kill(),o.selection.each((t=>{const o=fm.relative(t.start,t.finish),n=li(r,o);e.selection.setRng(n)})))},d=e=>0===e.button,u=(()=>{const e=em(xe.fromDom(s)),t=em(0);return{touchEnd:o=>{const n=xe.fromDom(o.target);if(ue("td")(n)||ue("th")(n)){const r=e.get(),s=t.get();Re(r,n)&&o.timeStamp-s<300&&(o.preventDefault(),i(n,n))}e.set(n),t.set(o.timeStamp)}}})();e.on("dragstart",(e=>{a.clearstate()})),e.on("mousedown",(e=>{d(e)&&Hm(e)&&a.mousedown(Fm(e))})),e.on("mouseover",(e=>{var t;void 0!==(t=e).buttons&&0==(1&t.buttons)||!Hm(e)||a.mouseover(Fm(e))})),e.on("mouseup",(e=>{d(e)&&Hm(e)&&a.mouseup(Fm(e))})),e.on("touchend",u.touchEnd),e.on("keyup",(t=>{const o=Fm(t);if(o.raw.shiftKey&&im(o.raw.which)){const t=e.selection.getRng(),n=xe.fromDom(t.startContainer),r=xe.fromDom(t.endContainer);c.keyup(o,n,t.startOffset,r,t.endOffset).each((e=>{m(o,e)}))}})),e.on("keydown",(o=>{const n=Fm(o);t.hide();const r=e.selection.getRng(),s=xe.fromDom(r.startContainer),l=xe.fromDom(r.endContainer),a=un(mm,dm)(xe.fromDom(e.selection.getStart()));c.keydown(n,s,r.startOffset,l,r.endOffset,a).each((e=>{m(n,e)})),t.show()})),e.on("NodeChange",(()=>{const t=e.selection,o=xe.fromDom(t.getStart()),r=xe.fromDom(t.getEnd());os(Kt,[o,r]).fold((()=>n.clear(s)),f)}))})),e.on("PreInit",(()=>{e.serializer.addTempAttr(fs.firstSelected),e.serializer.addTempAttr(fs.lastSelected)})),{getSelectedCells:()=>((e,t,o,n)=>{switch(e.tag){case"none":return t();case"single":return(e=>[e.dom])(e.element);case"multiple":return(e=>E(e,(e=>e.dom)))(e.elements)}})(o.get(),g([])),clearSelectedCells:e=>n.clear(xe.fromDom(e))}},Vm=e=>{let t=[];return{bind:e=>{if(void 0===e)throw new Error("Event bind error: undefined handler");t.push(e)},unbind:e=>{t=z(t,(t=>t!==e))},trigger:(...o)=>{const n={};N(e,((e,t)=>{n[e]=o[t]})),N(t,(e=>{e(n)}))}}},$m=e=>({registry:K(e,(e=>({bind:e.bind,unbind:e.unbind}))),trigger:K(e,(e=>e.trigger))}),Um=e=>e.slice(0).sort(),Gm=(e,t)=>{const o=z(t,(t=>!D(e,t)));o.length>0&&(e=>{throw new Error("Unsupported keys for object: "+Um(e).join(", "))})(o)},Km=e=>((e,t)=>((e,t,o)=>{if(0===t.length)throw new Error("You must specify at least one required field.");return((e,t)=>{if(!l(t))throw new Error("The required fields must be an array. Was: "+t+".");N(t,(t=>{if(!r(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")}))})("required",t),(e=>{const t=Um(e);W(t,((e,o)=>o<t.length-1&&e===t[o+1])).each((e=>{throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}))})(t),n=>{const r=$(n);P(t,(e=>D(r,e)))||((e,t)=>{throw new Error("All required keys ("+Um(e).join(", ")+") were not specified. Specified keys were: "+Um(t).join(", ")+".")})(t,r),e(t,r);const s=z(t,(e=>!o.validate(n[e],e)));return s.length>0&&((e,t)=>{throw new Error("All values need to be of type: "+t+". Keys ("+Um(e).join(", ")+") were not.")})(s,o.label),n}})(e,t,{validate:d,label:"function"}))(Gm,e),Ym=Km(["compare","extract","mutate","sink"]),Jm=Km(["element","start","stop","destroy"]),Qm=Km(["forceDrop","drop","move","delayDrop"]),Xm=()=>{const e=(()=>{const e=$m({move:Vm(["info"])});return{onEvent:f,reset:f,events:e.registry}})(),t=(()=>{let e=C.none();const t=$m({move:Vm(["info"])});return{onEvent:(o,n)=>{n.extract(o).each((o=>{const r=((t,o)=>{const n=e.map((e=>t.compare(e,o)));return e=C.some(o),n})(n,o);r.each((e=>{t.trigger.move(e)}))}))},reset:()=>{e=C.none()},events:t.registry}})();let o=e;return{on:()=>{o.reset(),o=t},off:()=>{o.reset(),o=e},isOn:()=>o===t,onEvent:(e,t)=>{o.onEvent(e,t)},events:t.events}},Zm=e=>{const t=e.replace(/\./g,"-");return{resolve:e=>t+"-"+e}},ed=Zm("ephox-dragster").resolve;var td=Ym({compare:(e,t)=>bn(t.left-e.left,t.top-e.top),extract:e=>C.some(bn(e.x,e.y)),sink:(e,t)=>{const o=(e=>{const t={layerClass:ed("blocker"),...e},o=xe.fromTag("div");return ge(o,"role","presentation"),Bt(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),zm(o,ed("blocker")),zm(o,t.layerClass),{element:g(o),destroy:()=>{$e(o)}}})(t),n=Im(o.element(),"mousedown",e.forceDrop),r=Im(o.element(),"mouseup",e.drop),s=Im(o.element(),"mousemove",e.move),l=Im(o.element(),"mouseout",e.delayDrop);return Jm({element:o.element,start:e=>{Ie(e,o.element())},stop:()=>{$e(o.element())},destroy:()=>{o.destroy(),r.unbind(),s.unbind(),l.unbind(),n.unbind()}})},mutate:(e,t)=>{e.mutate(t.left,t.top)}});const od=Zm("ephox-snooker").resolve,nd=od("resizer-bar"),rd=od("resizer-rows"),sd=od("resizer-cols"),ld=e=>{const t=dt(e.parent(),"."+nd);N(t,$e)},ad=(e,t,o)=>{const n=e.origin();N(t,(t=>{t.each((t=>{const r=o(n,t);zm(r,nd),Ie(e.parent(),r)}))}))},cd=(e,t,o,n,r)=>{const s=yn(o),l=t.isResizable,a=n.length>0?zn.positions(n,o):[],c=a.length>0?((e,t)=>j(e.all,((e,o)=>t(e.element)?[o]:[])))(e,l):[];((e,t,o,n)=>{ad(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=xe.fromTag("div");return Bt(s,{position:"absolute",left:t+"px",top:o-3.5+"px",height:"7px",width:n+"px"}),he(s,{"data-row":e,role:"presentation"}),s})(t.row,o.left-e.left,t.y-e.top,n);return zm(r,rd),r}))})(t,z(a,((e,t)=>O(c,(e=>t===e)))),s,Mo(o));const i=r.length>0?Ln.positions(r,o):[],m=i.length>0?((e,t)=>{const o=[];return k(e.grid.columns,(n=>{an(e,n).map((e=>e.element)).forall(t)&&o.push(n)})),z(o,(o=>{const n=nn(e,(e=>e.column===o));return P(n,(e=>t(e.element)))}))})(e,l):[];((e,t,o,n)=>{ad(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=xe.fromTag("div");return Bt(s,{position:"absolute",left:t-3.5+"px",top:o+"px",height:r+"px",width:"7px"}),he(s,{"data-column":e,role:"presentation"}),s})(t.col,t.x-e.left,o.top-e.top,0,n);return zm(r,sd),r}))})(t,z(i,((e,t)=>O(m,(e=>t===e)))),s,pn(o))},id=(e,t)=>{if(ld(e),e.isResizable(t)){const o=Zo(t),n=dn(o),r=cn(o);cd(o,e,t,n,r)}},md=(e,t)=>{const o=dt(e.parent(),"."+nd);N(o,t)},dd=e=>{md(e,(e=>{Nt(e,"display","none")}))},ud=e=>{md(e,(e=>{Nt(e,"display","block")}))},fd=od("resizer-bar-dragging"),gd=e=>{const t=(()=>{const e=$m({drag:Vm(["xDelta","yDelta","target"])});let t=C.none();const o=(()=>{const e=$m({drag:Vm(["xDelta","yDelta"])});return{mutate:(t,o)=>{e.trigger.drag(t,o)},events:e.registry}})();return o.events.drag.bind((o=>{t.each((t=>{e.trigger.drag(o.xDelta,o.yDelta,t)}))})),{assign:e=>{t=C.some(e)},get:()=>t,mutate:o.mutate,events:e.registry}})(),o=((e,t={})=>{var o;return((e,t,o)=>{let n=!1;const r=$m({start:Vm([]),stop:Vm([])}),s=Xm(),l=()=>{m.stop(),s.isOn()&&(s.off(),r.trigger.stop())},c=((e,t)=>{let o=null;const n=()=>{a(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...t)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,t)}),200)}}})(l);s.events.move.bind((o=>{t.mutate(e,o.info)}));const i=e=>(...t)=>{n&&e.apply(null,t)},m=t.sink(Qm({forceDrop:l,drop:i(l),move:i((e=>{c.cancel(),s.onEvent(e,t)})),delayDrop:i(c.throttle)}),o);return{element:m.element,go:e=>{m.start(e),s.on(),r.trigger.start()},on:()=>{n=!0},off:()=>{n=!1},isActive:()=>n,destroy:()=>{m.destroy()},events:r.registry}})(e,null!==(o=t.mode)&&void 0!==o?o:td,t)})(t,{});let n=C.none();const r=(e,t)=>C.from(pe(e,t));t.events.drag.bind((e=>{r(e.target,"data-row").each((t=>{const o=It(e.target,"top");Nt(e.target,"top",o+e.yDelta+"px")})),r(e.target,"data-column").each((t=>{const o=It(e.target,"left");Nt(e.target,"left",o+e.xDelta+"px")}))}));const s=(e,t)=>It(e,t)-Mt(e,"data-initial-"+t,0);o.events.stop.bind((()=>{t.get().each((t=>{n.each((o=>{r(t,"data-row").each((e=>{const n=s(t,"top");be(t,"data-initial-top"),d.trigger.adjustHeight(o,n,parseInt(e,10))})),r(t,"data-column").each((e=>{const n=s(t,"left");be(t,"data-initial-left"),d.trigger.adjustWidth(o,n,parseInt(e,10))})),id(e,o)}))}))}));const l=(n,r)=>{d.trigger.startAdjust(),t.assign(n),ge(n,"data-initial-"+r,It(n,r)),zm(n,fd),Nt(n,"opacity","0.2"),o.go(e.parent())},c=Im(e.parent(),"mousedown",(e=>{var t;t=e.target,Am(t,rd)&&l(e.target,"top"),(e=>Am(e,sd))(e.target)&&l(e.target,"left")})),i=t=>Re(t,e.view()),m=Im(e.view(),"mouseover",(t=>{var r;(r=t.target,bt(r,"table",i).filter(Lr)).fold((()=>{lt(t.target)&&ld(e)}),(t=>{o.isActive()&&(n=C.some(t),id(e,t))}))})),d=$m({adjustHeight:Vm(["table","delta","row"]),adjustWidth:Vm(["table","delta","column"]),startAdjust:Vm([])});return{destroy:()=>{c.unbind(),m.unbind(),o.destroy(),ld(e)},refresh:t=>{id(e,t)},on:o.on,off:o.off,hideBars:w(dd,e),showBars:w(ud,e),events:d.registry}},hd=(e,t,o)=>{const n=zn,r=Ln,s=gd(e),l=$m({beforeResize:Vm(["table","type"]),afterResize:Vm(["table","type"]),startDrag:Vm([])});return s.events.adjustHeight.bind((e=>{const t=e.table;l.trigger.beforeResize(t,"row");((e,t,o,n)=>{const r=Zo(e),s=((e,t,o)=>lr(e,t,o,Yn,(e=>e.getOrThunk(Ht))))(r,e,n),l=E(s,((e,n)=>o===n?Math.max(t+e,Ht()):e)),a=Wl(r,l),c=((e,t)=>E(e.all,((e,o)=>({element:e.element,height:t[o]}))))(r,l);N(c,(e=>{qn(e.element,e.height)})),N(a,(e=>{qn(e.element,e.height)}));const i=A(l,((e,t)=>e+t),0);qn(e,i)})(t,n.delta(e.delta,t),e.row,n),l.trigger.afterResize(t,"row")})),s.events.startAdjust.bind((e=>{l.trigger.startDrag()})),s.events.adjustWidth.bind((e=>{const n=e.table;l.trigger.beforeResize(n,"col");const s=r.delta(e.delta,n),a=o(n);_l(n,s,e.column,t,a),l.trigger.afterResize(n,"col")})),{on:s.on,off:s.off,refreshBars:s.refresh,hideBars:s.hideBars,showBars:s.showBars,destroy:s.destroy,events:l.registry}},pd=e=>m(e)&&"TABLE"===e.nodeName,wd="bar-",bd=e=>"false"!==pe(e,"data-mce-resize"),vd=e=>{const t=tm(),o=tm(),n=tm();let r,s;const l=t=>cc(e,t),a=()=>Xa(e)?As():zs();return e.on("init",(()=>{const r=((e,t)=>e.inline?((e,t,o)=>({parent:g(t),view:g(e),origin:g(bn(0,0)),isResizable:o}))(xe.fromDom(e.getBody()),(()=>{const e=xe.fromTag("div");return Bt(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Ie(at(xe.fromDom(document)),e),e})(),t):((e,t)=>{const o=me(e)?(e=>xe.fromDom(Ee(e).dom.documentElement))(e):e;return{parent:g(o),view:g(e),origin:g(bn(0,0)),isResizable:t}})(xe.fromDom(e.getDoc()),t))(e,bd);if(n.set(r),(e=>{const t=e.options.get("object_resizing");return D(t.split(","),"table")})(e)&&rc(e)){const n=a(),s=hd(r,n,l);s.on(),s.events.startDrag.bind((o=>{t.set(e.selection.getRng())})),s.events.beforeResize.bind((t=>{const o=t.table.dom;((e,t,o,n,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:o,height:n,origin:r})})(e,o,Ir(o),Fr(o),wd+t.type)})),s.events.afterResize.bind((o=>{const n=o.table,r=n.dom;jr(n),t.on((t=>{e.selection.setRng(t),e.focus()})),((e,t,o,n,r)=>{e.dispatch("ObjectResized",{target:t,width:o,height:n,origin:r})})(e,r,Ir(r),Fr(r),wd+o.type),e.undoManager.add()})),o.set(s)}})),e.on("ObjectResizeStart",(t=>{const o=t.target;if(pd(o)){const n=xe.fromDom(o);N(e.dom.select(".mce-clonedresizable"),(t=>{e.dom.addClass(t,"mce-"+Qa(e)+"-columns")})),!Sc(n)&&oc(e)?Oc(n):!Cc(n)&&tc(e)&&Dc(n),Tc(n)&&Tt(t.origin,wd)&&Dc(n),r=t.width,s=nc(e)?"":((e,t)=>{const o=e.dom.getStyle(t,"width")||e.dom.getAttrib(t,"width");return C.from(o).filter(Ot)})(e,o).getOr("")}})),e.on("ObjectResized",(t=>{const o=t.target;if(pd(o)){const n=xe.fromDom(o),c=t.origin;Tt(c,"corner-")&&((t,o,n)=>{const c=Rt(o,"e");if(""===s&&Dc(t),n!==r&&""!==s){Nt(t,"width",s);const o=a(),i=l(t),m=Xa(e)||c?(e=>Ls(e).columns)(t)-1:0;_l(t,n-r,m,o,i)}else if((e=>/^(\d+(\.\d+)?)%$/.test(e))(s)){const e=parseFloat(s.replace("%",""));Nt(t,"width",n*e/r+"%")}(e=>/^(\d+(\.\d+)?)px$/.test(e))(s)&&(e=>{const t=Zo(e);ln(t)||N(Ut(e),(e=>{const t=zt(e,"width");Nt(e,"width",t),be(e,"width")}))})(t)})(n,c,t.width),jr(n),Ha(e,n.dom,qa)}})),e.on("SwitchMode",(()=>{o.on((t=>{e.mode.isReadOnly()?t.hideBars():t.showBars()}))})),e.on("dragstart dragend",(e=>{o.on((t=>{"dragstart"===e.type?(t.hideBars(),t.off()):(t.on(),t.showBars())}))})),e.on("remove",(()=>{o.on((e=>{e.destroy()})),n.on((t=>{((e,t)=>{e.inline&&$e(t.parent())})(e,t)}))})),{refresh:e=>{o.on((t=>t.refreshBars(xe.fromDom(e))))},hide:()=>{o.on((e=>e.hideBars()))},show:()=>{o.on((e=>e.showBars()))}}},yd=e=>{(e=>{const t=e.options.register;t("table_clone_elements",{processor:"string[]"}),t("table_use_colgroups",{processor:"boolean",default:!0}),t("table_header_type",{processor:e=>{const t=D(["section","cells","sectionCells","auto"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: section, cells, sectionCells or auto."}},default:"section"}),t("table_sizing_mode",{processor:"string",default:"auto"}),t("table_default_attributes",{processor:"object",default:{border:"1"}}),t("table_default_styles",{processor:"object",default:{"border-collapse":"collapse"}}),t("table_column_resizing",{processor:e=>{const t=D(["preservetable","resizetable"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be preservetable, or resizetable."}},default:"preservetable"}),t("table_resize_bars",{processor:"boolean",default:!0}),t("table_style_by_css",{processor:"boolean",default:!0})})(e);const t=vd(e),o=qm(e,t),n=ic(e,t,o);return Gc(e,n),((e,t)=>{const o=_r(e),n=t=>ws(Pr(e)).bind((n=>Kt(n,o).map((o=>{const r=gs(bs(e),o,n);return t(o,r)})))).getOr("");G({mceTableRowType:()=>n(t.getTableRowType),mceTableCellType:()=>n(t.getTableCellType),mceTableColType:()=>n(t.getTableColType)},((t,o)=>e.addQueryValueHandler(o,t)))})(e,n),vs(e,n),{getSelectedCells:o.getSelectedCells,clearSelectedCells:o.clearSelectedCells}};e.add("dom",(e=>({table:yd(e)})))}();