from connector import PostgreSQLConnector
from flask import Flask, flash, render_template, redirect, request, make_response, jsonify
import uuid
import functools
import datetime
import os
import time
import pytz
import math


app = Flask(__name__)
app.secret_key = b'_5#y2L"F4Q8z\n\xec]/'


@app.route("/")
def _deans():
    return render_template("deans.html")

@app.route("/departments")
def _departments():
    return render_template("departments.html")

@app.route("/professors")
def _professors():
    return render_template("professors.html")


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=15007, debug=True)