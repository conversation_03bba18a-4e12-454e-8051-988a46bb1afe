from connector import get_db_connection
from flask import Flask, flash, render_template, redirect, request, make_response, jsonify, session, url_for
from config import AppConfig, DatabaseConfig
import uuid
import functools
import datetime
import os
import time
import pytz
import math
import re


app = Flask(__name__)
app.secret_key = AppConfig.SECRET_KEY

# Инициализация подключения к БД
try:
    db = get_db_connection()
    print("Подключение к базе данных установлено")
except Exception as e:
    print(f"Ошибка подключения к базе данных: {e}")
    db = None


def get_survey_stats():
    """Получить статистику опросов"""
    if not db:
        return None

    try:
        total_surveys = db.surveys.count().exec()
        total_users = db.users.count().exec()

        # Средний балл
        surveys_data = db.surveys.all().exec()
        avg_score = 0
        if surveys_data:
            total_score = sum([s['total_score'] for s in surveys_data])
            avg_score = round(total_score / len(surveys_data), 1)

        return {
            'total_surveys': total_surveys,
            'total_users': total_users,
            'avg_score': avg_score
        }
    except Exception as e:
        print(f"Ошибка получения статистики: {e}")
        return None


@app.route("/")
def dashboard():
    """Главная страница с выбором опроса"""
    stats = get_survey_stats()
    return render_template("dashboard.html", stats=stats)


@app.route("/start_survey", methods=['POST'])
def start_survey():
    """Начать опрос"""
    first_name = request.form.get('first_name', '').strip()
    last_name = request.form.get('last_name', '').strip()
    survey_type = request.form.get('survey_type', '').strip()

    # Валидация
    if not first_name or not last_name:
        flash('Пожалуйста, введите имя и фамилию', 'error')
        return redirect(url_for('dashboard'))

    if survey_type not in AppConfig.SURVEY_TYPES:
        flash('Неверный тип опроса', 'error')
        return redirect(url_for('dashboard'))

    # Сохранить данные в сессии
    session['user_data'] = {
        'first_name': first_name,
        'last_name': last_name,
        'survey_type': survey_type
    }

    # Перенаправить на соответствующий опрос
    if survey_type == 'deans':
        return redirect(url_for('survey_deans'))
    elif survey_type == 'departments':
        return redirect(url_for('survey_departments'))
    elif survey_type == 'professors':
        return redirect(url_for('survey_professors'))
    else:
        flash('Неверный тип опроса', 'error')
        return redirect(url_for('dashboard'))


@app.route("/deans")
def survey_deans():
    """Опрос для деканов"""
    if 'user_data' not in session:
        flash('Сначала заполните данные пользователя', 'error')
        return redirect(url_for('dashboard'))

    user_data = session['user_data']
    if user_data['survey_type'] != 'deans':
        flash('Неверный тип опроса', 'error')
        return redirect(url_for('dashboard'))

    return render_template("deans.html")


@app.route("/departments")
def survey_departments():
    """Опрос для заведующих кафедрами"""
    if 'user_data' not in session:
        flash('Сначала заполните данные пользователя', 'error')
        return redirect(url_for('dashboard'))

    user_data = session['user_data']
    if user_data['survey_type'] != 'departments':
        flash('Неверный тип опроса', 'error')
        return redirect(url_for('dashboard'))

    return render_template("departments.html")


@app.route("/professors")
def survey_professors():
    """Опрос для профессорско-преподавательского состава"""
    if 'user_data' not in session:
        flash('Сначала заполните данные пользователя', 'error')
        return redirect(url_for('dashboard'))

    user_data = session['user_data']
    if user_data['survey_type'] != 'professors':
        flash('Неверный тип опроса', 'error')
        return redirect(url_for('dashboard'))

    return render_template("professors.html")


def extract_score_from_value(value):
    """Извлечь числовое значение балла из строки ответа"""
    if not value:
        return 0

    # Попробовать преобразовать в число напрямую
    try:
        return int(value)
    except ValueError:
        pass

    # Найти число в строке
    numbers = re.findall(r'-?\d+', str(value))
    if numbers:
        return int(numbers[0])

    return 0


def save_survey_results(user_data, form_data, survey_type):
    """Сохранить результаты опроса в базу данных"""
    if not db:
        return None

    try:
        # Создать или найти пользователя
        existing_user = db.users.get(
            first_name=user_data['first_name'],
            last_name=user_data['last_name']
        ).exec()

        if existing_user:
            user_id = existing_user[0]['id']
        else:
            user = db.users.add(
                first_name=user_data['first_name'],
                last_name=user_data['last_name']
            ).exec()
            user_id = user[0]['id']

        # Получить ID типа опроса
        survey_type_data = db.survey_types.get(name=survey_type).exec()
        if not survey_type_data:
            return None
        survey_type_id = survey_type_data[0]['id']

        # Создать запись опроса
        survey = db.surveys.add(
            user_id=user_id,
            survey_type_id=survey_type_id,
            total_score=0
        ).exec()
        survey_id = survey[0]['id']

        # Сохранить ответы и подсчитать общий балл
        total_score = 0
        question_number = 1

        for field_name, field_value in form_data.items():
            if field_name.startswith('question_') or field_value:
                # Определить текст вопроса (упрощенно)
                question_text = f"Вопрос {question_number}"

                # Извлечь балл из ответа
                score = extract_score_from_value(field_value)
                total_score += score

                # Сохранить ответ
                db.survey_answers.add(
                    survey_id=survey_id,
                    question_number=question_number,
                    question_text=question_text,
                    answer_value=str(field_value),
                    answer_score=score
                ).exec()

                question_number += 1

        # Обновить общий балл опроса
        db.surveys.get(id=survey_id).update(total_score=total_score).exec()

        return survey_id

    except Exception as e:
        print(f"Ошибка сохранения результатов опроса: {e}")
        return None


@app.route("/submit_survey", methods=['POST'])
def submit_survey():
    """Обработать отправку опроса"""
    if 'user_data' not in session:
        flash('Сессия истекла. Начните заново.', 'error')
        return redirect(url_for('dashboard'))

    user_data = session['user_data']
    survey_type = user_data['survey_type']

    # Сохранить результаты
    survey_id = save_survey_results(user_data, request.form, survey_type)

    if survey_id:
        # Сохранить ID завершенного опроса для доступа к результатам
        session['completed_survey_id'] = survey_id
        # Очистить данные пользователя
        session.pop('user_data', None)
        flash('Опрос успешно завершен!', 'success')
        return redirect(url_for('survey_result', survey_id=survey_id))
    else:
        flash('Ошибка при сохранении результатов опроса', 'error')
        return redirect(url_for('dashboard'))


@app.route("/survey_result/<int:survey_id>")
def survey_result(survey_id):
    """Показать результаты опроса"""
    if not db:
        flash('Ошибка подключения к базе данных', 'error')
        return redirect(url_for('dashboard'))

    # Проверить, что пользователь только что завершил этот опрос
    if 'completed_survey_id' not in session or session['completed_survey_id'] != survey_id:
        flash('Доступ к результатам ограничен', 'error')
        return redirect(url_for('dashboard'))

    try:
        # Получить данные опроса
        survey_data = db.surveys.get(id=survey_id).exec()
        if not survey_data:
            flash('Опрос не найден', 'error')
            return redirect(url_for('dashboard'))

        survey = survey_data[0]

        # Получить данные пользователя
        user_data = db.users.get(id=survey['user_id']).exec()[0]

        # Получить тип опроса
        survey_type_data = db.survey_types.get(id=survey['survey_type_id']).exec()[0]

        # Получить ответы
        answers = db.survey_answers.get(survey_id=survey_id).exec()

        # Очистить сессию после просмотра результатов
        session.pop('completed_survey_id', None)

        return render_template('survey_result.html',
                             survey=survey,
                             user=user_data,
                             survey_type_name=survey_type_data['description'],
                             answers=answers)

    except Exception as e:
        print(f"Ошибка получения результатов опроса: {e}")
        flash('Ошибка при получении результатов опроса', 'error')
        return redirect(url_for('dashboard'))


@app.route("/admin")
def admin_login():
    """Страница входа администратора"""
    return render_template('admin_login.html')


@app.route("/admin/login", methods=['POST'])
def admin_authenticate():
    """Аутентификация администратора"""
    username = request.form.get('username', '').strip()
    password = request.form.get('password', '').strip()

    # Простая проверка (в реальном проекте используйте хеширование)
    if username == 'admin' and password == 'admin123':
        session['is_admin'] = True
        return redirect(url_for('admin_dashboard'))
    else:
        flash('Неверные данные для входа', 'error')
        return redirect(url_for('admin_login'))


@app.route("/admin/dashboard")
def admin_dashboard():
    """Административная панель"""
    if not session.get('is_admin'):
        flash('Доступ запрещен', 'error')
        return redirect(url_for('admin_login'))

    if not db:
        flash('Ошибка подключения к базе данных', 'error')
        return redirect(url_for('admin_login'))

    try:
        # Получить все опросы с данными пользователей
        surveys = db.surveys.all().exec()
        users = db.users.all().exec()
        survey_types = db.survey_types.all().exec()

        # Создать словари для быстрого поиска
        users_dict = {user['id']: user for user in users}
        types_dict = {st['id']: st for st in survey_types}

        # Обогатить данные опросов
        for survey in surveys:
            survey['user'] = users_dict.get(survey['user_id'], {})
            survey['survey_type'] = types_dict.get(survey['survey_type_id'], {})

        return render_template('admin_dashboard.html', surveys=surveys)

    except Exception as e:
        print(f"Ошибка в административной панели: {e}")
        flash('Ошибка при загрузке данных', 'error')
        return redirect(url_for('admin_login'))


@app.route("/admin/survey_details/<int:survey_id>")
def admin_survey_details(survey_id):
    """Подробности опроса для администратора"""
    if not session.get('is_admin'):
        flash('Доступ запрещен', 'error')
        return redirect(url_for('admin_login'))

    if not db:
        flash('Ошибка подключения к базе данных', 'error')
        return redirect(url_for('admin_dashboard'))

    try:
        # Получить данные опроса
        survey_data = db.surveys.get(id=survey_id).exec()
        if not survey_data:
            flash('Опрос не найден', 'error')
            return redirect(url_for('admin_dashboard'))

        survey = survey_data[0]

        # Получить данные пользователя
        user_data = db.users.get(id=survey['user_id']).exec()[0]

        # Получить тип опроса
        survey_type_data = db.survey_types.get(id=survey['survey_type_id']).exec()[0]

        # Получить ответы
        answers = db.survey_answers.get(survey_id=survey_id).exec()

        return render_template('admin_survey_details.html',
                             survey=survey,
                             user=user_data,
                             survey_type=survey_type_data,
                             answers=answers)

    except Exception as e:
        print(f"Ошибка получения подробностей опроса: {e}")
        flash('Ошибка при получении данных опроса', 'error')
        return redirect(url_for('admin_dashboard'))


@app.route("/admin/logout")
def admin_logout():
    """Выход из административной панели"""
    session.pop('is_admin', None)
    flash('Вы вышли из системы', 'success')
    return redirect(url_for('dashboard'))


if __name__ == "__main__":
    app.run(host=AppConfig.HOST, port=AppConfig.PORT, debug=AppConfig.DEBUG)