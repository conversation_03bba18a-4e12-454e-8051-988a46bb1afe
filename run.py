from connector import get_db_connection
from flask import Flask, flash, render_template, redirect, request, make_response, jsonify, session, url_for
from config import AppConfig, DatabaseConfig
import uuid
import functools
import datetime
import os
import time
import pytz
import math
import re


app = Flask(__name__)
app.secret_key = AppConfig.SECRET_KEY

# Инициализация подключения к БД
try:
    db = get_db_connection()
    print("Подключение к базе данных установлено")
except Exception as e:
    print(f"Ошибка подключения к базе данных: {e}")
    db = None


def get_survey_stats():
    """Получить статистику опросов"""
    if not db:
        return None

    try:
        total_surveys = db.surveys.count().exec()
        total_users = db.users.count().exec()

        # Средний балл
        surveys_data = db.surveys.all().exec()
        avg_score = 0
        if surveys_data:
            total_score = sum([s['total_score'] for s in surveys_data])
            avg_score = round(total_score / len(surveys_data), 1)

        return {
            'total_surveys': total_surveys,
            'total_users': total_users,
            'avg_score': avg_score
        }
    except Exception as e:
        print(f"Ошибка получения статистики: {e}")
        return None


@app.route("/")
def dashboard():
    """Главная страница с выбором опроса"""
    stats = get_survey_stats()
    return render_template("dashboard.html", stats=stats)


@app.route("/start_survey", methods=['POST'])
def start_survey():
    """Начать опрос"""
    first_name = request.form.get('first_name', '').strip()
    last_name = request.form.get('last_name', '').strip()
    survey_type = request.form.get('survey_type', '').strip()

    # Валидация
    if not first_name or not last_name:
        flash('Пожалуйста, введите имя и фамилию', 'error')
        return redirect(url_for('dashboard'))

    if survey_type not in AppConfig.SURVEY_TYPES:
        flash('Неверный тип опроса', 'error')
        return redirect(url_for('dashboard'))

    # Сохранить данные в сессии
    session['user_data'] = {
        'first_name': first_name,
        'last_name': last_name,
        'survey_type': survey_type
    }

    # Перенаправить на соответствующий опрос
    return redirect(url_for(f'survey_{survey_type}'))


@app.route("/survey/deans")
def survey_deans():
    """Опрос для деканов"""
    if 'user_data' not in session:
        flash('Сначала заполните данные пользователя', 'error')
        return redirect(url_for('dashboard'))

    return render_template("deans.html")


@app.route("/survey/departments")
def survey_departments():
    """Опрос для заведующих кафедрами"""
    if 'user_data' not in session:
        flash('Сначала заполните данные пользователя', 'error')
        return redirect(url_for('dashboard'))

    return render_template("departments.html")


@app.route("/survey/professors")
def survey_professors():
    """Опрос для профессорско-преподавательского состава"""
    if 'user_data' not in session:
        flash('Сначала заполните данные пользователя', 'error')
        return redirect(url_for('dashboard'))

    return render_template("professors.html")


def extract_score_from_value(value):
    """Извлечь числовое значение балла из строки ответа"""
    if not value:
        return 0

    # Попробовать преобразовать в число напрямую
    try:
        return int(value)
    except ValueError:
        pass

    # Найти число в строке
    numbers = re.findall(r'-?\d+', str(value))
    if numbers:
        return int(numbers[0])

    return 0


def save_survey_results(user_data, form_data, survey_type):
    """Сохранить результаты опроса в базу данных"""
    if not db:
        return None

    try:
        # Создать или найти пользователя
        existing_user = db.users.get(
            first_name=user_data['first_name'],
            last_name=user_data['last_name']
        ).exec()

        if existing_user:
            user_id = existing_user[0]['id']
        else:
            user = db.users.add(
                first_name=user_data['first_name'],
                last_name=user_data['last_name']
            ).exec()
            user_id = user[0]['id']

        # Получить ID типа опроса
        survey_type_data = db.survey_types.get(name=survey_type).exec()
        if not survey_type_data:
            return None
        survey_type_id = survey_type_data[0]['id']

        # Создать запись опроса
        survey = db.surveys.add(
            user_id=user_id,
            survey_type_id=survey_type_id,
            total_score=0
        ).exec()
        survey_id = survey[0]['id']

        # Сохранить ответы и подсчитать общий балл
        total_score = 0
        question_number = 1

        for field_name, field_value in form_data.items():
            if field_name.startswith('question_') or field_value:
                # Определить текст вопроса (упрощенно)
                question_text = f"Вопрос {question_number}"

                # Извлечь балл из ответа
                score = extract_score_from_value(field_value)
                total_score += score

                # Сохранить ответ
                db.survey_answers.add(
                    survey_id=survey_id,
                    question_number=question_number,
                    question_text=question_text,
                    answer_value=str(field_value),
                    answer_score=score
                ).exec()

                question_number += 1

        # Обновить общий балл опроса
        db.surveys.get(id=survey_id).update(total_score=total_score).exec()

        return survey_id

    except Exception as e:
        print(f"Ошибка сохранения результатов опроса: {e}")
        return None


@app.route("/submit_survey", methods=['POST'])
def submit_survey():
    """Обработать отправку опроса"""
    if 'user_data' not in session:
        flash('Сессия истекла. Начните заново.', 'error')
        return redirect(url_for('dashboard'))

    user_data = session['user_data']
    survey_type = user_data['survey_type']

    # Сохранить результаты
    survey_id = save_survey_results(user_data, request.form, survey_type)

    if survey_id:
        # Очистить сессию
        session.pop('user_data', None)
        flash('Опрос успешно завершен!', 'success')
        return redirect(url_for('survey_result', survey_id=survey_id))
    else:
        flash('Ошибка при сохранении результатов опроса', 'error')
        return redirect(url_for('dashboard'))


@app.route("/survey_result/<int:survey_id>")
def survey_result(survey_id):
    """Показать результаты опроса"""
    if not db:
        flash('Ошибка подключения к базе данных', 'error')
        return redirect(url_for('dashboard'))

    try:
        # Получить данные опроса
        survey_data = db.surveys.get(id=survey_id).exec()
        if not survey_data:
            flash('Опрос не найден', 'error')
            return redirect(url_for('dashboard'))

        survey = survey_data[0]

        # Получить данные пользователя
        user_data = db.users.get(id=survey['user_id']).exec()[0]

        # Получить тип опроса
        survey_type_data = db.survey_types.get(id=survey['survey_type_id']).exec()[0]

        # Получить ответы
        answers = db.survey_answers.get(survey_id=survey_id).exec()

        return render_template('survey_result.html',
                             survey=survey,
                             user=user_data,
                             survey_type_name=survey_type_data['description'],
                             answers=answers)

    except Exception as e:
        print(f"Ошибка получения результатов опроса: {e}")
        flash('Ошибка при получении результатов опроса', 'error')
        return redirect(url_for('dashboard'))


if __name__ == "__main__":
    app.run(host=AppConfig.HOST, port=AppConfig.PORT, debug=AppConfig.DEBUG)