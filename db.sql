-- KPI Опросник - База данных
-- Создание таблиц для хранения результатов опросов

-- Таблица пользователей
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Таблица типов опросов
CREATE TABLE IF NOT EXISTS survey_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    template_file VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Таблица опросов (заполненных форм)
CREATE TABLE IF NOT EXISTS surveys (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    survey_type_id INTEGER REFERENCES survey_types(id) ON DELETE CASCADE,
    total_score INTEGER DEFAULT 0,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'completed'
);

-- Таблица ответов на вопросы
CREATE TABLE IF NOT EXISTS survey_answers (
    id SERIAL PRIMARY KEY,
    survey_id INTEGER REFERENCES surveys(id) ON DELETE CASCADE,
    question_number INTEGER NOT NULL,
    question_text TEXT NOT NULL,
    answer_value VARCHAR(500),
    answer_score INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Вставка типов опросов
INSERT INTO survey_types (name, description, template_file) VALUES 
('deans', 'Факультет декани фаолияти самарадорлигини баҳолаш мезонлари (KPI)', 'deans.html'),
('departments', 'Кафедра мудири фаолияти самарадорлигини баҳолаш мезонлари (KPI)', 'departments.html'),
('professors', 'Кафедра профессор-ўқитувчилари фаолиятини баҳолаш мезонлари (KPI)', 'professors.html')
ON CONFLICT (name) DO NOTHING;

-- Индексы для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_surveys_user_id ON surveys(user_id);
CREATE INDEX IF NOT EXISTS idx_surveys_survey_type_id ON surveys(survey_type_id);
CREATE INDEX IF NOT EXISTS idx_survey_answers_survey_id ON survey_answers(survey_id);
CREATE INDEX IF NOT EXISTS idx_surveys_completed_at ON surveys(completed_at);

-- Комментарии к таблицам
COMMENT ON TABLE users IS 'Таблица пользователей, проходящих опросы';
COMMENT ON TABLE survey_types IS 'Типы опросов (деканы, заведующие кафедрами, профессора)';
COMMENT ON TABLE surveys IS 'Заполненные опросы';
COMMENT ON TABLE survey_answers IS 'Ответы на вопросы в опросах';

COMMENT ON COLUMN users.first_name IS 'Имя пользователя';
COMMENT ON COLUMN users.last_name IS 'Фамилия пользователя';
COMMENT ON COLUMN survey_types.template_file IS 'Имя файла шаблона для отображения опроса';
COMMENT ON COLUMN surveys.total_score IS 'Общий балл за опрос';
COMMENT ON COLUMN survey_answers.question_number IS 'Номер вопроса в опросе';
COMMENT ON COLUMN survey_answers.answer_value IS 'Значение ответа (текст или число)';
COMMENT ON COLUMN survey_answers.answer_score IS 'Балл за ответ';
