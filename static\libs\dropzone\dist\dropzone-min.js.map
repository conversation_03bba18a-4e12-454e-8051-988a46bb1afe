{"mappings": "wEAA+CA,GAC7C,QAAa,IAATA,EACF,MAAM,IAAIC,eAAe,6DAG3B,OAAOD,aCL+BE,EAAUC,GAChD,KAAMD,aAAoBC,GACxB,MAAM,IAAIC,UAAU,8CCFfC,EAAkBC,EAAQC,GACjC,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAME,OAAQD,IAAK,CACrC,IAAIE,EAAaH,EAAMC,GACvBE,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDC,OAAOC,eAAeT,EAAQI,EAAWM,IAAKN,eAIbP,EAAac,EAAYC,GAG5D,OAFID,GAAYZ,EAAkBF,EAAYgB,UAAWF,GACrDC,GAAab,EAAkBF,EAAae,GACzCf,WCbAiB,EAAeC,GAItB,OAHAD,EAAiBN,OAAOQ,eAAiBR,OAAOS,eAAiB,SAAwBF,GACvF,OAAOA,EAAEG,WAAaV,OAAOS,eAAeF,IAEvCD,EAAeC,YCJfI,EAAeJ,EAAGK,GAMzB,OALAD,EAAiBX,OAAOQ,gBAAkB,SAAwBD,EAAGK,GAEnE,OADAL,EAAEG,UAAYE,EACPL,GAGFI,EAAeJ,EAAGK,cCJOC,EAAUC,GAC1C,GAA0B,mBAAfA,GAA4C,OAAfA,EACtC,MAAM,IAAIxB,UAAU,sDAGtBuB,EAASR,UAAYL,OAAOe,OAAOD,GAAcA,EAAWT,UAAW,CACrEW,YAAa,CACXC,MAAOJ,EACPd,UAAU,EACVD,cAAc,KAGdgB,GDJGH,ECIwBE,EAAUC,cCXQ5B,EAAMgC,GACvD,OAAIA,GAA2B,YCJDC,EDITD,ICHLC,EAAIH,cAAgBI,OAAS,gBAAkBD,IDGJ,mBAATD,EAI3CG,EAAsBnC,GAHpBgC,MCLqBC,iBCiEvBG,EAAYH,GACnB,OAAOI,MAAMC,QAAQL,IAAiC,mBAA9B,GAAQM,SAASP,KAAKC,YAGvCO,EAAeC,GACtB,OAAQA,GAAsB,iBAAPA,GAAiC,mBAAPA,EAtEnDC,EA4BE,SAEOC,IACP,IAAIC,EAAO,GAAGC,MAAMb,KAAKc,WACrBC,GAAO,EACW,kBAAXH,EAAK,KACdG,EAAOH,EAAKI,SAEd,IAAIC,EAASL,EAAK,GAClB,GAAIJ,EAAeS,GACjB,MAAM,IAAIC,MAAM,8BAIlB,IAFA,IAAIC,EAAYP,EAAKC,MAAM,GACvBO,EAAMD,EAAU1C,OACXD,EAAI,EAAGA,EAAI4C,EAAK5C,IAAK,CAC5B,IAAI6C,EAAWF,EAAU3C,GACzB,IAAK,IAAIQ,KAAOqC,EACd,GAAIvC,OAAOK,UAAUmC,eAAetB,KAAKqB,EAAUrC,GAAM,CACvD,IAAIe,EAAQsB,EAASrC,GACrB,GAAI+B,GAAQX,EAAYL,GAAQ,CAC9B,IAAIwB,EAAOlB,MAAMC,QAAQP,GAAS,GAAK,GACvCkB,EAAOjC,GAAO2B,GACZ,EACA7B,OAAOK,UAAUmC,eAAetB,KAAKiB,EAAQjC,KAASwB,EAAeS,EAAOjC,IACxEiC,EAAOjC,GACPuC,EACJxB,QAGFkB,EAAOjC,GAAOe,GAKtB,OAAOkB,OC1DYO,EAAN,iCAAMA,WAAAA,YAAAA,EAAO,EAE1BxC,IAAA,WAAA,SAAGyC,EAAOC,GAOR,OANAC,KAAKC,WAAaD,KAAKC,YAAc,GAEhCD,KAAKC,WAAWH,KACnBE,KAAKC,WAAWH,GAAS,IAE3BE,KAAKC,WAAWH,GAAOI,KAAKH,GACrBC,QAGT3C,IAAA,aAAA,SAAKyC,GAAO,IAAA,IAAAK,EAAAhB,UAAArC,OAAGmC,EAAH,IAAOP,MAAPyB,EAAA,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAGnB,EAAHmB,EAAA,GAAAjB,UAAAiB,GACVJ,KAAKC,WAAaD,KAAKC,YAAc,GACrC,IAAII,EAAYL,KAAKC,WAAWH,GAGzBQ,GAAY,EAAZC,GAAY,EAAZC,OAAYC,EADnB,GAAIJ,EAAS,IACX,IAAG,IAAEK,EAAAC,EAAgBN,EAAS9B,OAAAqC,cAAzBN,GAAAI,EAAAC,EAAYE,QAAAC,MAAZR,GAAY,EAAA,CAAZ,IAAIS,EAAJL,EAAYtC,MACf2C,EAASC,MAAMhB,KAAMf,aADlBsB,GAAY,EAAZC,EAAYS,cAAZX,GAAY,MAAZK,EAAYO,QAAZP,EAAYO,oBAAZX,QAAAC,GAUP,OALIR,KAAKmB,SACPnB,KAAKmB,QAAQC,cACXpB,KAAKqB,UAAU,YAAcvB,EAAO,CAAEb,KAAMA,KAGzCe,QAGT3C,IAAA,kBAAA,SAAUiE,EAAWC,GACnB,IAAIC,EAAS,CAAEC,SAAS,EAAMC,YAAY,EAAMH,OAAQA,GAExD,GAAkC,mBAAvBI,OAAOC,YAChB,OAAO,IAAIA,YAAYN,EAAWE,GAIlC,IAAIK,EAAMC,SAASC,YAAY,eAO/B,OANAF,EAAIG,gBACFV,EACAE,EAAOC,QACPD,EAAOE,WACPF,EAAOD,QAEFM,KAOXxE,IAAA,YAAA,SAAIyC,EAAOC,GACT,IAAKC,KAAKC,YAAmC,IAArBd,UAAUrC,OAEhC,OADAkD,KAAKC,WAAa,GACXD,KAIT,IAAIK,EAAYL,KAAKC,WAAWH,GAChC,IAAKO,EACH,OAAOL,KAIT,GAAyB,IAArBb,UAAUrC,OAEZ,cADOkD,KAAKC,WAAWH,GAChBE,KAIT,IAAK,IAAInD,EAAI,EAAGA,EAAIwD,EAAUvD,OAAQD,IAAK,CACzC,IAAIkE,EAAWV,EAAUxD,GACzB,GAAIkE,IAAahB,EAAI,CACnBM,EAAU4B,OAAOpF,EAAG,GACpB,OAIJ,OAAOmD,SA/EUH,EAAN,GCDf,IAixBAqC,EAjxBqB,CAQnBC,IAAK,KAMLC,OAAQ,OAKRC,iBAAiB,EAMjBC,QAAS,KAMTC,gBAAiB,EASjBC,gBAAgB,EAQhBC,UAAU,EAOVC,eAAe,EAKfC,UAAW,QAKXC,sBAAsB,EAKtBC,aAAa,EAKbC,iBAAkB,EAKlBC,YAAa,IAObC,UAAW,OAKXC,uBAAuB,EAKvBC,qBAAsB,GAKtBC,eAAgB,IAKhBC,gBAAiB,IAMjBC,gBAAiB,OAUjBC,YAAa,KAKbC,aAAc,KAOdC,eAAgB,KAKhBC,cAAe,GAMfC,aAAc,UASdC,aAAc,IAQdC,SAAU,KAMVC,QAAS,KAQTC,gBAAgB,EAUhBC,WAAW,EAKXC,mBAAmB,EAanBC,cAAe,KAMfC,kBAAmB,KAYnBC,kBAAkB,EAMlBC,WAAW,EAOXC,gBAAgB,EAQhBC,kBAAmB,KAKnBC,iBAAiB,EASjBC,qBAAsB,OAUtBC,QAAS,KAKTC,eAAgB,KAOhBC,WAAY,KAQZC,eAAe,EAKfC,mBAAoB,4BAKpBC,oBACE,0DAOFC,iBACE,kFAMFC,eACE,uEAKFC,oBAAqB,uCAMrBC,kBAAmB,6CAKnBC,iBAAkB,gBAKlBC,mBAAoB,mBAKpBC,6BAA8B,+CAK9BC,eAAgB,cAKhBC,2BAA4B,KAM5BC,qBAAsB,qCAMtBC,kBAAmB,CAAEC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAKhEC,KAAA,aAYAvE,OAAA,SAAOwE,EAAOC,EAAKC,GACjB,GAAIA,EACF,MAAO,CACLC,OAAQD,EAAME,KAAKC,OAAOC,KAC1BC,aAAcL,EAAMM,MACpBC,gBAAiBP,EAAME,KAAKM,KAC5BC,YAAa3G,KAAK4G,QAAQjE,UAC1BkE,kBAAmBX,EAAME,KAAKC,OAAOS,gBACrCC,kBAAmBb,EAAMM,MAAQxG,KAAK4G,QAAQjE,YAcpDqE,OAAA,SAAOZ,EAAMtF,GACX,OAAOA,KASTmG,eAAgB,SAAUb,EAAMtF,GAC9BA,KASFoG,YAAY,EAOZC,SAAA,WAEE,IAAIC,EACJpH,KAAKmB,QAAQkG,UAAa,GAAyBC,OAAvBtH,KAAKmB,QAAQkG,UAAU,iCAE9C/G,GAAS,EAATC,GAAS,EAATC,OAASC,MAAd,IAAG,IAAEC,EAAAC,EAAaX,KAAKmB,QAAQoG,qBAAqB,OAAKhJ,OAAAqC,cAApDN,GAAAI,EAAAC,EAASE,QAAAC,MAATR,GAAS,EAAA,CAAT,IAAIkH,EAAJ9G,EAAStC,MACZ,GAAE,uBAAyBqJ,KAAKD,EAAMH,WAAY,CAChDD,EAAiBI,EACjBA,EAAMH,UAAY,aAClB,iBAJC9G,GAAS,EAATC,EAASS,cAATX,GAAS,MAATK,EAASO,QAATP,EAASO,oBAATX,QAAAC,GAOA4G,IACHA,EAAiBM,EAASC,cACxB,+CAEF3H,KAAKmB,QAAQyG,YAAYR,IAG3B,IAAIS,EAAOT,EAAeG,qBAAqB,QAAQ,GASvD,OARIM,IACsB,MAApBA,EAAKC,YACPD,EAAKC,YAAc9H,KAAK4G,QAAQ9B,oBACL,MAAlB+C,EAAKE,YACdF,EAAKE,UAAY/H,KAAK4G,QAAQ9B,sBAI3B9E,KAAKmB,QAAQyG,YAAY5H,KAAKgI,oBAevCC,OAAA,SAAO7B,EAAM8B,EAAOC,EAAQzE,GAC1B,IAAI0E,EAAO,CACTC,KAAM,EACNC,KAAM,EACNC,SAAUnC,EAAK8B,MACfM,UAAWpC,EAAK+B,QAGdM,EAAWrC,EAAK8B,MAAQ9B,EAAK+B,OAGpB,MAATD,GAA2B,MAAVC,GACnBD,EAAQE,EAAKG,SACbJ,EAASC,EAAKI,WACI,MAATN,EACTA,EAAQC,EAASM,EACE,MAAVN,IACTA,EAASD,EAAQO,GAOnB,IAAIC,GAHJR,EAAQS,KAAKC,IAAIV,EAAOE,EAAKG,YAC7BJ,EAASQ,KAAKC,IAAIT,EAAQC,EAAKI,YAI/B,GAAIJ,EAAKG,SAAWL,GAASE,EAAKI,UAAYL,EAE5C,GAAqB,SAAjBzE,EACE+E,EAAWC,GACbN,EAAKI,UAAYpC,EAAK+B,OACtBC,EAAKG,SAAWH,EAAKI,UAAYE,IAEjCN,EAAKG,SAAWnC,EAAK8B,MACrBE,EAAKI,UAAYJ,EAAKG,SAAWG,OAE9B,CAAA,GAAqB,YAAjBhF,EAQT,MAAM,IAAInE,MAAO,yBAAqC+H,OAAb5D,EAAa,MANlD+E,EAAWC,EACbP,EAASD,EAAQO,EAEjBP,EAAQC,EAASM,EAavB,OANAL,EAAKC,MAAQjC,EAAK8B,MAAQE,EAAKG,UAAY,EAC3CH,EAAKE,MAAQlC,EAAK+B,OAASC,EAAKI,WAAa,EAE7CJ,EAAKS,SAAWX,EAChBE,EAAKU,UAAYX,EAEVC,GAYTW,cAAA,SAAc3C,EAAMtF,GAClB,OACGd,KAAK4G,QAAQtD,aAAetD,KAAK4G,QAAQrD,eAC1C6C,EAAK4C,KAAKC,MAAK,WAERjJ,KAAKkJ,YACV9C,EACApG,KAAK4G,QAAQtD,YACbtD,KAAK4G,QAAQrD,aACbvD,KAAK4G,QAAQlD,aACb5C,GAGKA,EAAKsF,IAkBhB+C,gBAAiBC,EC3jBF,wqCDukBfC,KAAA,SAAKC,GACH,OAAOtJ,KAAKmB,QAAQoI,UAAUC,OAAO,kBAEvCC,UAAA,SAAUH,KACVI,QAAA,SAAQJ,GACN,OAAOtJ,KAAKmB,QAAQoI,UAAUC,OAAO,kBAEvCG,UAAA,SAAUL,GACR,OAAOtJ,KAAKmB,QAAQoI,UAAUK,IAAI,kBAEpCC,SAAA,SAASP,GACP,OAAOtJ,KAAKmB,QAAQoI,UAAUK,IAAI,kBAEpCE,UAAA,SAAUR,GACR,OAAOtJ,KAAKmB,QAAQoI,UAAUC,OAAO,kBAGvCO,MAAA,SAAMT,KAINU,MAAA,WACE,OAAOhK,KAAKmB,QAAQoI,UAAUC,OAAO,eAKvCS,UAAA,SAAU7D,GAKR,GAJIpG,KAAKmB,UAAYnB,KAAKsE,mBACxBtE,KAAKmB,QAAQoI,UAAUK,IAAI,cAGzB5J,KAAKsE,oBAAsBtE,KAAK4G,QAAQrC,gBAAiB,YAC3D6B,EAAK8D,eAAiBxC,EAASC,cAC7B3H,KAAK4G,QAAQuC,gBAAgBgB,QAE/B/D,EAAK+C,gBAAkB/C,EAAK8D,eAE5BlK,KAAKsE,kBAAkBsD,YAAYxB,EAAK8D,oBACnC5J,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYyF,EAAK8D,eAAeE,iBAAiB,kBAAgB7L,OAAAqC,cAAjEN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAAR,IAAI+J,EAAJ3J,EAAQtC,MACXiM,EAAKvC,YAAc1B,EAAKkE,eADrB/J,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,OAGA+J,GAAI,EAAJC,GAAI,EAAJC,OAAIhK,MAAT,IAAG,IAAEiK,EAAAC,EAAQvE,EAAK8D,eAAeE,iBAAiB,kBAAgB7L,OAAAqC,cAA7D2J,GAAAG,EAAAC,EAAI9J,QAAAC,MAAJyJ,GAAI,GAAJF,EAAAK,EAAItM,OACFwM,UAAY5K,KAAK6K,SAASzE,EAAKM,eADjC8D,GAAI,EAAJC,EAAIxJ,cAAJsJ,GAAI,MAAJI,EAAIzJ,QAAJyJ,EAAIzJ,oBAAJsJ,QAAAC,GAIDzK,KAAK4G,QAAQvC,iBACf+B,EAAK0E,YAAcpD,EAASC,cACzB,oEAA+FL,OAA5BtH,KAAK4G,QAAQtB,eAAe,SAElGc,EAAK8D,eAAetC,YAAYxB,EAAK0E,cAGvC,IAAIC,EAAkB,SAACzB,WAGrB,GAFAA,EAAE0B,iBACF1B,EAAE2B,kBACE7E,EAAK8E,SAAWxD,EAASyD,UAC3B,OAAOzD,EAAS0D,QAAOC,EAChBzE,QAAQvB,8BACb,WAAM,OAAMiG,EAADC,WAAWnF,cAGxB,OAAEiF,EAAOzE,QAAQrB,2BACRmC,EAAS0D,QAAOC,EAChBzE,QAAQrB,4BACb,WAAM,OAAMiG,EAADD,WAAWnF,MAGlBiF,EAAME,WAAWnF,IAKxBqF,GAAc,EAAdC,GAAc,EAAdC,OAAclL,MAAnB,IAAG,IAAEmL,EAAAC,EAAkBzF,EAAK8D,eAAeE,iBACzC,oBAAkB7L,OAAAqC,cADf6K,GAAAG,EAAAC,EAAchL,QAAAC,MAAd2K,GAAc,EAAA,CAAdG,EAAcxN,MAGN0N,iBAAiB,QAASf,aAHlCW,GAAc,EAAdC,EAAc1K,cAAdwK,GAAc,MAAdI,EAAc3K,QAAd2K,EAAc3K,oBAAdwK,QAAAC,MASTI,YAAA,SAAY3F,GAIV,OAH2B,MAAvBA,EAAK8D,gBAA4D,MAAlC9D,EAAK8D,eAAe8B,YACrD5F,EAAK8D,eAAe8B,WAAWC,YAAY7F,EAAK8D,gBAE3ClK,KAAKkM,+BAKdC,UAAA,SAAU/F,EAAMgG,GACd,GAAIhG,EAAK8D,eAAgB,CACvB9D,EAAK8D,eAAeX,UAAUC,OAAO,uBAChClJ,GAAoB,EAApBC,GAAoB,EAApBC,OAAoBC,MAAzB,IAAG,IAAEC,EAAAC,EAAwByF,EAAK8D,eAAeE,iBAC/C,uBAAqB7L,OAAAqC,cADlBN,GAAAI,EAAAC,EAAoBE,QAAAC,MAApBR,GAAoB,EAEtB,CAFE,IAAI+L,EAAJ3L,EAAoBtC,MAGvBiO,EAAiBC,IAAMlG,EAAKkE,KAC5B+B,EAAiBE,IAAMH,YAJpB7L,GAAoB,EAApBC,EAAoBS,cAApBX,GAAoB,MAApBK,EAAoBO,QAApBP,EAAoBO,oBAApBX,QAAAC,GAOL,OAAOgM,YACL,WAAM,OAAApG,EAAK8D,eAAeX,UAAUK,IAAI,sBACxC,KAON6C,MAAA,SAAMrG,EAAMsG,GACV,GAAItG,EAAK8D,eAAgB,CACvB9D,EAAK8D,eAAeX,UAAUK,IAAI,YACX,iBAAZ8C,GAAwBA,EAAQD,QACzCC,EAAUA,EAAQD,WAEfnM,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYyF,EAAK8D,eAAeE,iBACnC,0BAAwB7L,OAAAqC,cADrBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAARI,EAAQtC,MAGN0J,YAAc4E,YAHhBnM,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,MAQTmM,cAAA,aAKAC,WAAA,SAAWxG,GACT,GAAIA,EAAK8D,iBACP9D,EAAK8D,eAAeX,UAAUK,IAAI,iBAC9BxD,EAAK0E,aACP,OAAQ1E,EAAK0E,YAAYF,UAAY5K,KAAK4G,QAAQzB,kBAKxD0H,mBAAA,aAKAC,eAAA,SAAe1G,EAAM2G,EAAUC,OAEtB1M,GAAQ,EAARC,GAAQ,EAARC,OAAQC,EADf,GAAI2F,EAAK8D,eAAc,IACrB,IAAG,IAAExJ,EAAAC,EAAYyF,EAAK8D,eAAeE,iBACnC,4BAA0B7L,OAAAqC,cADvBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAAR,IAAI+J,EAAJ3J,EAAQtC,MAGO,aAAlBiM,EAAK4C,SACA5C,EAAKjM,MAAQ2O,EACb1C,EAAK6C,MAAMhF,MAAS,GAAWZ,OAATyF,EAAS,eALjCxM,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,KAYT2M,oBAAA,aAKAC,QAAA,aAEAC,gBAAA,aAIAC,QAAA,SAAQlH,GACN,GAAIA,EAAK8D,eACP,OAAO9D,EAAK8D,eAAeX,UAAUK,IAAI,eAI7C2D,gBAAA,aAGAC,SAAA,SAASpH,GACP,OAAOpG,KAAKyN,KAAK,QAASrH,EAAMpG,KAAK4G,QAAQxB,qBAG/CsI,iBAAA,aAIAC,SAAA,SAASvH,GAIP,GAHIA,EAAK0E,cACP1E,EAAK0E,YAAYF,UAAY5K,KAAK4G,QAAQtB,gBAExCc,EAAK8D,eACP,OAAO9D,EAAK8D,eAAeX,UAAUK,IAAI,gBAI7CgE,iBAAA,aAEAC,iBAAA,aAEAC,gBAAA,aAEAC,cAAA,aAEAC,WAAA,cE7wBmBtG,EAAN,SAAQuG,yBAAFvG,EAgDPwG,EAAItH,ST7CsBlJ,ES+ChCyJ,EAAUgH,EAYd,UA9DiBzG,cTGmBhK,ESHnBgK,ETIZjK,EAAeC,ISJKW,KAAA2B,QAmDpBmB,QAAU+M,IAEVE,kBAAoB,KACpBC,UAAY,KACZrI,MAAQ,GAEe,iBAAlBqF,EAAMlK,UAAoBkK,EAC7BlK,QAAUW,SAASwM,cAAajD,EAAMlK,WAI3CkK,EAAQlK,SAAoC,MAA7BkK,EAASlK,QAAQoN,SAChC,MAAM,IAAIhP,MAAM,6BAGlB,GAAE8L,EAAOlK,QAAQqN,SACf,MAAM,IAAIjP,MAAM,8BAIlBmI,EAAS+G,UAAUvO,KAAI1B,EAAA6M,MAGlBlK,QAAQqN,SAAQhQ,EAAA6M,GAErB,IAAIqD,EACmD,OAApDP,EAAOzG,EAASiH,kBAAiBtD,EAAMlK,UAAoBgN,EAAO,GAgBrE,KAdKvH,QAAUwC,EAAArK,EAAA,EACb,EACA,GACAmD,EACAwM,EACW,MAAX9H,EAAkBA,EAAU,MAGzBA,QAAQuC,gBAAekC,EAAQzE,QAAQuC,gBAAgByF,QAAO,OAEjE,IAIAvD,EAAOzE,QAAQhC,gBAAkB8C,EAASmH,qBAC1C,OAAMC,EAAAzD,EAAAA,EAAMzE,QAAQO,SAAS9I,KAAIG,EAAA6M,KAQnC,GAJwB,MAAtBA,EAAOzE,QAAQzE,MAAWkJ,EACrBzE,QAAQzE,IAAGkJ,EAAQlK,QAAQ4N,aAAa,YAG7C1D,EAAQzE,QAAQzE,IAChB,MAAM,IAAI5C,MAAM,oBAGlB,GAAE8L,EAAOzE,QAAQ3C,eAAaoH,EAASzE,QAAQ1C,kBAC7C,MAAM,IAAI3E,MACR,sGAIJ,GAAE8L,EAAOzE,QAAQpE,gBAAc6I,EAASzE,QAAQnE,SAC9C,MAAM,IAAIlD,MAAM,qDAGlB,GAAE8L,EAAOzE,QAAQM,YAAUmE,EAASzE,QAAQpE,eAC1C,MAAM,IAAIjD,MAAM,8DAIhB8L,EAAOzE,QAAQ1C,sBACV0C,QAAQ3C,cAAaoH,EAAQzE,QAAQ1C,yBACpCmH,EAAMzE,QAAQ1C,mBAIa,MAAjCmH,EAAOzE,QAAQlC,iBAAsB2G,EAChCzE,QAAQjC,WAAa,SAACyB,GACzB,OAAMiF,EAADzE,QAAQlC,eAAerG,KAAIG,EAAA6M,GAAOjF,EAAKkE,KAAMlE,KAGnB,iBAAzBiF,EAAMzE,QAAQxE,SAAmBiJ,EACpCzE,QAAQxE,OAAMiJ,EAAQzE,QAAQxE,OAAO4M,gBAGvC7H,EAAQkE,EAAQ4D,wBAA0B9H,EAAS6E,YAEtD7E,EAAS6E,WAAWC,YAAY9E,IAIK,IAArCkE,EAAOzE,QAAQtC,oBACb+G,EAAOzE,QAAQtC,kBAAiB+G,EAC3B/G,kBAAoBoD,EAASwH,WAAU7D,EACrCzE,QAAQtC,kBACb,uBAGGA,kBAAiB+G,EAAQlK,SAIhCkK,EAAOzE,QAAQ7C,aACgB,IAA7BsH,EAAOzE,QAAQ7C,UAAkBsH,EAC5B+C,kBAAoB,GAAMjN,WAE1BiN,kBAAoB1G,EAASyH,YAAW9D,EACtCzE,QAAQ7C,UACb,gBAKDgC,kBApKY2B,EAAQuG,KAARvG,EAAQ,EAwK3BrK,IAAA,yBAAA,WACE,OAAO2C,KAAKgG,MAAMoJ,QAAO,SAAChJ,GAAS,OAAAA,EAAKiJ,YAAUC,KAAI,SAAClJ,GAAS,OAAAA,QAKlE/I,IAAA,yBAAA,WACE,OAAO2C,KAAKgG,MAAMoJ,QAAO,SAAChJ,GAAS,OAACA,EAAKiJ,YAAUC,KAAI,SAAClJ,GAAS,OAAAA,QAGnE/I,IAAA,2BAAA,SAAmB6N,GACjB,OAAOlL,KAAKgG,MACToJ,QAAO,SAAChJ,GAAS,OAAAA,EAAK8E,SAAWA,KACjCoE,KAAI,SAAClJ,GAAS,OAAAA,QAInB/I,IAAA,uBAAA,WACE,OAAO2C,KAAKuP,mBAAmB7H,EAAS8H,WAG1CnS,IAAA,0BAAA,WACE,OAAO2C,KAAKuP,mBAAmB7H,EAASyD,cAG1C9N,IAAA,sBAAA,WACE,OAAO2C,KAAKuP,mBAAmB7H,EAAS+H,UAI1CpS,IAAA,uBAAA,WACE,OAAO2C,KAAKgG,MACToJ,QACC,SAAChJ,GACC,OAAAA,EAAK8E,SAAWxD,EAASyD,WAAa/E,EAAK8E,SAAWxD,EAAS8H,UAElEF,KAAI,SAAClJ,GAAS,OAAAA,QAKnB/I,IAAA,aAAA,4FAiBE,GAf6B,SAAzB2C,KAAKmB,QAAQuO,SACf1P,KAAKmB,QAAQwO,aAAa,UAAW,uBAIrC3P,KAAKmB,QAAQoI,UAAUqG,SAAS,cAC/B5P,KAAKmB,QAAQmN,cAAc,gBAE5BtO,KAAKmB,QAAQyG,YACXF,EAASC,cACN,8EAA6GL,OAAhCtH,KAAK4G,QAAQ/B,mBAAmB,qBAKhH7E,KAAKoO,kBAAkBtR,OAAQ,YAC7B+S,EAAuB,mBACvBxE,EAAOyE,iBAAezE,EACjByE,gBAAgB9D,WAAWC,YAAWZ,EAAMyE,mBAE9CA,gBAAkBhO,SAAS6F,cAAc,WACzCmI,gBAAgBH,aAAa,OAAQ,SACZ,OAA5BtE,EAAOzE,QAAQhD,UAAiByH,EAASzE,QAAQhD,SAAW,IAACyH,EACxDyE,gBAAgBH,aAAa,WAAY,cAE3CG,gBAAgBzI,UAAY,kBAEE,OAAjCgE,EAAOzE,QAAQ3C,eAAsBoH,EAChCyE,gBAAgBH,aACnB,SAAQtE,EACHzE,QAAQ3C,eAGY,OAA3BoH,EAAOzE,QAAQnC,SAAgB4G,EAC1ByE,gBAAgBH,aAAa,UAAStE,EAAOzE,QAAQnC,WAIvDqL,gBAAgBH,aAAa,WAAY,QAIzCG,gBAAgB5C,MAAM6C,WAAa,WACnCD,gBAAgB5C,MAAM8C,SAAW,aACjCF,gBAAgB5C,MAAM+C,IAAM,MAC5BH,gBAAgB5C,MAAMiB,KAAO,MAC7B2B,gBAAgB5C,MAAM/E,OAAS,MAC/B2H,gBAAgB5C,MAAMhF,MAAQ,IACnCR,EAASwH,WAAU7D,EACZzE,QAAQpC,qBACb,wBACAoD,YAAWyD,EAAMyE,mBACdA,gBAAgBhE,iBAAiB,UAAU,WAC9C,IAAM9F,EAA8BkK,EAAfJ,gBAAf9J,MAEC1F,GAAQ,EAARC,GAAQ,EAARC,OAAQC,EADf,GAAIuF,EAAMlJ,OAAM,IACd,IAAG,IAAE4D,EAAAC,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAAR,IAAI8F,EAAJ1F,EAAQtC,QACN+R,QAAQ/J,aADV7F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,KAIFiN,KAAK,aAAczH,GACxB6J,QAGJA,IAGF7P,KAAKoQ,IAAqB,OAAfzO,OAAOyO,IAAezO,OAAOyO,IAAMzO,OAAO0O,cAKhD/P,GAAa,EAAbC,GAAa,EAAbC,OAAaC,MAAlB,IAAG,IAAEC,EAAAC,EAAiBX,KAAKsQ,OAAM/R,OAAAqC,cAA5BN,GAAAI,EAAAC,EAAaE,QAAAC,MAAbR,GAAa,EAAA,CAAb,IAAIgB,EAAJZ,EAAatC,MAChB4B,KAAKuQ,GAAGjP,EAAWtB,KAAK4G,QAAQtF,cAD7Bf,GAAa,EAAbC,EAAaS,cAAbX,GAAa,MAAbK,EAAaO,QAAbP,EAAaO,oBAAbX,QAAAC,GAILR,KAAKuQ,GAAG,kBAAkB,WAAM,OAAMC,EAADC,+BAErCzQ,KAAKuQ,GAAG,eAAe,WAAM,OAAMjF,EAADmF,+BAElCzQ,KAAKuQ,GAAG,YAAY,SAACnK,GAAS,OAAMoF,EAADiC,KAAK,WAAYrH,MAGpDpG,KAAKuQ,GAAG,YAAY,SAACnK,WACnB,GACkC,IADhCsK,EACKC,gBAAgB7T,QACe,IADH4T,EAC5BE,oBAAoB9T,QACQ,IADI4T,EAChCG,iBAAiB/T,OAGtB,OAAO0P,YAAW,WAAM,OAAM0D,EAADzC,KAAK,mBAAkB,MAIxD,IAYIqD,EAAgB,SAAUxH,GAI5B,GAhBoB,SAAUA,GAC9B,GAAIA,EAAEyH,aAAaC,MAIjB,IAAK,IAAInU,EAAI,EAAGA,EAAIyM,EAAEyH,aAAaC,MAAMlU,OAAQD,IAC/C,GAAgC,UAA5ByM,EAAEyH,aAAaC,MAAMnU,GAAgB,OAAO,EAGpD,OAAO,EAOFoU,CAAc3H,GAEnB,OADAA,EAAE2B,kBACE3B,EAAE0B,eACG1B,EAAE0B,iBAED1B,EAAE4H,aAAc,GAyE5B,OApEAlR,KAAKqO,UAAY,CACf,CACElN,QAASnB,KAAKmB,QACdmP,OAAQ,CACN7G,UAAW,SAACH,GACV,OAAM6H,EAAM1D,KAAK,YAAanE,IAEhCK,UAAW,SAACL,GAEV,OADAwH,EAAcxH,GACR8H,EAAM3D,KAAK,YAAanE,IAEhCO,SAAU,SAACP,GAIT,IAAI+H,EACJ,IACEA,EAAO/H,EAAEyH,aAAaO,cACtB,MAAO7E,IAKT,OAJAnD,EAAEyH,aAAaQ,WACb,SAAWF,GAAQ,aAAeA,EAAO,OAAS,OAEpDP,EAAcxH,GACRkI,EAAM/D,KAAK,WAAYnE,IAE/BQ,UAAW,SAACR,GACV,OAAMmI,EAAMhE,KAAK,YAAanE,IAEhCD,KAAM,SAACC,GAEL,OADAwH,EAAcxH,GACRoI,EAAMrI,KAAKC,IAEnBI,QAAS,SAACJ,GACR,OAAMqI,EAAMlE,KAAK,UAAWnE,OAWpCtJ,KAAKoO,kBAAkBwD,SAAQ,SAACC,WAC9B,OAAMC,EAAMzD,UAAUnO,KAAK,CACzBiB,QAAS0Q,EACTvB,OAAQ,CACNyB,MAAO,SAAClQ,GAYN,OATEgQ,IAAgB3B,EAAU/O,SAC1BU,EAAIlF,SAAMuT,EAAU/O,SACpBuG,EAASsK,cACPnQ,EAAIlF,OAAMuT,EACL/O,QAAQmN,cAAc,kBAAa4B,EAGrCJ,gBAAgBiC,SAEhB,SAMf/R,KAAKiS,SAEEjS,KAAK4G,QAAQb,KAAK1H,KAAK2B,SAIhC3C,IAAA,gBAAA,WAUE,OATA2C,KAAKkS,UACLlS,KAAKmS,gBAAe,IAEM,MAAxBnS,KAAK8P,gBAA0B9P,KAAK8P,gBAAgB9D,gBAAavL,KAEjET,KAAK8P,gBAAgB9D,WAAWC,YAAYjM,KAAK8P,iBACjD9P,KAAK8P,gBAAkB,aAElB9P,KAAKmB,QAAQqN,SACb9G,EAAS+G,UAAUxM,OAAOyF,EAAS+G,UAAU2D,QAAQpS,MAAO,MAGrE3C,IAAA,kCAAA,WACE,IAAIgV,EACAC,EAAiB,EACjBC,EAAa,EAIjB,GAFkBvS,KAAKwS,iBAEP1V,OAAQ,KACjBwD,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYX,KAAKwS,iBAAcjU,OAAAqC,cAA/BN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAA2B,CAAnC,IAAI8F,EAAJ1F,EAAQtC,MACXkU,GAAkBlM,EAAKC,OAAO2G,UAC9BuF,GAAcnM,EAAKC,OAAOoM,gBAFvBlS,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAIL6R,EAAuB,IAAMC,EAAkBC,OAE/CF,EAAsB,IAGxB,OAAOrS,KAAKyN,KACV,sBACA4E,EACAE,EACAD,MAMJjV,IAAA,sBAAA,SAAcqV,GACZ,MAAsC,mBAA3B1S,KAAK4G,QAAQ5D,UACfhD,KAAK4G,QAAQ5D,UAAU0P,GAEtB,GACNpL,OADQtH,KAAK4G,QAAQ5D,WAEtBsE,OADCtH,KAAK4G,QAAQpE,eAAkB,IAAK8E,OAAFoL,EAAE,KAAK,OAO/CrV,IAAA,oBAAA,SAAY+I,GACV,MAAuC,mBAA5BpG,KAAK4G,QAAQjC,WACfyB,EAAKkE,KAEPtK,KAAK4G,QAAQjC,WAAWyB,MAOjC/I,IAAA,wBAAA,WACE,IAAIsV,EAAkBC,EACtB,GAAKD,EAAmB3S,KAAKiP,sBAC3B,OAAO0D,EAGT,IAAIE,EAAe,4BACf7S,KAAK4G,QAAQ7B,mBACf8N,GAAiB,MAAmCvL,OAA9BtH,KAAK4G,QAAQ7B,iBAAiB,SAEtD8N,GAAiB,4BACfvL,OAD0CtH,KAAK8S,cAAc,GAAG,MAEjExL,OADCtH,KAAK4G,QAAQpE,eAAiB,2BAAwB/B,EACvD,kDAED,IAAIsS,EAASrL,EAASC,cAAckL,GAWpC,MAV6B,SAAzB7S,KAAKmB,QAAQuO,SACfkD,EAAOlL,EAASC,cACb,iBAA2EL,OAA3DtH,KAAK4G,QAAQzE,IAAI,4CAA8DmF,OAApBtH,KAAK4G,QAAQxE,OAAO,eAE7FwF,YAAYmL,IAGjB/S,KAAKmB,QAAQwO,aAAa,UAAW,uBACrC3P,KAAKmB,QAAQwO,aAAa,SAAU3P,KAAK4G,QAAQxE,SAEpC,MAARwQ,EAAeA,EAAOG,KAM/B1V,IAAA,4BAAA,WACE,IAAI2V,EAAc,SAAUC,OACrB3S,GAAM,EAANC,GAAM,EAANC,OAAMC,MAAX,IAAG,IAAEC,EAAAC,EAAUsS,EAAQ1U,OAAAqC,cAAlBN,GAAAI,EAAAC,EAAME,QAAAC,MAANR,GAAM,EAAc,CAApB,IAAI4N,EAAJxN,EAAMtC,MACT,GAAE,qBAAuBqJ,KAAKyG,EAAG7G,WAC/B,OAAO6G,YAFN3N,GAAM,EAANC,EAAMS,cAANX,GAAM,MAANK,EAAMO,QAANP,EAAMO,oBAANX,QAAAC,KAOFF,GAAW,EAAXC,GAAW,EAAXC,OAAWC,MAAhB,IAAG,IAAEC,EAAAC,EAAe,CAAC,MAAO,QAAOpC,OAAAqC,cAA9BN,GAAAI,EAAAC,EAAWE,QAAAC,MAAXR,GAAW,EAAqB,CAAhC,IACC6G,EADGuI,EAAJhP,EAAWtC,MAEd,GACG+I,EAAW6L,EAAYhT,KAAKmB,QAAQoG,qBAAqBmI,IAE1D,OAAOvI,YALN5G,GAAW,EAAXC,EAAWS,cAAXX,GAAW,MAAXK,EAAWO,QAAXP,EAAWO,oBAAXX,QAAAC,OAWPnD,IAAA,4BAAA,WACE,OAAO2C,KAAKqO,UAAUiB,KAAI,SAAC4D,GACzB,OAAM,WACJ,IAAI5T,EAAS,GACb,IAAK,IAAIQ,KAASoT,EAAiB5C,OAAQ,CACzC,IAAI6C,EAAWD,EAAiB5C,OAAOxQ,GACvCR,EAAOY,KACLgT,EAAiB/R,QAAQ2K,iBAAiBhM,EAAOqT,GAAU,IAG/D,OAAO7T,EARH,SAcVjC,IAAA,6BAAA,WACE,OAAO2C,KAAKqO,UAAUiB,KAAI,SAAC4D,GACzB,OAAM,WACJ,IAAI5T,EAAS,GACb,IAAK,IAAIQ,KAASoT,EAAiB5C,OAAQ,CACzC,IAAI6C,EAAWD,EAAiB5C,OAAOxQ,GACvCR,EAAOY,KACLgT,EAAiB/R,QAAQiS,oBAAoBtT,EAAOqT,GAAU,IAGlE,OAAO7T,EARH,SAcVjC,IAAA,gBAAA,sBAOE,OANA2C,KAAKoO,kBAAkBwD,SAAQ,SAACzQ,GAC9B,OAAAA,EAAQoI,UAAUC,OAAO,mBAE3BxJ,KAAKqT,uBACLrT,KAAKsT,UAAW,EAETtT,KAAKgG,MAAMsJ,KAAI,SAAClJ,GAAS,OAAMiF,EAADkI,aAAanN,SAGpD/I,IAAA,eAAA,WAKE,cAJO2C,KAAKsT,SACZtT,KAAKoO,kBAAkBwD,SAAQ,SAACzQ,GAC9B,OAAAA,EAAQoI,UAAUK,IAAI,mBAEjB5J,KAAKwT,yBAIdnW,IAAA,iBAAA,SAASqJ,GACP,IAAI+M,EAAe,EACfC,EAAe,IAEnB,GAAIhN,EAAO,EAAG,CAGZ,IAFA,IAAIiN,EAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAE5B9W,EAAI,EAAGA,EAAI8W,EAAM7W,OAAQD,IAAK,CACrC,IAAI+W,EAAOD,EAAM9W,GAGjB,GAAI6J,GAFSiC,KAAKkL,IAAI7T,KAAK4G,QAAQjD,aAAc,EAAI9G,GAAK,GAEtC,CAClB4W,EAAe/M,EAAOiC,KAAKkL,IAAI7T,KAAK4G,QAAQjD,aAAc,EAAI9G,GAC9D6W,EAAeE,EACf,OAIJH,EAAe9K,KAAKmL,MAAM,GAAKL,GAAgB,GAGjD,MAAQ,WAAmCnM,OAAzBmM,EAAa,cAAyDnM,OAA7CtH,KAAK4G,QAAQnB,kBAAkBiO,OAI5ErW,IAAA,oCAAA,WACE,OAC2B,MAAzB2C,KAAK4G,QAAQhD,UACb5D,KAAK+T,mBAAmBjX,QAAUkD,KAAK4G,QAAQhD,UAE3C5D,KAAK+T,mBAAmBjX,SAAWkD,KAAK4G,QAAQhD,UAClD5D,KAAKyN,KAAK,kBAAmBzN,KAAKgG,OAE7BhG,KAAKmB,QAAQoI,UAAUK,IAAI,yBAE3B5J,KAAKmB,QAAQoI,UAAUC,OAAO,2BAIzCnM,IAAA,aAAA,SAAKiM,GACH,GAAKA,EAAEyH,aAAP,CAGA/Q,KAAKyN,KAAK,OAAQnE,GAKlB,IADA,IAAItD,EAAQ,GACHnJ,EAAI,EAAGA,EAAIyM,EAAEyH,aAAa/K,MAAMlJ,OAAQD,IAC/CmJ,EAAMnJ,GAAKyM,EAAEyH,aAAa/K,MAAMnJ,GAIlC,GAAImJ,EAAMlJ,OAAQ,CAChB,IAAMkX,EAAU1K,EAAEyH,aAAZiD,MACFA,GAASA,EAAMlX,QAAuC,MAA7BkX,EAAM,GAAGC,iBAEpCjU,KAAKkU,mBAAmBF,GAExBhU,KAAKmU,YAAYnO,GAIrBhG,KAAKyN,KAAK,aAAczH,OAG1B3I,IAAA,cAAA,SAAMiM,GACJ,GACwE,OA8nDzDlL,EA9nDE,MAALkL,EAAYA,EAAE8K,mBAAgB3T,EA8nDpB4T,EA9nD+B,SAACC,GAAM,OAAAA,EAAEN,OA+nDzD,MAAO5V,EACViW,EAAUjW,QACVqC,GAloDF,KA+nDerC,EAAOiW,EAznDtBrU,KAAKyN,KAAK,QAASnE,GACnB,IAAM0K,EAAU1K,EAAE8K,cAAZJ,MAEN,OAAIA,EAAMlX,OACDkD,KAAKkU,mBAAmBF,QADjC,MAKF3W,IAAA,oBAAA,SAAY2I,OACL1F,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAAR,IAAI8F,EAAJ1F,EAAQtC,MACX4B,KAAKmQ,QAAQ/J,aADV7F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,OAOPnD,IAAA,2BAAA,SAAmB2W,cACjB,OAAM,WACJ,IAAI1U,EAAS,GACRgB,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYqT,EAAKzV,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAW,CAAnB,IACCiU,EADGC,EAAJ9T,EAAQtC,MAGgB,MAAzBoW,EAAKP,mBACJM,EAAQC,EAAKP,oBAEVM,EAAME,OACRnV,EAAOY,KAAImL,EAAM8E,QAAQqE,EAAKE,cACrBH,EAAMI,YAEfrV,EAAOY,KAAImL,EAAMuJ,uBAAuBL,EAAOA,EAAMjK,OAErDhL,EAAOY,UAAKO,GAEa,MAAlB+T,EAAKE,YACG,MAAbF,EAAKK,MAA8B,SAAdL,EAAKK,MAC5BvV,EAAOY,KAAImL,EAAM8E,QAAQqE,EAAKE,cAKhCpV,EAAOY,UAAKO,aArBXF,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAwBL,OAAOlB,EA1BH,MA+BRjC,IAAA,+BAAA,SAAuByX,EAAWC,cAC5BC,EAAYF,EAAUG,eAEtBC,EAAe,SAACzI,GAClB,OA0kDmBnO,EA1kDH6W,QA0kDQC,EA1kDC,MA0kDWf,EA1kDJ,SAAC3W,GAAM,OAAAA,EAAE2X,IAAI5I,IA4kD/C,MAAOnO,GAEoB,mBAApBA,EAAI8W,GAEJf,EAAU/V,EAAK8W,QAEtB,MARqB9W,EAAK8W,EAAYf,GAxkDlCiB,EAAc,mBAChB,OAAON,EAAUM,aAAY,SAACC,GAC5B,GAAIA,EAAQzY,OAAS,EAAG,KACjBwD,GAAS,EAATC,GAAS,EAATC,OAASC,MAAd,IAAG,IAAEC,EAAAC,EAAa4U,EAAOhX,OAAAqC,cAApBN,GAAAI,EAAAC,EAASE,QAAAC,MAATR,GAAS,EAAa,CAAtB,IAAIiU,EAAJ7T,EAAStC,UACRmW,EAAME,OACRF,EAAMnO,MAAK,SAACA,GACV,IAAEoF,EACK5E,QAAQ5C,mBACiB,MAA9BoC,EAAKkE,KAAKkL,UAAU,EAAG,GAKzB,OADApP,EAAKqP,SAAY,GAAUnO,OAARyN,EAAK,KAAazN,OAAVlB,EAAKkE,MAC1BkB,EAAM2E,QAAQ/J,MAEbmO,EAAMI,aAAWrJ,EACrBsJ,uBAAuBL,EAAQ,GAAUjN,OAARyN,EAAK,KAAczN,OAAXiN,EAAMjK,iBAbnD/J,GAAS,EAATC,EAASS,cAATX,GAAS,MAATK,EAASO,QAATP,EAASO,oBAATX,QAAAC,GAoBL8U,IAEF,OAAO,OACNJ,IAGL,OAAOI,OASTjY,IAAA,eAAA,SAAO+I,EAAMtF,GAETd,KAAK4G,QAAQ7D,aACbqD,EAAKM,KAAO,QAAA1G,KAAK4G,QAAQ7D,YAEzBjC,EACEd,KAAK4G,QAAQ5B,eACV4J,QAAQ,eAAgBjG,KAAKmL,MAAM1N,EAAKM,KAAO,KAAO,OAAS,KAC/DkI,QAAQ,kBAAmB5O,KAAK4G,QAAQ7D,cAEnC2E,EAASgO,YAAYtP,EAAMpG,KAAK4G,QAAQ3C,eAGzB,MAAzBjE,KAAK4G,QAAQhD,UACb5D,KAAK+T,mBAAmBjX,QAAUkD,KAAK4G,QAAQhD,UAE/C9C,EACEd,KAAK4G,QAAQpB,qBAAqBoJ,QAChC,eACA5O,KAAK4G,QAAQhD,WAGjB5D,KAAKyN,KAAK,mBAAoBrH,IAE9BpG,KAAK4G,QAAQI,OAAO3I,KAAK2B,KAAMoG,EAAMtF,GAbrCA,EAAKd,KAAK4G,QAAQ3B,wBAiBtB5H,IAAA,gBAAA,SAAQ+I,cACNA,EAAKC,OAAS,CACZC,KAAMoB,EAASiO,SACf5I,SAAU,EAGV0F,MAAOrM,EAAKM,KACZsG,UAAW,EACX4I,SAAU5V,KAAK6V,YAAYzP,IAK7BpG,KAAKgG,MAAM9F,KAAKkG,GAEhBA,EAAK8E,OAASxD,EAAS+H,MAEvBzP,KAAKyN,KAAK,YAAarH,GAEvBpG,KAAK8V,kBAAkB1P,GAEvBpG,KAAKgH,OAAOZ,GAAM,SAACqG,GACbA,GACFrG,EAAKiJ,UAAW,IACX0G,iBAAiB,CAAC3P,GAAOqG,KAE9BrG,EAAKiJ,UAAW,EACdhE,EAAOzE,QAAQxC,WAASiH,EACnB2K,YAAY5P,MAGhB8F,oCAKT7O,IAAA,qBAAA,SAAa2I,OACN1F,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAAR,IAAI8F,EAAJ1F,EAAQtC,MACX4B,KAAKgW,YAAY5P,aADd7F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAGL,OAAO,QAGTnD,IAAA,oBAAA,SAAY+I,GACV,GAAIA,EAAK8E,SAAWxD,EAAS+H,QAA2B,IAAlBrJ,EAAKiJ,SAMzC,MAAM,IAAI9P,MACR,+FALF,GADA6G,EAAK8E,OAASxD,EAAS8H,OACnBxP,KAAK4G,QAAQzC,iBACf,OAAOqI,YAAW,WAAM,OAAMnB,EAAD4K,iBAAgB,MASnD5Y,IAAA,0BAAA,SAAkB+I,GAChB,GACEpG,KAAK4G,QAAQ3D,uBACbmD,EAAK4C,KAAKC,MAAK,YACf7C,EAAKM,MAAQ,QAAA1G,KAAK4G,QAAQ1D,qBAC1B,YAEA,OADAlD,KAAKkW,gBAAgBhW,KAAKkG,GACnBoG,YAAW,WAAM,OAAMnB,EAAD8K,2BAA0B,OAI3D9Y,IAAA,+BAAA,sBACE,IAAI2C,KAAKoW,sBAAwD,IAAhCpW,KAAKkW,gBAAgBpZ,OAAtD,CAIAkD,KAAKoW,sBAAuB,EAC5B,IAAIhQ,EAAOpG,KAAKkW,gBAAgB7W,QAChC,OAAOW,KAAKqW,gBACVjQ,EACApG,KAAK4G,QAAQzD,eACbnD,KAAK4G,QAAQxD,gBACbpD,KAAK4G,QAAQvD,iBACb,GACA,SAAC+I,GAGC,SAFKqB,KAAK,YAAarH,EAAMgG,KACxBgK,sBAAuB,EACtB/K,EAAM8K,gCAMlB9Y,IAAA,mBAAA,SAAW+I,GAOT,GANIA,EAAK8E,SAAWxD,EAASyD,WAC3BnL,KAAKuT,aAAanN,GAEpBpG,KAAKgG,MAAQsQ,EAAQtW,KAAKgG,MAAOI,GAEjCpG,KAAKyN,KAAK,cAAerH,GACC,IAAtBpG,KAAKgG,MAAMlJ,OACb,OAAOkD,KAAKyN,KAAK,YAKrBpQ,IAAA,uBAAA,SAAekZ,GAEY,MAArBA,IACFA,GAAoB,OAEjBjW,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYX,KAAKgG,MAAM9G,QAAKX,OAAAqC,cAA5BN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAAR,IAAI8F,EAAJ1F,EAAQtC,OACPgI,EAAK8E,SAAWxD,EAASyD,WAAaoL,IACxCvW,KAAKuL,WAAWnF,aAFf7F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAKL,OAAO,QAMTnD,IAAA,oBAAA,SAAY+I,EAAM8B,EAAOC,EAAQzE,EAAc3C,cAC7C,OAAOf,KAAKqW,gBACVjQ,EACA8B,EACAC,EACAzE,GACA,GACA,SAAC0I,EAASoK,GACR,GAAc,MAAVA,EAEF,OAAOzV,EAASqF,GAEhB,IAAM5C,EAA+B6H,EAAPzE,QAAxBpD,eACgB,MAAlBA,IACFA,EAAiB4C,EAAK4C,MAExB,IAAIyN,EAAiBD,EAAOE,UAC1BlT,EAAc6H,EACTzE,QAAQnD,eASf,MANqB,eAAnBD,GACmB,cAAnBA,IAGAiT,EAAiBE,EAAYC,QAAQxQ,EAAKyQ,QAASJ,IAE9C1V,EAAS2G,EAASoP,cAAcL,UAM/CpZ,IAAA,wBAAA,SAAgB+I,EAAM8B,EAAOC,EAAQzE,EAAcqT,EAAgBhW,cAC7DiW,EAAa,IAAIC,WAErBD,EAAWE,OAAS,WAClB9Q,EAAKyQ,QAAUG,EAAW1X,OAGR,kBAAd8G,EAAK4C,OAOJmO,uBACH/Q,EACA8B,EACAC,EACAzE,EACAqT,EACAhW,GAZgB,MAAZA,GACFA,EAASiW,EAAW1X,SAe1B0X,EAAWI,cAAchR,MAS3B/I,IAAA,4BAAA,SACEga,EACAC,EACAvW,EACAwW,EACAC,OAAAC,OAAsB,IAAtBD,GAAAA,EAKA,GAHAxX,KAAKyN,KAAK,YAAa4J,GACvBrX,KAAKyN,KAAK,WAAY4J,GAEjBI,EAGE,YAKLJ,EAASR,QAAUS,EAEnBtX,KAAKmX,uBACHE,EACArX,KAAK4G,QAAQzD,eACbnD,KAAK4G,QAAQxD,gBACbpD,KAAK4G,QAAQvD,gBACbrD,KAAK4G,QAAQmQ,gBAXF,SAAC5K,KACPsB,KAAK,YAAa4J,EAAUlL,GAC7BpL,GAAUA,MAWdwW,QAhBFvX,KAAKyN,KAAK,YAAa4J,EAAUC,GAC7BvW,GAAUA,OAoBlB1D,IAAA,+BAAA,SACE+I,EACA8B,EACAC,EACAzE,EACAqT,EACAhW,EACAwW,cAIIG,EAAM5V,SAAS6F,cAAc,OA6GjC,OA3GI4P,IACFG,EAAIH,YAAcA,GAIpBR,EACyD,cAAvDY,iBAAiB7V,SAAS8V,MAAwB,kBAE9Cb,EAENW,EAAIR,OAAS,mBACPW,EAAW,SAAC9W,GAAa,OAAAA,EAAS,IAQtC,MAPoB,oBAAT+W,MAAiC,OAATA,MAAiBf,IAClDc,EAAW,SAAC9W,GACV,OAAA+W,KAAKC,QAAQL,GAAK,WAChB,OAAO3W,EAAS+W,KAAKE,OAAOhY,KAAM,qBAIjC6X,GAAS,SAACI,GACf7R,EAAK8B,MAAQwP,EAAIxP,MACjB9B,EAAK+B,OAASuP,EAAIvP,OAElB,IAAI+P,EAAU5M,EAAQ1E,QAAQqB,OAAO5J,KAAIiN,EAEvClF,EACA8B,EACAC,EACAzE,GAGE8S,EAAS1U,SAAS6F,cAAc,UAChCwQ,EAAM3B,EAAO4B,WAAW,MAU5B,OARA5B,EAAOtO,MAAQgQ,EAAWrP,SAC1B2N,EAAOrO,OAAS+P,EAAWpP,UAEvBmP,EAAc,IAChBzB,EAAOtO,MAAQgQ,EAAWpP,UAC1B0N,EAAOrO,OAAS+P,EAAWrP,UAGrBoP,GACN,KAAK,EAEHE,EAAIE,UAAU7B,EAAOtO,MAAO,GAC5BiQ,EAAIG,OAAM,EAAI,GACd,MACF,KAAK,EAEFH,EAAGE,UAAU7B,EAAOtO,MAAOsO,EAAOrO,QACnCgQ,EAAII,OAAO5P,KAAK6P,IAChB,MACF,KAAK,EAEHL,EAAIE,UAAU,EAAG7B,EAAOrO,QACxBgQ,EAAIG,MAAM,GAAG,GACb,MACF,KAAK,EAEHH,EAAII,OAAO,GAAM5P,KAAK6P,IACtBL,EAAIG,MAAM,GAAG,GACb,MACF,KAAK,EAEFH,EAAGI,OAAO,GAAM5P,KAAK6P,IACtBL,EAAIE,UAAU,GAAI7B,EAAOtO,OACzB,MACF,KAAK,EAEHiQ,EAAII,OAAO,GAAM5P,KAAK6P,IACtBL,EAAIE,UAAU7B,EAAOrO,QAASqO,EAAOtO,OACrCiQ,EAAIG,OAAM,EAAI,GACd,MACF,KAAK,EAEHH,EAAII,QAAO,GAAO5P,KAAK6P,IACvBL,EAAIE,WAAW7B,EAAOrO,OAAQ,GAKlCsQ,EACEN,EACAT,EACmB,MAAnBQ,EAAW7P,KAAe6P,EAAW7P,KAAO,EACzB,MAAnB6P,EAAW5P,KAAe4P,EAAW5P,KAAO,EAC5C4P,EAAW3P,SACX2P,EAAW1P,UACQ,MAAnB0P,EAAWQ,KAAeR,EAAWQ,KAAO,EACzB,MAAnBR,EAAWS,KAAeT,EAAWS,KAAO,EAC5CT,EAAWrP,SACXqP,EAAWpP,WAGb,IAAIqD,EAAYqK,EAAOE,UAAU,aAEjC,GAAgB,MAAZ3V,EACF,OAAOA,EAASoL,EAAWqK,OAKjB,MAAZzV,IACF2W,EAAIkB,QAAU7X,GAGR2W,EAAInL,IAAMnG,EAAKyQ,WAIzBxZ,IAAA,qBAAA,WACE,IAAMkF,EAAoBvC,KAAK4G,QAAzBrE,gBACFsW,EAAmB7Y,KAAK4Q,oBAAoB9T,OAC5CD,EAAIgc,EAGR,KAAIA,GAAoBtW,GAAxB,CAIA,IAAIuW,EAAc9Y,KAAK6Q,iBAEvB,GAAMiI,EAAYhc,OAAS,EAA3B,CAIA,GAAIkD,KAAK4G,QAAQpE,eAEf,OAAOxC,KAAK+Y,aACVD,EAAY5Z,MAAM,EAAGqD,EAAkBsW,SAGlChc,EAAI0F,GAAiB,CAC1B,IAAKuW,EAAYhc,OACf,OAEFkD,KAAKgZ,YAAYF,EAAYzZ,SAC7BxC,UAMNQ,IAAA,oBAAA,SAAY+I,GACV,OAAOpG,KAAK+Y,aAAa,CAAC3S,OAI5B/I,IAAA,qBAAA,SAAa2I,OACN1F,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAW,CAAnB,IAAI8F,EAAJ1F,EAAQtC,MACXgI,EAAKwG,YAAa,EAClBxG,EAAK8E,OAASxD,EAASyD,UAEvBnL,KAAKyN,KAAK,aAAcrH,aAJrB7F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAWL,OAJIR,KAAK4G,QAAQpE,gBACfxC,KAAKyN,KAAK,qBAAsBzH,GAG3BhG,KAAKiZ,YAAYjT,MAG1B3I,IAAA,yBAAA,SAAiB4I,GAEf,OAAgBjG,KAAKgG,MAClBoJ,QAAO,SAAChJ,GAAS,OAAAA,EAAKH,MAAQA,KAC9BqJ,KAAI,SAAClJ,GAAS,OAAAA,QAOnB/I,IAAA,qBAAA,SAAa+I,GACX,GAAIA,EAAK8E,SAAWxD,EAASyD,UAAW,CACtC,IAAI+N,EAAelZ,KAAKmZ,iBAAiB/S,EAAKH,KACzC3F,GAAe,EAAfC,GAAe,EAAfC,OAAeC,MAApB,IAAG,IAAEC,EAAAC,EAAmBuY,EAAY3a,OAAAqC,cAA/BN,GAAAI,EAAAC,EAAeE,QAAAC,MAAfR,GAAe,EAAA,EAAX8Y,EAAJ1Y,EAAetC,OACN8M,OAASxD,EAAS2R,mBAD3B9Y,GAAe,EAAfC,EAAeS,cAAfX,GAAe,MAAfK,EAAeO,QAAfP,EAAeO,oBAAfX,QAAAC,QAGmB,IAAb4F,EAAKH,KACdG,EAAKH,IAAIqT,YAEN/O,GAAe,EAAfC,GAAe,EAAfC,OAAehK,MAApB,IAAG,IAAEiK,EAAAC,EAAmBuO,EAAY3a,OAAAqC,cAA/B2J,GAAAG,EAAAC,EAAe9J,QAAAC,MAAfyJ,GAAe,EAAA,CAAf,IAAI6O,EAAJ1O,EAAetM,MAClB4B,KAAKyN,KAAK,WAAY2L,aADnB5O,GAAe,EAAfC,EAAexJ,cAAfsJ,GAAe,MAAfI,EAAezJ,QAAfyJ,EAAezJ,oBAAfsJ,QAAAC,GAGDzK,KAAK4G,QAAQpE,gBACfxC,KAAKyN,KAAK,mBAAoByL,QAGhC9S,EAAK8E,SAAWxD,EAAS+H,OACzBrJ,EAAK8E,SAAWxD,EAAS8H,SAEzBpJ,EAAK8E,OAASxD,EAAS2R,SACvBrZ,KAAKyN,KAAK,WAAYrH,GAClBpG,KAAK4G,QAAQpE,gBACfxC,KAAKyN,KAAK,mBAAoB,CAACrH,KAInC,GAAIpG,KAAK4G,QAAQzC,iBACf,OAAOnE,KAAKiW,kBAIhB5Y,IAAA,sBAAA,SAAckc,GAAQ,IAAA,IAAApZ,EAAAhB,UAAArC,OAAGmC,EAAH,IAAOP,MAAPyB,EAAA,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAGnB,EAAHmB,EAAA,GAAAjB,UAAAiB,GACpB,MAAsB,mBAAXmZ,EACFA,EAAOvY,MAAMhB,KAAMf,GAErBsa,KAGTlc,IAAA,mBAAA,SAAW+I,GACT,OAAOpG,KAAKiZ,YAAY,CAAC7S,OAG3B/I,IAAA,oBAAA,SAAY2I,cACVhG,KAAKwZ,gBAAgBxT,GAAO,SAACyT,GAC3B,GAAEpO,EAAOzE,QAAQnE,SAAU,CAGzB,IAAIiX,EAAkBD,EAAiB,GACvCzT,EAAM,GAAGK,OAAOsT,QAAOtO,EAChBzE,QAAQnE,WAAQ4I,EACfzE,QAAQlE,eACZgX,EAAgBhT,KAAI2E,EAAQzE,QAAQjE,WACxCqD,EAAM,GAAGK,OAAOS,gBAAkB6B,KAAKiR,KACrCF,EAAgBhT,KAAI2E,EAAQzE,QAAQjE,WAIxC,GAAIqD,EAAM,GAAGK,OAAOsT,QAAS,aAKvBvT,EAAOJ,EAAM,GACb0T,EAAkBD,EAAiB,GAGvCrT,EAAKC,OAAOwT,OAAS,GAErB,IAAIC,EAAkB,eACpB,IAAIC,EAAa,OAGyBtZ,IAAnC2F,EAAKC,OAAOwT,OAAOE,IACxBA,IAIF,KAAIA,GAAc3T,EAAKC,OAAOS,iBAA9B,CAEAkT,EAEA,IAAIC,EAAQF,EAAUzO,EAAQ1E,QAAQjE,UAClCuX,EAAMvR,KAAKC,IACbqR,EAAK3O,EAAQ1E,QAAQjE,UACrB+W,EAAgBhT,MAGdyT,EAAY,CACd7P,KAAIgB,EAAOwH,cAAc,GACzBsH,KAAMV,EAAgBW,YAClBX,EAAgBW,YAAYJ,EAAOC,GACnCR,EAAgBxa,MAAM+a,EAAOC,GACjCtE,SAAUxP,EAAKC,OAAOuP,SACtBmE,WAAYA,GAGd3T,EAAKC,OAAOwT,OAAOE,GAAc,CAC/B3T,KAAMA,EACNI,MAAOuT,EACPI,UAAWA,EACXjP,OAAQxD,EAASyD,UACjB4B,SAAU,EACVuN,QAAS,KAGNC,YAAYvU,EAAO,CAACmU,MA8B3B,GA3BA/T,EAAKC,OAAOmU,oBAAsB,SAACtU,EAAOuU,WACpCC,GAAc,EAClBxU,EAAMgF,OAASxD,EAASiT,QAGxBzU,EAAMiU,UAAY,KAClBjU,EAAMuU,SAAWvU,EAAMD,IAAI2U,aAC3B1U,EAAM2U,gBAAkB3U,EAAMD,IAAI6U,wBAElC5U,EAAMD,IAAM,KAEZ,IAAK,IAAIpJ,EAAI,EAAGA,EAAIuJ,EAAKC,OAAOS,gBAAiBjK,IAAK,CACpD,QAA8B4D,IAA1B2F,EAAKC,OAAOwT,OAAOhd,GACrB,OAAOid,IAEL1T,EAAKC,OAAOwT,OAAOhd,GAAGqO,SAAWxD,EAASiT,UAC5CD,GAAc,GAIdA,GAAWvJ,EACRvK,QAAQK,eAAeb,GAAM,aAC3B2U,UAAU/U,EAAOyU,EAAU,UAKpCpP,EAAOzE,QAAQhE,qBACf,IAAK,IAAI/F,EAAI,EAAGA,EAAIuJ,EAAKC,OAAOS,gBAAiBjK,IAC/Cid,SAGFA,QAEG,CACL,IAAIkB,EAAa,GACjB,IAASne,EAAI,EAAGA,EAAImJ,EAAMlJ,OAAQD,IAChCme,EAAWne,GAAK,CACdyN,KAAIe,EAAOyH,cAAcjW,GACzBud,KAAMX,EAAiB5c,GACvB+Y,SAAU5P,EAAMnJ,GAAGwJ,OAAOuP,YAGzB2E,YAAYvU,EAAOgV,UAM9B3d,IAAA,kBAAA,SAAU+I,EAAMH,GACd,IAAK,IAAIpJ,EAAI,EAAGA,EAAIuJ,EAAKC,OAAOS,gBAAiBjK,IAC/C,QAC4B4D,IAA1B2F,EAAKC,OAAOwT,OAAOhd,IACnBuJ,EAAKC,OAAOwT,OAAOhd,GAAGoJ,MAAQA,EAE9B,OAAOG,EAAKC,OAAOwT,OAAOhd,MAUhCQ,IAAA,oBAAA,SAAY2I,EAAOgV,mCACb/U,EAAM,IAAIgV,eAGT3a,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEE,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,EAAJ8F,EAAJ1F,EAAQtC,OACN6H,IAAMA,YADR1F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAGDwF,EAAM,GAAGK,OAAOsT,UAGlB3T,EAAM,GAAGK,OAAOwT,OAAOmB,EAAW,GAAGjB,YAAY9T,IAAMA,GAGzD,IAAI7D,EAASpC,KAAKkb,cAAclb,KAAK4G,QAAQxE,OAAQ4D,EAAOgV,GACxD7Y,EAAMnC,KAAKkb,cAAclb,KAAK4G,QAAQzE,IAAK6D,EAAOgV,GACtD/U,EAAIkV,KAAK/Y,EAAQD,GAAK,GAGRnC,KAAKkb,cAAclb,KAAK4G,QAAQtE,QAAS0D,KAC1CC,EAAI3D,QAAUtC,KAAKkb,cAAclb,KAAK4G,QAAQtE,QAAS0D,IAGpEC,EAAI5D,kBAAoBrC,KAAK4G,QAAQvE,gBAErC4D,EAAIiR,OAAS,SAAC5N,KACP8R,mBAAmBpV,EAAOC,EAAKqD,IAGtCrD,EAAIoV,UAAY,aACTC,mBACHtV,EACAC,EACC,0BAAqDqB,OAAQmK,EAA/B7K,QAAQtE,QAAU,IAAK,cAI1D2D,EAAI2S,QAAU,aACP0C,mBAAmBtV,EAAOC,KAID,MAAdA,EAAII,OAAiBJ,EAAII,OAASJ,GACxCsV,WAAa,SAACjS,GACxB,OAAMqI,EAAD6J,2BAA2BxV,EAAOC,EAAKqD,IAE9C,IAAIzF,EAAU7D,KAAK4G,QAAQ9C,eACvB,CACE2X,OAAQ,mBACR,gBAAiB,WACjB,mBAAoB,kBAEtB,GAUJ,IAAK,IAAIC,KARL1b,KAAK4G,QAAQM,aACfrD,EAAQ,gBAAkBmC,EAAM,GAAGgD,MAGjChJ,KAAK4G,QAAQ/C,SACfuF,EAAArK,EAAA,CAAO8E,EAAS7D,KAAK4G,QAAQ/C,SAGRA,EAAS,CAC9B,IAAI8X,EAAc9X,EAAQ6X,GACtBC,GACF1V,EAAI2V,iBAAiBF,EAAYC,GAIrC,GAAI3b,KAAK4G,QAAQM,WAAY,CAGtB5G,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAKE,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAAR,IAAI8F,EAAJ1F,EAAQtC,MACX4B,KAAKyN,KAAK,UAAWrH,EAAMH,aADxB1F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAGDR,KAAK4G,QAAQpE,gBACfxC,KAAKyN,KAAK,kBAAmBzH,EAAOC,GAEtCjG,KAAK6b,cAAc5V,EAAK,KAAMD,OACzB,CACL,IAAI8V,EAAW,IAAIC,SAGnB,GAAI/b,KAAK4G,QAAQpF,OAAQ,CACvB,IAAIwa,EAAmBhc,KAAK4G,QAAQpF,OAUpC,IAAK,IAAInE,IATuB,mBAArB2e,IACTA,EAAmBA,EAAiB3d,KAClC2B,KACAgG,EACAC,EACAD,EAAM,GAAGK,OAAOsT,QAAU3Z,KAAKic,UAAUjW,EAAM,GAAIC,GAAO,OAI9C+V,EAAkB,CAChC,IAAI5d,EAAQ4d,EAAiB3e,GAC7B,GAAIqB,MAAMC,QAAQP,GAIhB,IAAK,IAAIvB,EAAI,EAAGA,EAAIuB,EAAMtB,OAAQD,IAChCif,EAASI,OAAO7e,EAAKe,EAAMvB,SAG7Bif,EAASI,OAAO7e,EAAKe,IAMtBkC,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAV,IAAEC,EAAL,IAAKC,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAA,CAAJ8F,EAAJ1F,EAAQtC,MACX4B,KAAKyN,KAAK,UAAWrH,EAAMH,EAAK6V,aAD7Bvb,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAGDR,KAAK4G,QAAQpE,gBACfxC,KAAKyN,KAAK,kBAAmBzH,EAAOC,EAAK6V,GAG3C9b,KAAKmc,oBAAoBL,GAIzB,IAASjf,EAAI,EAAGA,EAAIme,EAAWle,OAAQD,IAAK,CAC1C,IAAIsd,EAAYa,EAAWne,GAC3Bif,EAASI,OAAO/B,EAAU7P,KAAM6P,EAAUC,KAAMD,EAAUvE,UAG5D5V,KAAK6b,cAAc5V,EAAK6V,EAAU9V,OAKtC3I,IAAA,wBAAA,SAAgB2I,EAAOlF,GAIrB,6BACEuK,EAAKzE,QAAQmC,cAAc1K,KAAKgN,EAAMrF,EAAMnJ,IAAI,SAAC6c,GAC/CD,EAAiB5c,GAAK6c,IAChB0C,IAAgBpW,EAAMlJ,QAC1BgE,EAAK2Y,OAPPA,EAAmB,GAEnB2C,EAAc,EACTvf,EAAI,EAAGA,EAAImJ,EAAMlJ,OAAQD,IAACwf,EAAAxf,MAWrCQ,IAAA,4BAAA,SAAoBye,OAGXxb,GAAS,EAATC,GAAS,EAATC,OAASC,EADhB,GAA6B,SAAzBT,KAAKmB,QAAQuO,QAAkB,IACjC,IAAG,IAAE/O,EAAaX,KAAKmB,QAAQiJ,iBAC7B,mCAAiC7L,OAAAqC,cAD9BN,GAAAI,EAAAC,EAASE,QAAAC,MAATR,GAAS,EAEX,CAFE,IAAIgc,EAAJ5b,EAAStC,MAGRme,EAAYD,EAAMvN,aAAa,QAC/ByN,EAAYF,EAAMvN,aAAa,QAInC,GAHIyN,IAAWA,EAAYA,EAAUC,eAGjC,MAAOF,EAEX,GAAsB,WAAlBD,EAAM5M,SAAwB4M,EAAMI,aAAa,YAAa,CAE3Dpc,GAAU,EAAVC,GAAU,EAAVC,OAAUC,MAAZ,IAAEC,EAAL,IAAKC,EAAc2b,EAAM1V,QAAOrI,OAAAqC,cAA3BN,GAAAI,EAAAC,EAAUE,QAAAC,MAAVR,GAAU,EAAA,CAAV,IAAIiZ,EAAJ7Y,EAAUtC,MACTmb,EAAOoD,UACTb,EAASI,OAAOK,EAAWhD,EAAOnb,iBAFjCmC,GAAU,EAAVC,EAAUS,cAAVX,GAAU,MAAVK,EAAUO,QAAVP,EAAUO,oBAAVX,QAAAC,UAMJgc,GACc,aAAdA,GAA0C,UAAdA,GAC7BF,EAAMM,UAENd,EAASI,OAAOK,EAAWD,EAAMle,iBAtBhCmC,GAAS,EAATC,EAASS,cAATX,GAAS,MAATK,EAASO,QAATP,EAASO,oBAATX,QAAAC,OA8BTnD,IAAA,mCAAA,SAA2B2I,EAAOC,EAAKqD,OAG9BhJ,GAAQ,EAARC,GAAQ,EAARC,OAAQC,EAFf,GAAKuF,EAAM,GAAGK,OAAOsT,QA+Bd,CAKDvT,EAAOJ,EAAM,GAAjB,IAIIE,EAAQlG,KAAKic,UAAU7V,EAAMH,GAE7BqD,GACFpD,EAAM6G,SAAY,IAAMzD,EAAEuT,OAAUvT,EAAEmJ,MACtCvM,EAAMuM,MAAQnJ,EAAEmJ,MAChBvM,EAAM8G,UAAY1D,EAAEuT,SAGpB3W,EAAM6G,SAAW,IACjB7G,EAAM8G,UAAY9G,EAAMuM,OAI1BrM,EAAKC,OAAO0G,SAAW,EACvB3G,EAAKC,OAAOoM,MAAQ,EACpBrM,EAAKC,OAAO2G,UAAY,EACxB,IAAK,IAAInQ,EAAI,EAAGA,EAAIuJ,EAAKC,OAAOS,gBAAiBjK,IAE7CuJ,EAAKC,OAAOwT,OAAOhd,SACuB,IAAnCuJ,EAAKC,OAAOwT,OAAOhd,GAAGkQ,WAE7B3G,EAAKC,OAAO0G,UAAY3G,EAAKC,OAAOwT,OAAOhd,GAAGkQ,SAC9C3G,EAAKC,OAAOoM,OAASrM,EAAKC,OAAOwT,OAAOhd,GAAG4V,MAC3CrM,EAAKC,OAAO2G,WAAa5G,EAAKC,OAAOwT,OAAOhd,GAAGmQ,WAKnD5G,EAAKC,OAAO0G,SAAW3G,EAAKC,OAAO0G,SAAW3G,EAAKC,OAAOS,gBAE1D9G,KAAKyN,KACH,iBACArH,EACAA,EAAKC,OAAO0G,SACZ3G,EAAKC,OAAO2G,gBA1EY,IAE1B,IAAG,IAAEtM,EAAAC,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAW,CAAnB,IAAI8F,GAAAA,EAAJ1F,EAAQtC,OAEJiI,OAAOoM,OACZrM,EAAKC,OAAO2G,WACZ5G,EAAKC,OAAO2G,WAAa5G,EAAKC,OAAOoM,QAQnCnJ,GACFlD,EAAKC,OAAO0G,SAAY,IAAMzD,EAAEuT,OAAUvT,EAAEmJ,MAC5CrM,EAAKC,OAAOoM,MAAQnJ,EAAEmJ,MACtBrM,EAAKC,OAAO2G,UAAY1D,EAAEuT,SAG1BzW,EAAKC,OAAO0G,SAAW,IACvB3G,EAAKC,OAAO2G,UAAY5G,EAAKC,OAAOoM,OAGtCzS,KAAKyN,KACH,iBACArH,EACAA,EAAKC,OAAO0G,SACZ3G,EAAKC,OAAO2G,sBA1BXzM,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,OA6ETnD,IAAA,2BAAA,SAAmB2I,EAAOC,EAAKqD,GAC7B,IAAImR,EAEJ,GAAIzU,EAAM,GAAGkF,SAAWxD,EAAS2R,UAIV,IAAnBpT,EAAI6W,WAAR,CAIA,GAAyB,gBAArB7W,EAAI8W,cAAuD,SAArB9W,EAAI8W,eAC5CtC,EAAWxU,EAAI2U,aAGb3U,EAAI+W,kBAAkB,kBACrB/W,EAAI+W,kBAAkB,gBAAgB5K,QAAQ,qBAE/C,IACEqI,EAAWwC,KAAKC,MAAMzC,GACtB,MAAOhO,GACPnD,EAAImD,EACJgO,EAAW,qCAKjBza,KAAKwb,2BAA2BxV,EAAOC,GAEjC,KAAOA,EAAIiF,QAAUjF,EAAIiF,OAAS,IAGlClF,EAAM,GAAGK,OAAOsT,QAClB3T,EAAM,GAAGK,OAAOmU,oBACdxa,KAAKic,UAAUjW,EAAM,GAAIC,GACzBwU,GAGFza,KAAK+a,UAAU/U,EAAOyU,EAAUnR,GARlCtJ,KAAKsb,mBAAmBtV,EAAOC,EAAKwU,OAaxCpd,IAAA,2BAAA,SAAmB2I,EAAOC,EAAKwU,GAC7B,GAAIzU,EAAM,GAAGkF,SAAWxD,EAAS2R,SAAjC,CAIA,GAAIrT,EAAM,GAAGK,OAAOsT,SAAW3Z,KAAK4G,QAAQ/D,YAAa,CACvD,IAAIqD,EAAQlG,KAAKic,UAAUjW,EAAM,GAAIC,GACrC,GAAIC,EAAMoU,UAAYta,KAAK4G,QAAQ9D,iBAEjC,YADA9C,KAAKua,YAAYvU,EAAO,CAACE,EAAMiU,YAG/BhF,QAAQgI,KAAK,4CAIjBnd,KAAK+V,iBACH/P,EACAyU,GACEza,KAAK4G,QAAQ1B,kBAAkB0J,QAAQ,iBAAkB3I,EAAIiF,QAC/DjF,OAIJ5I,IAAA,sBAAA,SAAc4I,EAAK6V,EAAU9V,GAC3B,GAAsB,GAAlBC,EAAI6W,WAMR,GAAI9c,KAAK4G,QAAQM,WACf,GAAIlB,EAAM,GAAGK,OAAOsT,QAAS,CAC3B,IAAMzT,EAAQlG,KAAKic,UAAUjW,EAAM,GAAIC,GACvCA,EAAImX,KAAKlX,EAAMiU,UAAUC,WAEzBnU,EAAImX,KAAKpX,EAAM,SAGjBC,EAAImX,KAAKtB,QAbT3G,QAAQgI,KACN,oFAkBN9f,IAAA,kBAAA,SAAU2I,EAAO4U,EAActR,OACxBhJ,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAW,CAAnB,IAAI8F,EAAJ1F,EAAQtC,MACXgI,EAAK8E,OAASxD,EAASiT,QACvB3a,KAAKyN,KAAK,UAAWrH,EAAMwU,EAActR,GACzCtJ,KAAKyN,KAAK,WAAYrH,aAHnB7F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAUL,GALIR,KAAK4G,QAAQpE,iBACfxC,KAAKyN,KAAK,kBAAmBzH,EAAO4U,EAActR,GAClDtJ,KAAKyN,KAAK,mBAAoBzH,IAG5BhG,KAAK4G,QAAQzC,iBACf,OAAOnE,KAAKiW,kBAMhB5Y,IAAA,yBAAA,SAAiB2I,EAAO0G,EAASzG,OAC1B3F,GAAQ,EAARC,GAAQ,EAARC,OAAQC,MAAb,IAAG,IAAEC,EAAAC,EAAYqF,EAAKzH,OAAAqC,cAAjBN,GAAAI,EAAAC,EAAQE,QAAAC,MAARR,GAAQ,EAAW,CAAnB,IAAI8F,EAAJ1F,EAAQtC,MACXgI,EAAK8E,OAASxD,EAAS2V,MACvBrd,KAAKyN,KAAK,QAASrH,EAAMsG,EAASzG,GAClCjG,KAAKyN,KAAK,WAAYrH,aAHnB7F,GAAQ,EAARC,EAAQS,cAARX,GAAQ,MAARK,EAAQO,QAARP,EAAQO,oBAARX,QAAAC,GAUL,GALIR,KAAK4G,QAAQpE,iBACfxC,KAAKyN,KAAK,gBAAiBzH,EAAO0G,EAASzG,GAC3CjG,KAAKyN,KAAK,mBAAoBzH,IAG5BhG,KAAK4G,QAAQzC,iBACf,OAAOnE,KAAKiW,oBA/qDT5Y,IAAA,kBAAP,WAEE2C,KAAKxC,UAAUyQ,QAAUpO,EAUzBG,KAAKxC,UAAU8S,OAAS,CACtB,OACA,YACA,UACA,YACA,WACA,YACA,YACA,aACA,cACA,YACA,QACA,gBACA,aACA,qBACA,iBACA,sBACA,UACA,kBACA,UACA,kBACA,WACA,mBACA,WACA,mBACA,QACA,mBACA,kBACA,iBAGFtQ,KAAKxC,UAAU0Y,gBAAkB,GACjClW,KAAKxC,UAAU4Y,sBAAuB,KAuoDjC/Y,IAAA,eAAP,WACE,MAAO,uCAAuCuR,QAAO,SAEnD,SAAU0O,GACR,IAAIC,EAAqB,GAAhB5U,KAAK6U,SAAiB,EAE/B,OADY,MAANF,EAAYC,EAAS,EAAJA,EAAW,GACzB3e,SAAS,WA1rDL8I,EAAN,CAAuB7H,GA+rDtC6H,EAAS+V,YAYT/V,EAASd,QAAU,GAGnBc,EAASiH,kBAAoB,SAAUxN,GAErC,OAAIA,EAAQ4N,aAAa,MAChBrH,EAASd,QAAQ8W,EAASvc,EAAQ4N,aAAa,aAEtD,GAKJrH,EAAS+G,UAAY,GAGrB/G,EAASiW,WAAa,SAAUxc,GAI9B,GAHuB,iBAAZA,IACTA,EAAUW,SAASwM,cAAcnN,IAEqB,OAAxC,MAAXA,EAAkBA,EAAQqN,cAAW/N,GACxC,MAAM,IAAIlB,MACR,kNAGJ,OAAO4B,EAAQqN,UAIjB9G,EAASkW,SAAW,WAClB,IAAIC,EACJ,GAAI/b,SAASsI,iBACXyT,EAAY/b,SAASsI,iBAAiB,iBACjC,CACLyT,EAAY,GAEZ,IAAIC,EAAgB,SAAC7K,GACnB,OAAM,WACJ,IAAI3T,EAAS,GACRgB,GAAM,EAANC,GAAM,EAANC,OAAMC,MAAX,IAAG,IAAEC,EAAAC,EAAUsS,EAAQ1U,OAAAqC,cAAlBN,GAAAI,EAAAC,EAAME,QAAAC,MAANR,GAAM,EAAA,CAAN,IAAI4N,EAAJxN,EAAMtC,MACP,qBAAuBqJ,KAAKyG,EAAG7G,WAC/B/H,EAAOY,KAAK2d,EAAU3d,KAAKgO,IAE3B5O,EAAOY,UAAKO,aAJXF,GAAM,EAANC,EAAMS,cAANX,GAAM,MAANK,EAAMO,QAANP,EAAMO,oBAANX,QAAAC,GAOL,OAAOlB,EATH,IAWRwe,EAAchc,SAASyF,qBAAqB,QAC5CuW,EAAchc,SAASyF,qBAAqB,SAG9C,OAAM,WACJ,IAAIjI,EAAS,GACRgB,GAAY,EAAZC,GAAY,EAAZC,OAAYC,MAAjB,IAAG,IAAEC,EAAAC,EAAgBkd,EAAStf,OAAAqC,cAAzBN,GAAAI,EAAAC,EAAYE,QAAAC,MAAZR,GAAY,EAAA,CAAZ,IAAIkO,EAAJ9N,EAAYtC,OAE8B,IAAzCsJ,EAASiH,kBAAkBH,GAC7BlP,EAAOY,KAAK,IAAIwH,EAAS8G,IAEzBlP,EAAOY,UAAKO,aALXF,GAAY,EAAZC,EAAYS,cAAZX,GAAY,MAAZK,EAAYO,QAAZP,EAAYO,oBAAZX,QAAAC,GAQL,OAAOlB,EAVH,IAwBRoI,EAASqW,gBAAkB,mDAM3BrW,EAASmH,mBAAqB,WAC5B,IAAImP,GAAiB,EAErB,GACErc,OAAOsc,MACPtc,OAAOsV,YACPtV,OAAOuc,UACPvc,OAAOwc,MACPxc,OAAOoa,UACPja,SAASwM,cAET,GAAM,cAAexM,SAAS6F,cAAc,KAErC,MACgClH,IAAjCiH,EAAS0W,sBAGX1W,EAASqW,gBAAkBrW,EAAS0W,yBAGjC9d,GAAS,EAATC,GAAS,EAATC,OAASC,MAAd,IAAG,IAAEC,EAAAC,EAAa+G,EAASqW,gBAAexf,OAAAqC,cAArCN,GAAAI,EAAAC,EAASE,QAAAC,MAATR,GAAS,EAAA,CAATI,EAAStC,MACFqJ,KAAK4W,UAAUC,aACvBN,GAAiB,aAFhBzd,GAAS,EAATC,EAASS,cAATX,GAAS,MAATK,EAASO,QAATP,EAASO,oBAATX,QAAAC,SARLwd,GAAiB,OAgBnBA,GAAiB,EAGnB,OAAOA,GAGTtW,EAASoP,cAAgB,SAAUyH,GAWjC,IARA,IAAIC,EAAaC,KAAKF,EAAQG,MAAM,KAAK,IAGrCC,EAAaJ,EAAQG,MAAM,KAAK,GAAGA,MAAM,KAAK,GAAGA,MAAM,KAAK,GAG5DE,EAAK,IAAIC,YAAYL,EAAW1hB,QAChCgiB,EAAK,IAAIC,WAAWH,GAElB/hB,EAAI,EAAGqd,EAAMsE,EAAW1hB,OAAQkiB,EAAM,GAAK9E,EAC/C8E,EAAMniB,GAAKqd,EAAMrd,GAAKqd,EACtB8E,EAAMniB,IAAMA,IAEZiiB,EAAGjiB,GAAK2hB,EAAWS,WAAWpiB,GAIhC,OAAO,IAAIshB,KAAK,CAACS,GAAK,CAAE5V,KAAM2V,KAIhC,IAAMrI,EAAU,SAAC4I,EAAMC,GACrB,OAAAD,EAAK9P,QAAO,SAACoF,GAAS,OAAAA,IAAS2K,KAAc7P,KAAI,SAACkF,GAAS,OAAAA,MAGvDkJ,EAAW,SAAC0B,GAChB,OAAAA,EAAIxQ,QAAO,cAAe,SAAC3F,GAAU,OAAAA,EAAMoW,OAAO,GAAGrQ,kBAGvDtH,EAASC,cAAgB,SAAU2X,GACjC,IAAIC,EAAMzd,SAAS6F,cAAc,OAEjC,OADA4X,EAAI3U,UAAY0U,EACTC,EAAIC,WAAW,IAIxB9X,EAASsK,cAAgB,SAAU7Q,EAASse,GAC1C,GAAIte,IAAYse,EACd,OAAO,OAEDte,EAAUA,EAAQ6K,YACxB,GAAI7K,IAAYse,EACd,OAAO,EAGX,OAAO,GAGT/X,EAASwH,WAAa,SAAUhB,EAAI5D,GAClC,IAAInJ,EAMJ,GALkB,iBAAP+M,EACT/M,EAAUW,SAASwM,cAAcJ,GACT,MAAfA,EAAGK,WACZpN,EAAU+M,GAEG,MAAX/M,EACF,MAAM,IAAI5B,MACP,YAAiB+H,OAALgD,EAAK,8EAGtB,OAAOnJ,GAGTuG,EAASyH,YAAc,SAAUuQ,EAAKpV,GACpC,IAAI4D,EAAI+E,EACR,GAAIyM,aAAehhB,MAAO,CACxBuU,EAAW,GACX,QACO3S,GAAE,EAAFC,GAAE,EAAFC,OAAEC,MAAP,IAAG,IAAEE,EAAM+e,EAAGnhB,OAAAqC,cAATN,GAAAI,EAAAC,EAAEE,QAAAC,MAAFR,GAAE,EAAF4N,EAAAxN,EAAEtC,MACL6U,EAAS/S,KAAKF,KAAKkP,WAAWhB,EAAI5D,aAD/B/J,GAAE,EAAFC,EAAES,cAAFX,GAAE,MAAFK,EAAEO,QAAFP,EAAEO,oBAAFX,QAAAC,IAGL,MAAO8I,GACP2J,EAAW,WAER,GAAmB,iBAARyM,EAAkB,CAClCzM,EAAW,GACN3S,GAAE,EAAFC,GAAE,EAAFC,OAAEC,MAAJ,IAAEC,EAAL,IAAKC,EAAMmB,SAASsI,iBAAiBsV,GAAGnhB,OAAAqC,cAAnCN,GAAAI,EAAAC,EAAEE,QAAAC,MAAFR,GAAE,EAAF4N,EAAAxN,EAAEtC,MACL6U,EAAS/S,KAAKgO,YADX3N,GAAE,EAAFC,EAAES,cAAFX,GAAE,MAAFK,EAAEO,QAAFP,EAAEO,oBAAFX,QAAAC,SAGoB,MAAhBkf,EAAInR,WACb0E,EAAW,CAACyM,IAGd,GAAgB,MAAZzM,IAAqBA,EAASnW,OAChC,MAAM,IAAIyC,MACP,YAAiB+H,OAALgD,EAAK,+FAItB,OAAO2I,GAOTvL,EAAS0D,QAAU,SAAUuU,EAAUtQ,EAAUuQ,GAC/C,OAAIje,OAAOyJ,QAAQuU,GACVtQ,IACc,MAAZuQ,EACFA,SADF,GAQTlY,EAASgO,YAAc,SAAUtP,EAAMnC,GACrC,IAAKA,EACH,OAAO,EAETA,EAAgBA,EAAcya,MAAM,KAEpC,IAAImB,EAAWzZ,EAAK4C,KAChB8W,EAAeD,EAASjR,QAAO,QAAU,IAExCtO,GAAa,EAAbC,GAAa,EAAbC,OAAaC,MAAlB,IAAG,IAAEC,EAAAC,EAAiBsD,EAAa1F,OAAAqC,cAA9BN,GAAAI,EAAAC,EAAaE,QAAAC,MAAbR,GAAa,EAAmB,CAAhC,IAAIyf,EAAJrf,EAAatC,MAEhB,GAA4B,OAD5B2hB,EAAYA,EAAU5V,QACRkV,OAAO,IACnB,IAMU,IALRjZ,EAAKkE,KACFmS,cACArK,QACC2N,EAAUtD,cACVrW,EAAKkE,KAAKxN,OAASijB,EAAUjjB,QAGjC,OAAO,OAEJ,GAAE,QAAU2K,KAAKsY,IAEtB,GAAID,IAAiBC,EAAUnR,QAAO,QAAU,IAC9C,OAAO,OAGT,GAAIiR,IAAaE,EACf,OAAO,YApBRxf,GAAa,EAAbC,EAAaS,cAAbX,GAAa,MAAbK,EAAaO,QAAbP,EAAaO,oBAAbX,QAAAC,GAyBL,OAAO,GAIa,oBAAXwf,QAAqC,OAAXA,SACnCA,OAAOjgB,GAAGyO,SAAW,SAAU5H,GAC7B,OAAO5G,KAAKigB,MAAK,WACf,OAAO,IAAIvY,EAAS1H,KAAM4G,QAMhCc,EAAS+H,MAAQ,QAEjB/H,EAAS8H,OAAS,SAGlB9H,EAASwY,SAAWxY,EAAS8H,OAE7B9H,EAASyD,UAAY,YACrBzD,EAASyY,WAAazY,EAASyD,UAE/BzD,EAAS2R,SAAW,WACpB3R,EAAS2V,MAAQ,QACjB3V,EAASiT,QAAU,UAanB,IAoCIlC,EAAkB,SAAUN,EAAKT,EAAK0I,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GACpE,IAAIC,EArCqB,SAAUlJ,GAC1BA,EAAImJ,aAAb,IACIC,EAAKpJ,EAAIqJ,cACTvK,EAAS1U,SAAS6F,cAAc,UACpC6O,EAAOtO,MAAQ,EACfsO,EAAOrO,OAAS2Y,EAChB,IAAI3I,EAAM3B,EAAO4B,WAAW,MAC5BD,EAAI6I,UAAUtJ,EAAK,EAAG,OACtB,IAAM0C,EAASjC,EAAI8I,aAAa,EAAG,EAAG,EAAGH,GAAnC1G,KAGFiG,EAAK,EACLa,EAAKJ,EACLK,EAAKL,EACFK,EAAKd,GAGI,IAFFjG,EAAgB,GAAV+G,EAAK,GAAS,GAG9BD,EAAKC,EAELd,EAAKc,EAGPA,EAAMD,EAAKb,GAAO,EAEpB,IAAIe,EAAQD,EAAKL,EAEjB,OAAc,IAAVM,EACK,EAEAA,EAOaC,CAAqB3J,GAC3C,OAAOS,EAAI6I,UAAUtJ,EAAK0I,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAKC,IAMvDjK,EAAN,iCAAMA,WAAAA,YAAAA,EAAW,KAAA,EACRtZ,IAAA,kBAAP,WACE2C,KAAKshB,QACH,uEAGGjkB,IAAA,iBAAP,SAAgBif,OACd,IAAIiF,EAAS,GACTC,OAAO/gB,EACPghB,OAAOhhB,EACPihB,EAAO,GACPC,OAAOlhB,EACPmhB,OAAOnhB,EACPohB,OAAOphB,EACPqhB,EAAO,GACPjlB,EAAI,EAKN8kB,GAHAH,EAAOlF,EAAMzf,OAGE,EACf+kB,GAAgB,EAAPJ,IAAa,GAHtBC,EAAOnF,EAAMzf,OAGuB,EACpCglB,GAAgB,GAAPJ,IAAc,GAHvBC,EAAOpF,EAAMzf,OAGwB,EACrCilB,EAAc,GAAPJ,EACHK,MAAMN,GACRI,EAAOC,EAAO,GACLC,MAAML,KACfI,EAAO,IAETP,EACEA,EACAvhB,KAAKshB,QAAQjC,OAAOsC,GACpB3hB,KAAKshB,QAAQjC,OAAOuC,GACpB5hB,KAAKshB,QAAQjC,OAAOwC,GACpB7hB,KAAKshB,QAAQjC,OAAOyC,GACtBN,EAAOC,EAAOC,EAAO,GACrBC,EAAOC,EAAOC,EAAOC,EAAO,GACtBjlB,EAAIyf,EAAMxf,SAIlB,OAAOykB,KAGFlkB,IAAA,gBAAP,SAAe2kB,EAAgBC,GAC7B,IAAKD,EAAe/Y,MAAM,2BACxB,OAAOgZ,EAET,IAAIC,EAAWliB,KAAKmiB,SAClBH,EAAepT,QAAQ,0BAA2B,KAEhDwT,EAAWpiB,KAAKqiB,eAAeH,GAC/BI,EAAQtiB,KAAKuiB,iBAAiBN,EAAmBG,GACrD,MAAQ,0BAA8C9a,OAArBtH,KAAKwiB,SAASF,OAG1CjlB,IAAA,yBAAP,SAAwB4kB,EAAmBG,GACzC,IAAIK,EAAYziB,KAAK0iB,aAAaN,GAC9BO,EAAgB3iB,KAAK4iB,WAAWX,EAAmBQ,GAEvD,OADc,IAAI1D,WAAW4D,MAIxBtlB,IAAA,qBAAP,SAAoB+kB,OAClB,IAAIS,OAAMpiB,EACN6T,EAAI,EACDA,EAAI8N,EAAStlB,QAAQ,CAE1B,GAAgB,OADhB+lB,EAAMT,EAAS9N,IACN,GAA0B,MAAXuO,EAAI,GAC1B,OAAOA,EAETvO,IAEF,MAAO,MAGFjX,IAAA,mBAAP,SAAkB4kB,EAAmBQ,GACnC,IAAIK,EAAYb,EAAkBrT,QAAQ,0BAA2B,IACjEmU,EAAM/iB,KAAKmiB,SAASW,GACpBE,EAAgBD,EAAI3Q,QAAQ,IAAK,GACjC6Q,EAAMF,EAAI7jB,MAAM,EAAG8jB,GACnBE,EAAMH,EAAI7jB,MAAM8jB,GAChBG,EAAQF,EAGZ,OADAE,GADAA,EAAQA,EAAM7b,OAAOmb,IACPnb,OAAO4b,MAIhB7lB,IAAA,uBAAP,SAAsB+lB,OACpB,IAAIC,EAAO,EACPjB,EAAW,KACF,CAEX,GAA6B,MAAxBgB,EAAcC,GAA8C,MAA5BD,EAAcC,EAAO,GACxD,MAEF,GAA6B,MAAxBD,EAAcC,GAA8C,MAA5BD,EAAcC,EAAO,GACxDA,GAAQ,MACH,CAEL,IAAIC,EAAWD,GADoB,IAA1BD,EAAcC,EAAO,GAAWD,EAAcC,EAAO,IAC/B,EAC3BR,EAAMO,EAAclkB,MAAMmkB,EAAMC,GACpClB,EAASliB,KAAK2iB,GACdQ,EAAOC,EAET,GAAID,EAAOD,EAActmB,OACvB,MAGJ,OAAOslB,KAGF/kB,IAAA,iBAAP,SAAgBif,GACd,IACIkF,OAAO/gB,EACPghB,OAAOhhB,EACPihB,EAAO,GAEPE,OAAOnhB,EACPohB,OAAOphB,EACPqhB,EAAO,GACPjlB,EAAI,EACJkmB,EAAM,OAEI,sBACCQ,KAAKjH,IAClBnH,QAAQgI,KACN,oJAGJb,EAAQA,EAAM1N,QAAO,sBAAwB,IAM3C4S,EAJOxhB,KAAKshB,QAAQlP,QAAQkK,EAAM+C,OAAOxiB,OAIzB,GAHhB+kB,EAAO5hB,KAAKshB,QAAQlP,QAAQkK,EAAM+C,OAAOxiB,QAGX,EAC9B4kB,GAAgB,GAAPG,IAAc,GAHvBC,EAAO7hB,KAAKshB,QAAQlP,QAAQkK,EAAM+C,OAAOxiB,QAGJ,EACrC6kB,GAAgB,EAAPG,IAAa,GAHtBC,EAAO9hB,KAAKshB,QAAQlP,QAAQkK,EAAM+C,OAAOxiB,OAIzCkmB,EAAI7iB,KAAKshB,GACI,KAATK,GACFkB,EAAI7iB,KAAKuhB,GAEE,KAATK,GACFiB,EAAI7iB,KAAKwhB,GAEXF,EAAOC,EAAOC,EAAO,GACdE,EAAOC,EAAOC,EAAO,GACtBjlB,EAAIyf,EAAMxf,SAIlB,OAAOimB,MAxJLpM,EAAN,GA2JAA,EAAY8G,YC9rEZ9b,OAAO6hB,SAAW9b", "sources": ["node_modules/@swc/helpers/src/_assert_this_initialized.js", "node_modules/@swc/helpers/src/_class_call_check.js", "node_modules/@swc/helpers/src/_create_class.js", "node_modules/@swc/helpers/src/_get_prototype_of.js", "node_modules/@swc/helpers/src/_set_prototype_of.js", "node_modules/@swc/helpers/src/_inherits.js", "node_modules/@swc/helpers/src/_possible_constructor_return.js", "node_modules/@swc/helpers/src/_type_of.js", "node_modules/just-extend/index.js", "src/emitter.js", "src/options.js", "node_modules/@parcel/runtime-js/lib/bundles/runtime-389dff70499b0ec9.js", "src/dropzone.js", "tool/dropzone-global.js"], "sourcesContent": ["export default function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n", "export default function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}", "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nexport default function _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n", "function getPrototypeOf(o) {\n  getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return getPrototypeOf(o);\n}\n\nexport default function _getPrototypeOf(o) {\n  return getPrototypeOf(o);\n}", "function setPrototypeOf(o, p) {\n  setPrototypeOf = Object.setPrototypeOf || function setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return setPrototypeOf(o, p);\n}\n\nexport default function _setPrototypeOf(o, p) {\n  return setPrototypeOf(o, p);\n}\n", "import setPrototypeOf from './_set_prototype_of';\n\nexport default function _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\n", "import assertThisInitialized from './_assert_this_initialized';\nimport _typeof from './_type_of';\n\nexport default function _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return assertThisInitialized(self);\n}\n", "export default function _typeof(obj) {\n    return obj && obj.constructor === Symbol ? \"symbol\" : typeof obj;\n};\n", "module.exports = extend;\n\n/*\n  var obj = {a: 3, b: 5};\n  extend(obj, {a: 4, c: 8}); // {a: 4, b: 5, c: 8}\n  obj; // {a: 4, b: 5, c: 8}\n\n  var obj = {a: 3, b: 5};\n  extend({}, obj, {a: 4, c: 8}); // {a: 4, b: 5, c: 8}\n  obj; // {a: 3, b: 5}\n\n  var arr = [1, 2, 3];\n  var obj = {a: 3, b: 5};\n  extend(obj, {c: arr}); // {a: 3, b: 5, c: [1, 2, 3]}\n  arr.push(4);\n  obj; // {a: 3, b: 5, c: [1, 2, 3, 4]}\n\n  var arr = [1, 2, 3];\n  var obj = {a: 3, b: 5};\n  extend(true, obj, {c: arr}); // {a: 3, b: 5, c: [1, 2, 3]}\n  arr.push(4);\n  obj; // {a: 3, b: 5, c: [1, 2, 3]}\n\n  extend({a: 4, b: 5}); // {a: 4, b: 5}\n  extend({a: 4, b: 5}, 3); {a: 4, b: 5}\n  extend({a: 4, b: 5}, true); {a: 4, b: 5}\n  extend('hello', {a: 4, b: 5}); // throws\n  extend(3, {a: 4, b: 5}); // throws\n*/\n\nfunction extend(/* [deep], obj1, obj2, [objn] */) {\n  var args = [].slice.call(arguments);\n  var deep = false;\n  if (typeof args[0] == 'boolean') {\n    deep = args.shift();\n  }\n  var result = args[0];\n  if (isUnextendable(result)) {\n    throw new Error('extendee must be an object');\n  }\n  var extenders = args.slice(1);\n  var len = extenders.length;\n  for (var i = 0; i < len; i++) {\n    var extender = extenders[i];\n    for (var key in extender) {\n      if (Object.prototype.hasOwnProperty.call(extender, key)) {\n        var value = extender[key];\n        if (deep && isCloneable(value)) {\n          var base = Array.isArray(value) ? [] : {};\n          result[key] = extend(\n            true,\n            Object.prototype.hasOwnProperty.call(result, key) && !isUnextendable(result[key])\n              ? result[key]\n              : base,\n            value\n          );\n        } else {\n          result[key] = value;\n        }\n      }\n    }\n  }\n  return result;\n}\n\nfunction isCloneable(obj) {\n  return Array.isArray(obj) || {}.toString.call(obj) == '[object Object]';\n}\n\nfunction isUnextendable(val) {\n  return !val || (typeof val != 'object' && typeof val != 'function');\n}\n", "// The Emitter class provides the ability to call `.on()` on Dropzone to listen\n// to events.\n// It is strongly based on component's emitter class, and I removed the\n// functionality because of the dependency hell with different frameworks.\nexport default class Emitter {\n  // Add an event listener for given event\n  on(event, fn) {\n    this._callbacks = this._callbacks || {};\n    // Create namespace for this event\n    if (!this._callbacks[event]) {\n      this._callbacks[event] = [];\n    }\n    this._callbacks[event].push(fn);\n    return this;\n  }\n\n  emit(event, ...args) {\n    this._callbacks = this._callbacks || {};\n    let callbacks = this._callbacks[event];\n\n    if (callbacks) {\n      for (let callback of callbacks) {\n        callback.apply(this, args);\n      }\n    }\n    // trigger a corresponding DOM event\n    if (this.element) {\n      this.element.dispatchEvent(\n        this.makeEvent(\"dropzone:\" + event, { args: args })\n      );\n    }\n    return this;\n  }\n\n  makeEvent(eventName, detail) {\n    let params = { bubbles: true, cancelable: true, detail: detail };\n\n    if (typeof window.CustomEvent === \"function\") {\n      return new CustomEvent(eventName, params);\n    } else {\n      // IE 11 support\n      // https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/CustomEvent\n      var evt = document.createEvent(\"CustomEvent\");\n      evt.initCustomEvent(\n        eventName,\n        params.bubbles,\n        params.cancelable,\n        params.detail\n      );\n      return evt;\n    }\n  }\n\n  // Remove event listener for given event. If fn is not provided, all event\n  // listeners for that event will be removed. If neither is provided, all\n  // event listeners will be removed.\n  off(event, fn) {\n    if (!this._callbacks || arguments.length === 0) {\n      this._callbacks = {};\n      return this;\n    }\n\n    // specific event\n    let callbacks = this._callbacks[event];\n    if (!callbacks) {\n      return this;\n    }\n\n    // remove all handlers\n    if (arguments.length === 1) {\n      delete this._callbacks[event];\n      return this;\n    }\n\n    // remove specific handler\n    for (let i = 0; i < callbacks.length; i++) {\n      let callback = callbacks[i];\n      if (callback === fn) {\n        callbacks.splice(i, 1);\n        break;\n      }\n    }\n\n    return this;\n  }\n}\n", "import Dropzone from \"./dropzone\";\nimport defaultPreviewTemplate from \"bundle-text:./preview-template.html\";\n\nlet defaultOptions = {\n  /**\n   * Has to be specified on elements other than form (or when the form doesn't\n   * have an `action` attribute).\n   *\n   * You can also provide a function that will be called with `files` and\n   * `dataBlocks`  and must return the url as string.\n   */\n  url: null,\n\n  /**\n   * Can be changed to `\"put\"` if necessary. You can also provide a function\n   * that will be called with `files` and must return the method (since `v3.12.0`).\n   */\n  method: \"post\",\n\n  /**\n   * Will be set on the XHRequest.\n   */\n  withCredentials: false,\n\n  /**\n   * The timeout for the XHR requests in milliseconds (since `v4.4.0`).\n   * If set to null or 0, no timeout is going to be set.\n   */\n  timeout: null,\n\n  /**\n   * How many file uploads to process in parallel (See the\n   * Enqueuing file uploads documentation section for more info)\n   */\n  parallelUploads: 2,\n\n  /**\n   * Whether to send multiple files in one request. If\n   * this it set to true, then the fallback file input element will\n   * have the `multiple` attribute as well. This option will\n   * also trigger additional events (like `processingmultiple`). See the events\n   * documentation section for more information.\n   */\n  uploadMultiple: false,\n\n  /**\n   * Whether you want files to be uploaded in chunks to your server. This can't be\n   * used in combination with `uploadMultiple`.\n   *\n   * See [chunksUploaded](#config-chunksUploaded) for the callback to finalise an upload.\n   */\n  chunking: false,\n\n  /**\n   * If `chunking` is enabled, this defines whether **every** file should be chunked,\n   * even if the file size is below chunkSize. This means, that the additional chunk\n   * form data will be submitted and the `chunksUploaded` callback will be invoked.\n   */\n  forceChunking: false,\n\n  /**\n   * If `chunking` is `true`, then this defines the chunk size in bytes.\n   */\n  chunkSize: 2 * 1024 * 1024,\n\n  /**\n   * If `true`, the individual chunks of a file are being uploaded simultaneously.\n   */\n  parallelChunkUploads: false,\n\n  /**\n   * Whether a chunk should be retried if it fails.\n   */\n  retryChunks: false,\n\n  /**\n   * If `retryChunks` is true, how many times should it be retried.\n   */\n  retryChunksLimit: 3,\n\n  /**\n   * The maximum filesize (in MiB) that is allowed to be uploaded.\n   */\n  maxFilesize: 256,\n\n  /**\n   * The name of the file param that gets transferred.\n   * **NOTE**: If you have the option  `uploadMultiple` set to `true`, then\n   * Dropzone will append `[]` to the name.\n   */\n  paramName: \"file\",\n\n  /**\n   * Whether thumbnails for images should be generated\n   */\n  createImageThumbnails: true,\n\n  /**\n   * In MB. When the filename exceeds this limit, the thumbnail will not be generated.\n   */\n  maxThumbnailFilesize: 10,\n\n  /**\n   * If `null`, the ratio of the image will be used to calculate it.\n   */\n  thumbnailWidth: 120,\n\n  /**\n   * The same as `thumbnailWidth`. If both are null, images will not be resized.\n   */\n  thumbnailHeight: 120,\n\n  /**\n   * How the images should be scaled down in case both, `thumbnailWidth` and `thumbnailHeight` are provided.\n   * Can be either `contain` or `crop`.\n   */\n  thumbnailMethod: \"crop\",\n\n  /**\n   * If set, images will be resized to these dimensions before being **uploaded**.\n   * If only one, `resizeWidth` **or** `resizeHeight` is provided, the original aspect\n   * ratio of the file will be preserved.\n   *\n   * The `options.transformFile` function uses these options, so if the `transformFile` function\n   * is overridden, these options don't do anything.\n   */\n  resizeWidth: null,\n\n  /**\n   * See `resizeWidth`.\n   */\n  resizeHeight: null,\n\n  /**\n   * The mime type of the resized image (before it gets uploaded to the server).\n   * If `null` the original mime type will be used. To force jpeg, for example, use `image/jpeg`.\n   * See `resizeWidth` for more information.\n   */\n  resizeMimeType: null,\n\n  /**\n   * The quality of the resized images. See `resizeWidth`.\n   */\n  resizeQuality: 0.8,\n\n  /**\n   * How the images should be scaled down in case both, `resizeWidth` and `resizeHeight` are provided.\n   * Can be either `contain` or `crop`.\n   */\n  resizeMethod: \"contain\",\n\n  /**\n   * The base that is used to calculate the **displayed** filesize. You can\n   * change this to 1024 if you would rather display kibibytes, mebibytes,\n   * etc... 1024 is technically incorrect, because `1024 bytes` are `1 kibibyte`\n   * not `1 kilobyte`. You can change this to `1024` if you don't care about\n   * validity.\n   */\n  filesizeBase: 1000,\n\n  /**\n   * If not `null` defines how many files this Dropzone handles. If it exceeds,\n   * the event `maxfilesexceeded` will be called. The dropzone element gets the\n   * class `dz-max-files-reached` accordingly so you can provide visual\n   * feedback.\n   */\n  maxFiles: null,\n\n  /**\n   * An optional object to send additional headers to the server. Eg:\n   * `{ \"My-Awesome-Header\": \"header value\" }`\n   */\n  headers: null,\n\n  /**\n   * Should the default headers be set or not?\n   * Accept: application/json <- for requesting json response\n   * Cache-Control: no-cache <- Request shouldnt be cached\n   * X-Requested-With: XMLHttpRequest <- We sent the request via XMLHttpRequest\n   */\n  defaultHeaders: true,\n\n  /**\n   * If `true`, the dropzone element itself will be clickable, if `false`\n   * nothing will be clickable.\n   *\n   * You can also pass an HTML element, a CSS selector (for multiple elements)\n   * or an array of those. In that case, all of those elements will trigger an\n   * upload when clicked.\n   */\n  clickable: true,\n\n  /**\n   * Whether hidden files in directories should be ignored.\n   */\n  ignoreHiddenFiles: true,\n\n  /**\n   * The default implementation of `accept` checks the file's mime type or\n   * extension against this list. This is a comma separated list of mime\n   * types or file extensions.\n   *\n   * Eg.: `image/*,application/pdf,.psd`\n   *\n   * If the Dropzone is `clickable` this option will also be used as\n   * [`accept`](https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept)\n   * parameter on the hidden file input as well.\n   */\n  acceptedFiles: null,\n\n  /**\n   * **Deprecated!**\n   * Use acceptedFiles instead.\n   */\n  acceptedMimeTypes: null,\n\n  /**\n   * If false, files will be added to the queue but the queue will not be\n   * processed automatically.\n   * This can be useful if you need some additional user input before sending\n   * files (or if you want want all files sent at once).\n   * If you're ready to send the file simply call `myDropzone.processQueue()`.\n   *\n   * See the [enqueuing file uploads](#enqueuing-file-uploads) documentation\n   * section for more information.\n   */\n  autoProcessQueue: true,\n\n  /**\n   * If false, files added to the dropzone will not be queued by default.\n   * You'll have to call `enqueueFile(file)` manually.\n   */\n  autoQueue: true,\n\n  /**\n   * If `true`, this will add a link to every file preview to remove or cancel (if\n   * already uploading) the file. The `dictCancelUpload`, `dictCancelUploadConfirmation`\n   * and `dictRemoveFile` options are used for the wording.\n   */\n  addRemoveLinks: false,\n\n  /**\n   * Defines where to display the file previews – if `null` the\n   * Dropzone element itself is used. Can be a plain `HTMLElement` or a CSS\n   * selector. The element should have the `dropzone-previews` class so\n   * the previews are displayed properly.\n   */\n  previewsContainer: null,\n\n  /**\n   * Set this to `true` if you don't want previews to be shown.\n   */\n  disablePreviews: false,\n\n  /**\n   * This is the element the hidden input field (which is used when clicking on the\n   * dropzone to trigger file selection) will be appended to. This might\n   * be important in case you use frameworks to switch the content of your page.\n   *\n   * Can be a selector string, or an element directly.\n   */\n  hiddenInputContainer: \"body\",\n\n  /**\n   * If null, no capture type will be specified\n   * If camera, mobile devices will skip the file selection and choose camera\n   * If microphone, mobile devices will skip the file selection and choose the microphone\n   * If camcorder, mobile devices will skip the file selection and choose the camera in video mode\n   * On apple devices multiple must be set to false.  AcceptedFiles may need to\n   * be set to an appropriate mime type (e.g. \"image/*\", \"audio/*\", or \"video/*\").\n   */\n  capture: null,\n\n  /**\n   * **Deprecated**. Use `renameFile` instead.\n   */\n  renameFilename: null,\n\n  /**\n   * A function that is invoked before the file is uploaded to the server and renames the file.\n   * This function gets the `File` as argument and can use the `file.name`. The actual name of the\n   * file that gets used during the upload can be accessed through `file.upload.filename`.\n   */\n  renameFile: null,\n\n  /**\n   * If `true` the fallback will be forced. This is very useful to test your server\n   * implementations first and make sure that everything works as\n   * expected without dropzone if you experience problems, and to test\n   * how your fallbacks will look.\n   */\n  forceFallback: false,\n\n  /**\n   * The text used before any files are dropped.\n   */\n  dictDefaultMessage: \"Drop files here to upload\",\n\n  /**\n   * The text that replaces the default message text it the browser is not supported.\n   */\n  dictFallbackMessage:\n    \"Your browser does not support drag'n'drop file uploads.\",\n\n  /**\n   * The text that will be added before the fallback form.\n   * If you provide a  fallback element yourself, or if this option is `null` this will\n   * be ignored.\n   */\n  dictFallbackText:\n    \"Please use the fallback form below to upload your files like in the olden days.\",\n\n  /**\n   * If the filesize is too big.\n   * `{{filesize}}` and `{{maxFilesize}}` will be replaced with the respective configuration values.\n   */\n  dictFileTooBig:\n    \"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.\",\n\n  /**\n   * If the file doesn't match the file type.\n   */\n  dictInvalidFileType: \"You can't upload files of this type.\",\n\n  /**\n   * If the server response was invalid.\n   * `{{statusCode}}` will be replaced with the servers status code.\n   */\n  dictResponseError: \"Server responded with {{statusCode}} code.\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used for the cancel upload link.\n   */\n  dictCancelUpload: \"Cancel upload\",\n\n  /**\n   * The text that is displayed if an upload was manually canceled\n   */\n  dictUploadCanceled: \"Upload canceled.\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used for confirmation when cancelling upload.\n   */\n  dictCancelUploadConfirmation: \"Are you sure you want to cancel this upload?\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used to remove a file.\n   */\n  dictRemoveFile: \"Remove file\",\n\n  /**\n   * If this is not null, then the user will be prompted before removing a file.\n   */\n  dictRemoveFileConfirmation: null,\n\n  /**\n   * Displayed if `maxFiles` is st and exceeded.\n   * The string `{{maxFiles}}` will be replaced by the configuration value.\n   */\n  dictMaxFilesExceeded: \"You can not upload any more files.\",\n\n  /**\n   * Allows you to translate the different units. Starting with `tb` for terabytes and going down to\n   * `b` for bytes.\n   */\n  dictFileSizeUnits: { tb: \"TB\", gb: \"GB\", mb: \"MB\", kb: \"KB\", b: \"b\" },\n  /**\n   * Called when dropzone initialized\n   * You can add event listeners here\n   */\n  init() {},\n\n  /**\n   * Can be an **object** of additional parameters to transfer to the server, **or** a `Function`\n   * that gets invoked with the `files`, `xhr` and, if it's a chunked upload, `chunk` arguments. In case\n   * of a function, this needs to return a map.\n   *\n   * The default implementation does nothing for normal uploads, but adds relevant information for\n   * chunked uploads.\n   *\n   * This is the same as adding hidden input fields in the form element.\n   */\n  params(files, xhr, chunk) {\n    if (chunk) {\n      return {\n        dzuuid: chunk.file.upload.uuid,\n        dzchunkindex: chunk.index,\n        dztotalfilesize: chunk.file.size,\n        dzchunksize: this.options.chunkSize,\n        dztotalchunkcount: chunk.file.upload.totalChunkCount,\n        dzchunkbyteoffset: chunk.index * this.options.chunkSize,\n      };\n    }\n  },\n\n  /**\n   * A function that gets a [file](https://developer.mozilla.org/en-US/docs/DOM/File)\n   * and a `done` function as parameters.\n   *\n   * If the done function is invoked without arguments, the file is \"accepted\" and will\n   * be processed. If you pass an error message, the file is rejected, and the error\n   * message will be displayed.\n   * This function will not be called if the file is too big or doesn't match the mime types.\n   */\n  accept(file, done) {\n    return done();\n  },\n\n  /**\n   * The callback that will be invoked when all chunks have been uploaded for a file.\n   * It gets the file for which the chunks have been uploaded as the first parameter,\n   * and the `done` function as second. `done()` needs to be invoked when everything\n   * needed to finish the upload process is done.\n   */\n  chunksUploaded: function (file, done) {\n    done();\n  },\n\n  /**\n   * Sends the file as binary blob in body instead of form data.\n   * If this is set, the `params` option will be ignored.\n   * It's an error to set this to `true` along with `uploadMultiple` since\n   * multiple files cannot be in a single binary body.\n   */\n  binaryBody: false,\n\n  /**\n   * Gets called when the browser is not supported.\n   * The default implementation shows the fallback input field and adds\n   * a text.\n   */\n  fallback() {\n    // This code should pass in IE7... :(\n    let messageElement;\n    this.element.className = `${this.element.className} dz-browser-not-supported`;\n\n    for (let child of this.element.getElementsByTagName(\"div\")) {\n      if (/(^| )dz-message($| )/.test(child.className)) {\n        messageElement = child;\n        child.className = \"dz-message\"; // Removes the 'dz-default' class\n        break;\n      }\n    }\n    if (!messageElement) {\n      messageElement = Dropzone.createElement(\n        '<div class=\"dz-message\"><span></span></div>'\n      );\n      this.element.appendChild(messageElement);\n    }\n\n    let span = messageElement.getElementsByTagName(\"span\")[0];\n    if (span) {\n      if (span.textContent != null) {\n        span.textContent = this.options.dictFallbackMessage;\n      } else if (span.innerText != null) {\n        span.innerText = this.options.dictFallbackMessage;\n      }\n    }\n\n    return this.element.appendChild(this.getFallbackForm());\n  },\n\n  /**\n   * Gets called to calculate the thumbnail dimensions.\n   *\n   * It gets `file`, `width` and `height` (both may be `null`) as parameters and must return an object containing:\n   *\n   *  - `srcWidth` & `srcHeight` (required)\n   *  - `trgWidth` & `trgHeight` (required)\n   *  - `srcX` & `srcY` (optional, default `0`)\n   *  - `trgX` & `trgY` (optional, default `0`)\n   *\n   * Those values are going to be used by `ctx.drawImage()`.\n   */\n  resize(file, width, height, resizeMethod) {\n    let info = {\n      srcX: 0,\n      srcY: 0,\n      srcWidth: file.width,\n      srcHeight: file.height,\n    };\n\n    let srcRatio = file.width / file.height;\n\n    // Automatically calculate dimensions if not specified\n    if (width == null && height == null) {\n      width = info.srcWidth;\n      height = info.srcHeight;\n    } else if (width == null) {\n      width = height * srcRatio;\n    } else if (height == null) {\n      height = width / srcRatio;\n    }\n\n    // Make sure images aren't upscaled\n    width = Math.min(width, info.srcWidth);\n    height = Math.min(height, info.srcHeight);\n\n    let trgRatio = width / height;\n\n    if (info.srcWidth > width || info.srcHeight > height) {\n      // Image is bigger and needs rescaling\n      if (resizeMethod === \"crop\") {\n        if (srcRatio > trgRatio) {\n          info.srcHeight = file.height;\n          info.srcWidth = info.srcHeight * trgRatio;\n        } else {\n          info.srcWidth = file.width;\n          info.srcHeight = info.srcWidth / trgRatio;\n        }\n      } else if (resizeMethod === \"contain\") {\n        // Method 'contain'\n        if (srcRatio > trgRatio) {\n          height = width / srcRatio;\n        } else {\n          width = height * srcRatio;\n        }\n      } else {\n        throw new Error(`Unknown resizeMethod '${resizeMethod}'`);\n      }\n    }\n\n    info.srcX = (file.width - info.srcWidth) / 2;\n    info.srcY = (file.height - info.srcHeight) / 2;\n\n    info.trgWidth = width;\n    info.trgHeight = height;\n\n    return info;\n  },\n\n  /**\n   * Can be used to transform the file (for example, resize an image if necessary).\n   *\n   * The default implementation uses `resizeWidth` and `resizeHeight` (if provided) and resizes\n   * images according to those dimensions.\n   *\n   * Gets the `file` as the first parameter, and a `done()` function as the second, that needs\n   * to be invoked with the file when the transformation is done.\n   */\n  transformFile(file, done) {\n    if (\n      (this.options.resizeWidth || this.options.resizeHeight) &&\n      file.type.match(/image.*/)\n    ) {\n      return this.resizeImage(\n        file,\n        this.options.resizeWidth,\n        this.options.resizeHeight,\n        this.options.resizeMethod,\n        done\n      );\n    } else {\n      return done(file);\n    }\n  },\n\n  /**\n   * A string that contains the template used for each dropped\n   * file. Change it to fulfill your needs but make sure to properly\n   * provide all elements.\n   *\n   * If you want to use an actual HTML element instead of providing a String\n   * as a config option, you could create a div with the id `tpl`,\n   * put the template inside it and provide the element like this:\n   *\n   *     document\n   *       .querySelector('#tpl')\n   *       .innerHTML\n   *\n   */\n  previewTemplate: defaultPreviewTemplate,\n\n  /*\n   Those functions register themselves to the events on init and handle all\n   the user interface specific stuff. Overwriting them won't break the upload\n   but can break the way it's displayed.\n   You can overwrite them if you don't like the default behavior. If you just\n   want to add an additional event handler, register it on the dropzone object\n   and don't overwrite those options.\n   */\n\n  // Those are self explanatory and simply concern the DragnDrop.\n  drop(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  dragstart(e) {},\n  dragend(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  dragenter(e) {\n    return this.element.classList.add(\"dz-drag-hover\");\n  },\n  dragover(e) {\n    return this.element.classList.add(\"dz-drag-hover\");\n  },\n  dragleave(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n\n  paste(e) {},\n\n  // Called whenever there are no files left in the dropzone anymore, and the\n  // dropzone should be displayed as if in the initial state.\n  reset() {\n    return this.element.classList.remove(\"dz-started\");\n  },\n\n  // Called when a file is added to the queue\n  // Receives `file`\n  addedfile(file) {\n    if (this.element === this.previewsContainer) {\n      this.element.classList.add(\"dz-started\");\n    }\n\n    if (this.previewsContainer && !this.options.disablePreviews) {\n      file.previewElement = Dropzone.createElement(\n        this.options.previewTemplate.trim()\n      );\n      file.previewTemplate = file.previewElement; // Backwards compatibility\n\n      this.previewsContainer.appendChild(file.previewElement);\n      for (var node of file.previewElement.querySelectorAll(\"[data-dz-name]\")) {\n        node.textContent = file.name;\n      }\n      for (node of file.previewElement.querySelectorAll(\"[data-dz-size]\")) {\n        node.innerHTML = this.filesize(file.size);\n      }\n\n      if (this.options.addRemoveLinks) {\n        file._removeLink = Dropzone.createElement(\n          `<a class=\"dz-remove\" href=\"javascript:undefined;\" data-dz-remove>${this.options.dictRemoveFile}</a>`\n        );\n        file.previewElement.appendChild(file._removeLink);\n      }\n\n      let removeFileEvent = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (file.status === Dropzone.UPLOADING) {\n          return Dropzone.confirm(\n            this.options.dictCancelUploadConfirmation,\n            () => this.removeFile(file)\n          );\n        } else {\n          if (this.options.dictRemoveFileConfirmation) {\n            return Dropzone.confirm(\n              this.options.dictRemoveFileConfirmation,\n              () => this.removeFile(file)\n            );\n          } else {\n            return this.removeFile(file);\n          }\n        }\n      };\n\n      for (let removeLink of file.previewElement.querySelectorAll(\n        \"[data-dz-remove]\"\n      )) {\n        removeLink.addEventListener(\"click\", removeFileEvent);\n      }\n    }\n  },\n\n  // Called whenever a file is removed.\n  removedfile(file) {\n    if (file.previewElement != null && file.previewElement.parentNode != null) {\n      file.previewElement.parentNode.removeChild(file.previewElement);\n    }\n    return this._updateMaxFilesReachedClass();\n  },\n\n  // Called when a thumbnail has been generated\n  // Receives `file` and `dataUrl`\n  thumbnail(file, dataUrl) {\n    if (file.previewElement) {\n      file.previewElement.classList.remove(\"dz-file-preview\");\n      for (let thumbnailElement of file.previewElement.querySelectorAll(\n        \"[data-dz-thumbnail]\"\n      )) {\n        thumbnailElement.alt = file.name;\n        thumbnailElement.src = dataUrl;\n      }\n\n      return setTimeout(\n        () => file.previewElement.classList.add(\"dz-image-preview\"),\n        1\n      );\n    }\n  },\n\n  // Called whenever an error occurs\n  // Receives `file` and `message`\n  error(file, message) {\n    if (file.previewElement) {\n      file.previewElement.classList.add(\"dz-error\");\n      if (typeof message !== \"string\" && message.error) {\n        message = message.error;\n      }\n      for (let node of file.previewElement.querySelectorAll(\n        \"[data-dz-errormessage]\"\n      )) {\n        node.textContent = message;\n      }\n    }\n  },\n\n  errormultiple() {},\n\n  // Called when a file gets processed. Since there is a cue, not all added\n  // files are processed immediately.\n  // Receives `file`\n  processing(file) {\n    if (file.previewElement) {\n      file.previewElement.classList.add(\"dz-processing\");\n      if (file._removeLink) {\n        return (file._removeLink.innerHTML = this.options.dictCancelUpload);\n      }\n    }\n  },\n\n  processingmultiple() {},\n\n  // Called whenever the upload progress gets updated.\n  // Receives `file`, `progress` (percentage 0-100) and `bytesSent`.\n  // To get the total number of bytes of the file, use `file.size`\n  uploadprogress(file, progress, bytesSent) {\n    if (file.previewElement) {\n      for (let node of file.previewElement.querySelectorAll(\n        \"[data-dz-uploadprogress]\"\n      )) {\n        node.nodeName === \"PROGRESS\"\n          ? (node.value = progress)\n          : (node.style.width = `${progress}%`);\n      }\n    }\n  },\n\n  // Called whenever the total upload progress gets updated.\n  // Called with totalUploadProgress (0-100), totalBytes and totalBytesSent\n  totaluploadprogress() {},\n\n  // Called just before the file is sent. Gets the `xhr` object as second\n  // parameter, so you can modify it (for example to add a CSRF token) and a\n  // `formData` object to add additional information.\n  sending() {},\n\n  sendingmultiple() {},\n\n  // When the complete upload is finished and successful\n  // Receives `file`\n  success(file) {\n    if (file.previewElement) {\n      return file.previewElement.classList.add(\"dz-success\");\n    }\n  },\n\n  successmultiple() {},\n\n  // When the upload is canceled.\n  canceled(file) {\n    return this.emit(\"error\", file, this.options.dictUploadCanceled);\n  },\n\n  canceledmultiple() {},\n\n  // When the upload is finished, either with success or an error.\n  // Receives `file`\n  complete(file) {\n    if (file._removeLink) {\n      file._removeLink.innerHTML = this.options.dictRemoveFile;\n    }\n    if (file.previewElement) {\n      return file.previewElement.classList.add(\"dz-complete\");\n    }\n  },\n\n  completemultiple() {},\n\n  maxfilesexceeded() {},\n\n  maxfilesreached() {},\n\n  queuecomplete() {},\n\n  addedfiles() {},\n};\n\nexport default defaultOptions;\n", "module.exports = \"d07f0bb239092071\";", "import extend from \"just-extend\";\nimport Emitter from \"./emitter\";\nimport defaultOptions from \"./options\";\n\nexport default class Dropzone extends Emitter {\n  static initClass() {\n    // Exposing the emitter class, mainly for tests\n    this.prototype.Emitter = Emitter;\n\n    /*\n     This is a list of all available events you can register on a dropzone object.\n\n     You can register an event handler like this:\n\n     dropzone.on(\"dragEnter\", function() { });\n\n     */\n    this.prototype.events = [\n      \"drop\",\n      \"dragstart\",\n      \"dragend\",\n      \"dragenter\",\n      \"dragover\",\n      \"dragleave\",\n      \"addedfile\",\n      \"addedfiles\",\n      \"removedfile\",\n      \"thumbnail\",\n      \"error\",\n      \"errormultiple\",\n      \"processing\",\n      \"processingmultiple\",\n      \"uploadprogress\",\n      \"totaluploadprogress\",\n      \"sending\",\n      \"sendingmultiple\",\n      \"success\",\n      \"successmultiple\",\n      \"canceled\",\n      \"canceledmultiple\",\n      \"complete\",\n      \"completemultiple\",\n      \"reset\",\n      \"maxfilesexceeded\",\n      \"maxfilesreached\",\n      \"queuecomplete\",\n    ];\n\n    this.prototype._thumbnailQueue = [];\n    this.prototype._processingThumbnail = false;\n  }\n\n  constructor(el, options) {\n    super();\n    let fallback, left;\n    this.element = el;\n\n    this.clickableElements = [];\n    this.listeners = [];\n    this.files = []; // All files\n\n    if (typeof this.element === \"string\") {\n      this.element = document.querySelector(this.element);\n    }\n\n    // Not checking if instance of HTMLElement or Element since IE9 is extremely weird.\n    if (!this.element || this.element.nodeType == null) {\n      throw new Error(\"Invalid dropzone element.\");\n    }\n\n    if (this.element.dropzone) {\n      throw new Error(\"Dropzone already attached.\");\n    }\n\n    // Now add this dropzone to the instances.\n    Dropzone.instances.push(this);\n\n    // Put the dropzone inside the element itself.\n    this.element.dropzone = this;\n\n    let elementOptions =\n      (left = Dropzone.optionsForElement(this.element)) != null ? left : {};\n\n    this.options = extend(\n      true,\n      {},\n      defaultOptions,\n      elementOptions,\n      options != null ? options : {}\n    );\n\n    this.options.previewTemplate = this.options.previewTemplate.replace(\n      /\\n*/g,\n      \"\"\n    );\n\n    // If the browser failed, just call the fallback and leave\n    if (this.options.forceFallback || !Dropzone.isBrowserSupported()) {\n      return this.options.fallback.call(this);\n    }\n\n    // @options.url = @element.getAttribute \"action\" unless @options.url?\n    if (this.options.url == null) {\n      this.options.url = this.element.getAttribute(\"action\");\n    }\n\n    if (!this.options.url) {\n      throw new Error(\"No URL provided.\");\n    }\n\n    if (this.options.acceptedFiles && this.options.acceptedMimeTypes) {\n      throw new Error(\n        \"You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.\"\n      );\n    }\n\n    if (this.options.uploadMultiple && this.options.chunking) {\n      throw new Error(\"You cannot set both: uploadMultiple and chunking.\");\n    }\n\n    if (this.options.binaryBody && this.options.uploadMultiple) {\n      throw new Error(\"You cannot set both: binaryBody and uploadMultiple.\");\n    }\n\n    // Backwards compatibility\n    if (this.options.acceptedMimeTypes) {\n      this.options.acceptedFiles = this.options.acceptedMimeTypes;\n      delete this.options.acceptedMimeTypes;\n    }\n\n    // Backwards compatibility\n    if (this.options.renameFilename != null) {\n      this.options.renameFile = (file) =>\n        this.options.renameFilename.call(this, file.name, file);\n    }\n\n    if (typeof this.options.method === \"string\") {\n      this.options.method = this.options.method.toUpperCase();\n    }\n\n    if ((fallback = this.getExistingFallback()) && fallback.parentNode) {\n      // Remove the fallback\n      fallback.parentNode.removeChild(fallback);\n    }\n\n    // Display previews in the previewsContainer element or the Dropzone element unless explicitly set to false\n    if (this.options.previewsContainer !== false) {\n      if (this.options.previewsContainer) {\n        this.previewsContainer = Dropzone.getElement(\n          this.options.previewsContainer,\n          \"previewsContainer\"\n        );\n      } else {\n        this.previewsContainer = this.element;\n      }\n    }\n\n    if (this.options.clickable) {\n      if (this.options.clickable === true) {\n        this.clickableElements = [this.element];\n      } else {\n        this.clickableElements = Dropzone.getElements(\n          this.options.clickable,\n          \"clickable\"\n        );\n      }\n    }\n\n    this.init();\n  }\n\n  // Returns all files that have been accepted\n  getAcceptedFiles() {\n    return this.files.filter((file) => file.accepted).map((file) => file);\n  }\n\n  // Returns all files that have been rejected\n  // Not sure when that's going to be useful, but added for completeness.\n  getRejectedFiles() {\n    return this.files.filter((file) => !file.accepted).map((file) => file);\n  }\n\n  getFilesWithStatus(status) {\n    return this.files\n      .filter((file) => file.status === status)\n      .map((file) => file);\n  }\n\n  // Returns all files that are in the queue\n  getQueuedFiles() {\n    return this.getFilesWithStatus(Dropzone.QUEUED);\n  }\n\n  getUploadingFiles() {\n    return this.getFilesWithStatus(Dropzone.UPLOADING);\n  }\n\n  getAddedFiles() {\n    return this.getFilesWithStatus(Dropzone.ADDED);\n  }\n\n  // Files that are either queued or uploading\n  getActiveFiles() {\n    return this.files\n      .filter(\n        (file) =>\n          file.status === Dropzone.UPLOADING || file.status === Dropzone.QUEUED\n      )\n      .map((file) => file);\n  }\n\n  // The function that gets called when Dropzone is initialized. You\n  // can (and should) setup event listeners inside this function.\n  init() {\n    // In case it isn't set already\n    if (this.element.tagName === \"form\") {\n      this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n    }\n\n    if (\n      this.element.classList.contains(\"dropzone\") &&\n      !this.element.querySelector(\".dz-message\")\n    ) {\n      this.element.appendChild(\n        Dropzone.createElement(\n          `<div class=\"dz-default dz-message\"><button class=\"dz-button\" type=\"button\">${this.options.dictDefaultMessage}</button></div>`\n        )\n      );\n    }\n\n    if (this.clickableElements.length) {\n      let setupHiddenFileInput = () => {\n        if (this.hiddenFileInput) {\n          this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n        }\n        this.hiddenFileInput = document.createElement(\"input\");\n        this.hiddenFileInput.setAttribute(\"type\", \"file\");\n        if (this.options.maxFiles === null || this.options.maxFiles > 1) {\n          this.hiddenFileInput.setAttribute(\"multiple\", \"multiple\");\n        }\n        this.hiddenFileInput.className = \"dz-hidden-input\";\n\n        if (this.options.acceptedFiles !== null) {\n          this.hiddenFileInput.setAttribute(\n            \"accept\",\n            this.options.acceptedFiles\n          );\n        }\n        if (this.options.capture !== null) {\n          this.hiddenFileInput.setAttribute(\"capture\", this.options.capture);\n        }\n\n        // Making sure that no one can \"tab\" into this field.\n        this.hiddenFileInput.setAttribute(\"tabindex\", \"-1\");\n\n        // Not setting `display=\"none\"` because some browsers don't accept clicks\n        // on elements that aren't displayed.\n        this.hiddenFileInput.style.visibility = \"hidden\";\n        this.hiddenFileInput.style.position = \"absolute\";\n        this.hiddenFileInput.style.top = \"0\";\n        this.hiddenFileInput.style.left = \"0\";\n        this.hiddenFileInput.style.height = \"0\";\n        this.hiddenFileInput.style.width = \"0\";\n        Dropzone.getElement(\n          this.options.hiddenInputContainer,\n          \"hiddenInputContainer\"\n        ).appendChild(this.hiddenFileInput);\n        this.hiddenFileInput.addEventListener(\"change\", () => {\n          let { files } = this.hiddenFileInput;\n          if (files.length) {\n            for (let file of files) {\n              this.addFile(file);\n            }\n          }\n          this.emit(\"addedfiles\", files);\n          setupHiddenFileInput();\n        });\n      };\n      setupHiddenFileInput();\n    }\n\n    this.URL = window.URL !== null ? window.URL : window.webkitURL;\n\n    // Setup all event listeners on the Dropzone object itself.\n    // They're not in @setupEventListeners() because they shouldn't be removed\n    // again when the dropzone gets disabled.\n    for (let eventName of this.events) {\n      this.on(eventName, this.options[eventName]);\n    }\n\n    this.on(\"uploadprogress\", () => this.updateTotalUploadProgress());\n\n    this.on(\"removedfile\", () => this.updateTotalUploadProgress());\n\n    this.on(\"canceled\", (file) => this.emit(\"complete\", file));\n\n    // Emit a `queuecomplete` event if all files finished uploading.\n    this.on(\"complete\", (file) => {\n      if (\n        this.getAddedFiles().length === 0 &&\n        this.getUploadingFiles().length === 0 &&\n        this.getQueuedFiles().length === 0\n      ) {\n        // This needs to be deferred so that `queuecomplete` really triggers after `complete`\n        return setTimeout(() => this.emit(\"queuecomplete\"), 0);\n      }\n    });\n\n    const containsFiles = function (e) {\n      if (e.dataTransfer.types) {\n        // Because e.dataTransfer.types is an Object in\n        // IE, we need to iterate like this instead of\n        // using e.dataTransfer.types.some()\n        for (var i = 0; i < e.dataTransfer.types.length; i++) {\n          if (e.dataTransfer.types[i] === \"Files\") return true;\n        }\n      }\n      return false;\n    };\n\n    let noPropagation = function (e) {\n      // If there are no files, we don't want to stop\n      // propagation so we don't interfere with other\n      // drag and drop behaviour.\n      if (!containsFiles(e)) return;\n      e.stopPropagation();\n      if (e.preventDefault) {\n        return e.preventDefault();\n      } else {\n        return (e.returnValue = false);\n      }\n    };\n\n    // Create the listeners\n    this.listeners = [\n      {\n        element: this.element,\n        events: {\n          dragstart: (e) => {\n            return this.emit(\"dragstart\", e);\n          },\n          dragenter: (e) => {\n            noPropagation(e);\n            return this.emit(\"dragenter\", e);\n          },\n          dragover: (e) => {\n            // Makes it possible to drag files from chrome's download bar\n            // http://stackoverflow.com/questions/19526430/drag-and-drop-file-uploads-from-chrome-downloads-bar\n            // Try is required to prevent bug in Internet Explorer 11 (SCRIPT65535 exception)\n            let efct;\n            try {\n              efct = e.dataTransfer.effectAllowed;\n            } catch (error) {}\n            e.dataTransfer.dropEffect =\n              \"move\" === efct || \"linkMove\" === efct ? \"move\" : \"copy\";\n\n            noPropagation(e);\n            return this.emit(\"dragover\", e);\n          },\n          dragleave: (e) => {\n            return this.emit(\"dragleave\", e);\n          },\n          drop: (e) => {\n            noPropagation(e);\n            return this.drop(e);\n          },\n          dragend: (e) => {\n            return this.emit(\"dragend\", e);\n          },\n        },\n\n        // This is disabled right now, because the browsers don't implement it properly.\n        // \"paste\": (e) =>\n        //   noPropagation e\n        //   @paste e\n      },\n    ];\n\n    this.clickableElements.forEach((clickableElement) => {\n      return this.listeners.push({\n        element: clickableElement,\n        events: {\n          click: (evt) => {\n            // Only the actual dropzone or the message element should trigger file selection\n            if (\n              clickableElement !== this.element ||\n              evt.target === this.element ||\n              Dropzone.elementInside(\n                evt.target,\n                this.element.querySelector(\".dz-message\")\n              )\n            ) {\n              this.hiddenFileInput.click(); // Forward the click\n            }\n            return true;\n          },\n        },\n      });\n    });\n\n    this.enable();\n\n    return this.options.init.call(this);\n  }\n\n  // Not fully tested yet\n  destroy() {\n    this.disable();\n    this.removeAllFiles(true);\n    if (\n      this.hiddenFileInput != null ? this.hiddenFileInput.parentNode : undefined\n    ) {\n      this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n      this.hiddenFileInput = null;\n    }\n    delete this.element.dropzone;\n    return Dropzone.instances.splice(Dropzone.instances.indexOf(this), 1);\n  }\n\n  updateTotalUploadProgress() {\n    let totalUploadProgress;\n    let totalBytesSent = 0;\n    let totalBytes = 0;\n\n    let activeFiles = this.getActiveFiles();\n\n    if (activeFiles.length) {\n      for (let file of this.getActiveFiles()) {\n        totalBytesSent += file.upload.bytesSent;\n        totalBytes += file.upload.total;\n      }\n      totalUploadProgress = (100 * totalBytesSent) / totalBytes;\n    } else {\n      totalUploadProgress = 100;\n    }\n\n    return this.emit(\n      \"totaluploadprogress\",\n      totalUploadProgress,\n      totalBytes,\n      totalBytesSent\n    );\n  }\n\n  // @options.paramName can be a function taking one parameter rather than a string.\n  // A parameter name for a file is obtained simply by calling this with an index number.\n  _getParamName(n) {\n    if (typeof this.options.paramName === \"function\") {\n      return this.options.paramName(n);\n    } else {\n      return `${this.options.paramName}${\n        this.options.uploadMultiple ? `[${n}]` : \"\"\n      }`;\n    }\n  }\n\n  // If @options.renameFile is a function,\n  // the function will be used to rename the file.name before appending it to the formData\n  _renameFile(file) {\n    if (typeof this.options.renameFile !== \"function\") {\n      return file.name;\n    }\n    return this.options.renameFile(file);\n  }\n\n  // Returns a form that can be used as fallback if the browser does not support DragnDrop\n  //\n  // If the dropzone is already a form, only the input field and button are returned. Otherwise a complete form element is provided.\n  // This code has to pass in IE7 :(\n  getFallbackForm() {\n    let existingFallback, form;\n    if ((existingFallback = this.getExistingFallback())) {\n      return existingFallback;\n    }\n\n    let fieldsString = '<div class=\"dz-fallback\">';\n    if (this.options.dictFallbackText) {\n      fieldsString += `<p>${this.options.dictFallbackText}</p>`;\n    }\n    fieldsString += `<input type=\"file\" name=\"${this._getParamName(0)}\" ${\n      this.options.uploadMultiple ? 'multiple=\"multiple\"' : undefined\n    } /><input type=\"submit\" value=\"Upload!\"></div>`;\n\n    let fields = Dropzone.createElement(fieldsString);\n    if (this.element.tagName !== \"FORM\") {\n      form = Dropzone.createElement(\n        `<form action=\"${this.options.url}\" enctype=\"multipart/form-data\" method=\"${this.options.method}\"></form>`\n      );\n      form.appendChild(fields);\n    } else {\n      // Make sure that the enctype and method attributes are set properly\n      this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n      this.element.setAttribute(\"method\", this.options.method);\n    }\n    return form != null ? form : fields;\n  }\n\n  // Returns the fallback elements if they exist already\n  //\n  // This code has to pass in IE7 :(\n  getExistingFallback() {\n    let getFallback = function (elements) {\n      for (let el of elements) {\n        if (/(^| )fallback($| )/.test(el.className)) {\n          return el;\n        }\n      }\n    };\n\n    for (let tagName of [\"div\", \"form\"]) {\n      var fallback;\n      if (\n        (fallback = getFallback(this.element.getElementsByTagName(tagName)))\n      ) {\n        return fallback;\n      }\n    }\n  }\n\n  // Activates all listeners stored in @listeners\n  setupEventListeners() {\n    return this.listeners.map((elementListeners) =>\n      (() => {\n        let result = [];\n        for (let event in elementListeners.events) {\n          let listener = elementListeners.events[event];\n          result.push(\n            elementListeners.element.addEventListener(event, listener, false)\n          );\n        }\n        return result;\n      })()\n    );\n  }\n\n  // Deactivates all listeners stored in @listeners\n  removeEventListeners() {\n    return this.listeners.map((elementListeners) =>\n      (() => {\n        let result = [];\n        for (let event in elementListeners.events) {\n          let listener = elementListeners.events[event];\n          result.push(\n            elementListeners.element.removeEventListener(event, listener, false)\n          );\n        }\n        return result;\n      })()\n    );\n  }\n\n  // Removes all event listeners and cancels all files in the queue or being processed.\n  disable() {\n    this.clickableElements.forEach((element) =>\n      element.classList.remove(\"dz-clickable\")\n    );\n    this.removeEventListeners();\n    this.disabled = true;\n\n    return this.files.map((file) => this.cancelUpload(file));\n  }\n\n  enable() {\n    delete this.disabled;\n    this.clickableElements.forEach((element) =>\n      element.classList.add(\"dz-clickable\")\n    );\n    return this.setupEventListeners();\n  }\n\n  // Returns a nicely formatted filesize\n  filesize(size) {\n    let selectedSize = 0;\n    let selectedUnit = \"b\";\n\n    if (size > 0) {\n      let units = [\"tb\", \"gb\", \"mb\", \"kb\", \"b\"];\n\n      for (let i = 0; i < units.length; i++) {\n        let unit = units[i];\n        let cutoff = Math.pow(this.options.filesizeBase, 4 - i) / 10;\n\n        if (size >= cutoff) {\n          selectedSize = size / Math.pow(this.options.filesizeBase, 4 - i);\n          selectedUnit = unit;\n          break;\n        }\n      }\n\n      selectedSize = Math.round(10 * selectedSize) / 10; // Cutting of digits\n    }\n\n    return `<strong>${selectedSize}</strong> ${this.options.dictFileSizeUnits[selectedUnit]}`;\n  }\n\n  // Adds or removes the `dz-max-files-reached` class from the form.\n  _updateMaxFilesReachedClass() {\n    if (\n      this.options.maxFiles != null &&\n      this.getAcceptedFiles().length >= this.options.maxFiles\n    ) {\n      if (this.getAcceptedFiles().length === this.options.maxFiles) {\n        this.emit(\"maxfilesreached\", this.files);\n      }\n      return this.element.classList.add(\"dz-max-files-reached\");\n    } else {\n      return this.element.classList.remove(\"dz-max-files-reached\");\n    }\n  }\n\n  drop(e) {\n    if (!e.dataTransfer) {\n      return;\n    }\n    this.emit(\"drop\", e);\n\n    // Convert the FileList to an Array\n    // This is necessary for IE11\n    let files = [];\n    for (let i = 0; i < e.dataTransfer.files.length; i++) {\n      files[i] = e.dataTransfer.files[i];\n    }\n\n    // Even if it's a folder, files.length will contain the folders.\n    if (files.length) {\n      let { items } = e.dataTransfer;\n      if (items && items.length && items[0].webkitGetAsEntry != null) {\n        // The browser supports dropping of folders, so handle items instead of files\n        this._addFilesFromItems(items);\n      } else {\n        this.handleFiles(files);\n      }\n    }\n\n    this.emit(\"addedfiles\", files);\n  }\n\n  paste(e) {\n    if (\n      __guard__(e != null ? e.clipboardData : undefined, (x) => x.items) == null\n    ) {\n      return;\n    }\n\n    this.emit(\"paste\", e);\n    let { items } = e.clipboardData;\n\n    if (items.length) {\n      return this._addFilesFromItems(items);\n    }\n  }\n\n  handleFiles(files) {\n    for (let file of files) {\n      this.addFile(file);\n    }\n  }\n\n  // When a folder is dropped (or files are pasted), items must be handled\n  // instead of files.\n  _addFilesFromItems(items) {\n    return (() => {\n      let result = [];\n      for (let item of items) {\n        var entry;\n        if (\n          item.webkitGetAsEntry != null &&\n          (entry = item.webkitGetAsEntry())\n        ) {\n          if (entry.isFile) {\n            result.push(this.addFile(item.getAsFile()));\n          } else if (entry.isDirectory) {\n            // Append all files from that directory to files\n            result.push(this._addFilesFromDirectory(entry, entry.name));\n          } else {\n            result.push(undefined);\n          }\n        } else if (item.getAsFile != null) {\n          if (item.kind == null || item.kind === \"file\") {\n            result.push(this.addFile(item.getAsFile()));\n          } else {\n            result.push(undefined);\n          }\n        } else {\n          result.push(undefined);\n        }\n      }\n      return result;\n    })();\n  }\n\n  // Goes through the directory, and adds each file it finds recursively\n  _addFilesFromDirectory(directory, path) {\n    let dirReader = directory.createReader();\n\n    let errorHandler = (error) =>\n      __guardMethod__(console, \"log\", (o) => o.log(error));\n\n    var readEntries = () => {\n      return dirReader.readEntries((entries) => {\n        if (entries.length > 0) {\n          for (let entry of entries) {\n            if (entry.isFile) {\n              entry.file((file) => {\n                if (\n                  this.options.ignoreHiddenFiles &&\n                  file.name.substring(0, 1) === \".\"\n                ) {\n                  return;\n                }\n                file.fullPath = `${path}/${file.name}`;\n                return this.addFile(file);\n              });\n            } else if (entry.isDirectory) {\n              this._addFilesFromDirectory(entry, `${path}/${entry.name}`);\n            }\n          }\n\n          // Recursively call readEntries() again, since browser only handle\n          // the first 100 entries.\n          // See: https://developer.mozilla.org/en-US/docs/Web/API/DirectoryReader#readEntries\n          readEntries();\n        }\n        return null;\n      }, errorHandler);\n    };\n\n    return readEntries();\n  }\n\n  // If `done()` is called without argument the file is accepted\n  // If you call it with an error message, the file is rejected\n  // (This allows for asynchronous validation)\n  //\n  // This function checks the filesize, and if the file.type passes the\n  // `acceptedFiles` check.\n  accept(file, done) {\n    if (\n      this.options.maxFilesize &&\n      file.size > this.options.maxFilesize * 1024 * 1024\n    ) {\n      done(\n        this.options.dictFileTooBig\n          .replace(\"{{filesize}}\", Math.round(file.size / 1024 / 10.24) / 100)\n          .replace(\"{{maxFilesize}}\", this.options.maxFilesize)\n      );\n    } else if (!Dropzone.isValidFile(file, this.options.acceptedFiles)) {\n      done(this.options.dictInvalidFileType);\n    } else if (\n      this.options.maxFiles != null &&\n      this.getAcceptedFiles().length >= this.options.maxFiles\n    ) {\n      done(\n        this.options.dictMaxFilesExceeded.replace(\n          \"{{maxFiles}}\",\n          this.options.maxFiles\n        )\n      );\n      this.emit(\"maxfilesexceeded\", file);\n    } else {\n      this.options.accept.call(this, file, done);\n    }\n  }\n\n  addFile(file) {\n    file.upload = {\n      uuid: Dropzone.uuidv4(),\n      progress: 0,\n      // Setting the total upload size to file.size for the beginning\n      // It's actual different than the size to be transmitted.\n      total: file.size,\n      bytesSent: 0,\n      filename: this._renameFile(file),\n      // Not setting chunking information here, because the acutal data — and\n      // thus the chunks — might change if `options.transformFile` is set\n      // and does something to the data.\n    };\n    this.files.push(file);\n\n    file.status = Dropzone.ADDED;\n\n    this.emit(\"addedfile\", file);\n\n    this._enqueueThumbnail(file);\n\n    this.accept(file, (error) => {\n      if (error) {\n        file.accepted = false;\n        this._errorProcessing([file], error); // Will set the file.status\n      } else {\n        file.accepted = true;\n        if (this.options.autoQueue) {\n          this.enqueueFile(file);\n        } // Will set .accepted = true\n      }\n      this._updateMaxFilesReachedClass();\n    });\n  }\n\n  // Wrapper for enqueueFile\n  enqueueFiles(files) {\n    for (let file of files) {\n      this.enqueueFile(file);\n    }\n    return null;\n  }\n\n  enqueueFile(file) {\n    if (file.status === Dropzone.ADDED && file.accepted === true) {\n      file.status = Dropzone.QUEUED;\n      if (this.options.autoProcessQueue) {\n        return setTimeout(() => this.processQueue(), 0); // Deferring the call\n      }\n    } else {\n      throw new Error(\n        \"This file can't be queued because it has already been processed or was rejected.\"\n      );\n    }\n  }\n\n  _enqueueThumbnail(file) {\n    if (\n      this.options.createImageThumbnails &&\n      file.type.match(/image.*/) &&\n      file.size <= this.options.maxThumbnailFilesize * 1024 * 1024\n    ) {\n      this._thumbnailQueue.push(file);\n      return setTimeout(() => this._processThumbnailQueue(), 0); // Deferring the call\n    }\n  }\n\n  _processThumbnailQueue() {\n    if (this._processingThumbnail || this._thumbnailQueue.length === 0) {\n      return;\n    }\n\n    this._processingThumbnail = true;\n    let file = this._thumbnailQueue.shift();\n    return this.createThumbnail(\n      file,\n      this.options.thumbnailWidth,\n      this.options.thumbnailHeight,\n      this.options.thumbnailMethod,\n      true,\n      (dataUrl) => {\n        this.emit(\"thumbnail\", file, dataUrl);\n        this._processingThumbnail = false;\n        return this._processThumbnailQueue();\n      }\n    );\n  }\n\n  // Can be called by the user to remove a file\n  removeFile(file) {\n    if (file.status === Dropzone.UPLOADING) {\n      this.cancelUpload(file);\n    }\n    this.files = without(this.files, file);\n\n    this.emit(\"removedfile\", file);\n    if (this.files.length === 0) {\n      return this.emit(\"reset\");\n    }\n  }\n\n  // Removes all files that aren't currently processed from the list\n  removeAllFiles(cancelIfNecessary) {\n    // Create a copy of files since removeFile() changes the @files array.\n    if (cancelIfNecessary == null) {\n      cancelIfNecessary = false;\n    }\n    for (let file of this.files.slice()) {\n      if (file.status !== Dropzone.UPLOADING || cancelIfNecessary) {\n        this.removeFile(file);\n      }\n    }\n    return null;\n  }\n\n  // Resizes an image before it gets sent to the server. This function is the default behavior of\n  // `options.transformFile` if `resizeWidth` or `resizeHeight` are set. The callback is invoked with\n  // the resized blob.\n  resizeImage(file, width, height, resizeMethod, callback) {\n    return this.createThumbnail(\n      file,\n      width,\n      height,\n      resizeMethod,\n      true,\n      (dataUrl, canvas) => {\n        if (canvas == null) {\n          // The image has not been resized\n          return callback(file);\n        } else {\n          let { resizeMimeType } = this.options;\n          if (resizeMimeType == null) {\n            resizeMimeType = file.type;\n          }\n          let resizedDataURL = canvas.toDataURL(\n            resizeMimeType,\n            this.options.resizeQuality\n          );\n          if (\n            resizeMimeType === \"image/jpeg\" ||\n            resizeMimeType === \"image/jpg\"\n          ) {\n            // Now add the original EXIF information\n            resizedDataURL = ExifRestore.restore(file.dataURL, resizedDataURL);\n          }\n          return callback(Dropzone.dataURItoBlob(resizedDataURL));\n        }\n      }\n    );\n  }\n\n  createThumbnail(file, width, height, resizeMethod, fixOrientation, callback) {\n    let fileReader = new FileReader();\n\n    fileReader.onload = () => {\n      file.dataURL = fileReader.result;\n\n      // Don't bother creating a thumbnail for SVG images since they're vector\n      if (file.type === \"image/svg+xml\") {\n        if (callback != null) {\n          callback(fileReader.result);\n        }\n        return;\n      }\n\n      this.createThumbnailFromUrl(\n        file,\n        width,\n        height,\n        resizeMethod,\n        fixOrientation,\n        callback\n      );\n    };\n\n    fileReader.readAsDataURL(file);\n  }\n\n  // `mockFile` needs to have these attributes:\n  //\n  //     { name: 'name', size: 12345, imageUrl: '' }\n  //\n  // `callback` will be invoked when the image has been downloaded and displayed.\n  // `crossOrigin` will be added to the `img` tag when accessing the file.\n  displayExistingFile(\n    mockFile,\n    imageUrl,\n    callback,\n    crossOrigin,\n    resizeThumbnail = true\n  ) {\n    this.emit(\"addedfile\", mockFile);\n    this.emit(\"complete\", mockFile);\n\n    if (!resizeThumbnail) {\n      this.emit(\"thumbnail\", mockFile, imageUrl);\n      if (callback) callback();\n    } else {\n      let onDone = (thumbnail) => {\n        this.emit(\"thumbnail\", mockFile, thumbnail);\n        if (callback) callback();\n      };\n      mockFile.dataURL = imageUrl;\n\n      this.createThumbnailFromUrl(\n        mockFile,\n        this.options.thumbnailWidth,\n        this.options.thumbnailHeight,\n        this.options.thumbnailMethod,\n        this.options.fixOrientation,\n        onDone,\n        crossOrigin\n      );\n    }\n  }\n\n  createThumbnailFromUrl(\n    file,\n    width,\n    height,\n    resizeMethod,\n    fixOrientation,\n    callback,\n    crossOrigin\n  ) {\n    // Not using `new Image` here because of a bug in latest Chrome versions.\n    // See https://github.com/enyo/dropzone/pull/226\n    let img = document.createElement(\"img\");\n\n    if (crossOrigin) {\n      img.crossOrigin = crossOrigin;\n    }\n\n    // fixOrientation is not needed anymore with browsers handling imageOrientation\n    fixOrientation =\n      getComputedStyle(document.body)[\"imageOrientation\"] == \"from-image\"\n        ? false\n        : fixOrientation;\n\n    img.onload = () => {\n      let loadExif = (callback) => callback(1);\n      if (typeof EXIF !== \"undefined\" && EXIF !== null && fixOrientation) {\n        loadExif = (callback) =>\n          EXIF.getData(img, function () {\n            return callback(EXIF.getTag(this, \"Orientation\"));\n          });\n      }\n\n      return loadExif((orientation) => {\n        file.width = img.width;\n        file.height = img.height;\n\n        let resizeInfo = this.options.resize.call(\n          this,\n          file,\n          width,\n          height,\n          resizeMethod\n        );\n\n        let canvas = document.createElement(\"canvas\");\n        let ctx = canvas.getContext(\"2d\");\n\n        canvas.width = resizeInfo.trgWidth;\n        canvas.height = resizeInfo.trgHeight;\n\n        if (orientation > 4) {\n          canvas.width = resizeInfo.trgHeight;\n          canvas.height = resizeInfo.trgWidth;\n        }\n\n        switch (orientation) {\n          case 2:\n            // horizontal flip\n            ctx.translate(canvas.width, 0);\n            ctx.scale(-1, 1);\n            break;\n          case 3:\n            // 180° rotate left\n            ctx.translate(canvas.width, canvas.height);\n            ctx.rotate(Math.PI);\n            break;\n          case 4:\n            // vertical flip\n            ctx.translate(0, canvas.height);\n            ctx.scale(1, -1);\n            break;\n          case 5:\n            // vertical flip + 90 rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.scale(1, -1);\n            break;\n          case 6:\n            // 90° rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.translate(0, -canvas.width);\n            break;\n          case 7:\n            // horizontal flip + 90 rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.translate(canvas.height, -canvas.width);\n            ctx.scale(-1, 1);\n            break;\n          case 8:\n            // 90° rotate left\n            ctx.rotate(-0.5 * Math.PI);\n            ctx.translate(-canvas.height, 0);\n            break;\n        }\n\n        // This is a bugfix for iOS' scaling bug.\n        drawImageIOSFix(\n          ctx,\n          img,\n          resizeInfo.srcX != null ? resizeInfo.srcX : 0,\n          resizeInfo.srcY != null ? resizeInfo.srcY : 0,\n          resizeInfo.srcWidth,\n          resizeInfo.srcHeight,\n          resizeInfo.trgX != null ? resizeInfo.trgX : 0,\n          resizeInfo.trgY != null ? resizeInfo.trgY : 0,\n          resizeInfo.trgWidth,\n          resizeInfo.trgHeight\n        );\n\n        let thumbnail = canvas.toDataURL(\"image/png\");\n\n        if (callback != null) {\n          return callback(thumbnail, canvas);\n        }\n      });\n    };\n\n    if (callback != null) {\n      img.onerror = callback;\n    }\n\n    return (img.src = file.dataURL);\n  }\n\n  // Goes through the queue and processes files if there aren't too many already.\n  processQueue() {\n    let { parallelUploads } = this.options;\n    let processingLength = this.getUploadingFiles().length;\n    let i = processingLength;\n\n    // There are already at least as many files uploading than should be\n    if (processingLength >= parallelUploads) {\n      return;\n    }\n\n    let queuedFiles = this.getQueuedFiles();\n\n    if (!(queuedFiles.length > 0)) {\n      return;\n    }\n\n    if (this.options.uploadMultiple) {\n      // The files should be uploaded in one request\n      return this.processFiles(\n        queuedFiles.slice(0, parallelUploads - processingLength)\n      );\n    } else {\n      while (i < parallelUploads) {\n        if (!queuedFiles.length) {\n          return;\n        } // Nothing left to process\n        this.processFile(queuedFiles.shift());\n        i++;\n      }\n    }\n  }\n\n  // Wrapper for `processFiles`\n  processFile(file) {\n    return this.processFiles([file]);\n  }\n\n  // Loads the file, then calls finishedLoading()\n  processFiles(files) {\n    for (let file of files) {\n      file.processing = true; // Backwards compatibility\n      file.status = Dropzone.UPLOADING;\n\n      this.emit(\"processing\", file);\n    }\n\n    if (this.options.uploadMultiple) {\n      this.emit(\"processingmultiple\", files);\n    }\n\n    return this.uploadFiles(files);\n  }\n\n  _getFilesWithXhr(xhr) {\n    let files;\n    return (files = this.files\n      .filter((file) => file.xhr === xhr)\n      .map((file) => file));\n  }\n\n  // Cancels the file upload and sets the status to CANCELED\n  // **if** the file is actually being uploaded.\n  // If it's still in the queue, the file is being removed from it and the status\n  // set to CANCELED.\n  cancelUpload(file) {\n    if (file.status === Dropzone.UPLOADING) {\n      let groupedFiles = this._getFilesWithXhr(file.xhr);\n      for (let groupedFile of groupedFiles) {\n        groupedFile.status = Dropzone.CANCELED;\n      }\n      if (typeof file.xhr !== \"undefined\") {\n        file.xhr.abort();\n      }\n      for (let groupedFile of groupedFiles) {\n        this.emit(\"canceled\", groupedFile);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"canceledmultiple\", groupedFiles);\n      }\n    } else if (\n      file.status === Dropzone.ADDED ||\n      file.status === Dropzone.QUEUED\n    ) {\n      file.status = Dropzone.CANCELED;\n      this.emit(\"canceled\", file);\n      if (this.options.uploadMultiple) {\n        this.emit(\"canceledmultiple\", [file]);\n      }\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  resolveOption(option, ...args) {\n    if (typeof option === \"function\") {\n      return option.apply(this, args);\n    }\n    return option;\n  }\n\n  uploadFile(file) {\n    return this.uploadFiles([file]);\n  }\n\n  uploadFiles(files) {\n    this._transformFiles(files, (transformedFiles) => {\n      if (this.options.chunking) {\n        // Chunking is not allowed to be used with `uploadMultiple` so we know\n        // that there is only __one__file.\n        let transformedFile = transformedFiles[0];\n        files[0].upload.chunked =\n          this.options.chunking &&\n          (this.options.forceChunking ||\n            transformedFile.size > this.options.chunkSize);\n        files[0].upload.totalChunkCount = Math.ceil(\n          transformedFile.size / this.options.chunkSize\n        );\n      }\n\n      if (files[0].upload.chunked) {\n        // This file should be sent in chunks!\n\n        // If the chunking option is set, we **know** that there can only be **one** file, since\n        // uploadMultiple is not allowed with this option.\n        let file = files[0];\n        let transformedFile = transformedFiles[0];\n        let startedChunkCount = 0;\n\n        file.upload.chunks = [];\n\n        let handleNextChunk = () => {\n          let chunkIndex = 0;\n\n          // Find the next item in file.upload.chunks that is not defined yet.\n          while (file.upload.chunks[chunkIndex] !== undefined) {\n            chunkIndex++;\n          }\n\n          // This means, that all chunks have already been started.\n          if (chunkIndex >= file.upload.totalChunkCount) return;\n\n          startedChunkCount++;\n\n          let start = chunkIndex * this.options.chunkSize;\n          let end = Math.min(\n            start + this.options.chunkSize,\n            transformedFile.size\n          );\n\n          let dataBlock = {\n            name: this._getParamName(0),\n            data: transformedFile.webkitSlice\n              ? transformedFile.webkitSlice(start, end)\n              : transformedFile.slice(start, end),\n            filename: file.upload.filename,\n            chunkIndex: chunkIndex,\n          };\n\n          file.upload.chunks[chunkIndex] = {\n            file: file,\n            index: chunkIndex,\n            dataBlock: dataBlock, // In case we want to retry.\n            status: Dropzone.UPLOADING,\n            progress: 0,\n            retries: 0, // The number of times this block has been retried.\n          };\n\n          this._uploadData(files, [dataBlock]);\n        };\n\n        file.upload.finishedChunkUpload = (chunk, response) => {\n          let allFinished = true;\n          chunk.status = Dropzone.SUCCESS;\n\n          // Clear the data from the chunk\n          chunk.dataBlock = null;\n          chunk.response = chunk.xhr.responseText;\n          chunk.responseHeaders = chunk.xhr.getAllResponseHeaders();\n          // Leaving this reference to xhr will cause memory leaks.\n          chunk.xhr = null;\n\n          for (let i = 0; i < file.upload.totalChunkCount; i++) {\n            if (file.upload.chunks[i] === undefined) {\n              return handleNextChunk();\n            }\n            if (file.upload.chunks[i].status !== Dropzone.SUCCESS) {\n              allFinished = false;\n            }\n          }\n\n          if (allFinished) {\n            this.options.chunksUploaded(file, () => {\n              this._finished(files, response, null);\n            });\n          }\n        };\n\n        if (this.options.parallelChunkUploads) {\n          for (let i = 0; i < file.upload.totalChunkCount; i++) {\n            handleNextChunk();\n          }\n        } else {\n          handleNextChunk();\n        }\n      } else {\n        let dataBlocks = [];\n        for (let i = 0; i < files.length; i++) {\n          dataBlocks[i] = {\n            name: this._getParamName(i),\n            data: transformedFiles[i],\n            filename: files[i].upload.filename,\n          };\n        }\n        this._uploadData(files, dataBlocks);\n      }\n    });\n  }\n\n  /// Returns the right chunk for given file and xhr\n  _getChunk(file, xhr) {\n    for (let i = 0; i < file.upload.totalChunkCount; i++) {\n      if (\n        file.upload.chunks[i] !== undefined &&\n        file.upload.chunks[i].xhr === xhr\n      ) {\n        return file.upload.chunks[i];\n      }\n    }\n  }\n\n  // This function actually uploads the file(s) to the server.\n  //\n  //  If dataBlocks contains the actual data to upload (meaning, that this could\n  // either be transformed files, or individual chunks for chunked upload) then\n  // they will be used for the actual data to upload.\n  _uploadData(files, dataBlocks) {\n    let xhr = new XMLHttpRequest();\n\n    // Put the xhr object in the file objects to be able to reference it later.\n    for (let file of files) {\n      file.xhr = xhr;\n    }\n    if (files[0].upload.chunked) {\n      // Put the xhr object in the right chunk object, so it can be associated\n      // later, and found with _getChunk.\n      files[0].upload.chunks[dataBlocks[0].chunkIndex].xhr = xhr;\n    }\n\n    let method = this.resolveOption(this.options.method, files, dataBlocks);\n    let url = this.resolveOption(this.options.url, files, dataBlocks);\n    xhr.open(method, url, true);\n\n    // Setting the timeout after open because of IE11 issue: https://gitlab.com/meno/dropzone/issues/8\n    let timeout = this.resolveOption(this.options.timeout, files);\n    if (timeout) xhr.timeout = this.resolveOption(this.options.timeout, files);\n\n    // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n    xhr.withCredentials = !!this.options.withCredentials;\n\n    xhr.onload = (e) => {\n      this._finishedUploading(files, xhr, e);\n    };\n\n    xhr.ontimeout = () => {\n      this._handleUploadError(\n        files,\n        xhr,\n        `Request timedout after ${this.options.timeout / 1000} seconds`\n      );\n    };\n\n    xhr.onerror = () => {\n      this._handleUploadError(files, xhr);\n    };\n\n    // Some browsers do not have the .upload property\n    let progressObj = xhr.upload != null ? xhr.upload : xhr;\n    progressObj.onprogress = (e) =>\n      this._updateFilesUploadProgress(files, xhr, e);\n\n    let headers = this.options.defaultHeaders\n      ? {\n          Accept: \"application/json\",\n          \"Cache-Control\": \"no-cache\",\n          \"X-Requested-With\": \"XMLHttpRequest\",\n        }\n      : {};\n\n    if (this.options.binaryBody) {\n      headers[\"Content-Type\"] = files[0].type;\n    }\n\n    if (this.options.headers) {\n      extend(headers, this.options.headers);\n    }\n\n    for (let headerName in headers) {\n      let headerValue = headers[headerName];\n      if (headerValue) {\n        xhr.setRequestHeader(headerName, headerValue);\n      }\n    }\n\n    if (this.options.binaryBody) {\n      // Since the file is going to be sent as binary body, it doesn't make\n      // any sense to generate `FormData` for it.\n      for (let file of files) {\n        this.emit(\"sending\", file, xhr);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"sendingmultiple\", files, xhr);\n      }\n      this.submitRequest(xhr, null, files);\n    } else {\n      let formData = new FormData();\n\n      // Adding all @options parameters\n      if (this.options.params) {\n        let additionalParams = this.options.params;\n        if (typeof additionalParams === \"function\") {\n          additionalParams = additionalParams.call(\n            this,\n            files,\n            xhr,\n            files[0].upload.chunked ? this._getChunk(files[0], xhr) : null\n          );\n        }\n\n        for (let key in additionalParams) {\n          let value = additionalParams[key];\n          if (Array.isArray(value)) {\n            // The additional parameter contains an array,\n            // so lets iterate over it to attach each value\n            // individually.\n            for (let i = 0; i < value.length; i++) {\n              formData.append(key, value[i]);\n            }\n          } else {\n            formData.append(key, value);\n          }\n        }\n      }\n\n      // Let the user add additional data if necessary\n      for (let file of files) {\n        this.emit(\"sending\", file, xhr, formData);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"sendingmultiple\", files, xhr, formData);\n      }\n\n      this._addFormElementData(formData);\n\n      // Finally add the files\n      // Has to be last because some servers (eg: S3) expect the file to be the last parameter\n      for (let i = 0; i < dataBlocks.length; i++) {\n        let dataBlock = dataBlocks[i];\n        formData.append(dataBlock.name, dataBlock.data, dataBlock.filename);\n      }\n\n      this.submitRequest(xhr, formData, files);\n    }\n  }\n\n  // Transforms all files with this.options.transformFile and invokes done with the transformed files when done.\n  _transformFiles(files, done) {\n    let transformedFiles = [];\n    // Clumsy way of handling asynchronous calls, until I get to add a proper Future library.\n    let doneCounter = 0;\n    for (let i = 0; i < files.length; i++) {\n      this.options.transformFile.call(this, files[i], (transformedFile) => {\n        transformedFiles[i] = transformedFile;\n        if (++doneCounter === files.length) {\n          done(transformedFiles);\n        }\n      });\n    }\n  }\n\n  // Takes care of adding other input elements of the form to the AJAX request\n  _addFormElementData(formData) {\n    // Take care of other input elements\n    if (this.element.tagName === \"FORM\") {\n      for (let input of this.element.querySelectorAll(\n        \"input, textarea, select, button\"\n      )) {\n        let inputName = input.getAttribute(\"name\");\n        let inputType = input.getAttribute(\"type\");\n        if (inputType) inputType = inputType.toLowerCase();\n\n        // If the input doesn't have a name, we can't use it.\n        if (typeof inputName === \"undefined\" || inputName === null) continue;\n\n        if (input.tagName === \"SELECT\" && input.hasAttribute(\"multiple\")) {\n          // Possibly multiple values\n          for (let option of input.options) {\n            if (option.selected) {\n              formData.append(inputName, option.value);\n            }\n          }\n        } else if (\n          !inputType ||\n          (inputType !== \"checkbox\" && inputType !== \"radio\") ||\n          input.checked\n        ) {\n          formData.append(inputName, input.value);\n        }\n      }\n    }\n  }\n\n  // Invoked when there is new progress information about given files.\n  // If e is not provided, it is assumed that the upload is finished.\n  _updateFilesUploadProgress(files, xhr, e) {\n    if (!files[0].upload.chunked) {\n      // Handle file uploads without chunking\n      for (let file of files) {\n        if (\n          file.upload.total &&\n          file.upload.bytesSent &&\n          file.upload.bytesSent == file.upload.total\n        ) {\n          // If both, the `total` and `bytesSent` have already been set, and\n          // they are equal (meaning progress is at 100%), we can skip this\n          // file, since an upload progress shouldn't go down.\n          continue;\n        }\n\n        if (e) {\n          file.upload.progress = (100 * e.loaded) / e.total;\n          file.upload.total = e.total;\n          file.upload.bytesSent = e.loaded;\n        } else {\n          // No event, so we're at 100%\n          file.upload.progress = 100;\n          file.upload.bytesSent = file.upload.total;\n        }\n\n        this.emit(\n          \"uploadprogress\",\n          file,\n          file.upload.progress,\n          file.upload.bytesSent\n        );\n      }\n    } else {\n      // Handle chunked file uploads\n\n      // Chunked upload is not compatible with uploading multiple files in one\n      // request, so we know there's only one file.\n      let file = files[0];\n\n      // Since this is a chunked upload, we need to update the appropriate chunk\n      // progress.\n      let chunk = this._getChunk(file, xhr);\n\n      if (e) {\n        chunk.progress = (100 * e.loaded) / e.total;\n        chunk.total = e.total;\n        chunk.bytesSent = e.loaded;\n      } else {\n        // No event, so we're at 100%\n        chunk.progress = 100;\n        chunk.bytesSent = chunk.total;\n      }\n\n      // Now tally the *file* upload progress from its individual chunks\n      file.upload.progress = 0;\n      file.upload.total = 0;\n      file.upload.bytesSent = 0;\n      for (let i = 0; i < file.upload.totalChunkCount; i++) {\n        if (\n          file.upload.chunks[i] &&\n          typeof file.upload.chunks[i].progress !== \"undefined\"\n        ) {\n          file.upload.progress += file.upload.chunks[i].progress;\n          file.upload.total += file.upload.chunks[i].total;\n          file.upload.bytesSent += file.upload.chunks[i].bytesSent;\n        }\n      }\n      // Since the process is a percentage, we need to divide by the amount of\n      // chunks we've used.\n      file.upload.progress = file.upload.progress / file.upload.totalChunkCount;\n\n      this.emit(\n        \"uploadprogress\",\n        file,\n        file.upload.progress,\n        file.upload.bytesSent\n      );\n    }\n  }\n\n  _finishedUploading(files, xhr, e) {\n    let response;\n\n    if (files[0].status === Dropzone.CANCELED) {\n      return;\n    }\n\n    if (xhr.readyState !== 4) {\n      return;\n    }\n\n    if (xhr.responseType !== \"arraybuffer\" && xhr.responseType !== \"blob\") {\n      response = xhr.responseText;\n\n      if (\n        xhr.getResponseHeader(\"content-type\") &&\n        ~xhr.getResponseHeader(\"content-type\").indexOf(\"application/json\")\n      ) {\n        try {\n          response = JSON.parse(response);\n        } catch (error) {\n          e = error;\n          response = \"Invalid JSON response from server.\";\n        }\n      }\n    }\n\n    this._updateFilesUploadProgress(files, xhr);\n\n    if (!(200 <= xhr.status && xhr.status < 300)) {\n      this._handleUploadError(files, xhr, response);\n    } else {\n      if (files[0].upload.chunked) {\n        files[0].upload.finishedChunkUpload(\n          this._getChunk(files[0], xhr),\n          response\n        );\n      } else {\n        this._finished(files, response, e);\n      }\n    }\n  }\n\n  _handleUploadError(files, xhr, response) {\n    if (files[0].status === Dropzone.CANCELED) {\n      return;\n    }\n\n    if (files[0].upload.chunked && this.options.retryChunks) {\n      let chunk = this._getChunk(files[0], xhr);\n      if (chunk.retries++ < this.options.retryChunksLimit) {\n        this._uploadData(files, [chunk.dataBlock]);\n        return;\n      } else {\n        console.warn(\"Retried this chunk too often. Giving up.\");\n      }\n    }\n\n    this._errorProcessing(\n      files,\n      response ||\n        this.options.dictResponseError.replace(\"{{statusCode}}\", xhr.status),\n      xhr\n    );\n  }\n\n  submitRequest(xhr, formData, files) {\n    if (xhr.readyState != 1) {\n      console.warn(\n        \"Cannot send this request because the XMLHttpRequest.readyState is not OPENED.\"\n      );\n      return;\n    }\n    if (this.options.binaryBody) {\n      if (files[0].upload.chunked) {\n        const chunk = this._getChunk(files[0], xhr);\n        xhr.send(chunk.dataBlock.data);\n      } else {\n        xhr.send(files[0]);\n      }\n    } else {\n      xhr.send(formData);\n    }\n  }\n\n  // Called internally when processing is finished.\n  // Individual callbacks have to be called in the appropriate sections.\n  _finished(files, responseText, e) {\n    for (let file of files) {\n      file.status = Dropzone.SUCCESS;\n      this.emit(\"success\", file, responseText, e);\n      this.emit(\"complete\", file);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"successmultiple\", files, responseText, e);\n      this.emit(\"completemultiple\", files);\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  // Called internally when processing is finished.\n  // Individual callbacks have to be called in the appropriate sections.\n  _errorProcessing(files, message, xhr) {\n    for (let file of files) {\n      file.status = Dropzone.ERROR;\n      this.emit(\"error\", file, message, xhr);\n      this.emit(\"complete\", file);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"errormultiple\", files, message, xhr);\n      this.emit(\"completemultiple\", files);\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  static uuidv4() {\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(\n      /[xy]/g,\n      function (c) {\n        let r = (Math.random() * 16) | 0,\n          v = c === \"x\" ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n      }\n    );\n  }\n}\nDropzone.initClass();\n\n// This is a map of options for your different dropzones. Add configurations\n// to this object for your different dropzone elemens.\n//\n// Example:\n//\n//     Dropzone.options.myDropzoneElementId = { maxFilesize: 1 };\n//\n// And in html:\n//\n//     <form action=\"/upload\" id=\"my-dropzone-element-id\" class=\"dropzone\"></form>\nDropzone.options = {};\n\n// Returns the options for an element or undefined if none available.\nDropzone.optionsForElement = function (element) {\n  // Get the `Dropzone.options.elementId` for this element if it exists\n  if (element.getAttribute(\"id\")) {\n    return Dropzone.options[camelize(element.getAttribute(\"id\"))];\n  } else {\n    return undefined;\n  }\n};\n\n// Holds a list of all dropzone instances\nDropzone.instances = [];\n\n// Returns the dropzone for given element if any\nDropzone.forElement = function (element) {\n  if (typeof element === \"string\") {\n    element = document.querySelector(element);\n  }\n  if ((element != null ? element.dropzone : undefined) == null) {\n    throw new Error(\n      \"No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.\"\n    );\n  }\n  return element.dropzone;\n};\n\n// Looks for all .dropzone elements and creates a dropzone for them\nDropzone.discover = function () {\n  let dropzones;\n  if (document.querySelectorAll) {\n    dropzones = document.querySelectorAll(\".dropzone\");\n  } else {\n    dropzones = [];\n    // IE :(\n    let checkElements = (elements) =>\n      (() => {\n        let result = [];\n        for (let el of elements) {\n          if (/(^| )dropzone($| )/.test(el.className)) {\n            result.push(dropzones.push(el));\n          } else {\n            result.push(undefined);\n          }\n        }\n        return result;\n      })();\n    checkElements(document.getElementsByTagName(\"div\"));\n    checkElements(document.getElementsByTagName(\"form\"));\n  }\n\n  return (() => {\n    let result = [];\n    for (let dropzone of dropzones) {\n      // Create a dropzone unless auto discover has been disabled for specific element\n      if (Dropzone.optionsForElement(dropzone) !== false) {\n        result.push(new Dropzone(dropzone));\n      } else {\n        result.push(undefined);\n      }\n    }\n    return result;\n  })();\n};\n\n// Some browsers support drag and drog functionality, but not correctly.\n//\n// So I created a blocklist of userAgents. Yes, yes. Browser sniffing, I know.\n// But what to do when browsers *theoretically* support an API, but crash\n// when using it.\n//\n// This is a list of regular expressions tested against navigator.userAgent\n//\n// ** It should only be used on browser that *do* support the API, but\n// incorrectly **\nDropzone.blockedBrowsers = [\n  // The mac os and windows phone version of opera 12 seems to have a problem with the File drag'n'drop API.\n  /opera.*(Macintosh|Windows Phone).*version\\/12/i,\n];\n\n// Checks if the browser is supported\nDropzone.isBrowserSupported = function () {\n  let capableBrowser = true;\n\n  if (\n    window.File &&\n    window.FileReader &&\n    window.FileList &&\n    window.Blob &&\n    window.FormData &&\n    document.querySelector\n  ) {\n    if (!(\"classList\" in document.createElement(\"a\"))) {\n      capableBrowser = false;\n    } else {\n      if (Dropzone.blacklistedBrowsers !== undefined) {\n        // Since this has been renamed, this makes sure we don't break older\n        // configuration.\n        Dropzone.blockedBrowsers = Dropzone.blacklistedBrowsers;\n      }\n      // The browser supports the API, but may be blocked.\n      for (let regex of Dropzone.blockedBrowsers) {\n        if (regex.test(navigator.userAgent)) {\n          capableBrowser = false;\n          continue;\n        }\n      }\n    }\n  } else {\n    capableBrowser = false;\n  }\n\n  return capableBrowser;\n};\n\nDropzone.dataURItoBlob = function (dataURI) {\n  // convert base64 to raw binary data held in a string\n  // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this\n  let byteString = atob(dataURI.split(\",\")[1]);\n\n  // separate out the mime component\n  let mimeString = dataURI.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\n  // write the bytes of the string to an ArrayBuffer\n  let ab = new ArrayBuffer(byteString.length);\n  let ia = new Uint8Array(ab);\n  for (\n    let i = 0, end = byteString.length, asc = 0 <= end;\n    asc ? i <= end : i >= end;\n    asc ? i++ : i--\n  ) {\n    ia[i] = byteString.charCodeAt(i);\n  }\n\n  // write the ArrayBuffer to a blob\n  return new Blob([ab], { type: mimeString });\n};\n\n// Returns an array without the rejected item\nconst without = (list, rejectedItem) =>\n  list.filter((item) => item !== rejectedItem).map((item) => item);\n\n// abc-def_ghi -> abcDefGhi\nconst camelize = (str) =>\n  str.replace(/[\\-_](\\w)/g, (match) => match.charAt(1).toUpperCase());\n\n// Creates an element from string\nDropzone.createElement = function (string) {\n  let div = document.createElement(\"div\");\n  div.innerHTML = string;\n  return div.childNodes[0];\n};\n\n// Tests if given element is inside (or simply is) the container\nDropzone.elementInside = function (element, container) {\n  if (element === container) {\n    return true;\n  } // Coffeescript doesn't support do/while loops\n  while ((element = element.parentNode)) {\n    if (element === container) {\n      return true;\n    }\n  }\n  return false;\n};\n\nDropzone.getElement = function (el, name) {\n  let element;\n  if (typeof el === \"string\") {\n    element = document.querySelector(el);\n  } else if (el.nodeType != null) {\n    element = el;\n  }\n  if (element == null) {\n    throw new Error(\n      `Invalid \\`${name}\\` option provided. Please provide a CSS selector or a plain HTML element.`\n    );\n  }\n  return element;\n};\n\nDropzone.getElements = function (els, name) {\n  let el, elements;\n  if (els instanceof Array) {\n    elements = [];\n    try {\n      for (el of els) {\n        elements.push(this.getElement(el, name));\n      }\n    } catch (e) {\n      elements = null;\n    }\n  } else if (typeof els === \"string\") {\n    elements = [];\n    for (el of document.querySelectorAll(els)) {\n      elements.push(el);\n    }\n  } else if (els.nodeType != null) {\n    elements = [els];\n  }\n\n  if (elements == null || !elements.length) {\n    throw new Error(\n      `Invalid \\`${name}\\` option provided. Please provide a CSS selector, a plain HTML element or a list of those.`\n    );\n  }\n\n  return elements;\n};\n\n// Asks the user the question and calls accepted or rejected accordingly\n//\n// The default implementation just uses `window.confirm` and then calls the\n// appropriate callback.\nDropzone.confirm = function (question, accepted, rejected) {\n  if (window.confirm(question)) {\n    return accepted();\n  } else if (rejected != null) {\n    return rejected();\n  }\n};\n\n// Validates the mime type like this:\n//\n// https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept\nDropzone.isValidFile = function (file, acceptedFiles) {\n  if (!acceptedFiles) {\n    return true;\n  } // If there are no accepted mime types, it's OK\n  acceptedFiles = acceptedFiles.split(\",\");\n\n  let mimeType = file.type;\n  let baseMimeType = mimeType.replace(/\\/.*$/, \"\");\n\n  for (let validType of acceptedFiles) {\n    validType = validType.trim();\n    if (validType.charAt(0) === \".\") {\n      if (\n        file.name\n          .toLowerCase()\n          .indexOf(\n            validType.toLowerCase(),\n            file.name.length - validType.length\n          ) !== -1\n      ) {\n        return true;\n      }\n    } else if (/\\/\\*$/.test(validType)) {\n      // This is something like a image/* mime type\n      if (baseMimeType === validType.replace(/\\/.*$/, \"\")) {\n        return true;\n      }\n    } else {\n      if (mimeType === validType) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n};\n\n// Augment jQuery\nif (typeof jQuery !== \"undefined\" && jQuery !== null) {\n  jQuery.fn.dropzone = function (options) {\n    return this.each(function () {\n      return new Dropzone(this, options);\n    });\n  };\n}\n\n// Dropzone file status codes\nDropzone.ADDED = \"added\";\n\nDropzone.QUEUED = \"queued\";\n// For backwards compatibility. Now, if a file is accepted, it's either queued\n// or uploading.\nDropzone.ACCEPTED = Dropzone.QUEUED;\n\nDropzone.UPLOADING = \"uploading\";\nDropzone.PROCESSING = Dropzone.UPLOADING; // alias\n\nDropzone.CANCELED = \"canceled\";\nDropzone.ERROR = \"error\";\nDropzone.SUCCESS = \"success\";\n\n/*\n\n Bugfix for iOS 6 and 7\n Source: http://stackoverflow.com/questions/11929099/html5-canvas-drawimage-ratio-bug-ios\n based on the work of https://github.com/stomita/ios-imagefile-megapixel\n\n */\n\n// Detecting vertical squash in loaded image.\n// Fixes a bug which squash image vertically while drawing into canvas for some images.\n// This is a bug in iOS6 devices. This function from https://github.com/stomita/ios-imagefile-megapixel\nlet detectVerticalSquash = function (img) {\n  let iw = img.naturalWidth;\n  let ih = img.naturalHeight;\n  let canvas = document.createElement(\"canvas\");\n  canvas.width = 1;\n  canvas.height = ih;\n  let ctx = canvas.getContext(\"2d\");\n  ctx.drawImage(img, 0, 0);\n  let { data } = ctx.getImageData(1, 0, 1, ih);\n\n  // search image edge pixel position in case it is squashed vertically.\n  let sy = 0;\n  let ey = ih;\n  let py = ih;\n  while (py > sy) {\n    let alpha = data[(py - 1) * 4 + 3];\n\n    if (alpha === 0) {\n      ey = py;\n    } else {\n      sy = py;\n    }\n\n    py = (ey + sy) >> 1;\n  }\n  let ratio = py / ih;\n\n  if (ratio === 0) {\n    return 1;\n  } else {\n    return ratio;\n  }\n};\n\n// A replacement for context.drawImage\n// (args are for source and destination).\nvar drawImageIOSFix = function (ctx, img, sx, sy, sw, sh, dx, dy, dw, dh) {\n  let vertSquashRatio = detectVerticalSquash(img);\n  return ctx.drawImage(img, sx, sy, sw, sh, dx, dy, dw, dh / vertSquashRatio);\n};\n\n// Based on MinifyJpeg\n// Source: http://www.perry.cz/files/ExifRestorer.js\n// http://elicon.blog57.fc2.com/blog-entry-206.html\nclass ExifRestore {\n  static initClass() {\n    this.KEY_STR =\n      \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n  }\n\n  static encode64(input) {\n    let output = \"\";\n    let chr1 = undefined;\n    let chr2 = undefined;\n    let chr3 = \"\";\n    let enc1 = undefined;\n    let enc2 = undefined;\n    let enc3 = undefined;\n    let enc4 = \"\";\n    let i = 0;\n    while (true) {\n      chr1 = input[i++];\n      chr2 = input[i++];\n      chr3 = input[i++];\n      enc1 = chr1 >> 2;\n      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n      enc4 = chr3 & 63;\n      if (isNaN(chr2)) {\n        enc3 = enc4 = 64;\n      } else if (isNaN(chr3)) {\n        enc4 = 64;\n      }\n      output =\n        output +\n        this.KEY_STR.charAt(enc1) +\n        this.KEY_STR.charAt(enc2) +\n        this.KEY_STR.charAt(enc3) +\n        this.KEY_STR.charAt(enc4);\n      chr1 = chr2 = chr3 = \"\";\n      enc1 = enc2 = enc3 = enc4 = \"\";\n      if (!(i < input.length)) {\n        break;\n      }\n    }\n    return output;\n  }\n\n  static restore(origFileBase64, resizedFileBase64) {\n    if (!origFileBase64.match(\"data:image/jpeg;base64,\")) {\n      return resizedFileBase64;\n    }\n    let rawImage = this.decode64(\n      origFileBase64.replace(\"data:image/jpeg;base64,\", \"\")\n    );\n    let segments = this.slice2Segments(rawImage);\n    let image = this.exifManipulation(resizedFileBase64, segments);\n    return `data:image/jpeg;base64,${this.encode64(image)}`;\n  }\n\n  static exifManipulation(resizedFileBase64, segments) {\n    let exifArray = this.getExifArray(segments);\n    let newImageArray = this.insertExif(resizedFileBase64, exifArray);\n    let aBuffer = new Uint8Array(newImageArray);\n    return aBuffer;\n  }\n\n  static getExifArray(segments) {\n    let seg = undefined;\n    let x = 0;\n    while (x < segments.length) {\n      seg = segments[x];\n      if ((seg[0] === 255) & (seg[1] === 225)) {\n        return seg;\n      }\n      x++;\n    }\n    return [];\n  }\n\n  static insertExif(resizedFileBase64, exifArray) {\n    let imageData = resizedFileBase64.replace(\"data:image/jpeg;base64,\", \"\");\n    let buf = this.decode64(imageData);\n    let separatePoint = buf.indexOf(255, 3);\n    let mae = buf.slice(0, separatePoint);\n    let ato = buf.slice(separatePoint);\n    let array = mae;\n    array = array.concat(exifArray);\n    array = array.concat(ato);\n    return array;\n  }\n\n  static slice2Segments(rawImageArray) {\n    let head = 0;\n    let segments = [];\n    while (true) {\n      var length;\n      if ((rawImageArray[head] === 255) & (rawImageArray[head + 1] === 218)) {\n        break;\n      }\n      if ((rawImageArray[head] === 255) & (rawImageArray[head + 1] === 216)) {\n        head += 2;\n      } else {\n        length = rawImageArray[head + 2] * 256 + rawImageArray[head + 3];\n        let endPoint = head + length + 2;\n        let seg = rawImageArray.slice(head, endPoint);\n        segments.push(seg);\n        head = endPoint;\n      }\n      if (head > rawImageArray.length) {\n        break;\n      }\n    }\n    return segments;\n  }\n\n  static decode64(input) {\n    let output = \"\";\n    let chr1 = undefined;\n    let chr2 = undefined;\n    let chr3 = \"\";\n    let enc1 = undefined;\n    let enc2 = undefined;\n    let enc3 = undefined;\n    let enc4 = \"\";\n    let i = 0;\n    let buf = [];\n    // remove all characters that are not A-Z, a-z, 0-9, +, /, or =\n    let base64test = /[^A-Za-z0-9\\+\\/\\=]/g;\n    if (base64test.exec(input)) {\n      console.warn(\n        \"There were invalid base64 characters in the input text.\\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\\nExpect errors in decoding.\"\n      );\n    }\n    input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, \"\");\n    while (true) {\n      enc1 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc2 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc3 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc4 = this.KEY_STR.indexOf(input.charAt(i++));\n      chr1 = (enc1 << 2) | (enc2 >> 4);\n      chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n      chr3 = ((enc3 & 3) << 6) | enc4;\n      buf.push(chr1);\n      if (enc3 !== 64) {\n        buf.push(chr2);\n      }\n      if (enc4 !== 64) {\n        buf.push(chr3);\n      }\n      chr1 = chr2 = chr3 = \"\";\n      enc1 = enc2 = enc3 = enc4 = \"\";\n      if (!(i < input.length)) {\n        break;\n      }\n    }\n    return buf;\n  }\n}\nExifRestore.initClass();\n\n/*\n * contentloaded.js\n *\n * Author: Diego Perini (diego.perini at gmail.com)\n * Summary: cross-browser wrapper for DOMContentLoaded\n * Updated: 20101020\n * License: MIT\n * Version: 1.2\n *\n * URL:\n * http://javascript.nwbox.com/ContentLoaded/\n * http://javascript.nwbox.com/ContentLoaded/MIT-LICENSE\n */\n\n// @win window reference\n// @fn function reference\nlet contentLoaded = function (win, fn) {\n  let done = false;\n  let top = true;\n  let doc = win.document;\n  let root = doc.documentElement;\n  let add = doc.addEventListener ? \"addEventListener\" : \"attachEvent\";\n  let rem = doc.addEventListener ? \"removeEventListener\" : \"detachEvent\";\n  let pre = doc.addEventListener ? \"\" : \"on\";\n  var init = function (e) {\n    if (e.type === \"readystatechange\" && doc.readyState !== \"complete\") {\n      return;\n    }\n    (e.type === \"load\" ? win : doc)[rem](pre + e.type, init, false);\n    if (!done && (done = true)) {\n      return fn.call(win, e.type || e);\n    }\n  };\n\n  var poll = function () {\n    try {\n      root.doScroll(\"left\");\n    } catch (e) {\n      setTimeout(poll, 50);\n      return;\n    }\n    return init(\"poll\");\n  };\n\n  if (doc.readyState !== \"complete\") {\n    if (doc.createEventObject && root.doScroll) {\n      try {\n        top = !win.frameElement;\n      } catch (error) {}\n      if (top) {\n        poll();\n      }\n    }\n    doc[add](pre + \"DOMContentLoaded\", init, false);\n    doc[add](pre + \"readystatechange\", init, false);\n    return win[add](pre + \"load\", init, false);\n  }\n};\n\nfunction __guard__(value, transform) {\n  return typeof value !== \"undefined\" && value !== null\n    ? transform(value)\n    : undefined;\n}\nfunction __guardMethod__(obj, methodName, transform) {\n  if (\n    typeof obj !== \"undefined\" &&\n    obj !== null &&\n    typeof obj[methodName] === \"function\"\n  ) {\n    return transform(obj, methodName);\n  } else {\n    return undefined;\n  }\n}\n\nexport { Dropzone };\n", "import Dropzone from \"../src/dropzone\";\n\nwindow.Dropzone = Dropzone;\n\nexport default Dropzone;\n"], "names": ["self", "ReferenceError", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "$068d7638c7686533$var$_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "protoProps", "staticProps", "prototype", "$da42839ea2c5b431$var$getPrototypeOf", "o", "setPrototypeOf", "getPrototypeOf", "__proto__", "$636ee2f214a98c8f$var$setPrototypeOf", "p", "subClass", "superClass", "create", "constructor", "value", "call", "obj", "Symbol", "$cf4679e12ceee72e$export$2e2bcd8739ae039", "$114dff7b9008f5c0$var$isCloneable", "Array", "isArray", "toString", "$114dff7b9008f5c0$var$isUnextendable", "val", "$114dff7b9008f5c0$exports", "$114dff7b9008f5c0$var$extend", "args", "slice", "arguments", "deep", "shift", "result", "Error", "extenders", "len", "extender", "hasOwnProperty", "base", "$a527f8347b4b94dc$export$2e2bcd8739ae039", "event", "fn", "this", "_callbacks", "push", "_len", "_key", "callbacks", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "undefined", "_step", "_iterator", "iterator", "next", "done", "callback", "apply", "err", "return", "element", "dispatchEvent", "makeEvent", "eventName", "detail", "params", "bubbles", "cancelable", "window", "CustomEvent", "evt", "document", "createEvent", "initCustomEvent", "splice", "$588a9ad8284f77de$export$2e2bcd8739ae039", "url", "method", "withCredentials", "timeout", "parallelUploads", "uploadMultiple", "chunking", "forceChunking", "chunkSize", "parallelChunkUploads", "retryChunks", "retryChunksLimit", "maxFilesize", "paramName", "createImageThumbnails", "maxThumbnailFilesize", "thumbnailWidth", "thumbnailHeight", "thumbnail<PERSON><PERSON><PERSON>", "resizeWidth", "resizeHeight", "resizeMimeType", "resizeQuality", "resizeMethod", "filesizeBase", "maxFiles", "headers", "defaultHeaders", "clickable", "ignoreHiddenFiles", "acceptedFiles", "acceptedMimeTypes", "autoProcessQueue", "autoQueue", "addRemoveLinks", "previewsContainer", "disablePreviews", "hiddenInputContainer", "capture", "renameFilename", "renameFile", "force<PERSON><PERSON><PERSON>", "dictDefaultMessage", "dictFallbackMessage", "dictFallbackText", "dictFileTooBig", "dictInvalidFileType", "dictResponseError", "dictCancelUpload", "dictUploadCanceled", "dictCancelUploadConfirmation", "dictRemoveFile", "dictRemoveFileConfirmation", "dictMaxFilesExceeded", "dictFileSizeUnits", "tb", "gb", "mb", "kb", "b", "init", "files", "xhr", "chunk", "d<PERSON><PERSON>", "file", "upload", "uuid", "dzchunkindex", "index", "dztotalfilesize", "size", "dzchunksize", "options", "d<PERSON><PERSON><PERSON><PERSON>nkcount", "totalChunkCount", "dzchunkbyteoffset", "accept", "chunksUploaded", "binaryBody", "fallback", "messageElement", "className", "concat", "getElementsByTagName", "child", "test", "$0b112e5f3be94b9d$export$2e2bcd8739ae039", "createElement", "append<PERSON><PERSON><PERSON>", "span", "textContent", "innerText", "getFallbackForm", "resize", "width", "height", "info", "srcX", "srcY", "srcWidth", "srcHeight", "srcRatio", "trgRatio", "Math", "min", "trgWidth", "trgHeight", "transformFile", "type", "match", "resizeImage", "previewTemplate", "$parcel$interopDefault", "drop", "e", "classList", "remove", "dragstart", "dragend", "dragenter", "add", "dragover", "dragleave", "paste", "reset", "addedfile", "previewElement", "trim", "querySelectorAll", "node", "name", "_iteratorNormalCompletion1", "_didIteratorError1", "_iteratorError1", "_step1", "_iterator1", "innerHTML", "filesize", "_removeLink", "removeFileEvent", "preventDefault", "stopPropagation", "status", "UPLOADING", "confirm", "_this", "_this1", "removeFile", "_this2", "_iteratorNormalCompletion2", "_didIteratorError2", "_iteratorError2", "_step2", "_iterator2", "addEventListener", "removedfile", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_updateMaxFilesReachedClass", "thumbnail", "dataUrl", "thumbnailElement", "alt", "src", "setTimeout", "error", "message", "errormultiple", "processing", "processingmultiple", "uploadprogress", "progress", "bytesSent", "nodeName", "style", "totaluploadprogress", "sending", "sendingmultiple", "success", "successmultiple", "canceled", "emit", "canceledmultiple", "complete", "completemultiple", "maxfilesexceeded", "maxfilesreached", "queuecomplete", "addedfiles", "Emitter", "el", "left", "clickableElements", "listeners", "querySelector", "nodeType", "dropzone", "instances", "elementOptions", "optionsForElement", "replace", "isBrowserSupported", "$d605d54dd3f172a3$export$2e2bcd8739ae039", "getAttribute", "toUpperCase", "getExistingFallback", "getElement", "getElements", "filter", "accepted", "map", "getFilesWithStatus", "QUEUED", "ADDED", "tagName", "setAttribute", "contains", "setupHiddenFileInput", "hiddenFileInput", "visibility", "position", "top", "_this11", "addFile", "URL", "webkitURL", "events", "on", "_this12", "updateTotalUploadProgress", "_this3", "getAddedFiles", "getUploadingFiles", "getQueuedFiles", "noPropagation", "dataTransfer", "types", "containsFiles", "returnValue", "_this4", "_this5", "efct", "effectAllowed", "dropEffect", "_this6", "_this7", "_this8", "_this9", "for<PERSON>ach", "clickableElement", "_this10", "click", "elementInside", "enable", "disable", "removeAllFiles", "indexOf", "totalUploadProgress", "totalBytesSent", "totalBytes", "getActiveFiles", "total", "n", "existingFallback", "form", "fieldsString", "_getParamName", "fields", "get<PERSON>allback", "elements", "elementListeners", "listener", "removeEventListener", "removeEventListeners", "disabled", "cancelUpload", "setupEventListeners", "selectedSize", "<PERSON><PERSON><PERSON><PERSON>", "units", "unit", "pow", "round", "getAcceptedFiles", "items", "webkitGetAsEntry", "_addFilesFromItems", "handleFiles", "clipboardData", "transform", "x", "entry", "item", "isFile", "getAsFile", "isDirectory", "_addFilesFromDirectory", "kind", "directory", "path", "<PERSON><PERSON><PERSON><PERSON>", "createReader", "<PERSON><PERSON><PERSON><PERSON>", "console", "methodName", "log", "readEntries", "entries", "substring", "fullPath", "isValidFile", "uuidv4", "filename", "_renameFile", "_enqueueThumbnail", "_errorProcessing", "enqueueFile", "processQueue", "_thumbnailQueue", "_processThumbnailQueue", "_processingThumbnail", "createThumbnail", "$0b112e5f3be94b9d$var$without", "cancelIfNecessary", "canvas", "resizedDataURL", "toDataURL", "$0b112e5f3be94b9d$var$ExifRestore", "restore", "dataURL", "dataURItoBlob", "fixOrientation", "fileReader", "FileReader", "onload", "createThumbnailFromUrl", "readAsDataURL", "mockFile", "imageUrl", "crossOrigin", "param", "resizeThumbnail", "img", "getComputedStyle", "body", "loadExif", "EXIF", "getData", "getTag", "orientation", "resizeInfo", "ctx", "getContext", "translate", "scale", "rotate", "PI", "$0b112e5f3be94b9d$var$drawImageIOSFix", "trgX", "trgY", "onerror", "processingLength", "queuedFiles", "processFiles", "processFile", "uploadFiles", "groupedFiles", "_getFilesWithXhr", "groupedFile", "CANCELED", "abort", "option", "_transformFiles", "transformedFiles", "transformedFile", "chunked", "ceil", "chunks", "handleNextChunk", "chunkIndex", "startedChunkCount", "start", "end", "dataBlock", "data", "webkitSlice", "retries", "_uploadData", "finishedChunkUpload", "response", "allFinished", "SUCCESS", "responseText", "responseHeaders", "getAllResponseHeaders", "_finished", "dataBlocks", "XMLHttpRequest", "resolveOption", "open", "_finishedUploading", "ontimeout", "_handleUploadError", "onprogress", "_updateFilesUploadProgress", "Accept", "headerName", "headerValue", "setRequestHeader", "submitRequest", "formData", "FormData", "additionalParams", "_getChunk", "append", "_addFormElementData", "<PERSON><PERSON><PERSON><PERSON>", "_loop", "input", "inputName", "inputType", "toLowerCase", "hasAttribute", "selected", "checked", "loaded", "readyState", "responseType", "getResponseHeader", "JSON", "parse", "warn", "send", "ERROR", "c", "r", "random", "initClass", "$0b112e5f3be94b9d$var$camelize", "forElement", "discover", "dropzones", "checkElements", "blockedBrowsers", "capableBrowser", "File", "FileList", "Blob", "blacklistedBrowsers", "navigator", "userAgent", "dataURI", "byteString", "atob", "split", "mimeString", "ab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ia", "Uint8Array", "asc", "charCodeAt", "list", "rejectedItem", "str", "char<PERSON>t", "string", "div", "childNodes", "container", "els", "question", "rejected", "mimeType", "baseMimeType", "validType", "j<PERSON><PERSON><PERSON>", "each", "ACCEPTED", "PROCESSING", "sx", "sy", "sw", "sh", "dx", "dy", "dw", "dh", "vertSquashRatio", "naturalWidth", "ih", "naturalHeight", "drawImage", "getImageData", "ey", "py", "ratio", "$0b112e5f3be94b9d$var$detectVerticalSquash", "KEY_STR", "output", "chr1", "chr2", "chr3", "enc1", "enc2", "enc3", "enc4", "isNaN", "origFileBase64", "resizedFileBase64", "rawImage", "decode64", "segments", "slice2Segments", "image", "exifManipulation", "encode64", "exifArray", "getExifArray", "newImageArray", "insertExif", "seg", "imageData", "buf", "separatePoint", "mae", "ato", "array", "rawImageArray", "head", "endPoint", "exec", "Dropzone"], "version": 3, "file": "dropzone-min.js.map"}