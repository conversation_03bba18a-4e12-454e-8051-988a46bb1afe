{"version": 3, "file": "no_active_items.js", "sources": ["../../../src/plugins/no_active_items/plugin.ts"], "sourcesContent": ["/**\n * Plugin: \"no_active_items\" (<PERSON>)\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\n\nexport default function(this:TomSelect) {\n\tthis.hook('instead','setActiveItem',() => {});\n\tthis.hook('instead','selectAll',() => {});\n};\n"], "names": ["hook"], "mappings": ";;;;;;;;;;;CAAA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAIe,eAAyB,IAAA;CACvC,EAAKA,IAAAA,CAAAA,IAAL,CAAU,SAAV,EAAoB,eAApB,EAAoC,MAAM,EAA1C,CAAA,CAAA;CACA,EAAKA,IAAAA,CAAAA,IAAL,CAAU,SAAV,EAAoB,WAApB,EAAgC,MAAM,EAAtC,CAAA,CAAA;CACA;;;;;;;;"}