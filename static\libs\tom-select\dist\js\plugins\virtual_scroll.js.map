{"version": 3, "file": "virtual_scroll.js", "sources": ["../../../node_modules/@orchidjs/unicode-variants/dist/esm/index.js", "../../../node_modules/@orchidjs/sifter/lib/utils.ts", "../../../src/vanilla.ts", "../../../src/plugins/virtual_scroll/plugin.ts"], "sourcesContent": ["/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */\nimport { toArray, setToPattern, escape_regex, arrayToPattern, sequencePattern } from './regex.js';\nexport { escape_regex } from './regex.js';\nimport { allSubstrings } from './strings.js';\n\n/**\n * @typedef {{[key:string]:string}} TUnicodeMap\n * @typedef {{[key:string]:Set<string>}} TUnicodeSets\n * @typedef {[[number,number]]} TCodePoints\n * @typedef {{folded:string,composed:string,code_point:number}} TCodePointObj\n * @typedef {{start:number,end:number,length:number,substr:string}} TSequencePart\n */\n/** @type {TCodePoints} */\n\nconst code_points = [[0, 65535]];\nconst accent_pat = '[\\u0300-\\u036F\\u{b7}\\u{2be}\\u{2bc}]';\n/** @type {TUnicodeMap} */\n\nlet unicode_map;\n/** @type {RegExp} */\n\nlet multi_char_reg;\nconst max_char_length = 3;\n/** @type {TUnicodeMap} */\n\nconst latin_convert = {};\n/** @type {TUnicodeMap} */\n\nconst latin_condensed = {\n  '/': '⁄∕',\n  '0': '߀',\n  \"a\": \"ⱥɐɑ\",\n  \"aa\": \"ꜳ\",\n  \"ae\": \"æǽǣ\",\n  \"ao\": \"ꜵ\",\n  \"au\": \"ꜷ\",\n  \"av\": \"ꜹꜻ\",\n  \"ay\": \"ꜽ\",\n  \"b\": \"ƀɓƃ\",\n  \"c\": \"ꜿƈȼↄ\",\n  \"d\": \"đɗɖᴅƌꮷԁɦ\",\n  \"e\": \"ɛǝᴇɇ\",\n  \"f\": \"ꝼƒ\",\n  \"g\": \"ǥɠꞡᵹꝿɢ\",\n  \"h\": \"ħⱨⱶɥ\",\n  \"i\": \"ɨı\",\n  \"j\": \"ɉȷ\",\n  \"k\": \"ƙⱪꝁꝃꝅꞣ\",\n  \"l\": \"łƚɫⱡꝉꝇꞁɭ\",\n  \"m\": \"ɱɯϻ\",\n  \"n\": \"ꞥƞɲꞑᴎлԉ\",\n  \"o\": \"øǿɔɵꝋꝍᴑ\",\n  \"oe\": \"œ\",\n  \"oi\": \"ƣ\",\n  \"oo\": \"ꝏ\",\n  \"ou\": \"ȣ\",\n  \"p\": \"ƥᵽꝑꝓꝕρ\",\n  \"q\": \"ꝗꝙɋ\",\n  \"r\": \"ɍɽꝛꞧꞃ\",\n  \"s\": \"ßȿꞩꞅʂ\",\n  \"t\": \"ŧƭʈⱦꞇ\",\n  \"th\": \"þ\",\n  \"tz\": \"ꜩ\",\n  \"u\": \"ʉ\",\n  \"v\": \"ʋꝟʌ\",\n  \"vy\": \"ꝡ\",\n  \"w\": \"ⱳ\",\n  \"y\": \"ƴɏỿ\",\n  \"z\": \"ƶȥɀⱬꝣ\",\n  \"hv\": \"ƕ\"\n};\n\nfor (let latin in latin_condensed) {\n  let unicode = latin_condensed[latin] || '';\n\n  for (let i = 0; i < unicode.length; i++) {\n    let char = unicode.substring(i, i + 1);\n    latin_convert[char] = latin;\n  }\n}\n\nconst convert_pat = new RegExp(Object.keys(latin_convert).join('|') + '|' + accent_pat, 'gu');\n/**\n * Initialize the unicode_map from the give code point ranges\n *\n * @param {TCodePoints=} _code_points\n */\n\nconst initialize = _code_points => {\n  if (unicode_map !== undefined) return;\n  unicode_map = generateMap(_code_points || code_points);\n};\n/**\n * Helper method for normalize a string\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize\n * @param {string} str\n * @param {string} form\n */\n\nconst normalize = (str, form = 'NFKD') => str.normalize(form);\n/**\n * Remove accents without reordering string\n * calling str.normalize('NFKD') on \\u{594}\\u{595}\\u{596} becomes \\u{596}\\u{594}\\u{595}\n * via https://github.com/krisk/Fuse/issues/133#issuecomment-318692703\n * @param {string} str\n * @return {string}\n */\n\nconst asciifold = str => {\n  return toArray(str).reduce(\n  /**\n   * @param {string} result\n   * @param {string} char\n   */\n  (result, char) => {\n    return result + _asciifold(char);\n  }, '');\n};\n/**\n * @param {string} str\n * @return {string}\n */\n\nconst _asciifold = str => {\n  str = normalize(str).toLowerCase().replace(convert_pat, (\n  /** @type {string} */\n  char) => {\n    return latin_convert[char] || '';\n  }); //return str;\n\n  return normalize(str, 'NFC');\n};\n/**\n * Generate a list of unicode variants from the list of code points\n * @param {TCodePoints} code_points\n * @yield {TCodePointObj}\n */\n\nfunction* generator(code_points) {\n  for (const [code_point_min, code_point_max] of code_points) {\n    for (let i = code_point_min; i <= code_point_max; i++) {\n      let composed = String.fromCharCode(i);\n      let folded = asciifold(composed);\n\n      if (folded == composed.toLowerCase()) {\n        continue;\n      } // skip when folded is a string longer than 3 characters long\n      // bc the resulting regex patterns will be long\n      // eg:\n      // folded صلى الله عليه وسلم length 18 code point 65018\n      // folded جل جلاله length 8 code point 65019\n\n\n      if (folded.length > max_char_length) {\n        continue;\n      }\n\n      if (folded.length == 0) {\n        continue;\n      }\n\n      yield {\n        folded: folded,\n        composed: composed,\n        code_point: i\n      };\n    }\n  }\n}\n/**\n * Generate a unicode map from the list of code points\n * @param {TCodePoints} code_points\n * @return {TUnicodeSets}\n */\n\nconst generateSets = code_points => {\n  /** @type {{[key:string]:Set<string>}} */\n  const unicode_sets = {};\n  /**\n   * @param {string} folded\n   * @param {string} to_add\n   */\n\n  const addMatching = (folded, to_add) => {\n    /** @type {Set<string>} */\n    const folded_set = unicode_sets[folded] || new Set();\n    const patt = new RegExp('^' + setToPattern(folded_set) + '$', 'iu');\n\n    if (to_add.match(patt)) {\n      return;\n    }\n\n    folded_set.add(escape_regex(to_add));\n    unicode_sets[folded] = folded_set;\n  };\n\n  for (let value of generator(code_points)) {\n    addMatching(value.folded, value.folded);\n    addMatching(value.folded, value.composed);\n  }\n\n  return unicode_sets;\n};\n/**\n * Generate a unicode map from the list of code points\n * ae => (?:(?:ae|Æ|Ǽ|Ǣ)|(?:A|Ⓐ|Ａ...)(?:E|ɛ|Ⓔ...))\n *\n * @param {TCodePoints} code_points\n * @return {TUnicodeMap}\n */\n\nconst generateMap = code_points => {\n  /** @type {TUnicodeSets} */\n  const unicode_sets = generateSets(code_points);\n  /** @type {TUnicodeMap} */\n\n  const unicode_map = {};\n  /** @type {string[]} */\n\n  let multi_char = [];\n\n  for (let folded in unicode_sets) {\n    let set = unicode_sets[folded];\n\n    if (set) {\n      unicode_map[folded] = setToPattern(set);\n    }\n\n    if (folded.length > 1) {\n      multi_char.push(escape_regex(folded));\n    }\n  }\n\n  multi_char.sort((a, b) => b.length - a.length);\n  const multi_char_patt = arrayToPattern(multi_char);\n  multi_char_reg = new RegExp('^' + multi_char_patt, 'u');\n  return unicode_map;\n};\n/**\n * Map each element of an array from it's folded value to all possible unicode matches\n * @param {string[]} strings\n * @param {number} min_replacement\n * @return {string}\n */\n\nconst mapSequence = (strings, min_replacement = 1) => {\n  let chars_replaced = 0;\n  strings = strings.map(str => {\n    if (unicode_map[str]) {\n      chars_replaced += str.length;\n    }\n\n    return unicode_map[str] || str;\n  });\n\n  if (chars_replaced >= min_replacement) {\n    return sequencePattern(strings);\n  }\n\n  return '';\n};\n/**\n * Convert a short string and split it into all possible patterns\n * Keep a pattern only if min_replacement is met\n *\n * 'abc'\n * \t\t=> [['abc'],['ab','c'],['a','bc'],['a','b','c']]\n *\t\t=> ['abc-pattern','ab-c-pattern'...]\n *\n *\n * @param {string} str\n * @param {number} min_replacement\n * @return {string}\n */\n\nconst substringsToPattern = (str, min_replacement = 1) => {\n  min_replacement = Math.max(min_replacement, str.length - 1);\n  return arrayToPattern(allSubstrings(str).map(sub_pat => {\n    return mapSequence(sub_pat, min_replacement);\n  }));\n};\n/**\n * Convert an array of sequences into a pattern\n * [{start:0,end:3,length:3,substr:'iii'}...] => (?:iii...)\n *\n * @param {Sequence[]} sequences\n * @param {boolean} all\n */\n\nconst sequencesToPattern = (sequences, all = true) => {\n  let min_replacement = sequences.length > 1 ? 1 : 0;\n  return arrayToPattern(sequences.map(sequence => {\n    let seq = [];\n    const len = all ? sequence.length() : sequence.length() - 1;\n\n    for (let j = 0; j < len; j++) {\n      seq.push(substringsToPattern(sequence.substrs[j] || '', min_replacement));\n    }\n\n    return sequencePattern(seq);\n  }));\n};\n/**\n * Return true if the sequence is already in the sequences\n * @param {Sequence} needle_seq\n * @param {Sequence[]} sequences\n */\n\n\nconst inSequences = (needle_seq, sequences) => {\n  for (const seq of sequences) {\n    if (seq.start != needle_seq.start || seq.end != needle_seq.end) {\n      continue;\n    }\n\n    if (seq.substrs.join('') !== needle_seq.substrs.join('')) {\n      continue;\n    }\n\n    let needle_parts = needle_seq.parts;\n    /**\n     * @param {TSequencePart} part\n     */\n\n    const filter = part => {\n      for (const needle_part of needle_parts) {\n        if (needle_part.start === part.start && needle_part.substr === part.substr) {\n          return false;\n        }\n\n        if (part.length == 1 || needle_part.length == 1) {\n          continue;\n        } // check for overlapping parts\n        // a = ['::=','==']\n        // b = ['::','===']\n        // a = ['r','sm']\n        // b = ['rs','m']\n\n\n        if (part.start < needle_part.start && part.end > needle_part.start) {\n          return true;\n        }\n\n        if (needle_part.start < part.start && needle_part.end > part.start) {\n          return true;\n        }\n      }\n\n      return false;\n    };\n\n    let filtered = seq.parts.filter(filter);\n\n    if (filtered.length > 0) {\n      continue;\n    }\n\n    return true;\n  }\n\n  return false;\n};\n\nclass Sequence {\n  constructor() {\n    /** @type {TSequencePart[]} */\n    this.parts = [];\n    /** @type {string[]} */\n\n    this.substrs = [];\n    this.start = 0;\n    this.end = 0;\n  }\n  /**\n   * @param {TSequencePart|undefined} part\n   */\n\n\n  add(part) {\n    if (part) {\n      this.parts.push(part);\n      this.substrs.push(part.substr);\n      this.start = Math.min(part.start, this.start);\n      this.end = Math.max(part.end, this.end);\n    }\n  }\n\n  last() {\n    return this.parts[this.parts.length - 1];\n  }\n\n  length() {\n    return this.parts.length;\n  }\n  /**\n   * @param {number} position\n   * @param {TSequencePart} last_piece\n   */\n\n\n  clone(position, last_piece) {\n    let clone = new Sequence();\n    let parts = JSON.parse(JSON.stringify(this.parts));\n    let last_part = parts.pop();\n\n    for (const part of parts) {\n      clone.add(part);\n    }\n\n    let last_substr = last_piece.substr.substring(0, position - last_part.start);\n    let clone_last_len = last_substr.length;\n    clone.add({\n      start: last_part.start,\n      end: last_part.start + clone_last_len,\n      length: clone_last_len,\n      substr: last_substr\n    });\n    return clone;\n  }\n\n}\n/**\n * Expand a regular expression pattern to include unicode variants\n * \teg /a/ becomes /aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐɑAⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ/\n *\n * Issue:\n *  ﺊﺋ [ 'ﺊ = \\\\u{fe8a}', 'ﺋ = \\\\u{fe8b}' ]\n *\tbecomes:\tئئ [ 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}', 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}' ]\n *\n *\tİĲ = IIJ = ⅡJ\n *\n * \t1/2/4\n *\n * @param {string} str\n * @return {string|undefined}\n */\n\n\nconst getPattern = str => {\n  initialize();\n  str = asciifold(str);\n  let pattern = '';\n  let sequences = [new Sequence()];\n\n  for (let i = 0; i < str.length; i++) {\n    let substr = str.substring(i);\n    let match = substr.match(multi_char_reg);\n    const char = str.substring(i, i + 1);\n    const match_str = match ? match[0] : null; // loop through sequences\n    // add either the char or multi_match\n\n    let overlapping = [];\n    let added_types = new Set();\n\n    for (const sequence of sequences) {\n      const last_piece = sequence.last();\n\n      if (!last_piece || last_piece.length == 1 || last_piece.end <= i) {\n        // if we have a multi match\n        if (match_str) {\n          const len = match_str.length;\n          sequence.add({\n            start: i,\n            end: i + len,\n            length: len,\n            substr: match_str\n          });\n          added_types.add('1');\n        } else {\n          sequence.add({\n            start: i,\n            end: i + 1,\n            length: 1,\n            substr: char\n          });\n          added_types.add('2');\n        }\n      } else if (match_str) {\n        let clone = sequence.clone(i, last_piece);\n        const len = match_str.length;\n        clone.add({\n          start: i,\n          end: i + len,\n          length: len,\n          substr: match_str\n        });\n        overlapping.push(clone);\n      } else {\n        // don't add char\n        // adding would create invalid patterns: 234 => [2,34,4]\n        added_types.add('3');\n      }\n    } // if we have overlapping\n\n\n    if (overlapping.length > 0) {\n      // ['ii','iii'] before ['i','i','iii']\n      overlapping = overlapping.sort((a, b) => {\n        return a.length() - b.length();\n      });\n\n      for (let clone of overlapping) {\n        // don't add if we already have an equivalent sequence\n        if (inSequences(clone, sequences)) {\n          continue;\n        }\n\n        sequences.push(clone);\n      }\n\n      continue;\n    } // if we haven't done anything unique\n    // clean up the patterns\n    // helps keep patterns smaller\n    // if str = 'r₨㎧aarss', pattern will be 446 instead of 655\n\n\n    if (i > 0 && added_types.size == 1 && !added_types.has('3')) {\n      pattern += sequencesToPattern(sequences, false);\n      let new_seq = new Sequence();\n      const old_seq = sequences[0];\n\n      if (old_seq) {\n        new_seq.add(old_seq.last());\n      }\n\n      sequences = [new_seq];\n    }\n  }\n\n  pattern += sequencesToPattern(sequences, true);\n  return pattern;\n};\n\nexport { _asciifold, asciifold, code_points, generateMap, generateSets, generator, getPattern, initialize, mapSequence, normalize, substringsToPattern, unicode_map };\n//# sourceMappingURL=index.js.map\n", "\nimport { asciifold } from '@orchidjs/unicode-variants';\nimport * as T from './types';\n\n\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttr = (obj:{[key:string]:any}, name:string ) => {\n    if (!obj ) return;\n    return obj[name];\n};\n\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttrNesting = (obj:{[key:string]:any}, name:string ) => {\n    if (!obj ) return;\n    var part, names = name.split(\".\");\n\twhile( (part = names.shift()) && (obj = obj[part]));\n    return obj;\n};\n\n/**\n * Calculates how close of a match the\n * given value is against a search token.\n *\n */\nexport const scoreValue = (value:string, token:T.Token, weight:number ):number => {\n\tvar score, pos;\n\n\tif (!value) return 0;\n\n\tvalue = value + '';\n\tif( token.regex == null ) return 0;\n\tpos = value.search(token.regex);\n\tif (pos === -1) return 0;\n\n\tscore = token.string.length / value.length;\n\tif (pos === 0) score += 0.5;\n\n\treturn score * weight;\n};\n\n\n/**\n * Cast object property to an array if it exists and has a value\n *\n */\nexport const propToArray = (obj:{[key:string]:any}, key:string) => {\n\tvar value = obj[key];\n\n\tif( typeof value == 'function' ) return value;\n\n\tif( value && !Array.isArray(value) ){\n\t\tobj[key] = [value];\n\t}\n}\n\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n\n\n\nexport const cmp = (a:number|string, b:number|string) => {\n\tif (typeof a === 'number' && typeof b === 'number') {\n\t\treturn a > b ? 1 : (a < b ? -1 : 0);\n\t}\n\ta = asciifold(a + '').toLowerCase();\n\tb = asciifold(b + '').toLowerCase();\n\tif (a > b) return 1;\n\tif (b > a) return -1;\n\treturn 0;\n};\n", "\nimport { iterate } from '@orchidjs/sifter/lib/utils';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\11\\12\\14\\15\\40]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * Plugin: \"restore_on_backspace\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport TomSelect from '../../tom-select';\nimport { TomOption } from '../../types/index';\nimport { addClasses } from '../../vanilla';\n\nexport default function(this:TomSelect) {\n\tconst self\t\t\t\t\t\t\t= this;\n\tconst orig_canLoad\t\t\t\t\t= self.canLoad;\n\tconst orig_clearActiveOption\t\t= self.clearActiveOption;\n\tconst orig_loadCallback\t\t\t\t= self.loadCallback;\n\n\tvar pagination:{[key:string]:any}\t= {};\n\tvar dropdown_content:HTMLElement;\n\tvar loading_more\t\t\t\t\t= false;\n\tvar load_more_opt:HTMLElement;\n\tvar default_values: string[]\t\t= [];\n\n\tif( !self.settings.shouldLoadMore ){\n\n\t\t// return true if additional results should be loaded\n\t\tself.settings.shouldLoadMore = ():boolean=>{\n\n\t\t\tconst scroll_percent = dropdown_content.clientHeight / (dropdown_content.scrollHeight - dropdown_content.scrollTop);\n\t\t\tif( scroll_percent > 0.9 ){\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif( self.activeOption ){\n\t\t\t\tvar selectable\t= self.selectable();\n\t\t\t\tvar index\t\t= Array.from(selectable).indexOf(self.activeOption);\n\t\t\t\tif( index >= (selectable.length-2) ){\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn false;\n\t\t}\n\t}\n\n\n\tif( !self.settings.firstUrl ){\n\t\tthrow 'virtual_scroll plugin requires a firstUrl() method';\n\t}\n\n\n\t// in order for virtual scrolling to work,\n\t// options need to be ordered the same way they're returned from the remote data source\n\tself.settings.sortField\t\t\t= [{field:'$order'},{field:'$score'}];\n\n\n\t// can we load more results for given query?\n\tconst canLoadMore = (query:string):boolean => {\n\n\t\tif( typeof self.settings.maxOptions === 'number' && dropdown_content.children.length >= self.settings.maxOptions ){\n\t\t\treturn false;\n\t\t}\n\n\t\tif( (query in pagination) && pagination[query] ){\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t};\n\n\tconst clearFilter = (option:TomOption, value:string):boolean => {\n\t\tif( self.items.indexOf(value) >= 0 || default_values.indexOf(value) >= 0 ){\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t};\n\n\n\t// set the next url that will be\n\tself.setNextUrl = (value:string,next_url:any):void => {\n\t\tpagination[value] = next_url;\n\t};\n\n\t// getUrl() to be used in settings.load()\n\tself.getUrl = (query:string):any =>{\n\n\t\tif( query in pagination ){\n\t\t\tconst next_url = pagination[query];\n\t\t\tpagination[query] = false;\n\t\t\treturn next_url;\n\t\t}\n\n\t\t// if the user goes back to a previous query\n\t\t// we need to load the first page again\n\t\tpagination = {};\n\n\t\treturn self.settings.firstUrl.call(self,query);\n\t};\n\n\n\t// don't clear the active option (and cause unwanted dropdown scroll)\n\t// while loading more results\n\tself.hook('instead','clearActiveOption',()=>{\n\n\t\tif( loading_more ){\n\t\t\treturn;\n\t\t}\n\n\t\treturn orig_clearActiveOption.call(self);\n\t});\n\n\t// override the canLoad method\n\tself.hook('instead','canLoad',(query:string)=>{\n\n\t\t// first time the query has been seen\n\t\tif( !(query in pagination) ){\n\t\t\treturn orig_canLoad.call(self,query);\n\t\t}\n\n\t\treturn canLoadMore(query);\n\t});\n\n\n\t// wrap the load\n\tself.hook('instead','loadCallback',( options:TomOption[], optgroups:TomOption[])=>{\n\n\t\tif( !loading_more ){\n\t\t\tself.clearOptions(clearFilter);\n\t\t}else if( load_more_opt ){\n\t\t\tconst first_option = options[0];\n\t\t\tif( first_option !== undefined ){\n\t\t\t\tload_more_opt.dataset.value\t\t= first_option[self.settings.valueField];\n\t\t\t}\n\t\t}\n\n\t\torig_loadCallback.call( self, options, optgroups);\n\n\t\tloading_more = false;\n\t});\n\n\n\t// add templates to dropdown\n\t//\tloading_more if we have another url in the queue\n\t//\tno_more_results if we don't have another url in the queue\n\tself.hook('after','refreshOptions',()=>{\n\n\t\tconst query\t\t= self.lastValue;\n\t\tvar option;\n\n\t\tif( canLoadMore(query) ){\n\n\t\t\toption = self.render('loading_more',{query:query});\n\t\t\tif( option ){\n\t\t\t\toption.setAttribute('data-selectable',''); // so that navigating dropdown with [down] keypresses can navigate to this node\n\t\t\t\tload_more_opt = option;\n\t\t\t}\n\n\t\t}else if( (query in pagination) && !dropdown_content.querySelector('.no-results') ){\n\t\t\toption = self.render('no_more_results',{query:query});\n\t\t}\n\n\t\tif( option ){\n\t\t\taddClasses(option,self.settings.optionClass);\n\t\t\tdropdown_content.append( option );\n\t\t}\n\n\t});\n\n\n\t// add scroll listener and default templates\n\tself.on('initialize',()=>{\n\t\tdefault_values = Object.keys(self.options);\n\t\tdropdown_content = self.dropdown_content;\n\n\t\t// default templates\n\t\tself.settings.render = Object.assign({}, {\n\t\t\tloading_more:() => {\n\t\t\t\treturn `<div class=\"loading-more-results\">Loading more results ... </div>`;\n\t\t\t},\n\t\t\tno_more_results:() =>{\n\t\t\t\treturn `<div class=\"no-more-results\">No more results</div>`;\n\t\t\t}\n\t\t},self.settings.render);\n\n\n\t\t// watch dropdown content scroll position\n\t\tdropdown_content.addEventListener('scroll',()=>{\n\n\t\t\tif( !self.settings.shouldLoadMore.call(self) ){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// !important: this will get checked again in load() but we still need to check here otherwise loading_more will be set to true\n\t\t\tif( !canLoadMore(self.lastValue) ){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// don't call load() too much\n\t\t\tif( loading_more ) return;\n\n\n\t\t\tloading_more = true;\n\t\t\tself.load.call(self,self.lastValue);\n\t\t});\n\t});\n\n};\n"], "names": ["iterate", "object", "callback", "Array", "isArray", "for<PERSON>ach", "key", "hasOwnProperty", "addClasses", "elmts", "classes", "norm_classes", "classesArray", "cast<PERSON><PERSON><PERSON><PERSON>", "map", "el", "cls", "classList", "add", "args", "_classes", "trim", "split", "concat", "filter", "Boolean", "arg", "self", "orig_canLoad", "canLoad", "orig_clearActiveOption", "clearActiveOption", "orig_loadCallback", "loadCallback", "pagination", "dropdown_content", "loading_more", "load_more_opt", "default_values", "settings", "shouldLoadMore", "scroll_percent", "clientHeight", "scrollHeight", "scrollTop", "activeOption", "selectable", "index", "from", "indexOf", "length", "firstUrl", "sortField", "field", "canLoadMore", "query", "maxOptions", "children", "clearFilter", "option", "value", "items", "setNextUrl", "next_url", "getUrl", "call", "hook", "options", "optgroups", "clearOptions", "first_option", "undefined", "dataset", "valueField", "lastValue", "render", "setAttribute", "querySelector", "optionClass", "append", "on", "Object", "keys", "assign", "no_more_results", "addEventListener", "load"], "mappings": ";;;;;;;;;;;EAAA;EAeA,MAAM,UAAU,GAAG,qCAAqC,CAAC;EAQzD;AACA;EACA,MAAM,aAAa,GAAG,EAAE,CAAC;EACzB;AACA;EACA,MAAM,eAAe,GAAG;EACxB,EAAE,GAAG,EAAE,IAAI;EACX,EAAE,GAAG,EAAE,GAAG;EACV,EAAE,GAAG,EAAE,KAAK;EACZ,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,IAAI,EAAE,KAAK;EACb,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,IAAI,EAAE,IAAI;EACZ,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,GAAG,EAAE,KAAK;EACZ,EAAE,GAAG,EAAE,MAAM;EACb,EAAE,GAAG,EAAE,UAAU;EACjB,EAAE,GAAG,EAAE,MAAM;EACb,EAAE,GAAG,EAAE,IAAI;EACX,EAAE,GAAG,EAAE,QAAQ;EACf,EAAE,GAAG,EAAE,MAAM;EACb,EAAE,GAAG,EAAE,IAAI;EACX,EAAE,GAAG,EAAE,IAAI;EACX,EAAE,GAAG,EAAE,QAAQ;EACf,EAAE,GAAG,EAAE,UAAU;EACjB,EAAE,GAAG,EAAE,KAAK;EACZ,EAAE,GAAG,EAAE,SAAS;EAChB,EAAE,GAAG,EAAE,SAAS;EAChB,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,GAAG,EAAE,QAAQ;EACf,EAAE,GAAG,EAAE,KAAK;EACZ,EAAE,GAAG,EAAE,OAAO;EACd,EAAE,GAAG,EAAE,OAAO;EACd,EAAE,GAAG,EAAE,OAAO;EACd,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,GAAG,EAAE,GAAG;EACV,EAAE,GAAG,EAAE,KAAK;EACZ,EAAE,IAAI,EAAE,GAAG;EACX,EAAE,GAAG,EAAE,GAAG;EACV,EAAE,GAAG,EAAE,KAAK;EACZ,EAAE,GAAG,EAAE,OAAO;EACd,EAAE,IAAI,EAAE,GAAG;EACX,CAAC,CAAC;AACF;EACA,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE;EACnC,EAAE,IAAI,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAC7C;EACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC3C,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3C,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;EAChC,GAAG;EACH,CAAC;AACD;EACoB,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,UAAU,EAAE,IAAI;;ECf5F;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,MAAMA,OAAO,GAAG,CAACC,MAAD,EAA+BC,QAA/B,KAAqE;EAE3F,EAAA,IAAKC,KAAK,CAACC,OAAN,CAAcH,MAAd,CAAL,EAA4B;EAC3BA,IAAAA,MAAM,CAACI,OAAP,CAAeH,QAAf,CAAA,CAAA;EAEA,GAHD,MAGK;EAEJ,IAAA,KAAK,IAAII,GAAT,IAAgBL,MAAhB,EAAwB;EACvB,MAAA,IAAIA,MAAM,CAACM,cAAP,CAAsBD,GAAtB,CAAJ,EAAgC;EAC/BJ,QAAAA,QAAQ,CAACD,MAAM,CAACK,GAAD,CAAP,EAAcA,GAAd,CAAR,CAAA;EACA,OAAA;EACD,KAAA;EACD,GAAA;EACD,CAbM;;EClBP;EACA;EACA;EACA;;EACO,MAAME,UAAU,GAAG,CAAEC,KAAF,EAAmC,GAAGC,OAAtC,KAAuE;EAEhG,EAAA,IAAIC,YAAY,GAAIC,YAAY,CAACF,OAAD,CAAhC,CAAA;EACAD,EAAAA,KAAK,GAAMI,WAAW,CAACJ,KAAD,CAAtB,CAAA;EAEAA,EAAAA,KAAK,CAACK,GAAN,CAAWC,EAAE,IAAI;EAChBJ,IAAAA,YAAY,CAACG,GAAb,CAAkBE,GAAG,IAAI;EACxBD,MAAAA,EAAE,CAACE,SAAH,CAAaC,GAAb,CAAkBF,GAAlB,CAAA,CAAA;EACA,KAFD,CAAA,CAAA;EAGA,GAJD,CAAA,CAAA;EAKA,CAVM,CAAA;EA6BP;EACA;EACA;EACA;;EACO,MAAMJ,YAAY,GAAIO,IAAD,IAAuC;EAClE,EAAIT,IAAAA,OAAgB,GAAG,EAAvB,CAAA;EACAV,EAAAA,OAAO,CAAEmB,IAAF,EAASC,QAAD,IAAa;EAC3B,IAAA,IAAI,OAAOA,QAAP,KAAoB,QAAxB,EAAkC;EACjCA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,IAAT,EAAgBC,CAAAA,KAAhB,CAAsB,mBAAtB,CAAX,CAAA;EACA,KAAA;;EACD,IAAA,IAAInB,KAAK,CAACC,OAAN,CAAcgB,QAAd,CAAJ,EAA6B;EAC5BV,MAAAA,OAAO,GAAGA,OAAO,CAACa,MAAR,CAAeH,QAAf,CAAV,CAAA;EACA,KAAA;EACD,GAPM,CAAP,CAAA;EASA,EAAA,OAAOV,OAAO,CAACc,MAAR,CAAeC,OAAf,CAAP,CAAA;EACA,CAZM,CAAA;EAeP;EACA;EACA;EACA;;EACO,MAAMZ,WAAW,GAAIa,GAAD,IAAwB;EAClD,EAAA,IAAI,CAACvB,KAAK,CAACC,OAAN,CAAcsB,GAAd,CAAL,EAAyB;EACvBA,IAAAA,GAAG,GAAG,CAACA,GAAD,CAAN,CAAA;EACA,GAAA;;EACF,EAAA,OAAOA,GAAP,CAAA;EACA,CALM;;EClHP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAMe,eAAyB,IAAA;EACvC,EAAMC,MAAAA,IAAI,GAAS,IAAnB,CAAA;EACA,EAAA,MAAMC,YAAY,GAAOD,IAAI,CAACE,OAA9B,CAAA;EACA,EAAA,MAAMC,sBAAsB,GAAIH,IAAI,CAACI,iBAArC,CAAA;EACA,EAAA,MAAMC,iBAAiB,GAAML,IAAI,CAACM,YAAlC,CAAA;EAEA,EAAIC,IAAAA,UAA6B,GAAG,EAApC,CAAA;EACA,EAAA,IAAIC,gBAAJ,CAAA;EACA,EAAIC,IAAAA,YAAY,GAAO,KAAvB,CAAA;EACA,EAAA,IAAIC,aAAJ,CAAA;EACA,EAAIC,IAAAA,cAAwB,GAAI,EAAhC,CAAA;;EAEA,EAAA,IAAI,CAACX,IAAI,CAACY,QAAL,CAAcC,cAAnB,EAAmC;EAElC;EACAb,IAAAA,IAAI,CAACY,QAAL,CAAcC,cAAd,GAA+B,MAAY;EAE1C,MAAA,MAAMC,cAAc,GAAGN,gBAAgB,CAACO,YAAjB,IAAiCP,gBAAgB,CAACQ,YAAjB,GAAgCR,gBAAgB,CAACS,SAAlF,CAAvB,CAAA;;EACA,MAAIH,IAAAA,cAAc,GAAG,GAArB,EAA0B;EACzB,QAAA,OAAO,IAAP,CAAA;EACA,OAAA;;EAED,MAAId,IAAAA,IAAI,CAACkB,YAAT,EAAuB;EACtB,QAAA,IAAIC,UAAU,GAAGnB,IAAI,CAACmB,UAAL,EAAjB,CAAA;EACA,QAAA,IAAIC,KAAK,GAAI5C,KAAK,CAAC6C,IAAN,CAAWF,UAAX,CAAA,CAAuBG,OAAvB,CAA+BtB,IAAI,CAACkB,YAApC,CAAb,CAAA;;EACA,QAAA,IAAIE,KAAK,IAAKD,UAAU,CAACI,MAAX,GAAkB,CAAhC,EAAoC;EACnC,UAAA,OAAO,IAAP,CAAA;EACA,SAAA;EACD,OAAA;;EAED,MAAA,OAAO,KAAP,CAAA;EACA,KAhBD,CAAA;EAiBA,GAAA;;EAGD,EAAA,IAAI,CAACvB,IAAI,CAACY,QAAL,CAAcY,QAAnB,EAA6B;EAC5B,IAAA,MAAM,oDAAN,CAAA;EACA,GArCsC;EAyCvC;;;EACAxB,EAAAA,IAAI,CAACY,QAAL,CAAca,SAAd,GAA4B,CAAC;EAACC,IAAAA,KAAK,EAAC,QAAA;EAAP,GAAD,EAAkB;EAACA,IAAAA,KAAK,EAAC,QAAA;EAAP,GAAlB,CAA5B,CA1CuC;;EA8CvC,EAAMC,MAAAA,WAAW,GAAIC,KAAD,IAA0B;EAE7C,IAAI,IAAA,OAAO5B,IAAI,CAACY,QAAL,CAAciB,UAArB,KAAoC,QAApC,IAAgDrB,gBAAgB,CAACsB,QAAjB,CAA0BP,MAA1B,IAAoCvB,IAAI,CAACY,QAAL,CAAciB,UAAtG,EAAkH;EACjH,MAAA,OAAO,KAAP,CAAA;EACA,KAAA;;EAED,IAAKD,IAAAA,KAAK,IAAIrB,UAAV,IAAyBA,UAAU,CAACqB,KAAD,CAAvC,EAAgD;EAC/C,MAAA,OAAO,IAAP,CAAA;EACA,KAAA;;EAED,IAAA,OAAO,KAAP,CAAA;EACA,GAXD,CAAA;;EAaA,EAAA,MAAMG,WAAW,GAAG,CAACC,MAAD,EAAmBC,KAAnB,KAA4C;EAC/D,IAAA,IAAIjC,IAAI,CAACkC,KAAL,CAAWZ,OAAX,CAAmBW,KAAnB,CAAA,IAA6B,CAA7B,IAAkCtB,cAAc,CAACW,OAAf,CAAuBW,KAAvB,CAAA,IAAiC,CAAvE,EAA0E;EACzE,MAAA,OAAO,IAAP,CAAA;EACA,KAAA;;EACD,IAAA,OAAO,KAAP,CAAA;EACA,GALD,CA3DuC;;;EAoEvCjC,EAAAA,IAAI,CAACmC,UAAL,GAAkB,CAACF,KAAD,EAAcG,QAAd,KAAoC;EACrD7B,IAAAA,UAAU,CAAC0B,KAAD,CAAV,GAAoBG,QAApB,CAAA;EACA,GAFD,CApEuC;;;EAyEvCpC,EAAAA,IAAI,CAACqC,MAAL,GAAeT,KAAD,IAAqB;EAElC,IAAIA,IAAAA,KAAK,IAAIrB,UAAb,EAAyB;EACxB,MAAA,MAAM6B,QAAQ,GAAG7B,UAAU,CAACqB,KAAD,CAA3B,CAAA;EACArB,MAAAA,UAAU,CAACqB,KAAD,CAAV,GAAoB,KAApB,CAAA;EACA,MAAA,OAAOQ,QAAP,CAAA;EACA,KANiC;EASlC;;;EACA7B,IAAAA,UAAU,GAAG,EAAb,CAAA;EAEA,IAAOP,OAAAA,IAAI,CAACY,QAAL,CAAcY,QAAd,CAAuBc,IAAvB,CAA4BtC,IAA5B,EAAiC4B,KAAjC,CAAP,CAAA;EACA,GAbD,CAzEuC;EA0FvC;;;EACA5B,EAAAA,IAAI,CAACuC,IAAL,CAAU,SAAV,EAAoB,mBAApB,EAAwC,MAAI;EAE3C,IAAA,IAAI9B,YAAJ,EAAkB;EACjB,MAAA,OAAA;EACA,KAAA;;EAED,IAAA,OAAON,sBAAsB,CAACmC,IAAvB,CAA4BtC,IAA5B,CAAP,CAAA;EACA,GAPD,EA3FuC;;EAqGvCA,EAAAA,IAAI,CAACuC,IAAL,CAAU,SAAV,EAAoB,SAApB,EAA+BX,KAAD,IAAgB;EAE7C;EACA,IAAA,IAAI,EAAEA,KAAK,IAAIrB,UAAX,CAAJ,EAA4B;EAC3B,MAAA,OAAON,YAAY,CAACqC,IAAb,CAAkBtC,IAAlB,EAAuB4B,KAAvB,CAAP,CAAA;EACA,KAAA;;EAED,IAAOD,OAAAA,WAAW,CAACC,KAAD,CAAlB,CAAA;EACA,GARD,EArGuC;;EAiHvC5B,EAAAA,IAAI,CAACuC,IAAL,CAAU,SAAV,EAAoB,cAApB,EAAmC,CAAEC,OAAF,EAAuBC,SAAvB,KAA+C;EAEjF,IAAI,IAAA,CAAChC,YAAL,EAAmB;EAClBT,MAAAA,IAAI,CAAC0C,YAAL,CAAkBX,WAAlB,CAAA,CAAA;EACA,KAFD,MAEM,IAAIrB,aAAJ,EAAmB;EACxB,MAAA,MAAMiC,YAAY,GAAGH,OAAO,CAAC,CAAD,CAA5B,CAAA;;EACA,MAAIG,IAAAA,YAAY,KAAKC,SAArB,EAAgC;EAC/BlC,QAAAA,aAAa,CAACmC,OAAd,CAAsBZ,KAAtB,GAA+BU,YAAY,CAAC3C,IAAI,CAACY,QAAL,CAAckC,UAAf,CAA3C,CAAA;EACA,OAAA;EACD,KAAA;;EAEDzC,IAAAA,iBAAiB,CAACiC,IAAlB,CAAwBtC,IAAxB,EAA8BwC,OAA9B,EAAuCC,SAAvC,CAAA,CAAA;EAEAhC,IAAAA,YAAY,GAAG,KAAf,CAAA;EACA,GAdD,EAjHuC;EAmIvC;EACA;;EACAT,EAAAA,IAAI,CAACuC,IAAL,CAAU,OAAV,EAAkB,gBAAlB,EAAmC,MAAI;EAEtC,IAAA,MAAMX,KAAK,GAAI5B,IAAI,CAAC+C,SAApB,CAAA;EACA,IAAA,IAAIf,MAAJ,CAAA;;EAEA,IAAA,IAAIL,WAAW,CAACC,KAAD,CAAf,EAAwB;EAEvBI,MAAAA,MAAM,GAAGhC,IAAI,CAACgD,MAAL,CAAY,cAAZ,EAA2B;EAACpB,QAAAA,KAAK,EAACA,KAAAA;EAAP,OAA3B,CAAT,CAAA;;EACA,MAAA,IAAII,MAAJ,EAAY;EACXA,QAAAA,MAAM,CAACiB,YAAP,CAAoB,iBAApB,EAAsC,EAAtC,EADW;;EAEXvC,QAAAA,aAAa,GAAGsB,MAAhB,CAAA;EACA,OAAA;EAED,KARD,MAQM,IAAKJ,KAAK,IAAIrB,UAAV,IAAyB,CAACC,gBAAgB,CAAC0C,aAAjB,CAA+B,aAA/B,CAA9B,EAA6E;EAClFlB,MAAAA,MAAM,GAAGhC,IAAI,CAACgD,MAAL,CAAY,iBAAZ,EAA8B;EAACpB,QAAAA,KAAK,EAACA,KAAAA;EAAP,OAA9B,CAAT,CAAA;EACA,KAAA;;EAED,IAAA,IAAII,MAAJ,EAAY;EACXnD,MAAAA,UAAU,CAACmD,MAAD,EAAQhC,IAAI,CAACY,QAAL,CAAcuC,WAAtB,CAAV,CAAA;EACA3C,MAAAA,gBAAgB,CAAC4C,MAAjB,CAAyBpB,MAAzB,CAAA,CAAA;EACA,KAAA;EAED,GAtBD,EArIuC;;EA+JvChC,EAAAA,IAAI,CAACqD,EAAL,CAAQ,YAAR,EAAqB,MAAI;EACxB1C,IAAAA,cAAc,GAAG2C,MAAM,CAACC,IAAP,CAAYvD,IAAI,CAACwC,OAAjB,CAAjB,CAAA;EACAhC,IAAAA,gBAAgB,GAAGR,IAAI,CAACQ,gBAAxB,CAFwB;;EAKxBR,IAAAA,IAAI,CAACY,QAAL,CAAcoC,MAAd,GAAuBM,MAAM,CAACE,MAAP,CAAc,EAAd,EAAkB;EACxC/C,MAAAA,YAAY,EAAC,MAAM;EAClB,QAAA,OAAQ,CAAR,iEAAA,CAAA,CAAA;EACA,OAHuC;EAIxCgD,MAAAA,eAAe,EAAC,MAAK;EACpB,QAAA,OAAQ,CAAR,kDAAA,CAAA,CAAA;EACA,OAAA;EANuC,KAAlB,EAOrBzD,IAAI,CAACY,QAAL,CAAcoC,MAPO,CAAvB,CALwB;;EAgBxBxC,IAAAA,gBAAgB,CAACkD,gBAAjB,CAAkC,QAAlC,EAA2C,MAAI;EAE9C,MAAI,IAAA,CAAC1D,IAAI,CAACY,QAAL,CAAcC,cAAd,CAA6ByB,IAA7B,CAAkCtC,IAAlC,CAAL,EAA8C;EAC7C,QAAA,OAAA;EACA,OAJ6C;;;EAO9C,MAAA,IAAI,CAAC2B,WAAW,CAAC3B,IAAI,CAAC+C,SAAN,CAAhB,EAAkC;EACjC,QAAA,OAAA;EACA,OAT6C;;;EAY9C,MAAA,IAAItC,YAAJ,EAAmB,OAAA;EAGnBA,MAAAA,YAAY,GAAG,IAAf,CAAA;EACAT,MAAAA,IAAI,CAAC2D,IAAL,CAAUrB,IAAV,CAAetC,IAAf,EAAoBA,IAAI,CAAC+C,SAAzB,CAAA,CAAA;EACA,KAjBD,CAAA,CAAA;EAkBA,GAlCD,CAAA,CAAA;EAoCA;;;;;;;;"}